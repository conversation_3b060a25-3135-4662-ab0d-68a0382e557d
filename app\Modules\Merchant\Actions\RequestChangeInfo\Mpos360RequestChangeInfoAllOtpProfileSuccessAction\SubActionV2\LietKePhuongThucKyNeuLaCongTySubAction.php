<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessAction\SubActionV2;

use App\Lib\partner\MNP;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoGetConfigAction\Mpos360RequestChangeInfoGetConfigAction;

class LietKePhuongThucKyNeuLaCongTySubAction
{

	public function run()
	{
		$mnpConfig = app(Mpos360RequestChangeInfoGetConfigAction::class)->run();
		$signProcess = $mnpConfig['data']['signProcess'];

		/**
		 * [
				[
					'mpos360_sign_code' => 'KY_DIEN_TU_MEGADOC',
					'code' => 'E_CONTRACT',
					'name' => '<PERSON>ý điện tử (Megadoc)',
					'signature_url' => ''
				],
				
				[
					'mpos360_sign_code' => 'KY_GIAY',
					'code' => 'PAPER_CONTRACT',
					'name' => 'Ký giấy',
					'signature_url' => ''
				]
			];
		 */
		$signProcess = collect($signProcess)->map(function ($item) {
			$item['signature_url'] = '';
			
			if ($item['code'] == 'E_CONTRACT') {
				$item['mpos360_sign_code'] = 'KY_DIEN_TU_MEGADOC';
			}

			if ($item['code'] == 'PAPER_CONTRACT') {
				$item['mpos360_sign_code'] = 'KY_GIAY';
			}

			if ($item['code'] == 'SALE_SUPPORT') {
				$item['mpos360_sign_code'] = 'SALE_HO_TRO';
			}

			return $item;
		})->values()->all();

		return $signProcess;
	}
} // End class