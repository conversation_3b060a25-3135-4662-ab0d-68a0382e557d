<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthForgotGetNewPasswordV4Action;

use App\Exceptions\BusinessException;
use App\Lib\partner\MPOS;
use App\Lib\PasswordHandler;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360CodeOtp;
use App\Modules\Merchant\Requests\Authen\Mpos360\V4\Mpos360AuthForgotGetNewPasswordV4Request;

class Mpos360AuthForgotGetNewPasswordV4Action
{
	public MPOS $mpos;

	public function __construct(MPOS $mpos)
	{
		$this->mpos = $mpos;	
	}

	public function run(Mpos360AuthForgotGetNewPasswordV4Request $request)
	{
		$mpos360CodeOtp = Mpos360CodeOtp::query()->firstWhere([
			'command_code' => 'FORGOT_PASSWORD',
			'id' => $request->json('data.otp_id'),
		]);

		if (!$mpos360CodeOtp) {
			throw new BusinessException('Lỗi không đúng otp. Bạn vui lòng thử lại');
		}

		if (!$mpos360CodeOtp->isFinalStatus()) {
			throw new BusinessException('Otp không đúng trạng thái. Từ chối cấp mật khẩu mới');
		}

		// Kiểm tra xem mật khẩu mới có phải là mật khẩu đơn giản không, nếu đơn giản thì từ chối xử lý
		$passwordHandler = new PasswordHandler([]);
		$isSimplePassword = $passwordHandler->isSimplePassword($request->json('data.password'));

		if ($isSimplePassword) {
			throw new BusinessException('Mật khẩu mới của bạn quá đơn giản. Hãy thử một mật khẩu khác');
		}

		$getNewPasswordResult = $this->mpos->forgotGetNewPassword([
			'serviceName' => 'CHANGE_PASSWORDS_LV2',
			'username' => $request->json('data.username'),
			'verifyCode' => $mpos360CodeOtp->otp,
			'newPass' => $request->json('data.password')
		]);

		$apiCode = $getNewPasswordResult['data']['error']['code'] ?? '00';

		if ($apiCode != Mpos360Enum::API_SUCCESS_CODE) {
			$mposErrMsg = $getNewPasswordResult['data']['error']['message'] ?? 'Lỗi không cấp được mật khẩu mới';
			$msg = sprintf('Mpos Err - %s (Code: %s)', $mposErrMsg, $apiCode);
			throw new BusinessException($msg);
		}

		return [
			'username' => $request->json('data.username'),
			'status' => 'SUCCESS',
			'msg_title' => 'Mật khẩu đã được cập nhật thành công',
			'msg_desc' => 'Vui lòng đăng nhập bằng mật khẩu mới để tiếp tục sử dụng',
		];
	}
} // End class
