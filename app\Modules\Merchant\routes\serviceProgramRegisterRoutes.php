<?php 

use Illuminate\Support\Facades\Route;
use App\Modules\Merchant\Controllers\ServiceProgramRegister\Mpos360ServiceProgramRegisterController;

// Route::any('/Mpos360ServiceProgramRegisterList', [
// 	'uses' => Mpos360ServiceProgramRegisterController::class . '@Mpos360ServiceProgramRegisterList',
// 	'as' => 'Mpos360ServiceProgramRegisterListAction'
// ])->middleware('checkSumForAnyMobile:email');

// Route::any('/Mpos360ServiceProgramRegisterCheck', [
// 	'uses' => Mpos360ServiceProgramRegisterController::class . '@Mpos360ServiceProgramRegisterCheck',
// 	'as' => 'Mpos360ServiceProgramRegisterCheckAction'
// ])->middleware('checkSumForAnyMobile:email|service_program_id');

// Route::any('/Mpos360ServiceProgramRegisterViewPdf', [
// 	'uses' => Mpos360ServiceProgramRegisterController::class . '@Mpos360ServiceProgramRegisterViewPdf',
// 	'as' => 'Mpos360ServiceProgramRegisterViewPdfAction'
// ])->middleware('checkSumForAnyMobile:email|merchant_id|service_program_id|merchant_type');

// Route::any('/Mpos360ServiceProgramRegisterCreate', [
// 	'uses' => Mpos360ServiceProgramRegisterController::class . '@Mpos360ServiceProgramRegisterCreate',
// 	'as' => 'Mpos360ServiceProgramRegisterCreateAction'
// ])->middleware('checkSumForAnyMobile:email|merchant_id|service_program_id|merchant_type');

// Route::any('/Mpos360ServiceProgramRegisterAttachSign', [
// 	'uses' => Mpos360ServiceProgramRegisterController::class . '@Mpos360ServiceProgramRegisterAttachSign',
// 	'as' => 'Mpos360ServiceProgramRegisterAttachSignAction'
// ])->middleware('checkSumForAnyMobile:email|request_id|signatureUrl');

// Route::any('/Mpos360ServiceProgramRegisterFinish', [
// 	'uses' => Mpos360ServiceProgramRegisterController::class . '@Mpos360ServiceProgramRegisterFinish',
// 	'as' => 'Mpos360ServiceProgramRegisterFinishAction'
// ])->middleware('checkSumForAnyMobile:email|request_id|qts_request_id');

// // cronjob tạo bản ghi
// Route::any('/Mpos360ServiceProgramRegisterPushRecord', [
// 	'uses' => Mpos360ServiceProgramRegisterController::class . '@Mpos360ServiceProgramRegisterPushRecord',
// 	'as' => 'Mpos360ServiceProgramRegisterPushRecordAction'
// ]);


