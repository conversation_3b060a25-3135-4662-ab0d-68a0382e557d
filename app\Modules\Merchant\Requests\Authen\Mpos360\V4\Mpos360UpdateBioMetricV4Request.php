<?php

namespace App\Modules\Merchant\Requests\Authen\Mpos360\V4;

use App\Modules\Merchant\Requests\MerchantRequest;
use Illuminate\Validation\Rule;

class Mpos360UpdateBioMetricV4Request extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.email' => ['required', 'string', 'email', 'max:255'],
			'data.os' => ['required', 'string', Rule::in(['ANDROID', 'IOS'])],
			'data.deviceToken' => ['required', 'string'],
			'data.bioMetricType' => ['present', Rule::in(['FINGER_PRINT', 'FACE_ID', ''])],
			'data.bioMetricData' => ['present', 'string']
		];
	}

	public function messages()
	{
		return [
			'data.email.required' => '<PERSON>ai là bắt buộc',
			'data.email.email' => '<PERSON>ail không đúng định dạng',
			'data.os.required' => 'OS là bắt buộc',
			'data.os.in' => 'OS phải thuộc một trong các giá trị: ANDROID, IOS',
			'data.bioMetricType.required' => 'Loại sinh trắc là bắt buộc',
			'data.bioMetricType.in' => 'Loại sinh trắc phải thuộc một trong các giá trị: vân tay hoặc faceId',
			'data.bioMetricData.required' => 'Data sinh trắc là bắt buộc',
			'data.bioMetricData.STRING' => 'Data sinh trắc phải là kiểu chuỗi',
		];
	}

	public function isOffBioMetric(): bool {
		return empty($this->json('data.bioMetricType'));
	}
} // End class
