<?php

namespace App\Modules\Merchant\Rules\TingTing;

use Illuminate\Contracts\Validation\Rule;

class NgaySinhTu18TuoiRule implements Rule
{
	public function passes($attribute, $birthDay): bool
	{
		$year = substr($birthDay, -4);
		if (now()->year - $year < 18) {
			return false;
		}

		return true;
	}

	/**
	 * Get the validation error message.
	 *
	 * @return string
	 */
	public function message()
	{
		return sprintf('Bạn chưa đủ 18 tuổi để tạo tài khoản. Vui lòng kiểm tra lại thông tin hoặc liên hệ CSKH qua Hotline 1900.63.64.88 để được hỗ trợ', PHP_EOL);
	}
}
