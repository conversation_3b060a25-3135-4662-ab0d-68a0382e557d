<?php

namespace App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalListV2Action;

use App\Lib\partner\MPOS;
use App\Modules\Merchant\Requests\InstantWithdrawal\V3\Mpos360InstantWithdrawalListV2Request;

class Mpos360InstantWithdrawalListV2Action
{
	public MPOS $mpos;

	public function __construct(MPOS $mpos)
	{
		$this->mpos = $mpos;
	}

	public function run(Mpos360InstantWithdrawalListV2Request $request)
	{
		$returnData = [
			'filter' => $this->__getRtnFilter(),
			'data' => []
		];

		$deviceSession = $request->getCurrentDeviceSession();
		$rangeTime = $request->getStartDateEndDate();

		$params = [
			"serviceName" => "GET_DATA_PAGE",
			"merchantFk" => $deviceSession->getMerchantId(),
			"tokenLogin" => $deviceSession->getMposToken(),
			"pageSize" => $request->json('data.limit', 10),
			"pageIndex" => $request->json('data.currentPage'),
		];

		if (!empty($rangeTime['startDate'])) {
			$params['startDate'] = $rangeTime['startDate'];
		}

		if (!empty($rangeTime['endDate'])) {
			$params['endDate'] = $rangeTime['endDate'];
		}

		$result = $this->mpos->getListPaymentNow($params);
		dd($result);
		return $returnData;
	} // End method

	private function __getRtnFilter()
	{
		return [
			[
				'key' => 'time',
				'name' => 'Thời gian',
				'list' => [
					['label' => 'Tất cả', 'value' => 'ALL'],
					['label' => 'Hôm nay', 'value' => 'TODAY'],
					['label' => 'Hôm qua', 'value' => 'YESTERDAY'],
					['label' => 'Tuần này', 'value' => 'THIS_WEEK'],
					['label' => 'Tháng này', 'value' => 'THIS_MONTH'],
				]
			],

			[
				'key' => 'status',
				'name' => 'Trạng thái',
				'list' => [
					['label' => 'Tất cả', 'value' => 'ALL'],
					['label' => 'Chờ duyệt', 'value' => 'CHO_DUYET'],
					['label' => 'Ngân hàng đang chi', 'value' => 'NH_DANG_CHI'],
					['label' => 'Đã thanh toán', 'value' => 'DA_THANH_TOAN'],
					['label' => 'Chi tiền thất bại', 'value' => 'CHI_TIEN_THAT_BAI'],
				]
			],
		];
	}
} // End clas
