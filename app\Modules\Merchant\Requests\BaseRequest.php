<?php

namespace App\Modules\Merchant\Requests;

use Exception;
use App\Lib\Security;
use App\Lib\Utilities;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\Merchant\Model\PartnerConfig;

class BaseRequest extends FormRequest
{
  public static $currentClient;

  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      //
    ];
  }


  public function getCurrentClient()
  {
    if (!self::$currentClient) {
      $apiKey = $this->json('api_key');
      $currentClient = PartnerConfig::getCurrentClient(['api_key' => $apiKey]);
      self::$currentClient = $currentClient;
    }

    return self::$currentClient;
  }

  public function getApiKey()
  {
    return $this->json('api_key');
  }

  public function getApiSecret()
  {
    $currentClient = $this->getCurrentClient();
    return decrypt($currentClient->api_secret);
  }

  protected function passedValidation()
  {
    $params = $this->all();
    
    if (isset($params['data']['mobile'])) {
      $mobile = Security::cleanXss($params['data']['mobile']);
      $mobile = Utilities::mobileNumberFull($mobile);
  
      $params['data']['mobile'] = $mobile;
    }
   
    $this->merge($params);
  }
} // End class
