<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction;

use App\Exceptions\BusinessException;
use App\Lib\partner\MPOS;
use App\Modules\Merchant\Enums\Mpos360Enum;

class LoginMerchantViaMposSubAction
{
	public function run(string $email, string $password, string $os='iOS', $fcmDeviceToken='')
	{
		$params = [
			'deviceIdentifier' => $fcmDeviceToken,
			'os'               => $os,
			'language'         => 'vi',
			'email'            => $email,
			'password'         => $password,
			'bundle'           => ''
		];

		$loginMpos = (new MPOS())->login($params);
		
		$params['password'] = 'HIDDEN_BY_SYSTEM';
		mylog([
			'Params' => $params,
			'Result' => $loginMpos
		]);
		
		$apiCode = $loginMpos['data']['error']['code'];

		if (empty($apiCode)) {
			throw new BusinessException('MPOS Err: ' . vmsg('LoginMerchantViaMposSubAction_KhongCoThongTinTraVeTuDoiTac'));
		}

		if ($apiCode == Mpos360Enum::API_WRONG_PASSWORD_CODE) {
			throw new BusinessException(vmsg('LoginMerchantViaMposSubAction_MatKhauKhongChinhXac'));
		}

		if ($apiCode != Mpos360Enum::API_SUCCESS_CODE) {
			$message = $loginMpos['data']['error']['message'] ?? vmsg('LoginMerchantViaMposSubAction_LoiApiDangNhapDoiTac');
			throw new BusinessException('MPOS Err: ' . $message);
		}

		if (!filter_var($loginMpos['data']['emailMerchant'], FILTER_VALIDATE_EMAIL)) {
			$loginMpos['data']['emailMerchant'] = $loginMpos['data']['emailMerchant'] . '@mpos360.vn';
		}
		
		return $loginMpos;
	}
}
