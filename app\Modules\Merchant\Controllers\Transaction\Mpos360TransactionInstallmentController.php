<?php

namespace App\Modules\Merchant\Controllers\Transaction;

use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionInstallmentListRequest;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionInstallmentDetailRequest;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransactionInstallmentListAction\Mpos360TransactionInstallmentListAction;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransactionInstallmentDetailAction\Mpos360TransactionInstallmentDetailAction;

// xử lý các giao dịch trả góp
class Mpos360TransactionInstallmentController extends Controller
{
	public function Mpos360TransactionInstallmentList(Mpos360TransactionInstallmentListRequest $request)
	{
		try {
			$result = app(Mpos360TransactionInstallmentListAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360TransactionInstallmentDetail(Mpos360TransactionInstallmentDetailRequest $request)
	{
		try {
			$result = app(Mpos360TransactionInstallmentDetailAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
} // End class
