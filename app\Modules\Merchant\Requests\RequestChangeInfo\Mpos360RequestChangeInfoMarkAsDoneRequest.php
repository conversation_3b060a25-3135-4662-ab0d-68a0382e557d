<?php

namespace App\Modules\Merchant\Requests\RequestChangeInfo;

use App\Modules\Merchant\Requests\MerchantRequest;
use Illuminate\Validation\Rule;

class Mpos360RequestChangeInfoMarkAsDoneRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.id' => ['required', 'numeric', 'integer', 'min:1'],
			'data.other_data' => ['required', 'array'],
			'data.other_data.is_matching_facescan' => ['required', Rule::in([0, 1])],
			'data.other_data.matching_percent' => ['required', 'numeric', 'integer', 'min:1', 'max:100'],
		];
	}
}
