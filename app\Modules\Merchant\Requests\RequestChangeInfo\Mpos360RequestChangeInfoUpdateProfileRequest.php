<?php

namespace App\Modules\Merchant\Requests\RequestChangeInfo;

use App\Modules\Merchant\Requests\MerchantRequest;
use App\Modules\Merchant\Rules\RequestChangeInfoRule\GroupProfileRequiredRule;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;

class Mpos360RequestChangeInfoUpdateProfileRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}


	public function rules()
	{
		$code = $this->json('data.request_change_info.code');
		$rules = [
			'data.email' => ['required', 'string', 'email'],
			'data.choice' => [
				'present',
				'string',
				Rule::in([
					'DOI_NGUOI_DAI_DIEN_MOI',
					'DOI_THONG_TIN_LIEN_HE',
					'DOI_CCCD_MOI',
					'',
				]),
				'required_if:data.request_change_info.code,CHANGE_REPRESENT_INFO'
			],
			'data.request_change_info' => ['required', 'array'],
			'data.request_change_info.code' => ['required', 'string', 'max:255', Rule::in(['CHANGE_BANK_ACCOUN_INFO', 'CHANGE_REPRESENT_INFO'])],
			'data.request_change_info.name' => ['required', 'string', 'max:500'],
			'data.request_change_info.profiles' => ['required', 'array', new GroupProfileRequiredRule($code)],
			'data.request_change_info.profiles.*.profileKey' => ['required', 'string', 'max:255'],
			'data.request_change_info.profiles.*.value' => ['present', 'string', 'max:255'],
		];

		if ($code == 'CHANGE_BANK_ACCOUN_INFO') {
			$rule[] = [
				'data.request_vefify' => ['array', 'required'],
				'data.request_vefify.*.field' => ['required', 'string'],
				'data.request_vefify.*.value' => ['required', 'string'],
				'data.request_vefify.*.status_verify' => ['required', 'string', Rule::in(['1'])],
				'data.request_vefify.*.date_verify' => ['required', 'numeric', 'integer'],
			];
		}
		
		return $rules;
	}

	public function isChangeThongTinNganHang(): bool {
		return $this->json('data.request_change_info.code') == 'CHANGE_BANK_ACCOUN_INFO';
	}

	public function isChangeNguoiDaiDien(): bool {
		return $this->json('data.request_change_info.code') == 'CHANGE_REPRESENT_INFO';
	}

	public function isDoiThongTinLienHe(): bool {
		return $this->isChangeNguoiDaiDien() && $this->json('data.choice') == 'DOI_THONG_TIN_LIEN_HE';
	}

	public function isDoiCccdMoi(): bool {
		return $this->isChangeNguoiDaiDien() && $this->json('data.choice') == 'DOI_CCCD_MOI';
	}

	public function isDoiNguoiDaiDienMoi(): bool {
		return $this->isChangeNguoiDaiDien() && $this->json('data.choice') == 'DOI_NGUOI_DAI_DIEN_MOI';
	}

	public function getMnpGroupCode(): string {
		return $this->json('data.request_change_info.code');
	}

	public function getMnpProfiles(): array {
		return $this->json('data.request_change_info.profiles');
	}

	public function getEmailNguoiDaiDienMoi(): string {
		$collect = collect($this->json('data.request_change_info.profiles'));
		return $collect->where('profileKey', 'authoriserEmail')->first()['value'];
	}

	public function getMobileNguoiDaiDienMoi(): string {
		$collect = collect($this->json('data.request_change_info.profiles'));
		return $collect->where('profileKey', 'authoriserContactNumber')->first()['value'];
	}

	protected function passedValidation()
	{
		$params = $this->all();

		foreach ($params['data']['request_change_info']['profiles'] as &$profile) {
			if (is_string($profile['value']) && $profile['type'] == 'INPUT') {
				$profile['value'] = cleanXSS($profile['value']);
			}
		}

		if (!empty($params['data']['request_vefify'])) {
			foreach ($params['data']['request_vefify'] as &$p) {
				if ( is_string($p['value']) ) {
					$p['value'] = cleanXSS($p['value']);
				}
			}
		}

		$this->merge($params);
	}
} // End class
