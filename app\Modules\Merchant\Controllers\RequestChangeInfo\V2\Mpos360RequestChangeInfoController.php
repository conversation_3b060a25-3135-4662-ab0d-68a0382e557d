<?php

namespace App\Modules\Merchant\Controllers\RequestChangeInfo\V2;

use App\Lib\Helper;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\RequestChangeInfo\V2\Mpos360RequestChangeInfoPickSignMethodRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\V2\Mpos360RequestChangeInfoUploadPhuLucKyRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\V2\Mpos360RequestChangeInfoAttachSignatureRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUploadPhuLucKyAction\Mpos360RequestChangeInfoUploadPhuLucKyAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAttachSignatureAction\Mpos360RequestChangeInfoAttachSignatureAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessAction\Mpos360RequestChangeInfoPickSignMethodAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessAction\Mpos360RequestChangeInfoAllOtpProfileSuccessV2Action;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\Mpos360RequestChangeInfoAdditionalAttachmentVer2Action;

class Mpos360RequestChangeInfoController extends Controller
{
	// bổ sung đính kèm
	public function Mpos360RequestChangeInfoAdditionalAttachment(Mpos360RequestChangeInfoAdditionalAttachmentRequest $request)
	{
		try {
			$result = app(Mpos360RequestChangeInfoAdditionalAttachmentVer2Action::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	// Đánh dấu các là yêu cầu đã đc verify toàn bộ
	public function Mpos360RequestChangeInfoAllOtpProfileSuccess(Mpos360RequestChangeInfoAllOtpProfileSuccessRequest $request)
	{
		try {
			$result = app(Mpos360RequestChangeInfoAllOtpProfileSuccessV2Action::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	// Chọn phương thức ký
	public function Mpos360RequestChangeInfoPickSignMethod(Mpos360RequestChangeInfoPickSignMethodRequest $request)
	{
		try {
			$result = app(Mpos360RequestChangeInfoPickSignMethodAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360RequestChangeInfoUploadPhuLucKy(Mpos360RequestChangeInfoUploadPhuLucKyRequest $request)
	{
		try {
			$result = app(Mpos360RequestChangeInfoUploadPhuLucKyAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	// Attach chữ ký vào trong yêu cầu
	public function Mpos360RequestChangeInfoAttachSignature(Mpos360RequestChangeInfoAttachSignatureRequest $request)
	{
		try {
			$deviceSession = $request->getCurrentDeviceSession();
			
			$result = app(Mpos360RequestChangeInfoAttachSignatureAction::class)->run(
				$deviceSession,
				$request->json('data.signature_id'),
				$request->json('data.request_id')
			);

			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
} // End class
