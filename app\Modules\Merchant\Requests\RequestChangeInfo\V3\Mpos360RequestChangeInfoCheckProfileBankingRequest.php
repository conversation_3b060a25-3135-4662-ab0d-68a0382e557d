<?php

namespace App\Modules\Merchant\Requests\RequestChangeInfo\V3;

use Illuminate\Support\Str;
use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360RequestChangeInfoCheckProfileBankingRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.bankId' => ['required', 'string'], // thể hiện của bankId
			'data.bankName' => ['required', 'string'], // thể hiện của bankId
			'data.accountNo' => ['required', 'string'],
			'data.holderName' => ['required', 'string']
		];
	}

	public function getParamCheckBankVmmc(): array {
		$holderName =  Str::of($this->json('data.holderName'))->slug(' ')->trim()->upper()->__toString();
		$bankId = trim($this->json('data.bankId', '')); // gia tri nay se lay tu ben MNP
		
		return [
			'bank_account_holder' => $holderName,
			'bank_account' => trim($this->json('data.accountNo')),
			'bank_id' => config('profilemnp.bankMnpVimo.' . $bankId, '')
		];
	}

	protected function passedValidation()
	{
		$params = $this->all();
		$params['data']['holderName'] = Str::of($params['data']['holderName'])->slug(' ')->trim()->upper()->__toString();
		$this->merge($params);
	}
} // End class
