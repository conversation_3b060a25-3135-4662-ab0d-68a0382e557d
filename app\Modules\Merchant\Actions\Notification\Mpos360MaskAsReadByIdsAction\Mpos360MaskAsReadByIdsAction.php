<?php

namespace App\Modules\Merchant\Actions\Notification\Mpos360MaskAsReadByIdsAction;

use App\Exceptions\BusinessException;
use App\Lib\partner\MNPNOTIFY;
use App\Modules\Merchant\Requests\Notification\Mpos360MaskAsReadByIdsRequest;

class Mpos360MaskAsReadByIdsAction
{
	public function run(Mpos360MaskAsReadByIdsRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();

		$mposToken = $deviceSession->getMposToken();
		$mposEmail = $deviceSession->getMerchantEmail();

		$notificationIds = $request->json('data.notificationIds');

		if (empty($notificationIds)) {
			throw new BusinessException('NotificationIds là bắt buộc');
		}

		mylog([
			'mposEmail' => $mposEmail,
			'mposToken' => $mposToken,
			'mposToken' => $mposToken,
			'notificationIds' => $notificationIds
		]);

		$mnpNotify = new MNPNOTIFY($mposToken);

		$markAsReadResult = $mnpNotify->markAllAsRead($deviceSession->getMerchantUserName(), $notificationIds);
		mylog(['markAsReadResult' => $markAsReadResult]);
		return $markAsReadResult;
	}
} // End class