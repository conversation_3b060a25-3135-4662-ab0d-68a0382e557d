<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransQRDetailAction;

use App\Lib\Helper;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransQRListAction\Mpos360TransQRListSubAction\TransQRMappingSubAction;

class Mpos360TransQRDetailAction
{

	public function run($request)
	{
        $mapTrangThai = app(TransQRMappingSubAction::class)->mappingStatusTransaction();
        $mapType = app(TransQRMappingSubAction::class)->mappingTransactionType();

		$returnData = [
			'data' =>
			[
				[
					'key' => 'ThongTinChung',
					'name' => 'Thông tin chung',
					'list' =>
					[
						[
							"key" => "PhuongThuc",
							"label" => "Phương thức",
							"value" => "VietQR",
							"other_data" => (object)[]
						],
						[
							"key" => "LoaiQR",
							"label" => "Loại QR",
							"value" => "QR động",
							"other_data" => (object)[]
						],
						[
							"key" => "MaGD",
							"label" => "Mã GD",
							"value" => "Ma_GD",
							"other_data" => (object)[]
						],
						[
							"key" => "MaChuanChi",
							"label" => "Mã chuẩn chi",
							"value" => "Ma_chuan_chi",
							"other_data" => (object)[]
						],
						[
							"key" => "ThoiGianGD",
							"label" => "Thời gian GD",
							"value" => "10:30, 12/10/2024",
							"other_data" => (object)[]
						],
						[
							"key" => "TGKetToan",
							"label" => "TG Kết toán",
							"value" => "10:30, 12/10/2024",
							"other_data" => (object)[]
						],
						[
							"key" => "TGHuyGD",
							"label" => "TG Huỷ GD",
							"value" => "10:30, 12/10/2024",
							"other_data" => (object)[]
						],
						[
							"key" => "TrangThaiGD",
							"label" => "Trạng thái GD",
							"value" => "Đã kết toán",
							"other_data" => (object)[
								'text_color' => '#ffffff',
								'bg_color' => '#008BF4',
								'display_type' => 'pills'
							]
						],
					]
				],

				[
					'key' => 'ThongTinDonHang',
					'name' => 'Thông tin đơn hàng',
					'list' =>
					[
						[
							"key" => "SoTienDonHang",
							"label" => "Số tiền đơn hàng",
							"value" => "100.000.000 VND",
							"other_data" => (object)[]
						],
						[
							"key" => "KhuyenMai",
							"label" => "Khuyến mại",
							"value" => "100.000.000 VND",
							"other_data" => (object)[]
						],
						[
							"key" => "PhiGD",
							"label" => "Phí GD(thu KH)",
							"value" => "100.000.000 VND",
							"other_data" => (object)[]
						],
						[
							"key" => "TongThanhToan",
							"label" => "Tổng thanh toán",
							"value" => "100.000.000 VND",
							"other_data" => (object)[
								"text_color" => "#ffffff",
								"bg_color" => "#008BF4"
							]
						],
					]
				],

				[
					'key' => 'KhachHang',
					'name' => 'Khách hàng',
					'list' =>
					[
						[
							"key" => "ChuThe",
							"label" => "Chủ thẻ",
							"value" => "Tên chủ thẻ",
							"other_data" => (object)[]
						],
						[
							"key" => "LoaiThe",
							"label" => "Thẻ/TKNH/Ví điện tử",
							"value" => "Visa1111",
							"other_data" => (object)[]
						],
						[
							"key" => "NhaPhatHanh",
							"label" => "Nhà phát hành thẻ/Ứng dụng",
							"value" => "HSBC Paywave",
							"other_data" => (object)[]
						],

					],
				],

				[
					'key' => 'ThongTinThanhToan',
					'name' => 'Thông tin thanh toán',
					'list' =>
					[
						[
							"key" => "SoTienThanhToan",
							"label" => "Số tiền thanh toán",
							"value" => "100.000.000 VND",
							"other_data" => (object)[]
						],
						[
							"key" => "PhiGD",
							"label" => "Phí GD(thu Merchant)",
							"value" => "100.000 VND",
							"other_data" => (object)[
								"text_color" => "#ffffff",
								"bg_color" => "#008BF4",
								"font_weight" => ""
							]
						],
						[
							"key" => "TrangThai",
							"label" => "Trạng thái",
							"value" => "Chưa thanh toán",
							"other_data" => (object)[
								'text_color' => '#ffffff',
								'bg_color' => '#FDB62F',
								'display_type' => 'pills'
							]
						],

					],
				],

				[
					'key' => 'ThongTinChungBoSung',
					'name' => 'Thông tin chung',
					'list' =>
					[
						[
							"key" => "SoLo",
							"label" => "Số lô",
							"value" => "11111",
							"other_data" => (object)[]
						],
						[
							"key" => "TID",
							"label" => "TID",
							"value" => "1111111",
							"other_data" => (object)[]
						],
						[
							"key" => "MID",
							"label" => "MID",
							"value" => "121212",
							"other_data" => (object)[]
						],
						[
							"key" => "MaCapPhep",
							"label" => "Mã cấp phép",
							"value" => "121212",
							"other_data" => (object)[]
						],
						[
							"key" => "SoThamChieu",
							"label" => "Số tham chiếu",
							"value" => "121212",
							"other_data" => (object)[]
						],
						[
							"key" => "MobileUser",
							"label" => "Mobile User",
							"value" => "121212",
							"other_data" => (object)[]
						],

					],
				],
			]
		];
		
		return $returnData;
	}
}