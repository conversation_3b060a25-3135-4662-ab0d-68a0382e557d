<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction;

class BuildChoiceOptionSubAction
{
	/**
	 * Mặc định: khi đổi thông tin người đại diện thì có 3 option:
	 * 1-đổi người đại diện mới
	 * 2-đổi cccd mới
	 * 3-đổi thông tin liên hệ (option này muốn làm thì lúc get detail ra phải có giá trị cccd trước mới ko bị lỗi)
	 * 
	 * Class này sẽ check xem lúc get detail profiles đã có hay chưa
	 * 
	 * @param array $mnpMerchantDetail
	 * array:3 [
			"status" => true
			"data" => array:26 [
				"bank" => array:8 [
					"editable" => 1
					"display" => "ABB - ABBANK - NH TMCP AN BÌNH"
					"format" => "TEXT"
					"position" => 0
					"label" => "Ngân hàng"
					"type" => "DROPDOWN"
					"value" => "5fb22b472cc8fd12d0623364"
					"group" => "CHANGE_BANK_ACCOUN_INFO"
				]
				"passport" => array:8 [
					"editable" => 1
					"display" => "***********"
					"format" => "TEXT"
					"position" => 0
					"label" => "Số CMND/CCCD/Hộ chiếu"
					"type" => "INPUT"
					"value" => "***********"
					"group" => "CHANGE_REPRESENT_INFO"
				]
				....
			]
			"message" => "DO_SERVICE_SUCCESS"
		]
	 */
	public function run(array $mnpMerchantDetail): array
	{
		$choice = [
			['code' => 'DOI_NGUOI_DAI_DIEN_MOI', 'name' => vmsg('Đổi người đại diện mới')],
			['code' => 'DOI_CCCD_MOI', 'name' => vmsg('Đổi CCCD mới')],
			['code' => 'DOI_THONG_TIN_LIEN_HE', 'name' => vmsg('Đổi thông tin liên hệ')]
		];

		return $choice;
	}
} // End class
