<section class="ftco-section login-wap">

  <div class="wrapper-page">
    <div class="head-wrap">
        <label><PERSON><PERSON>ng ký tài k<PERSON>n</label>
        <span><img src="assets/img/logo-mpos360.svg"></span>
    </div>

    <div class="login-wrap">


      <div class="mpos360-head">
        <label>X<PERSON><PERSON> thực tài kho<PERSON>n</label>
      </div>
      <div class="mpos360-form">
        <label class="mf-title">Căn cước/CCCD chủ cửa hàng
          <!-- <a href=""><img src="assets/img/icon-qr.svg"></a> -->
        </label>
        <div class="notifi-item">
          <form id="form-submit">
            <div class="form-floating mb-3">
              <input type="text" class="form-control" id="name" name="name" placeholder="Họ và tên" autocomplete="off" value="{{ old('name', $data['name']) }}">
              <label for="name">Họ và tên</label>
            </div>
            <div class="form-floating mb-3">
              <input type="tel" class="form-control" id="cccd" name="cccd" placeholder="Số CCCD" autocomplete="off" value="{{ old('cccd', $data['cccd']) }}">
              <label for="cccd">Số CCCD</label>
            </div>
						
						<div class="form-floating mb-3">
              <input type="text" class="form-control" id="birthDay" name="birthDay" autocomplete="off" value="{{ old('birthDay', $data['birthDay']) }}">
              <label for="birthDay">Ngày sinh</label>
            </div>

            <div class="form-floating mb-3">
              <select class="form-select" id="gender" name="gender">
                <option value="">-- Chọn Giới tính --</option>
                <option value="male" {{ old('gender', $data['gender']) == 'male' ? 'selected' : '' }}>Nam</option>
                <option value="female" {{ old('gender', $data['gender']) == 'female' ? 'selected' : '' }}>Nữ</option>
              </select>
              <label for="gender">Giới tính</label>
            </div>
            <div class="form-floating mb-3">
              <input type="text" class="form-control" id="address" name="address" placeholder="Địa chỉ thường trú" autocomplete="off" value="{{ old('address', $data['address']) }}">
              <label for="address">Địa chỉ thường trú</label>
            </div>
           
          </form>


        </div>

      </div>

      <div class="mpos360-footer">
        <a href="#" class="text-center btn-blue w-100 d-block submit-form">Tiếp tục</a>
      </div>

    </div>
  </div>

</section>