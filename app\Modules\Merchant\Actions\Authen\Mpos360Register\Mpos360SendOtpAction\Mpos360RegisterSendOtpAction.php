<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360SendOtpAction;

use App\Lib\MnpOnboardNewMcHelper;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360Register\Mpos360SendOtpRequest;
use App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360SendOtpAction\SubAction\SendOtpSubAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360SendOtpAction\SubAction\CreateOtpDangKySubAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360SendOtpAction\SubAction\GetOtpChuaSuDungSubAction;

class Mpos360RegisterSendOtpAction
{
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run(Mpos360SendOtpRequest $request): array
	{
		// Kiểm tra MC hiện tại đã có otp hay chưa, nếu có rồi thì bắt đợi -> tranh spam
		// $mpos360CodeOtpCoSan = app(GetOtpChuaSuDungSubAction::class)->run($request->json('data.username'), 'REGISTER');
		
		// if ($mpos360CodeOtpCoSan) {
		// 	$msg = sprintf('Bạn cần phải đợi trong %s giây nữa mới có thể nhận otp đăng ký tài khoản', $mpos360CodeOtpCoSan->time_out - now()->timestamp);
		// 	throw new BusinessException($msg);
		// }

		if ($request->json('data.channel') == 'ZALO') {
			$currentHour = now()->hour;
			if ($currentHour >= 22 || $currentHour < 7) {
					throw new BusinessException('Để tiếp tục, vui lòng chọn nhận mã qua SMS. Zalo không hỗ trợ gửi mã vào khung giờ 22:00 - 07:00');
			}
		}

		if ($request->isTaoMoiOtpDangKy()) {
			$mpos360CodeOtp = app(CreateOtpDangKySubAction::class)->run($request, $request->json('data.otp_id'));
		}

		if ($request->isGuiLaiOtpDangKy()) {
			$mpos360CodeOtp = app(CreateOtpDangKySubAction::class)->run($request, $request->json('data.otp_id'));
		}

		$sendOtpResult = app(SendOtpSubAction::class)->run($request, $mpos360CodeOtp);

		return [
			'otp_id' => $mpos360CodeOtp->id,
			'channel' => $request->json('data.channel'),
			'username' => $request->json('data.username'),
			'countdown_time_get_new_otp' => $mpos360CodeOtp->time_out - time(),
			'other_data' => (object) []
		];
	}
}
