<?php

namespace App\Modules\Merchant\Requests\Authen\Mpos360\V4;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360GetBioMetricV4Request extends FormRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.email' => ['present', 'string', 'max:255'],
			'data.os' => ['required', 'string', Rule::in(['ANDROID', 'IOS'])],
			'data.deviceToken' => ['required', 'string'],
		];
	}

	public function messages()
	{
		return [
			'data.email.required' => 'Emai là bắt buộc',
			'data.email.email' => 'Email không đúng định dạng',
			'data.os.required' => 'OS là bắt buộc',
			'data.os.in' => 'OS phải thuộc một trong các giá trị: ANDROID, IOS',
			'data.deviceToken.required' => 'DeviceToken là bắt buộc',
		];
	}
} // End class
