<section class="ftco-section login-wap">


  <div class="wrapper-page">
    <div class="head-wrap">
        <label><PERSON><PERSON>ng ký tài kho<PERSON>n</label>
        <span><img src="assets/img/logo-mpos360.svg"></span>
    </div>
    <div class="login-wrap">
      <div class="mpos360-head">
        <label><PERSON><PERSON><PERSON> thực tài kho<PERSON>n</label>
      </div>
			<form action="/TBWebMpos360SubmitAuthenAccountStep2" enctype="multipart/form-data" method="POST" onsubmit="return onSubmitCCCD(event)">

				<div class="mpos360-form">
					<label class="mf-title">Căn cước/CCCD chủ cửa hàng <a href="#" id="edit-authen-account"><img src="assets/img/icon-edit.svg"></a></label>
					<ul class="mf-review">
						<li>
							<p><PERSON><PERSON> và tên</p>
							<span>{{ isset($data['name']) ? $data['name'] : '' }}</span>
							<input type="hidden" name="name" value="{{ isset($data['name']) ? $data['name'] : '' }}">
						</li>
						<li>
							<p>Số căn cước</p>
							<span>{{ isset($data['cccd']) ? $data['cccd'] : '' }}</span>
							<input type="hidden" name="cccd" value="{{ isset($data['cccd']) ? $data['cccd'] : '' }}">
						</li>
						<li>
							<p>Giới tính</p>
							<span>{{ isset($data['gender']) ? $data['gender'] : '' }}</span>
							<input type="hidden" name="gender" value="{{ isset($data['gender']) ? $data['gender'] : '' }}">
						</li>
						<li>
							<p>Địa chỉ thường trú</p>
							<span>{{ isset($data['address']) ? $data['address'] : '' }}</span>
							<input type="hidden" name="address" value="{{ isset($data['address']) ? $data['address'] : '' }}">
						</li>
						<li>
							<p>Ngày sinh</p>
							<span>{{ isset($data['birthDay']) ? $data['birthDay'] : '' }}</span>
							<input type="hidden" name="birthDay" value="{{ isset($data['birthDay']) ? $data['birthDay'] : '' }}">
						</li>
					</ul>

					<ul class="mf-picture d-flex row">
						<li class="col-6">
							<input type="file" name="files[]" class="file-input" accept="image/*" data-face="before" style="display: none;">
							<a href="javascript:void(0)" class="upload-trigger">
								<img src="{{ cumtomAsset('assets/img/cccd_temp.png') }}" class="preview-image" style="height: 120px;">
								<span><img src="{{ cumtomAsset('assets/img/icon-camera.svg') }}">Chọn ảnh</span>
							</a>
							<p>Ảnh mặt trước</p>
						</li>

						<li class="col-6">
							<input type="file" name="files[]" class="file-input" data-face="after" accept="image/*" style="display: none;">
							<a href="javascript:void(0)" class="upload-trigger">
								<img src="{{ cumtomAsset('assets/img/cccd_temp.png') }}" class="preview-image" style="height: 120px;">
								<span><img src="{{ cumtomAsset('assets/img/icon-camera.svg') }}">Chọn ảnh</span>
							</a>
							<p>Ảnh mặt sau</p>
						</li>
					</ul>
				</div>

				<input type="hidden" name="merchantId" value="{{ $data['merchantId'] }}">
				<input type="hidden" name="signed" value="{{ $data['signed'] }}">

				<div class="mpos360-footer">
					<button type="submit" class="text-center btn-blue w-100" style="border: none;" onclick="return onHoanTatCCCD()">Hoàn tất</button> 
				</div>
			</form>

    </div>
  </div>

</section>
<script>
  $(document).ready(function() {
    const APP_URL = "";

    // Khi nhấn vào "Chụp lại"
    $('.upload-trigger').on('click', function(e) {
      e.preventDefault();
      $(this).siblings('.file-input').trigger('click');
    });

    // Khi người dùng chọn file
    $('.file-input').on('change', function() {
      const file = this.files[0]; // Lấy file đầu tiên

      const reader = new FileReader();
      const $previewImage = $(this).siblings('.upload-trigger').find('.preview-image');

      // Kiểm tra xem file có phải là ảnh
      if (file && file.type.startsWith('image/')) {
        reader.onload = function(e) {
          // Cập nhật hình ảnh preview
          $previewImage.attr('src', e.target.result);
        };
        reader.readAsDataURL(file); // Đọc file
      } else {
        alert('Vui lòng chọn một tệp ảnh hợp lệ.');
        $(this).val(''); // Xóa giá trị của input file
      }
    });

    $(document).on('click', '#edit-authen-account', function(e) {
      e.preventDefault();
      $('#loadingPage').addClass('se-pre-con');

      const formData = {
        name: "{{ $data['name'] }}",
        cccd: "{{ $data['cccd'] }}",
        gender: "{{ $data['gender'] }}",
        address: "{{ $data['address'] }}",
        birthDay: "{{ $data['birthDay'] }}",
        sessionId: "{{ $data['sessionId'] }}",
        merchantId: "{{ $data['merchantId'] }}",
        signed: "{{ $data['signed'] }}",
      };

      $.ajax({
        url: APP_URL + "/TBWebMpos360EditAuthenAccount",
        type: "POST",
        headers: {
          "X-CSRF-TOKEN": "{{ csrf_token() }}",
        },
        contentType: "application/json",
        data: JSON.stringify(formData),
        success: function(data) {
          if (data.success) {
              $('#content-step-1').html(data.html);
              $('#content-step-2').html('');
							
							$("#birthDay").flatpickr({
								dateFormat: 'd/m/Y',
								enableTime: false,
								disableMobile: "true"
							});
          } else {
            alert(data.message);
          }
          $('#loadingPage').removeClass('se-pre-con');
        },
        error: function(error) {
          $('#loadingPage').removeClass('se-pre-con');
          alert("Có lỗi xảy ra, Hãy thông báo kỹ thuật hỗ trợ xử lý.");
        },
      });
    });

  
    window.addEventListener('beforeunload', (event) => {
      $('#loadingPage').addClass('se-pre-con');
    });
  });

	function onSubmitCCCD(event) {
		var arrayFile = [];
		$('.file-input').each(function (index, item) {
			if (item.files[0]) {
				arrayFile.push(item.files[0]);
			}
		})
		

		if (arrayFile.length != 2) {
			alert('Bạn phải chọn đầy đủ ảnh 2 mặt CCCD');
			event.preventDefault();
			return false;
		}
		
		$('#loadingPage').addClass('se-pre-con');
		
		return true;
	}
</script>