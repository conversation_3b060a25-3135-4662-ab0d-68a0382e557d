<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction;

use App\Modules\Merchant\Model\Setting;
use App\Modules\Merchant\DTOs\RequestChangeInfo\CreateMerchantRequestDto;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\GetMNPAccessTokenSubAction;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoUpdateProfileRequest;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForBankingSA\TaoYcDoiSTKKhacCuaHKDSA;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\SubAction\KiemTraQuyenTaoYeuCauThayDoiSubAction;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForRepresentationSA\TaoYcNguoiDaiDienVer3SA;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForBankingSA\TaoYcDoiSTKKhacCuaDoanhNghiepSA;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForRepresentationSA\SA\MappingProfileWithMnpV3SA;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForBankingSA\TaoYcDoiSTKCuaCaNhanDuocHKDUyQuyenSA;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForBankingSA\TaoYcDoiSTKCuaCaNhanDuocDoanhNghiepUyQuyenSA;

class Mpos360RequestChangeInfoUpdateProfileAction
{
	public function run(Mpos360RequestChangeInfoUpdateProfileRequest $request)
	{
		// Build param hồ sơ để sẵn sàng đẩy sang MNP
		$profileGroup = app(MappingProfileWithMnpV3SA::class)->run($request);
		mylog(['profileGroup' => $profileGroup]);


		// Build DTO
		$deviceSession = $request->getCurrentDeviceSession();
		$merchantId = $deviceSession->getMerchantId();


		$deviceSessionWithToken = app(GetMNPAccessTokenSubAction::class)->run($deviceSession);
		$listMcCanSpamCreateRequest = Setting::query()->where('key', 'LIST_MERCHANT_CAN_SPAM_REQUEST')->first();

		$emailsCanSpamCreate = [];

		if (!empty(optional($listMcCanSpamCreateRequest)->value)) {
			$emailsCanSpamCreate = json_decode($listMcCanSpamCreateRequest->value, true);
		}

		$merchantEmail = $deviceSession->getMerchantEmail();

		if (!in_array($merchantEmail, $emailsCanSpamCreate)) {
			$validate = app(KiemTraQuyenTaoYeuCauThayDoiSubAction::class)->run($deviceSessionWithToken, $request);
		}

		$dto = new CreateMerchantRequestDto(
			$merchantId,
			$deviceSession->getMerchantUserName(),
			json_encode([$profileGroup]),
			'{}',
			''
		);
		
		$dto->version = 3;

		// Đổi TKNH
		if ($request->isChangeThongTinNganHang()) {
			$dto->setRequestVerifyIntoDataRequest(request()->json('data.request_vefify', []));
			if ($request->isDoiSTKKhacCuaHKD()) {
				return app(TaoYcDoiSTKKhacCuaHKDSA::class)->run($dto, $request);
			}

			if ($request->isDoiSTKCuaCaNhanDuocHKDUyQuyen()) {
				return app(TaoYcDoiSTKCuaCaNhanDuocHKDUyQuyenSA::class)->run($dto, $request);
			}

			if ($request->isDoiSTKKhacCuaDoanhNghiep()) {
				return app(TaoYcDoiSTKKhacCuaDoanhNghiepSA::class)->run($dto, $request);
			}

			if ($request->isDoiSTKCuaCaNhanDuocDoanhNghiepUyQuyen()) {
				return app(TaoYcDoiSTKCuaCaNhanDuocDoanhNghiepUyQuyenSA::class)->run($dto, $request);
			}
		}


		// Đổi info người đại diện
		if ($request->isChangeNguoiDaiDien()) {
			return app(TaoYcNguoiDaiDienVer3SA::class)->run($dto, $request);
		}
	} // End method
} // End class
