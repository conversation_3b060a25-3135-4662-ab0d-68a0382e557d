<?php

namespace App\Modules\Merchant\DTOs\Transaction\Mpos360TransactionHistoryDto;

use App\Lib\Helper;

class Mpos360TransactionHistoryDto
{
	// Tổng tiền giao dịch
	public int $totalAmountTransaction;
	public int $totalAmountSettlement;
	public int $totalAmountNotSettlement;
	public int $countTransaction;
	public int $countSettlement;
	public int $countNotSettlement;
	public int $countQuickWithdraw;
	public int $countWithdraw;
	public int $countTransactionInstallment;

	/**
	 * @param int $totalAmountTransaction [Tổng tiền giao dịch]
	 * @param int $totalAmountSettlement [Tổng tiền kết toán]
	 * @param int $totalAmountNotSettlement [Tổng tiền chưa kết toán]
	 * @param int $countTransaction [Số lượng gd thanh toán thường]
	 * @param int $countSettlement [Tổng số lượng giao dịch đã kết toán]
	 * @param int $countNotSettlement [Tổng số lượng giao dịch chưa kết toán]
	 * 
	 * @param int $countQuickWithdraw [số lượng yêu cầu rút tiền nhanh]
	 * @param int $countWithdraw [số lượng yc nhận tiền về ]
	 * @param int $countTransactionInstallment [Số lượng giao dịch trả góp]
	 */
	public function __construct(
		int $totalAmountTransaction,
		int $totalAmountSettlement,
		int $totalAmountNotSettlement,
		int $countTransaction,
		int $countSettlement,
		int $countNotSettlement,
		int $countQuickWithdraw,
		int $countWithdraw,
		int $countTransactionInstallment
	) {
		$this->totalAmountTransaction = $totalAmountTransaction;
		$this->totalAmountSettlement = $totalAmountSettlement;
		$this->totalAmountNotSettlement = $totalAmountNotSettlement;
		$this->countTransaction = $countTransaction;
		$this->countSettlement = $countSettlement;
		$this->countNotSettlement = $countNotSettlement;
		$this->countQuickWithdraw = $countQuickWithdraw;
		$this->countWithdraw = $countWithdraw;
		$this->countTransactionInstallment = $countTransactionInstallment;
	}
}
