<?php

namespace App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360Register;

use Illuminate\Foundation\Http\FormRequest;

class Mpos360GetFormAuthenAccountRequest extends FormRequest
{
    	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.date' => ['required', 'string'],
		];
	}

	public function messages() {
		return [
			'data.date.required' => 'date là bắt buộc',
			'data.date.string' => 'date phải là kiểu chuỗi ký tự',
		];
	}
}