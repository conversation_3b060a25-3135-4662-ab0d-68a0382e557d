<?php

namespace App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalFetchEstimateAction\SubAction;

use App\Lib\Helper;

class XuLyGiaoDichVietQrHopLeSubAction
{
	/**
	 * PhươngCH, 10:46 AM
			array:4 [
				"status" => true
				"data" => array:8 [
					"totalAmountTransaction" => 1832000
					"countTransaction" => 3
					"sumTrxFee" => 32700
					"sumInsFee" => 0
					"sumPayNowFee" => 0
					"sumAmountAfterFee" => 1799300
					"transactionVAQRList" => array:3 [
						0 => array:10 [
							"id" => 43562227
							"amount" => 600000
							"status" => 104
							"txid" => "MPQR_MPVA8210"
							"transactionType" => "VIETQR"
							"issuerCode" => "VAQR"
							"pan" => "131232******2234"
							"createdDate" => "16/10/2024 10:32:10"
							"transactionFee" => 550
							"payNowFee" => 0
						]
						1 => array:10 [
							"id" => 43562230
							"amount" => 600000
							"status" => 104
							"txid" => "MPQR_MPVA8211"
							"transactionType" => "VIETQR"
							"issuerCode" => "VAQR"
							"pan" => "423423******3323"
							"createdDate" => "16/10/2024 10:33:15"
							"transactionFee" => 550
							"payNowFee" => 0
						]
						2 => array:10 [
							"id" => 42118976
							"amount" => 632000
							"status" => 104
							"txid" => "MPQR_MP359669"
							"transactionType" => "VIETQR"
							"issuerCode" => "VAQR"
							"pan" => "456457******7546"
							"createdDate" => "10/09/2024 14:24:10"
							"transactionFee" => 31600
							"payNowFee" => 0
						]
					]
					"error" => array:2 [
						"code" => 1000
						"message" => "DO_SERVICE_SUCCESS"
					]
				]
				"status_code_partner" => 1000
				"message" => "DO_SERVICE_SUCCESS"
			]


			$getSoTienToiDaCoTheRut = [
				'is_over' => boolean,
				'max_amount' => int
			]
	 */
	public function run($estimateResult, $getSoTienToiDaCoTheRut)
	{
		$list = [
			[
				'key' => 'SoTienGD',
				'label' => __('rtn.Số GD VietQR'),
				'value' => $estimateResult['data']['countTransaction'],
				'other_data' => (object) [
					'value' => [
						'text' => '{message}',
						'data' => [
							'message' => [
								'text' => $estimateResult['data']['countTransaction'],
							],
						]
					]
				]
			],
			[
				'key' => 'TongTienGD',
				'label' => __('rtn.Tổng tiền GD'),
				'value' => '',
				'other_data' => (object) [
					'value' => [
						'text' => '{message}',
						'data' => [
							'message' => [
								'text' => Helper::priceFormat($estimateResult['data']['totalAmountTransaction']), // gia tri tien,
								'font_weight' => 'bold'
							],
						]
					]
				]
			],

			// [
			// 	'key' => 'TongPhiGiaoDich',
			// 	'label' => __('rtn.Tổng phí giao dịch'),
			// 	'value' => '',
			// 	'other_data' => (object) [
			// 		'value' => [
			// 			'text' => '{message}',
			// 			'data' => [
			// 				'message' => [
			// 					'text' => Helper::priceFormat($estimateResult['data']['sumTrxFee']),
			// 				],
			// 			]
			// 		]
			// 	]
			// ],

			// [
			// 	'key' => 'TongPhiTraGop',
			// 	'label' => __('rtn.Tổng phí trả góp'),
			// 	'value' => '',
			// 	'other_data' => (object) [
			// 		'value' => [
			// 			'text' => '{message}',
			// 			'data' => [
			// 				'message' => [
			// 					'text' => Helper::priceFormat($estimateResult['data']['sumInsFee']), 
			// 				],
			// 			]
			// 		]
			// 	]
			// ],

			[
				'key' => 'TongPhiRutTienNgay',
				'label' => __('rtn.Phí dịch vụ'),
				'value' => '',
				'other_data' => (object) [
					'value' => [
						'text' => '{message}',
						'data' => [
							'message' => [
								'text' => __('rtn.Miễn phí'), 
							],
						]
					]
				]
			],


			[
				'key' => 'ThucNhanTamTinh',
				'label' => __('rtn.Thực nhận'),
				'value' => '{message} {unit}',
				'other_data' => (object) [
					'value' => [
						'text' => '{message}',
						'data' => [
							'message' => [
								'text' => Helper::priceFormat($estimateResult['data']['sumAmountAfterFee']), // gia tri tien,
								'font_weight' => 'bold'
							],
						]
					]
				]
			],
		];

		if (!empty($getSoTienToiDaCoTheRut['is_over'])) {
			$list[] = [
				'key' => 'SoTienToiDaCoTheRut',
				'label' => __('rtn.Số tiền tối đa có thể rút'),
				'value' => '{message} {unit}',
				'other_data' => (object) [
					'value' => [
						'text' => '{message}',
						'data' => [
							'message' => [
								'text' => Helper::priceFormat($getSoTienToiDaCoTheRut['max_amount']), 
								'font_weight' => 'bold',
								'text_color' => '#da2128'
							],
						]
					]
				]
			];
		}

		return $list;
	}
} // End class