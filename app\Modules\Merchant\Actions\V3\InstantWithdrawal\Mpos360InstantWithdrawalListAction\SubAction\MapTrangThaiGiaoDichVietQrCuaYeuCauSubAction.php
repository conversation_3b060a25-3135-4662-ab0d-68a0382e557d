<?php

namespace App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalListAction\SubAction;

class MapTrangThaiGiaoDichVietQrCuaYeuCauSubAction
{
	public function run()
	{
		$returnData = [
			[
				'key' => 'PENDING',
				'label' => __('rtn.Chờ xử lý'),
				'text_color' => '#ffffff',
				'bg_color' => '#008BF4'
			],

			[
				'key' => 'APPROVED',
				'label' => __('rtn.Thành công'),
				'text_color' => '#ffffff',
				'bg_color' => '#DA2128'
			],

			[
				'key' => 'FAIL',
				'label' => __('rtn.Thất bại'),
				'text_color' => '#ffffff',
				'bg_color' => '#da2128'
			],

			[
				'key' => 'FAILED',
				'label' => __('rtn.Đã hủy'),
				'text_color' => '#ffffff',
				'bg_color' => '#E99323'
			],

			[
				'key' => 'HAS_BALANCE',
				'label' => __('rtn.Đã kết toán'),
				'text_color' => '#ffffff',
				'bg_color' => '#008bf4'
			],
			[
				'key' => 'PAID',
				'label' => __('rtn.Đã thanh toán'),
				'text_color' => '#ffffff',
				'bg_color' => '#008bf4'
			],
		];

		$map = collect($returnData)->keyBy('key')->toArray();
		return $map;
	}
}
