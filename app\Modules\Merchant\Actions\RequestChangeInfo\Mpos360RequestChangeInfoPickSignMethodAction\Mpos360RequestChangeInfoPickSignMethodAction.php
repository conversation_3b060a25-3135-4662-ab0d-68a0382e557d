<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessAction;

use Exception;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\V2\Mpos360RequestChangeInfoPickSignMethodRequest;


class Mpos360RequestChangeInfoPickSignMethodAction
{
	public function run(Mpos360RequestChangeInfoPickSignMethodRequest $request)
	{
		$id = $request->json('data.id');
		$mpos360McRequest = Mpos360MerchantRequest::query()->find($id);

		mylog(['Yeu cau duoc xu ly la' => $mpos360McRequest->only(['id', 'mynextpay_id', 'order_code'])]);

		if (!$mpos360McRequest) {
			throw new BusinessException('Lỗi: không tìm thấy thông tin yêu cầu');
		}

		$dataRequest = json_decode($mpos360McRequest->data_request, true);
		$dataRequest[0]['signProcess'] = $request->json('data.sign_method');
		$r = $mpos360McRequest->forceFill([
			'data_request' => json_encode($dataRequest)
		])->update();

		if (!$r) {
			throw new BusinessException('Lỗi cập nhật thông tin yêu cầu');
		}

		$phuongThucKy = $request->json('data.sign_method.mpos360_sign_code');
		$can = Mpos360Enum::MPOS360_CAN_GOTO_STEP3;

		if ($phuongThucKy == 'KY_DIEN_TU_MEGADOC') {
			$can = Mpos360Enum::MPOS360_CAN_GOTO_KY_DIEN_TU_MEGADOC;
		}

		if ($phuongThucKy == 'KY_GIAY') {
			$can = Mpos360Enum::MPOS360_CAN_GOTO_UPLOAD_PHU_LUC_GIAY;
		}

		return [
			'status' => '1',
			'msg' => 'Cập nhật phương thức ký thành công',
			'can' => $can
		];
	}
} // End class
