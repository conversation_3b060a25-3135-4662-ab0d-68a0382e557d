<?php

namespace App\Modules\Merchant\Controllers\Device;

use App\Lib\Helper;
use App\Modules\Merchant\Model\Device;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\Device\Mpos360SaveDeviceInfoRequest;

class DeviceController extends Controller
{
	public function Mpos360SaveDeviceInfo(Mpos360SaveDeviceInfoRequest $request)
	{
		try {
			$device = Device::updateOrCreate(
				['os' => $request->json('data.device.os'), 'token' => $request->json('data.device.token')],
				['time_created' => time(), 'time_updated' => time()]
			);

			return $this->successResponse(['id' => $device->id], $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
