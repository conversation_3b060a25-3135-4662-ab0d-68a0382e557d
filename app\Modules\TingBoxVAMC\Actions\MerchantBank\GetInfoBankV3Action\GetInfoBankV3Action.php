<?php

namespace App\Modules\TingBoxVAMC\Actions\MerchantBank\GetInfoBankV3Action;

use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Lib\partner\SoundBox;
use App\Lib\MnpOnboardNewMcHelper;
use App\Modules\Merchant\Model\Setting;
use App\Modules\TingBoxVAMC\Models\BankVAMC;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\Mpos360GetInfoBankV2Request;


class GetInfoBankV3Action
{
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public string $isShowTrungGian = 'YES';

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run(Request $request)
	{
		$nganhNgheNganHangThanhPho = $this->mnpOnboardNewMcHelper->getNganhNgheNganHangThanhPho(true);

		$listBankVamc = BankVAMC::query()->where('isDisplay', '!=', BankVAMC::DISPLAY_NONE)
			->orWhere('isComingSoon', 1)
			->orderBy('isComingSoon', 'DESC')
			->orderBy('sort', 'DESC')
			->get();

		$listDisplay = $listBankVamc->filter(function (BankVAMC $it) {
			return $it->isDisplay == BankVAMC::DISPLAY_PROD || $it->isComingSoon == 1;
		});

		$settingDungThuVamc = Setting::query()->firstWhere(['key' => 'LIST_USERNAME_DUNG_THU_VAMC']);

		$listBanhThuNghiem = Collection::make([]);

		if ($settingDungThuVamc) {
			$username = $request->json('data.username', $request->json('data.merchantId'));
			$listUsernameDungThu = json_decode($settingDungThuVamc->value, true);
			$listBanhThuNghiem = $listBankVamc->filter(function (BankVAMC $b) use ($username, $listUsernameDungThu) {
				return $b->isDisplay == BankVAMC::DISPLAY_DEV_MODE && in_array($username, $listUsernameDungThu);
			});
		}

		$warningText = 'Khi xác nhận thay đổi, mã QR trên loa Tingbox sẽ tự động cập nhật, mã QR dán giấy cũ (nếu có) sẽ không được sử dụng được. Vui lòng tải mã QR mới tại menu \'QR cửa hàng\', in và dán lại';

		$listBankVamc = $listDisplay->concat($listBanhThuNghiem);

		$listBankVamc = $listBankVamc->map(function (BankVAMC $it) use ($request, $settingDungThuVamc) {
			$it->termsLink = $it->getTermLinkByVersionApp($request->get('versionApp'));

			if ($it->isDisplay == BankVAMC::DISPLAY_SAP_RA_MAT) {
				$username = $request->json('data.username', $request->json('data.merchantId'));
				
				$listUsernameDungThu = json_decode($settingDungThuVamc->value, true);

				if (in_array($username, $listUsernameDungThu)) {
					$it->isDisplay = BankVAMC::DISPLAY_PROD;
					$it->isComingSoon = 2;
				}
			}
			
			// Đoạn này cấu hình hiển thị bank trên từng version
			$versionApp = $request->get('versionApp');

			if (!empty($versionApp)) {
				$versionApp = str_replace('.', '', $versionApp);
				$versionApp = intval($versionApp);
				
				// Xử lý case app mới lên của ACB;
				if ($versionApp >= 225 && $it->bankCode == 'ACB') {
					$it->isDisplay = BankVAMC::DISPLAY_PROD;
					$it->isComingSoon = 2;
				} 
			}
			return $it;
		})->values()->toArray();
		
		
		$returnData = [
			'listBankCommon' => $nganhNgheNganHangThanhPho['banks'],
			'listBankDirect' => $listBankVamc,
			'holderName' => '',
			'cardIdentity' => '',
			'isShowTrungGian' => $this->isShowTrungGian,
			'warningText' => $warningText
		];

		return $returnData;
	}
}
