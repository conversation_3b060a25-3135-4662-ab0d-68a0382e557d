<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction;

use App\Exceptions\BusinessException;
use App\Lib\Helper;
use App\Lib\PasswordHandler;
use Illuminate\Support\Str;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\DTOs\Authen\Mpos360Auth\LoginMposSuccessDto;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360AuthLoginRequest;
use App\Modules\Merchant\Actions\Device\GetDeviceIdByOsAndTokenAction\GetDeviceIdByOsAndTokenAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction\CreateMpos360UserSubAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction\CreateDeviceSessionSubAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction\LoginMerchantViaMposSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestCronPushRecordAction\SubAction\GetMnpTokenByMerchantIdSubAction;
use App\Modules\Merchant\Model\PartnerConfig;
use App\Modules\Merchant\Model\Setting;
use Exception;

class Mpos360AuthLoginAction
{
	public bool $isCoThietBi = false;

	public function run(Mpos360AuthLoginRequest $request): DeviceSession
	{
		$loginMpos = app(LoginMerchantViaMposSubAction::class)->run(
			$request->json('data.email'),
			$request->json('data.password'),
			$request->json('data.os'),
			$request->json('data.token', '')
		);

		if (!empty($loginMpos['data']['countSoundBoxOfMc']) || !empty($loginMpos['data']['countReaderBoxOfMc'])) {
			$this->isCoThietBi = true;
		}

		$loginMposSuccessDto = new LoginMposSuccessDto(
			$loginMpos['data']['mobileUserToken'],
			$loginMpos['data']['merchantId'],
			$loginMpos['data']['username'],
			$loginMpos['data']['emailMerchant'],
			$loginMpos['data']['merchantName'],
		);

		// Get partner config
		$partnerConfig = PartnerConfig::query()->firstWhere('api_key', $request->json('api_key'));
		
		if (!$partnerConfig) {
			throw new BusinessException(vmsg('DangNhapLoiKhongTimThayThongTinPartner'));
		}
		
		// Thực hiện create user
		$mpos360User = app(CreateMpos360UserSubAction::class)->run($loginMposSuccessDto, $loginMpos);

		// Thực hiện lưu thông tin thiết bị
		$device = app(GetDeviceIdByOsAndTokenAction::class)->run(
			$request->json('data.os'),
			$request->json('data.token'),
			$mpos360User->id
		);

		// Đoạn này call sang MPOS để lấy thông tin
		$apiKey = (string) Str::uuid();
		
		$createDeviceSessionParams = [
			'device_id'          => $device->id,
			'user_id'						 => $mpos360User->id,
			'api_key'            => $apiKey,
			'api_secret'         => '',
			'time_expired'       => now()->addHours(8)->timestamp,
			'time_created'       => now()->timestamp,
			'partner_config_id'	 => $partnerConfig->id
		];

		$deviceSession = app(CreateDeviceSessionSubAction::class)->run(
			$createDeviceSessionParams,
			$loginMposSuccessDto 
		);

		$deviceSession->visibleAndDecryptSecret();
		$deviceSession->merchant_info = $loginMposSuccessDto->toJson();
		$deviceSession->can = Mpos360Enum::CAN_GO_TO_HOME_SCREEN;

	
		// Không có thiết bị thì ra màn hình nhận mã OTP qua email
		if (!$this->isCoThietBi) {
			if (Helper::isUserNameEqualEmail($loginMposSuccessDto->username)) {
				$deviceSession->can = Mpos360Enum::CAN_GO_TO_OTP_LOGIN_FLOW_EMAIL;
			}else {
				$deviceSession->can = Mpos360Enum::CAN_GO_TO_VERIFY_MPOS_DEVICE_SCREEN;
			}
		}

		// Có thiết bị, thì kiểm tra mk
		if ($this->isCoThietBi) {
			$passwordRuleSetting = Setting::getPasswordRules();
			$passwordHandler = new PasswordHandler($passwordRuleSetting);

			if ($passwordHandler->isSimplePassword($request->json('data.password'))) {
				$deviceSession->can = Mpos360Enum::CAN_GO_TO_VERIFY_MPOS_DEVICE_SCREEN;
			}
		}

		if (trim($request->json('data.email')) == '<EMAIL>') {
			$deviceSession->can = Mpos360Enum::CAN_GO_TO_HOME_SCREEN;
		}

		// Add thêm menu bottom nav
		$settingBottomNavApp = Setting::query()->firstWhere(['key' => 'LIST_MENU_BOTTOM_NAV_APP']);
		$deviceSession->bottom_nav_config = [];

		if ($settingBottomNavApp) {
			$settingValue = json_decode($settingBottomNavApp->value, true);
			$bottomNavAppMenu = collect($settingValue['nav'])
			->filter(function ($item) {
				return  !empty($item['display']);
			})
			->sortBy(function ($item) {
				return $item['sort'];
			})
			->map(function ($item) {
				return [
					'title' => $item['title']['vi'],
					'titleEn' => $item['title']['en'],
					'icon' => cumtomAsset($item['icon']),
					'screen' => $item['screen'],
					'sort' => $item['sort'],
					'display' => $item['display'],
					'is_main' => strval($item['is_main'] ?? 0)
				];
			})
			->values()
			->all();

			$deviceSession->bottom_nav_config = $bottomNavAppMenu;
		}

		return $deviceSession;
	}

	/**
	 * Thực hiện gọi sang mnp để lấy thông tin token của mnp
	 * Lưu ý: Nếu gọi lỗi thì bỏ qua luôn
	 */
	public function getMnpTokenAfterLoginSuccess(DeviceSession $deviceSession)
	{
		$merchantId = $deviceSession->getMerchantId();
		try {
			$mnpAccessToken = app(GetMnpTokenByMerchantIdSubAction::class)->run($merchantId);
			if (!empty($mnpAccessToken)) {
				$wasUpdated = DeviceSession::query()->where([
					'id' => $deviceSession->id,
					'api_key' => $deviceSession->api_key
				])->update([
					'mnp_token' => $mnpAccessToken,
					'time_updated' => now()->timestamp
				]);

				mylog([
					'MnpAccessToken' => Str::limit($mnpAccessToken, 15),
					'UpdateTokenVaoDB' => $wasUpdated
				]);
			}
		} catch (\Throwable $th) {
			mylog([
				'Err' => 'Lỗi truy vấn lấy thông tin token của bên mnp',
				'ErrDetail' => $th->getMessage()
			]);
		} finally {
			return true;
		}
	}
} // End class
