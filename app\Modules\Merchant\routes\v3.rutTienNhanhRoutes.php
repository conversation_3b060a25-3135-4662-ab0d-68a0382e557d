<?php

use Illuminate\Support\Facades\Route;
use App\Http\Middleware\MakeSureThatRequestIsJsonMiddleware;
use App\Modules\Merchant\Controllers\V3\InstantWithdrawal\Mpos360InstantWithdrawalController;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalListAction\Mpos360InstantWithdrawalListAction;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalCreateAction\Mpos360InstantWithdrawalCreateAction;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalDetailAction\Mpos360InstantWithdrawalDetailAction;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalFetchEstimateAction\Mpos360InstantWithdrawalFetchEstimateAction;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360ListTransByWithdrawalOrderCodeAction\Mpos360ListTransByWithdrawalOrderCodeAction;

Route::group([
	'prefix' => 'v3', 
	'middleware' => [MakeSureThatRequestIsJsonMiddleware::class]
], function () {
	
	Route::any('/Mpos360InstantWithdrawalFetchEstimate', [
		'uses' => Mpos360InstantWithdrawalController::class . '@Mpos360InstantWithdrawalFetchEstimate',
		'as' => Mpos360InstantWithdrawalFetchEstimateAction::class
	])->middleware('validateHash:email');

	Route::any('/Mpos360InstantWithdrawalCreate', [
		'uses' => Mpos360InstantWithdrawalController::class . '@Mpos360InstantWithdrawalCreate',
		'as' => Mpos360InstantWithdrawalCreateAction::class
	])->middleware('validateHash:email');

	Route::any('/Mpos360InstantWithdrawalList', [
		'uses' => Mpos360InstantWithdrawalController::class . '@Mpos360InstantWithdrawalList',
		'as' => Mpos360InstantWithdrawalListAction::class
	])->middleware('validateHash:email');

	Route::any('/Mpos360InstantWithdrawalListV2', [
		'uses' => Mpos360InstantWithdrawalController::class . '@Mpos360InstantWithdrawalListV2',
		'as' => 'Mpos360InstantWithdrawalListV2Action'
	])->middleware('checkSumForAnyMobile:merchantId');

	Route::any('/Mpos360InstantWithdrawalDetail', [
		'uses' => Mpos360InstantWithdrawalController::class . '@Mpos360InstantWithdrawalDetail',
		'as' => Mpos360InstantWithdrawalDetailAction::class
	])->middleware('validateHash:email|order_code');

	Route::any('/Mpos360ListTransByWithdrawalOrderCode', [
		'uses' => Mpos360InstantWithdrawalController::class . '@Mpos360ListTransByWithdrawalOrderCode',
		'as' => Mpos360ListTransByWithdrawalOrderCodeAction::class
	])->middleware('validateHash:email|order_code|type');
});



