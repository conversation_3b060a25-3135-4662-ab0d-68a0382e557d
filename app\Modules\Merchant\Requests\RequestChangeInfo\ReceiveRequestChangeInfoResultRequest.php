<?php

namespace App\Modules\Merchant\Requests\RequestChangeInfo;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ReceiveRequestChangeInfoResultRequest extends FormRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.mynextpay_id' => ['required', 'string'],
			'data.data_feedback' => ['required', 'string', 'json'],
			'data.comment' => ['required', 'string', 'json'],
			'data.status' => [
				'nullable', 
				'string', 
				Rule::in(['APPROVED', 'DENIED']),
				'required_if:data.is_need_supplement_docs,0'
			],
			'data.is_need_supplement_docs' => ['required', 'in:0,1'],
			'data.supplement_docs_reason' => ['present', 'string'],
		];
	}

	protected function prepareForValidation()
	{
		parent::prepareForValidation();
		
		$params = $this->all();
		if (empty($params['data']['is_need_supplement_docs'])) {
			$params['data']['is_need_supplement_docs'] = 0;
		}

		if (empty($params['data']['supplement_docs_reason'])) {
			$params['data']['supplement_docs_reason'] = '';
		}
		$this->merge($params);
	}

	public function messages()
	{
		return [
			'data.mynextpay_id.required' => 'Id mnp là bắt buộc',
			'data.mynextpay_id.string' => 'Id mnp phải là kiểu chuỗi',
			'data.data_feedback.required' => 'Data feedback là bắt buộc',
			'data.data_feedback.string' => 'Data feedback phải là kiểu chuỗi',
			'data.data_feedback.json' => 'Data feedback phải là chuỗi json',
			'data.comment.required' => 'comment là bắt buộc',
			'data.comment.string' => 'comment phải là kiểu chuỗi',
			'data.comment.json' => 'comment phải là chuỗi json',
			'data.status.string' => 'trạng thái phải là kiểu chuỗi',
			'data.status.string' => 'trạng thái phải thuộc 1 trong các giá trị: APPROVED, DENIED',
			'data.status.required_if' => 'trạng thái phải là bắt buộc nếu không phải là bổ sung thông tin',
			'data.is_need_supplement_docs.required' => 'đánh dấu bổ sung thông tin là bắt buộc',
			'data.is_need_supplement_docs.in' => 'đánh dấu bổ sung thông tin phải thuộc 1 trong các giá trị: 0, 1',
			'data.supplement_docs_reason.present' => 'lý do phải bổ sung thông tin là bắt buộc',
			'data.supplement_docs_reason.string' => 'lý do phải bổ sung thông tin phải là kiểu chuỗi',
		];
	}

	public function isBoSungThongTin(): bool {
		return $this->json('data.is_need_supplement_docs') == 1;
	}
} // End class
