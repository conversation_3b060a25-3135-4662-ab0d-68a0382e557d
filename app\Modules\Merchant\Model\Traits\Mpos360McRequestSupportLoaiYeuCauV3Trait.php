<?php

namespace App\Modules\Merchant\Model\Traits;

use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;

trait Mpos360McRequestSupportLoaiYeuCauV3Trait
{
	public function getDataRequestV3(): array
	{
		return json_decode($this->data_request, true);
	}

	public function isDoiTknhV3(): bool
	{
		$dataRequest = $this->getDataRequestV3();
		return $dataRequest[0]['type'] == 'CHANGE_BANK_ACCOUN_INFO';
	}

	public function isDoiThongTinNguoiDaiDienV3(): bool
	{
		$dataRequest = $this->getDataRequestV3();
		return $dataRequest[0]['type'] == 'CHANGE_REPRESENT_INFO';
	}

	public function isYcDoiTknhHkd(): bool
	{
		$dataRequest = $this->getDataRequestV3();
		return $this->isDoiTknhV3() && $dataRequest[0]['choice'] == 'DOI_STK_KHAC_CUA_HKD';
	}

	public function isYcDoiTknhCaNhanMaHkdUyQuyen()
	{
		$dataRequest = $this->getDataRequestV3();
		return $this->isDoiTknhV3() && $dataRequest[0]['choice'] == 'DOI_SO_TKNH_CA_NHAN_DUOC_HKD_UY_QUYEN';
	}

	public function isYcDoiTknhDoanhNghiep() {
		$dataRequest = $this->getDataRequestV3();
		return $this->isDoiTknhV3() && $dataRequest[0]['choice'] == 'DOI_STK_KHAC_CUA_DOANH_NGHIEP';
	}

	public function isYcDoiTknhCaNhanMaDoanhNghiepUyQuyen() {
		$dataRequest = $this->getDataRequestV3();
		return $this->isDoiTknhV3() && $dataRequest[0]['choice'] == 'DOI_SO_TKNH_CA_NHAN_DUOC_DOANH_NGHIEP_UY_QUYEN';
	}

	public function getTenNguoiThuHuongV3(): string {
		$dataRequest = $this->getDataRequestV3();
		return $dataRequest[0]['profiles']['holderName'];
	}

	public function isYcDoiThongTinLienHeHKDV3(): bool {
		$dataRequest = $this->getDataRequestV3();
		return $this->isDoiThongTinNguoiDaiDienV3() && $dataRequest[0]['sub_choice'] == 'DOI_THONG_TIN_LIEN_HE_HKD';
	}

	public function isYcDoiThongTinLienHeDoanhNghiepV3(): bool {
		$dataRequest = $this->getDataRequestV3();
		return $this->isDoiThongTinNguoiDaiDienV3() && $dataRequest[0]['sub_choice'] == 'DOI_THONG_TIN_LIEN_HE_DN';
	}

	public function isYcDoiCccdHKDV3(): bool {
		$dataRequest = $this->getDataRequestV3();
		return $this->isDoiThongTinNguoiDaiDienV3() && $dataRequest[0]['sub_choice'] == 'DOI_CCCD_MOI_HKD';
	}

	public function isYcDoiCccdDoanhNghiepV3(): bool {
		$dataRequest = $this->getDataRequestV3();
		return $this->isDoiThongTinNguoiDaiDienV3() && $dataRequest[0]['sub_choice'] == 'DOI_CCCD_MOI_DN';
	}

	public function isYcDoiNguoiDaiDienMoiHKDV3(): bool {
		$dataRequest = $this->getDataRequestV3();
		return $this->isDoiThongTinNguoiDaiDienV3() && $dataRequest[0]['sub_choice'] == 'DOI_NGUOI_DAI_DIEN_MOI_HKD';
	}

	public function isYcDoiNguoiDaiDienMoiDoanhNghiepV3(): bool {
		$dataRequest = $this->getDataRequestV3();
		return $this->isDoiThongTinNguoiDaiDienV3() && $dataRequest[0]['sub_choice'] == 'DOI_NGUOI_DAI_DIEN_MOI_DN';
	}

	public function getSignatureUrlV3(): string {
		$dataRequest = $this->getDataRequestV3();
		return $dataRequest[0]['signProcess']['signature_url'] ?? '';
	}

	public function getRawAttachmentsV3(): array {
		$dataRequest = $this->getDataRequestV3();
		return $dataRequest[0]['raw_attachments'] ?? [];
	}

	public function isDoiThongTinLienHeEmailV3(): bool {
		$dataRequest = $this->getDataRequestV3();
		return $this->isDoiThongTinLienHe() && !empty($dataRequest[0]['profiles']['representEmail']);
	}

	public function isDoiThongTinLienHeMobileV3(): bool {
		$dataRequest = $this->getDataRequestV3();
		return $this->isDoiThongTinLienHe() && !empty($dataRequest[0]['profiles']['representMobile']);
	}

	public function getSoLuongProfileThayDoiV3(): int {
		$dataRequest = $this->getDataRequestV3();
		return count($dataRequest[0]['profiles']);
	}

	public function isVerifiedProfile(string $groupType, array $profileKeys=[], bool $isIgnoreGroupType=false): bool {
		$dataRequest = $this->getDataRequestV3();
		if (empty($isIgnoreGroupType)) {
			$group = collect($dataRequest)->where('type', $groupType)->first();
		}else {
			$group = $dataRequest[0];
		}
		
		if (empty($group)) {
			throw new BusinessException('Group Not Found');
		}

		$requestVerifyProfile = collect($group['request_vefify']);
		return $requestVerifyProfile->contains(function ($item) use ($profileKeys) {
			return in_array($item['field'], $profileKeys) && $item['status_verify'] == '1';
		});
	}

	public function isYcVer3(): bool {
		return $this->version == 3;
	}

	public function getChoiceV3() {
		$dataRequest = $this->getDataRequestV3();
		return $dataRequest[0]['choice'];
	}

	public function getSubChoiceV3() {
		$dataRequest = $this->getDataRequestV3();
		return $dataRequest[0]['sub_choice'];
	}

	public function getTypeNameV3() {
		if ($this->isDoiThongTinNganHang()) {
			$choice = $this->getChoiceV3();

			switch ($choice) {
				case 'DOI_STK_KHAC_CUA_HKD':
					return __('dttv3.Đổi số TKNH khác của hộ kinh doanh');
					break;

				case 'DOI_SO_TKNH_CA_NHAN_DUOC_HKD_UY_QUYEN':
					return __('dttv3.Đổi số TKNH cá nhân được HKD ủy quyền');
					break;

				case 'DOI_STK_KHAC_CUA_DOANH_NGHIEP':
					return __('dttv3.Đổi số TKNH của doanh nghiệp');
					break;

				case 'DOI_SO_TKNH_CA_NHAN_DUOC_DOANH_NGHIEP_UY_QUYEN':
					return __('dttv3.Đổi số TKNH cá nhân được doanh nghiệp ủy quyền');
					break;
			}

			return __('dttv3.Đổi TKNH');
		}

		if ($this->isDoiNguoiDaiDien()) {
			$subChoice = $this->getSubChoiceV3();

			if (empty($subChoice)) {
				return __('dttv3.Đổi thông tin người đại diện');
			}

			switch ($subChoice) {
				case 'DOI_NGUOI_DAI_DIEN_MOI_HKD':
					return __('dttv3.Đổi người đại diện mới của HKD');
					break;

				case 'DOI_CCCD_MOI_HKD':
					return __('dttv3.Đổi CCCD mới của HKD');
					break;

				case 'DOI_THONG_TIN_LIEN_HE_HKD':
					return __('dttv3.Đổi thông tin liên hệ của HKD');
					break;

				case 'DOI_NGUOI_DAI_DIEN_MOI_DN':
					return __('dttv3.Đổi người đại diện mới của doanh nghiệp');
					break;

				case 'DOI_CCCD_MOI_DN':
					return __('dttv3.Đổi CCCD mới của doanh nghiệp');
					break;

				case 'DOI_THONG_TIN_LIEN_HE_DN':
					return __('dttv3.Đổi thông tin liên hệ của doanh nghiệp');
					break;
			}

			return __('dttv3.Đổi thông tin người đại diện');
		}

		return __('dttv3.Không xác định');
	}

	public function isTrangThaiCuoiV3(): bool {
		return in_array($this->status, [
			Mpos360Enum::MPOS360_MC_REQUEST_STT_DA_XU_LY,
			Mpos360Enum::MPOS360_MC_REQUEST_STT_DA_TU_CHOI,
			Mpos360Enum::MPOS360_MC_REQUEST_STT_MC_TU_HUY
		]);
	}
} // End class