<?php

namespace App\Modules\Merchant\Requests\Authen\Mpos360\V4;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360AuthVerifyOtpForgotPasswordV4Request extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.username' => ['required', 'string'],
			'data.otp_id' => ['required', 'string'],
			'data.otp' => ['required', 'string'],
			'data.merchant_id' => ['required', 'string'],
		];
	}

	public function messages() {
		return [
			'data.username.required' => 'Username là bắt buộc',
			'data.username.string' => 'Username phải là kiểu chuỗi ký tự',
			
			'data.otp_id.required' => 'OtpId là bắt buộc',
			'data.otp_id.string' => 'OtpId phải là kiểu chuỗi ký tự',
			'data.merchant_id.required' => 'MerchantId là bắt buộc',
			'data.merchant_id.string' => 'MerchantId phải là kiểu chuỗi ký tự',
		];
	}
} // End class
