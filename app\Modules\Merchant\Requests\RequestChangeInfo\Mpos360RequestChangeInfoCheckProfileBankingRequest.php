<?php

namespace App\Modules\Merchant\Requests\RequestChangeInfo;

use Illuminate\Support\Str;
use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360RequestChangeInfoCheckProfileBankingRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.bankId' => ['required', 'string'], // thể hiện của bankId
			'data.bankName' => ['required', 'string'], // thể hiện của bankId
			'data.accountNo' => ['required', 'string'],
			'data.holderName' => ['required', 'string']
		];
	}

	protected function prepareForValidation()
	{
		parent::prepareForValidation();
		
		$params = $this->all();
		$params['data']['holderName'] = Str::of($params['data']['holderName'])->slug(' ')->upper()->trim()->__toString();
		$this->merge($params);
	}

	public function getParamCheckBankVmmc(): array {
		$holderName =  Str::of($this->json('data.holderName'))->trim()->upper()->__toString();
		$bankId = trim($this->json('data.bankId', '')); // gia tri nay se lay tu ben MNP
		
		return [
			'bank_account_holder' => $holderName,
			'bank_account' => trim($this->json('data.accountNo')),
			'bank_id' => config('profilemnp.bankMnpVimo.' . $bankId, '')
		];
	}
} // End class
