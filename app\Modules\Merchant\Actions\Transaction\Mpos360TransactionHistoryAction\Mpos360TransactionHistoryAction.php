<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransactionHistoryAction;

use App\Lib\Helper;
use App\Lib\partner\MPOS;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionHistoryRequest;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransactionHistoryAction\SubAction\GetTransactionHistoryMposSubAction;
use Carbon\Carbon;

class Mpos360TransactionHistoryAction
{
	public  $mpos;

	public function __construct(MPOS $mpos)
	{
		$this->mpos = new MPOS();
	}
	private function __convertDataReturn($data,$listTransaction) {
		foreach ($data as $key => $value) {
			if(isset($listTransaction[$key])) {
				$data[$key] = $listTransaction[$key];
			}
		}
		return $data;
	}
	public function run(Mpos360TransactionHistoryRequest $request)
	{
		$data = [
			'totalAmountTransaction'=> 0,
			'totalAmountSettlement'=> 0,
			'totalAmountNotSettlement'=> 0,
			'countTransaction'=> 0,
			'countSettlement'=> 0,
			'countNotSettlement'=> 0,

			'countTransactionInstallment'=> 0,
			'countQuickWithdraw'=> 0,
			'countWithdraw'=> 0,
			'countPaymentNow' => 0,
			'countPaymentQR' => 0,
		];
		

		$returnData = [
			'report_today' => [
				[
					'label' => vmsg('Mpos360TransactionHistoryAction_TongTienGiaoDich'),
					'value' => $data['totalAmountTransaction'],
					'extra_value' => Helper::priceFormat($data['totalAmountTransaction'])
				],

				[
					'label' => vmsg('Mpos360TransactionHistoryAction_ChuaKetToan'),
					'value' => sprintf('(%s %s)', $data['countNotSettlement'], vmsg('Mpos360TransactionHistoryAction_GD')),
					'extra_value' => Helper::priceFormat($data['totalAmountNotSettlement'])
				],

				[
					'label' => vmsg('Mpos360TransactionHistoryAction_DaKetToan'),
					'value' => sprintf('(%s %s)', $data['countSettlement'], vmsg('Mpos360TransactionHistoryAction_GD')),
					'extra_value' => Helper::priceFormat($data['totalAmountSettlement'])
				]
			], // end report today

			'transaction_categories' => [
				// group item  cho bên mobile dễ foreach
				[
					[
						'icon' => cumtomAsset('images/transaction/transaction-history/regular_payment.png'),
						'label' => vmsg('Mpos360TransactionHistoryAction_ThanhToanThuong'),
						'value' => sprintf('(%s %s)', $data['countTransaction'], vmsg('Mpos360TransactionHistoryAction_GD')),
						'code' => 'REGULAR_PAYMENT'
					],

					[
						'icon' => cumtomAsset('images/transaction/transaction-history/qr_transaction.png'),
						'label' => vmsg('Mpos360TransactionHistoryAction_GiaoDichQr'),
						'value' => sprintf('(%s %s)', $data['countPaymentQR'] ?? 0, vmsg('Mpos360TransactionHistoryAction_GD')),
						'code' => 'QR_TRANSACTION'
					],
					
					[
						'icon' => cumtomAsset('images/transaction/transaction-history/installment_payment.png'),
						'label' => vmsg('Mpos360TransactionHistoryAction_ThanhToanTraGop'),
						// 'value' => sprintf('%s %s', $dto->countTransactionInstallment, vmsg('Mpos360TransactionHistoryAction_GD')), // dang khong co thanh toan tra gop
						'value' => sprintf('(%s %s)', $data['countTransactionInstallment'], vmsg('Mpos360TransactionHistoryAction_GD')),
						'code' => 'INSTALLMENT_PAYMENT'
					],
					// [
					// 	'icon' => cumtomAsset('images/transaction/transaction-history/cash_advance.png'),
					// 	'label' => 'Tạm ứng',
					// 	'value' => '(12 GD)',
					// 	'code' => 'CASH_ADVANCE'
					// ],
					// [
					// 	'icon' => cumtomAsset('images/transaction/transaction-history/moto_payment.png'),
					// 	'label' => 'Thanh toán MOTO',
					// 	'value' => '(12 GD)',
					// 	'code' => 'MOTO_PAYMENT'
					// ],
				],

				// [
				// 	[
				// 		'icon' => cumtomAsset('images/transaction/transaction-history/atm_360_service.png'),
				// 		'label' => 'Dịch vụ ATM 360',
				// 		'value' => '(12 GD)',
				// 		'code' => 'ATM_360_SERVICE'
				// 	],
				// 	[
				// 		'icon' => cumtomAsset('images/transaction/transaction-history/cash_advance_recovery.png'),
				// 		'label' => 'Thu hồi ứng',
				// 		'value' => '(12 GD)',
				// 		'code' => 'CASH_ADVANCE_RECOVERY'
				// 	],
				// ],

				[
					[
						'icon' => cumtomAsset('images/transaction/transaction-history/quick_withdrawal_request.png'),
						'label' => vmsg('Mpos360TransactionHistoryAction_YeuCauRutTienNhanh'),
						'value' => sprintf('(%s %s)', $data['countQuickWithdraw'], vmsg('Mpos360TransactionHistoryAction_GD')),
						'code' => 'QUICK_WITHDRAWAL_REQUEST'
					],
					[
						'icon' => cumtomAsset('images/transaction/transaction-history/bank_account_funds_receipt.png'),
						'label' =>  vmsg('Mpos360TransactionHistoryAction_NhanTienVeTKNH'),
						'value' => sprintf('(%s %s)', $data['countWithdraw'], vmsg('Mpos360TransactionHistoryAction_GD')),
						'code' => 'BANK_ACCOUNT_FUNDS_RECEIPT'
					],
				],

				[
					[
						'icon' => cumtomAsset('images/transaction/transaction-history/vietqr_ntn.png'),
						'label' => vmsg('Mpos360TransactionHistoryAction_YeuCauNhanTienNhanhVietQr'),
						'value' => sprintf('(%s %s)', $data['countPaymentNow'] ?? 0, vmsg('Mpos360TransactionHistoryAction_GD')),
						'code' => 'VIETQR_NTN'
					],
				]
			]
		];

		return $returnData;
	}
}
