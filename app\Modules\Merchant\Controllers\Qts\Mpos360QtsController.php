<?php

namespace App\Modules\Merchant\Controllers\Qts;

use App\Lib\Helper;
use App\Modules\Merchant\Controllers\Controller;
use Illuminate\Http\Request;

class Mpos360QtsController extends Controller
{
	public function Mpos360QtsGetConfig(Request $request)
	{
		$qtsConfig = env('QTS_DATA');

		try {
			$result = json_decode($qtsConfig, true);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
} // End class
