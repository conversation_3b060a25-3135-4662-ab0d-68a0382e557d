<?php

namespace App\Modules\WebBackend\Controllers\RequestChangeInfo;

use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use Illuminate\Http\Request;
use App\Modules\WebBackend\Controllers\Controller;
use App\Modules\WebBackend\Resources\RequestChangeInfoResource;

class RequestChangeInfoController extends Controller
{
	public function GetAllRequestChangeInfo(Request $request)
	{
		$listYcDoiThongTin = Mpos360MerchantRequest::query()
			->where('type', Mpos360Enum::MPOS360_MC_REQUEST_THAY_DOI_THONG_TIN)
			->where('status_verify', '!=', Mpos360Enum::MPOS360_MC_VERIFY_STT_KHONG_CO_THONG_TIN_XAC_THUC);

		$email = $request->json('data.email', '');

		if (!empty($email)) {
			$listYcDoiThongTin->where('email', trim($email));
		}

		$merchantId = $request->json('data.merchantId', '');
		if (!empty($merchantId)) {
			$listYcDoiThongTin->where('merchant_id', trim($merchantId));
		}

		$listYcDoiThongTin = $listYcDoiThongTin->orderBy('id', 'DESC')->paginate(
			$request->json('data.limit', 20),
			['*'],
			'page',
			$request->json('data.page', 1)
		);

		$resources = new RequestChangeInfoResource($listYcDoiThongTin);
		$response = $resources->toResponse($request)->getData(true);
		return $this->successResponse([
			'rows' => $response,
			'other_data' => (object) [
				'status' => (object) [
					Mpos360Enum::MPOS360_MC_REQUEST_STT_NHAP => 'Nháp',
					Mpos360Enum::MPOS360_MC_REQUEST_STT_CHUA_GUI => 'Chưa gửi',
					Mpos360Enum::MPOS360_MC_REQUEST_STT_DANG_GUI => 'Đang gửi',
					Mpos360Enum::MPOS360_MC_REQUEST_STT_DA_GUI_SANG_MNP => 'Đã gửi sang MNP',
					Mpos360Enum::MPOS360_MC_REQUEST_STT_DA_XU_LY => 'Đã xử lý',
					Mpos360Enum::MPOS360_MC_REQUEST_STT_DA_TU_CHOI => 'Đã từ chối',
					Mpos360Enum::MPOS360_MC_REQUEST_STT_CAP_NHAT_LOI => 'Cập nhật lỗi',
					Mpos360Enum::MPOS360_MC_REQUEST_STT_MC_TU_HUY => 'MC tự hủy',
				],

				'status_verify' => (object) [
					Mpos360Enum::MPOS360_MC_VERIFY_STT_KHONG_CO_THONG_TIN_XAC_THUC => 'Không có thông tin xác thực',
					Mpos360Enum::MPOS360_MC_VERIFY_STT_CHUA_XAC_THUC => 'Chưa xác thực',
					Mpos360Enum::MPOS360_MC_VERIFY_STT_DA_XAC_THUC => 'Đã xác thực',
					Mpos360Enum::MPOS360_MC_VERIFY_STT_DA_THUC_HIEN_BUOC3 => 'Đã thực hiện B3',
				],

				'status_sign' => (object) [
					Mpos360Enum::MPOS360_MC_SIGN_STT_KHONG_CO_CHU_KY => 'Không có chữ ký',
					Mpos360Enum::MPOS360_MC_SIGN_STT_CHUA_KY => 'Chưa ký',
					Mpos360Enum::MPOS360_MC_SIGN_STT_DANG_KY => 'Đang ký',
					Mpos360Enum::MPOS360_MC_SIGN_STT_DA_KY => 'Đã ký',
					Mpos360Enum::MPOS360_MC_SIGN_STT_KY_LOI => 'Ký lỗi',
				]
			]
		], $request);
	}
} // End class
