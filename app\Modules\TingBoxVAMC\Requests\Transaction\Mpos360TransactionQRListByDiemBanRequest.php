<?php

namespace App\Modules\TingBoxVAMC\Requests\Transaction;

use Illuminate\Foundation\Http\FormRequest;

class Mpos360TransactionQRListByDiemBanRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.merchantId' => ['required'],
      'data.transaction_time' => ['present', 'string'],
      'data.transaction_status' => ['present', 'string'],
      'data.pageIndex' => ['required', 'numeric', 'integer', 'min:1'],
      'data.startDate' => ['present', 'string', 'date_format:d/m/Y H:i', 'required_with:data.endDate'],
    	'data.endDate' => ['present', 'string', 'date_format:d/m/Y H:i', 'required_with:data.startDate'],
      'data.vaMode' => ['required', 'string', 'in:VAMC,VANP,VAMC/VANP'],
      'data.muid' => ['present', 'string'],
			'data.username' => ['present', 'string']
    ];
  }

	public function messages()
	{
		return [
			'data.merchantId.required' => 'Mã merchant là bắt buộc',
			'data.merchantId.string' => 'Mã merchant phải là kiểu chuỗi',
			'data.transaction_time.present' => 'Thời gian giao dịch là bắt buộc',
			'data.transaction_time.string' => 'Thời gian giao dịch phải là kiểu chuỗi',
			'data.transaction_status.present' => 'Trạng thái giao dịch là bắt buộc',
			'data.transaction_status.string' => 'Trạng thái giao dịch phải là kiểu chuỗi',

			'data.pageIndex.required' => 'Phân trang hiện tại là bắt buộc',
			'data.pageIndex.numeric' => 'Phân trang hiện tại phải là kiểu số',
			'data.pageIndex.integer' => 'Phân trang hiện tại phải là kiểu số tự nhiên',
			'data.pageIndex.min' => 'Phân trang hiện tại phải bắt đầu từ trang 1',

			'data.startDate.present' => 'Bộ lọc ngày bắt đầu là bắt buộc',
			'data.startDate.string' => 'Bộ lọc ngày bắt đầu phải là kiểu chuỗi',
			'data.startDate.date_format' => 'Bộ lọc ngày bắt đầu phải đúng định dạng: d/m/Y H:i',
			'data.startDate.required_with' => 'Bộ lọc ngày bắt đầu phải có giá trị khi bạn đã chọn bộ lọc ngày kết thúc',

			'data.endDate.present' => 'Bộ lọc ngày kết thúc là bắt buộc',
			'data.endDate.string' => 'Bộ lọc ngày kết thúc phải là kiểu chuỗi',
			'data.endDate.date_format' => 'Bộ lọc ngày kết thúc phải đúng định dạng: d/m/Y H:i',
			'data.endDate.required_with' => 'Bộ lọc ngày kết thúc phải có giá trị khi bạn đã chọn bộ lọc ngày bắt đầu',

			'data.vaMode.required' => 'Loại giao dịch là bắt buộc',
			'data.vaMode.string' => 'Loại giao dịch phải là kiểu chuỗi',
			'data.vaMode.in' => 'Loại giao dịch phải thuộc các giá trị: Trực tiếp hoặc trung gian',

			'data.muid.present' => 'Mã cửa hàng là bắt buộc',
			'data.muid.string' => 'Mã cửa hàng phải là kiểu chuỗi',
		];
	}

	public function getStartDateEndDate()
	{
		$startDate = $this->json('data.startDate');
		$endDate = $this->json('data.endDate');

		if (!empty($startDate) || !empty($endDate)) {
			return [
				'startDate' => sprintf('%s:00', $startDate),
				'endDate' => sprintf('%s:59', $endDate),
			];
		}

		$time = $this->json('data.transaction_time');

		switch ($time) {
			case 'LAST_MONTH':
				return [
					'startDate' => now()->subMonth()->startOfMonth()->format('d/m/Y H:i:s'),
					'endDate' => now()->subMonth()->endOfMonth('d/m/Y H:i:s'),
				];

			case 'THIS_MONTH':
				return [
					'startDate' => now()->startOfMonth()->format('d/m/Y H:i:s'),
					'endDate' => now()->endOfDay()->format('d/m/Y H:i:s'),
				];

			case 'TODAY':
				return [
					'startDate' => now()->startOfDay()->format('d/m/Y H:i:s'),
					'endDate' => now()->format('d/m/Y H:i:s'),
				];

			case 'YESTERDAY':
				return [
					'startDate' => now()->subDay()->startOfDay()->format('d/m/Y H:i:s'),
					'endDate' => now()->subDay()->endOfDay()->format('d/m/Y H:i:s'),
				];
			
			case 'THIS_WEEK':
				return [
					'startDate' => now()->startOfWeek()->format('d/m/Y H:i:s'),
					'endDate' => now()->format('d/m/Y H:i:s'),
				];

			default:
				return [
					'startDate' => now()->subDays(30)->startOfDay()->format('d/m/Y H:i:s'),
					'endDate' => now()->format('d/m/Y H:i:s'),
				];
		}

		return [];
	}
} // End class
