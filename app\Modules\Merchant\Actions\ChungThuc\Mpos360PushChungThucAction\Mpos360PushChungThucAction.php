<?php

namespace App\Modules\Merchant\Actions\ChungThuc\Mpos360PushChungThucAction;

use App\Exceptions\BusinessException;
use App\Lib\Helper;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360ChungThuc;
use App\Modules\Merchant\Actions\ChungThuc\Mpos360PushChungThucAction\SubAction\PushChungThucSangMnpSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestCronPushRecordAction\SubAction\GetCccdInfoByQtsRequestIdSubAction;
use App\Modules\Merchant\Model\Setting;

class Mpos360PushChungThucAction
{
	public array $returnData = [];

	private array $__exceptIds = [];

	public GetCccdInfoByQtsRequestIdSubAction $getCccdInfoByQtsRequestIdSubAction;

	public function __construct(
		GetCccdInfoByQtsRequestIdSubAction $getCccdInfoByQtsRequestIdSubAction
	) {
		$this->getCccdInfoByQtsRequestIdSubAction = $getCccdInfoByQtsRequestIdSubAction;
	}

	public function run(): array
	{
		$settingOverWriteChungThuc = Setting::query()->firstWhere(['key' => 'LIST_MCID_CAN_OVERWITE_CHUNG_THUC']);
		
		for ($i = 1; $i <= 50; $i++) {
			try {
				$result = $this->handle($settingOverWriteChungThuc);
				if ($result === 'EMPTY') {
					$this->returnData[] = 'EMPTY';
					break;
				}

				if (optional($result)->id) {
					$this->returnData[] = $result->id;
				}
			} catch (\Throwable $th) {
				mylog(['Loi xu ly chung thuc' => Helper::traceError($th)]);
				continue;
			}
		}

		return $this->returnData;
	}

	public function handle(Setting $settingOverWriteChungThuc)
	{
		$mpos360ChungThuc = Mpos360ChungThuc::query()
			->where('status', Mpos360Enum::MPOS360_CHUNG_THUC_STT_DANG_XAC_NHAN)
			->orWhere(function ($q) {
				return $q->where('status', Mpos360Enum::MPOS360_CHUNG_THUC_STT_DA_XAC_NHAN)
								 ->where('is_push_mnp', 0);
			});

		if (!empty($this->__exceptIds)) {
			$mpos360ChungThuc = $mpos360ChungThuc->whereNotIn('id', $this->__exceptIds);
		}

		$mpos360ChungThuc = $mpos360ChungThuc->first();

		if (!$mpos360ChungThuc) {
			mylog(['EMPTY' => 'khong co thong tin ban ghi chung thuc']);
			return 'EMPTY';
		}

		$this->__exceptIds[] = $mpos360ChungThuc->id;
		mylog(['Ban ghi chung thuc xu ly la' => $mpos360ChungThuc->id]);

		try {
			// là cccd thì update số cccd mới vào trong DB
			if ($mpos360ChungThuc->key_code == 'CCCD' && $mpos360ChungThuc->status == Mpos360Enum::MPOS360_CHUNG_THUC_STT_DANG_XAC_NHAN) {
				mylog(['khop dieu kien' => 'yes']);
				$detailQtsResultDto = $this->getCccdInfoByQtsRequestIdSubAction->run($mpos360ChungThuc->qts_request_id);

				$paramUpdate = [
					'value_confirmed' => $detailQtsResultDto->currentCardId,
					'time_confirmed'  => now()->timestamp,
					'time_updated'    => now()->timestamp,
					'status'          => Mpos360Enum::MPOS360_CHUNG_THUC_STT_DA_XAC_NHAN,
					'other_data'      => json_encode($detailQtsResultDto->result),
					'data_response' => '{}',
					'is_replace_new_represention' => 0
				];

				// Đoạn này kiểm tra chống ghi đè
				if (
					$detailQtsResultDto->currentCardId == $mpos360ChungThuc->value_confirmed
					|| $detailQtsResultDto->oldCardId == $mpos360ChungThuc->value_confirmed
				) {
					mylog(['khop thong tin' => 'ok']);
					// Đúng thông tin chứng thực rồi
					$r = Mpos360ChungThuc::query()
					->where([
						'id' => $mpos360ChungThuc->id,
						'status' => Mpos360Enum::MPOS360_CHUNG_THUC_STT_DANG_XAC_NHAN
					])->update($paramUpdate);
					mylog(['ket qua update' => $r]);
				}else {
					/**
					 * Sai thông tin chứng thực (dùng CCCD của người khác để làm chứng thực)
					 * Kiểm tra xem có thuộc list ghi đè hay không? 
					 * 	-Nếu thuộc thì cho phép ghi đè
					 * 	-Nếu không thuộc thì từ chối ngay lập tức
					 */
					$listMcIdChoPhepGhiDeChungThuc = json_decode($settingOverWriteChungThuc->value, true);
					if (in_array($mpos360ChungThuc->merchant_id, $listMcIdChoPhepGhiDeChungThuc) || !empty($mpos360ChungThuc->is_replace_new_represention)) {
						$r = Mpos360ChungThuc::query()
																	->where([
																		'id' => $mpos360ChungThuc->id,
																		'status' => Mpos360Enum::MPOS360_CHUNG_THUC_STT_DANG_XAC_NHAN
																	])->update($paramUpdate);
					}else {
						$r = Mpos360ChungThuc::query()
																	->where([
																		'id' => $mpos360ChungThuc->id,
																	])->update([
																		'status' => Mpos360Enum::MPOS360_CHUNG_THUC_STT_TU_CHOI_XAC_NHAN,
																		'data_response' => json_encode([
																			'error_chung_thuc' => [
																				'Cần phải làm chứng thực cho số cccd' => $mpos360ChungThuc->value_confirmed,
																				'Số CCCD đã thực hiện QTS chứng thực' => $detailQtsResultDto->currentCardId,
																				'Họ tên đã thực hiện QTS chứng thực' => $detailQtsResultDto->fullName,
																			]
																		])
																	]);
					}
				}
				

				if (!$r) {
					throw new BusinessException('Lỗi không lưu được trạng thái xác nhận CCCD');
				}
			}

			// không phải cccd thì chỉ cần update status thôi là được
			if ($mpos360ChungThuc->key_code != 'CCCD') {
				mylog(['khong phai cccd' => 'yes']);
				$paramUpdate = [
					'time_confirmed'  => now()->timestamp,
					'time_updated'    => now()->timestamp,
					'status'          => Mpos360Enum::MPOS360_CHUNG_THUC_STT_DA_XAC_NHAN,
				];

				$r = Mpos360ChungThuc::query()
				->where([
					'id' => $mpos360ChungThuc->id,
				])->update($paramUpdate);

				if (!$r) {
					throw new BusinessException('Lỗi không lưu được trạng thái xác nhận');
				}
			}

			// Xử lý đẩy sang MNP
			$mpos360ChungThuc->refresh();
			$pushMnp = @app(PushChungThucSangMnpSubAction::class)->run($mpos360ChungThuc);

			return $mpos360ChungThuc;
		} catch (\Throwable $th) {
			
			mylog(['Loi set chung thuc' => Helper::traceError($th)]);
			throw $th;
		}
	} // End method

} // End class
