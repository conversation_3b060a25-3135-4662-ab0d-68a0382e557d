<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\SubAction;

use App\Lib\partner\MNP;
use App\Exceptions\BusinessException;
use Illuminate\Support\Facades\Cache;
use App\Modules\Merchant\Model\DeviceSession;

class GetMNPAccessTokenSubAction
{
	public function run(DeviceSession $deviceSession): DeviceSession
	{
		$cacheMnpToken = 'MnpTokenViaMerchant';
		$cacheTimeInSeconds = 3*24*60*60;

		if (Cache::has($cacheMnpToken)) {
			$deviceSession->mnp_token = Cache::get($cacheMnpToken);
			return $deviceSession;
		}


		/**
		 * array:3 [
				"status" => true
				"data" => "Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJJOTk5OTk5OSIsInJvbGVzIjpbIklOU0lERSJdLCJpc3MiOiJGR19NQU5VQUxfR0VOX1RPS0VOX1ZOX1ZOMDBfRk1NIiwiaWF0IjoxNzI0MDU2MjQzLCJleHAiOjM0NDg5NzY0ODZ9.pqKTLUOa1MOFMhlLew92obDiWdwCW-ppcYun_Sz8J6NgqvFxPrSRqqOnevCBhN5rqVWQnqa_TakQE9OwKGFRXQ"
				"message" => "Thành công"
			]
		 */
		$mnp = new MNP();
		$mnpToken = $mnp->getToken($deviceSession->getMerchantId());
		
		if (empty($mnpToken['status'])) {
			$message = $mnpToken['message'] ?? vmsg('LoiGoiDoiTacMnp');
			$err = sprintf('MNP Err: %s (Code: %s)', $message, $mnpToken['code'] ?? '00');
			throw new BusinessException($err);
		}
		
		$cacheValue = $mnpToken['data'];
		
		Cache::put($cacheMnpToken, $cacheValue, $cacheTimeInSeconds);
		$deviceSession->mnp_token = $cacheValue;
		return $deviceSession;
	}
} // End class
