<?php

namespace App\Modules\Merchant\Requests\RequestChangeInfo\V3;

use App\Modules\Merchant\Requests\MerchantRequest;
use Illuminate\Validation\Rule;

class Mpos360RequestChangeInfoMarkAsDoneRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.id' => ['required', 'numeric', 'integer', 'min:1'],
			'data.qts_request_id' => ['present', 'string'],
			'data.otp_id' => ['present', 'string'],
			'data.other_data' => ['present', 'array'],
			'data.other_data.is_matching_facescan' => ['present', Rule::in([0, 1])],
			'data.other_data.matching_percent' => ['present', 'numeric', 'integer', 'min:1', 'max:100'],
		];
	}
} // End class
