<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransactionCAHistoryCodeDetailAction;

use Illuminate\Support\Str;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionCAHistoryCodeDetailRequest;

class Mpos360TransactionCAHistoryCodeDetailAction
{
	public function run(Mpos360TransactionCAHistoryCodeDetailRequest $request)
	{
		$returnData = [
			'content' => [
				'warning' => [
					[
						'label' => '',
						'value' => 'Cần cung cấp chứng từ',
						'other_data' => [
							'text_color' => '#d329a0',
							'bg_color' => '#fbeaf6'
						]
					]
				],

				// thông tin chung
				'common_info' => [
					['label' => 'Loại GD', 'value' => 'Thanh toán thường', 'other_data' => (object) []],
					['label' => 'Hình thức', 'value' => 'Quet the', 'other_data' => (object) []],
					['label' => 'Ngân hàng/Kỳ hạn', 'value' => 'HSBC/3 tháng', 'other_data' => (object) []],
					['label' => 'Mã GD', 'value' => '14252369', 'other_data' => (object) []],
					['label' => 'Mã chuẩn chi', 'value' => '14252369', 'other_data' => (object) []],
					['label' => 'Thời gian GD', 'value' => '12:20, 14/08/2024', 'other_data' => (object) []],
					['label' => 'Thời gian kết toán', 'value' => '12:20, 14/08/2024', 'other_data' => (object) []],
					['label' => 'Thời gian hủy GD', 'value' => '12:20, 14/08/2024', 'other_data' => (object) []],
					['label' => 'Trạng thái GD', 'value' => '12:20, 14/08/2024', 'other_data' => (object) []],
					[
						'label' => 'Trạng thái trả góp',
						'value' => 'Thành công',
						'other_data' => ['text_color' => '#ffffff', 'bg_color' => '#d12e29']
					],
				], // common_info

				// kh thanh toán
				'customer_payment' => [
					['label' => 'Số tiền đơn hàng', 'value' => '10.000.000 vnd', 'other_data' => (object) []],
					['label' => 'Khuyến mại', 'value' => '1.000 vnd', 'other_data' => (object) []],
					['label' => 'Phí GD (thu KH)', 'value' => '0 vnd', 'other_data' => (object) []],
					['label' => 'Tổng thanh toán', 'value' => '9.999.000 vnd', 'other_data' => (object) []],
					[
						'label' => 'Mô tả',
						'value' => 'Lorem ipsum dolor sit amet consectetur adipisicing elit. Sed cumque earum quibusdam enim temporibus.',
						'other_data' => ['text_color' => '#404041', 'bg_color' => '#ebebeb']
					],
				], // customer_payment

				// thông tin thanh toán
				'payment_info' => [
					['label' => 'Số tiền thanh toán', 'value' => '10.000.000 vnd', 'other_data' => (object) []],
					['label' => 'Phí GD (thu Merchant)', 'value' => '1000 vnd', 'other_data' => (object) []],
					['label' => 'Phí TG (thu Merchant)', 'value' => '9.999.000 vnd', 'other_data' => (object) []],
					[
						'label' => 'Trạng thái',
						'value' => 'Chưa thanh toán',
						'other_data' => ['text_color' => '#ffffff', 'bg_color' => '#ee9c00']
					],
				], // payment_info

				// chương trình khuyến mại
				'promotions' => [
					['label' => 'Chương trình', 'value' => 'Sale Upto 30%', 'other_data' => (object) []],
					['label' => 'Mã khuyến mại', 'value' => 'KM123', 'other_data' => (object) []],
					['label' => 'Số tiền KM', 'value' => '1000 vnd', 'other_data' => (object) []],
				], // promotions

				// khách hàng
				'customer' => [
					['label' => 'Chủ thẻ', 'value' => 'Nguyen Van A', 'other_data' => (object) []],
					['label' => 'Thẻ/TKNH/Ví điện tử', 'value' => 'Visa: *****123', 'other_data' => (object) []],
				], // customer

				// khác
				'other' => [
					['label' => 'Số lô', 'value' => '1425652695', 'other_data' => (object) []],
					['label' => 'TID', 'value' => '1425652695', 'other_data' => (object) []],
					['label' => 'MID', 'value' => '1425652695', 'other_data' => (object) []],
					['label' => 'Mã cấp phép', 'value' => '652569525', 'other_data' => (object) []],
					['label' => 'Số tham chiếu', 'value' => '841520256', 'other_data' => (object) []],
					['label' => 'Mobile User', 'value' => '<EMAIL>', 'other_data' => (object) []],
				], // other
			],

			'other_data' => [
				'common_info' => ['label' => 'Thông tin chung'],
				'customer_payment' => ['label' => 'KH thanh toán'],
				'payment_info' => ['label' => 'Thông tin thanh toán'],
				'promotions' => ['label' => 'Chương trình khuyến mại'],
				'customer' => ['label' => 'Khách hàng'],
				'other' => ['label' => 'Thông tin khác'],
			]
		];

		return $returnData;
	}
}
