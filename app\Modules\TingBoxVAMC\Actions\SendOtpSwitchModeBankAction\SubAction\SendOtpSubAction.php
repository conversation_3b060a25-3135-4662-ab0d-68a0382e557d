<?php

namespace App\Modules\TingBoxVAMC\Actions\SendOtpSwitchModeBankAction\SubAction;

use Illuminate\Http\Request;
use App\Lib\MnpOnboardNewMcHelper;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Model\Mpos360CodeOtp;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\SendSmsOtpMNPSubAction;

class SendOtpSubAction
{
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run(Request $request,DeviceSession $deviceSession, Mpos360CodeOtp $mpos360CodeOtp)
	{
		$channel = strtolower($request->json('data.channel')) == 'zalo' ? true : false;
		$sendOtp = app(SendSmsOtpMNPSubAction::class)->run($mpos360CodeOtp, $deviceSession, $channel);
		return $sendOtp;
	}
}
