<?php

namespace App\Modules\Merchant\Model;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class Mpos360PhuongThucBuoc3 extends Model
{
	protected $connection = 'mpos360_data';

	protected $table      = 'authenticate_method_priority';

	protected $guarded    = [];

	public $timestamps    = false;

	protected $dates      = [];

	protected $hidden     = [];

	public static function getPhuongThucBuoc3(): Collection
	{
		$listPhuongThuc = Mpos360PhuongThucBuoc3::query()->where('status', 2)->get();
		return $listPhuongThuc;
	}
} // End class
