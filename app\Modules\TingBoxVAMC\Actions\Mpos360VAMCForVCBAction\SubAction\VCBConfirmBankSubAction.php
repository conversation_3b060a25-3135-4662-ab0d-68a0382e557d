<?php

namespace App\Modules\TingBoxVAMC\Actions\Mpos360VAMCForVCBAction\SubAction;

use App\Exceptions\BusinessException;
use App\Modules\TingBoxVAMC\Actions\MerchantBank\LinkBankAction\SubAction\SaveBankTrungGianSubAction;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use App\Modules\TingBoxVAMC\Enums\TingBoxVAMCEnum;
use Illuminate\Support\Facades\DB;

class VCBConfirmBankSubAction
{
	public function run($request)
	{
		// Lấy data từ vaResponse
		$vaResponse = $request->json('vaResponse');
		if (!isset($vaResponse['data']['vaNextpayNumber'])) {
			throw new BusinessException('Thiếu thông tin vaNextpayNumber.');
		}

		// Tìm bản ghi dựa vào vaNextpayNumber
		$merchantShopBank = MerchantShopBank::query()
			->where('partner_request_id', $vaResponse['data']['vaNextpayNumber'])
			->first();

		if (!$merchantShopBank) {
			throw new BusinessException('Không tìm thấy thông tin ngân hàng.');
		}

		
		// Call sang mnp để báo là có tài khoản ngân hàng
		$merchantShopBank->load('merchantBank');
		
		if ($merchantShopBank->merchantBank) {
			if ( !empty($request->json('verifyBank')) 
				&& ( empty($request->json('merchantType')) || $request->json('merchantType') == 'PERSONAL' )
			) {
				@app(SaveBankTrungGianSubAction::class)->run($merchantShopBank, $merchantShopBank->merchantBank);
			}
		}
		

		try {
			// DB::beginTransaction();

			$merchantShopBank->status_link = MerchantShopBank::STT_LINK_DA_LIEN_KET;
			$merchantShopBank->time_updated = now()->timestamp;
			$merchantShopBank->is_sync_tingbox = 2; // Đánh dấu đã đồng bộ
			$merchantShopBank->account_qr = $vaResponse['data']['qrCode'];
			$merchantShopBank->account_number_partner = $vaResponse['data']['vaBankNumber'];
			$merchantShopBank->data_linked = json_encode($request->json(), JSON_UNESCAPED_UNICODE);
			
			if (!empty($request->json('verifyBank'))) {
				$merchantShopBank->isVerifyBankAccount = 1;
			}

			if (!$merchantShopBank->save()) {
				throw new BusinessException('Không thể cập nhật trạng thái liên kết.');
			}

			// DB::commit();

			return [
				'msg' => 'Liên kết tài khoản ngân hàng thành công.',
				'can' => TingBoxVAMCEnum::CAN_GO_TO_TINGBOX_VAMC_SUCCESS,
				'merchantShopBankId' => $merchantShopBank->id,
				'status' => $merchantShopBank->status_link
			];
		} catch (\Throwable $th) {
			// DB::rollBack();
			throw new BusinessException($th->getMessage());
		}
	}
}
