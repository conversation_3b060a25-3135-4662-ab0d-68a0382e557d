<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForRepresentationSA\SA;

use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoUpdateProfileRequest;
use Exception;

class TaoThayDoiTaiKhoanMposSA
{

	public function run(
		Mpos360MerchantRequest $mpos360McRequest,
		array $replaceMposAccountField,
		array $profiles
	): Mpos360MerchantRequest {

		if (empty($replaceMposAccountField)) {
			return $mpos360McRequest;
		}

		$dataRequest = $mpos360McRequest->getDataRequestV3();
		$groupDoiThongTinTaiKhoanMpos = [
			'type' => 'CHANGE_MPOS_ACCOUNT_INFO',
			'choice' => 'DOI_THONG_TIN_TAI_KHOAN_MPOS',
			'sub_choice' => '',
			'profiles' => []
		];

		foreach ($replaceMposAccountField as $field) {
			$profileValue =  collect($profiles)->where('profileKey', $field)->first()['value'];
			
			if ($field == 'authoriserContactNumber') {
				$groupDoiThongTinTaiKhoanMpos['profiles']['accountMobile'] = $profileValue;
			}

			if ($field == 'authoriserEmail') {
				$groupDoiThongTinTaiKhoanMpos['profiles']['accountEmail'] = $profileValue;
			}
		}

		if (empty($groupDoiThongTinTaiKhoanMpos['profiles'])) {
			throw new BusinessException('Lỗi logic chọn trường đổi TK MPOS');
		}

		$doiTaiKhoanMposIndex = collect($dataRequest)->search(function ($item) {
			return $item['type'] == 'CHANGE_MPOS_ACCOUNT_INFO';
		});

		if ($doiTaiKhoanMposIndex === false) {
			$dataRequest[] =  $groupDoiThongTinTaiKhoanMpos;
		}else {
			$dataRequest[$doiTaiKhoanMposIndex] =  $groupDoiThongTinTaiKhoanMpos;
		}
		
		$mpos360McRequest->data_request = json_encode($dataRequest);
		$r = $mpos360McRequest->save();

		if (!$r) {
			throw new BusinessException('Lỗi lưu thông tin tạo yc đổi tài khoản mpos');
		} 

		return $mpos360McRequest;
	}
} // End class