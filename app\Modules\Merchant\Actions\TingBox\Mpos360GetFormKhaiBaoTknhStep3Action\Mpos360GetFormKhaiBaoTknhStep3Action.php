<?php

namespace App\Modules\Merchant\Actions\TingBox\Mpos360GetFormKhaiBaoTknhStep3Action;

use Illuminate\Support\Str;
use App\Lib\MnpOnboardNewMcHelper;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Requests\TingBox\Mpos360GetFormKhaiBaoTknhStep3Request;

class Mpos360GetFormKhaiBaoTknhStep3Action
{
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run(Mpos360GetFormKhaiBaoTknhStep3Request $request)
	{
		$nganhNgheNganHangThanhPho = $this->mnpOnboardNewMcHelper->getNganhNgheNganHangThanhPho(true);

		$detailMc = $this->mnpOnboardNewMcHelper->detailMc([
			'mposMcId' => $request->json('data.merchantId')
		]);


		if (empty($detailMc['result']) || $detailMc['code'] != 1000) {
			throw new BusinessException(
				'Lỗi không lấy được thông tin tên chủ tài khoản',
				$detailMc['code']
			);
		}

		$holderName = '';

		if (!empty($detailMc['data']['holderName'])) {
			$holderName = trim($detailMc['data']['holderName']);
		} elseif (!empty($detailMc['data']['customerName'])) {
			$holderName = Str::of($detailMc['data']['customerName'])->slug(' ')->upper()->trim()->__toString();
		}


		$returnData = [
			'listBank' => $nganhNgheNganHangThanhPho['banks'],
			'holderName' => $holderName
		];

		return $returnData;
	}
}
