<?php

namespace App\Lib;

use Monolog\Logger;
use Monolog\Handler\StreamHandler;
use Illuminate\Support\Facades\Log;
use Elastic\Monolog\Formatter\ElasticCommonSchemaFormatter;
use Illuminate\Http\JsonResponse;

class Logs
{
	public $_service;
	public $_nameFile;
	public $_data = [];

	public function __construct()
	{
		$this->_nameFile = 'mpos360_app_api.log';
		$this->_service = config('app.app_service_name');
	}

	public function writeFileLog($data)
	{
		$message = $this->__buildMessageParams($data);

		if (env('INFRASTRUCTURE') == 'vps') {
			Log::channel($this->_service)->error($data);
		}

		$log = new Logger($this->_service);
		
		$handler = new StreamHandler('php://stdout', Logger::DEBUG);
		$handler->setFormatter(new ElasticCommonSchemaFormatter());
		$log->pushHandler($handler);
		$log->info($message, array('service.name' => $this->_service));
	}

	protected function __buildMessageParams($data)
	{
		$message = 'Not run try catch';
		try {
			if (!isset($data) || empty($data)) {
				$message = 'Params is null';
			} else if (isset($data) && !empty($data) && (is_array($data) || is_object($data))) {
				$message = json_encode($data, JSON_UNESCAPED_UNICODE);
			} else if (!empty($data) && is_string($data)) {
				$message = $data;
			} else {
				$message = 'Params not check';
			}
		} catch (\Exception $ex) {
			$message = 'Unknown error';
		}

		return $message;
	}

	public function concatData($data = [])
	{
		$this->_data[] = $data;
	}

	public function cc($data)
	{
		return $this->concatData($data);
	}

	public function logging()
	{	
		$rules = [
			'api_key' => 'first4last4',
			'current_password' => 'full',
			'password' => 'unset',
			'password_confirmation' => 'unset',
			'currentPass' => 'unset',
			'newPass' => 'unset',
			'confirmPass' => 'unset',
			'secretKey' => 'unset',
			'secret_key' => 'unset',
			
			'pas' => 'last3', // tknh
			
			'passport' => 'maskIfArray',
			'token' => 'full',
			'tokenLogin' => 'full',
			'mpos_token' => 'full',
			'mnp_token' => 'full',

			'appId' => 'full',
			'privateKey' => 'full',
			'partnerPublicKey' => 'full',
			'urlLiveness' => 'full',

			'bank_account' => 'last3', // tknh
			'accountNo' => 'last3', // tknh
			'merchantPassport' => 'last3',

			'confirm_password' => 'full',
		];

		if (config('app.env') === 'local' || config('app.env') === 'development') {
			$rules['api_key'] = 'none';
			$rules['mpos_token'] = 'none';
			$rules['tokenLogin'] = 'none';
		}

		foreach ($this->_data as &$dt) {
			if (!empty($dt['response']) && $dt['response'] instanceof JsonResponse) {
				$dt['response'] = $dt['response']->getData(true);
			}
		}

		maskArrayKeysWithRules($this->_data, $rules);
		
		$log_data = json_encode($this->_data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
		return $this->writeFileLog($log_data);
	}

	public static function writeInfo($logName='', $logContext) {
		if (!is_array($logContext)) {
			$logContext = ['err' => $logContext];
		};
		logger()->channel('stdout')->info($logName, $logContext);
	}
} // End class
