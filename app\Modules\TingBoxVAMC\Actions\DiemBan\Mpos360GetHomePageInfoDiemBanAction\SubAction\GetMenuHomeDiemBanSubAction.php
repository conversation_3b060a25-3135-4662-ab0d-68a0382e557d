<?php

namespace App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360GetHomePageInfoDiemBanAction\SubAction;


class GetMenuHomeDiemBanSubAction
{
	public function run()
	{
		$iconVer = 'ver3';

		$menus = [
			[
				"code" => "NhanTienTuVietQr",
				"name" => "Rút tiền",
				"img" => "https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/mpos360/app/67dbc28a4465eb435f2c9851RutTien.png",
				"type" => "deeplink",
				"target" => "appScreen",
				"value" => [
					"appScreen" => [
						"screenName" => "",
						"params" => (object)[]
					],
					"webviewUrl" => "",
					"browserUrl" => ""
				],
				"sort" => "3"
			],

			[
				"code" => "YcNhanTienNhanh",
				"name" => "<PERSON><PERSON><PERSON> s<PERSON> tiền",
				"img" => "https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/mpos360/app/67dbc28a4465eb435f2c9852LichSuRutTien.png",	
				"type" => "deeplink",
				"target" => "appScreen",
				"value" => [
					"appScreen" => [
						"screenName" => "",
						"params" => (object)[]
					],
					"webviewUrl" => "",
					"browserUrl" => ""
				],
				"sort" => "2"
			],

			[
				"code" => "CaiDatWifiLoaTingBox",
				"name" => "Cài đặt Wifi/4G Tingbox",
				"img" =>  "https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/mpos360/app/67dbc28a4465eb435f2c9853CaiDatWifiTingBox.png",
				"type" => "deeplink",
				"target" => "appScreen",
				"value" => [
					"appScreen" => [
						"screenName" => "",
						"params" => (object)[]
					],
					"webviewUrl" => "",
					"browserUrl" => ""
				],
				"sort" => "4"
			]
		];

		return $menus;
	}
}
