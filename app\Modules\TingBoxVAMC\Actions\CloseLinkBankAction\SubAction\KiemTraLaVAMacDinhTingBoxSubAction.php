<?php

namespace App\Modules\TingBoxVAMC\Actions\CloseLinkBankAction\SubAction;

use App\Lib\Logs;
use App\Lib\Helper;
use App\Lib\partner\SoundBox;
use PhpParser\Node\Expr\Throw_;
use App\Exceptions\BusinessException;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\CloseLinkBankRequest;
use App\Modules\TingBoxVAMC\Actions\SetPaymentDefaultAction\SubAction\GetDefaultQrVAMSubAction;

class KiemTraLaVAMacDinhTingBoxSubAction
{
	public SoundBox $soundBox;

	public function __construct(SoundBox $soundBox)
	{
		$this->soundBox = $soundBox;
	}

	public function run(CloseLinkBankRequest $request, MerchantShopBank $merchantShopBank)
	{
		// Kiểm tra tiếp ở api detail tingbox, nếu VA này đang có state=DEFAULT thì không cho hủy
		$paramDetailMcTingBox = [
			"mcId" => $request->json('data.merchantId'), // mcId
			"muName" => $request->json('data.userMobileId'), // muId / muName
			"partnerCode" => Helper::getPartnerCode($request->json('data.merchantId')), 
		];

		$mcTingBox = $this->soundBox->getMcTingBox($paramDetailMcTingBox);
		

		if (empty($mcTingBox['data'])) {
			throw new BusinessException('Lỗi không tìm thấy thông tin merchant Tingbox');
		}

		// Comment do có thể đang đứng từ NP và bấm hủy
		// if (empty($mcTingBox['data']['mcQr'])) {
		// 	throw new BusinessException('Lỗi không tìm thấy VA mặc định của hiện tại của bạn');
		// }

		$defaultVA = collect($mcTingBox['data']['mcQr'])->first(function ($it) {
			return $it['integratedMethod'] == 'VAMC' && isset($it['state']) && $it['state'] == 'DEFAULT';
		});

		// Throw lỗi nếu cái đang chuẩn bị hủy và cái default đang là giống nhau
		// if (empty($defaultVA)) {
		// 	throw new BusinessException('Không tìm thấy tài khoản ngân hàng mặc định của bạn');
		// }

		if (!empty($defaultVA['vaNextPayNumber']) && $defaultVA['vaNextPayNumber'] == $merchantShopBank->partner_request_id) {
			throw new BusinessException('Bạn không được phép hủy liên kết tài khoản ngân hàng mặc định');
		}

		$currentVa = collect($mcTingBox['data']['mcQr'])->where('vaNextPayNumber', $merchantShopBank->partner_request_id)->first();
		
		if (empty($currentVa)) {
			throw new BusinessException('Không tìm thấy liên kết tài khoản ngân hàng hiện tại của bạn');
		}

		
		// if (isset($currentVa['status']) && $currentVa['status'] != 'ACTIVE') {
		// 	throw new BusinessException('Liên kết tài khoản ngân hàng đang không Active, từ chối hủy');
		// }

		return true;
	}
} // End class
