<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransactionBAFReceiveDetail;

use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionBAFReceiveDetailRequest;
use App\Modules\Merchant\Actions\Transaction\subAction\Mpos360TransactionDefineConfigSubAction;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransactionBAFReceiveDetail\SubAction\Mpos360PhieuChiComponentSubAction;

class Mpos360DetailPhieuChiAction
{
	public $_type = 'DETAIL_WITHDRAW';
	public function run(Mpos360TransactionBAFReceiveDetailRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		$params = [
			'typeTransaction' => $this->_type,
			'merchantFk' => $deviceSession->getMerchantId(),
			'id' => $request->json('data.order_code', ''),
			'tokenLogin' => $deviceSession->getMposToken()
		];
		$listTransaction = app(Mpos360TransactionDefineConfigSubAction::class)->requestTransDetail($params);
		return (new Mpos360PhieuChiComponentSubAction())->__returnData($listTransaction);
	}
}
