<?php

namespace App\Modules\Merchant\Requests\TingBox;

use Illuminate\Support\Str;
use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360SaveTknhStep3Request extends MerchantRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data.username' => ['required', 'string'],
			'data.bankAccountHolder' => ['required', 'string'],
			'data.bankAccountNumber' => ['required', 'string'],
			'data.bankId' => ['required', 'string'],
    ];
  }

    public function messages()
    {
        return [
          'data.bankAccountNumber.required' => 'Vui lòng nhập số tài khoản',
          'data.bankAccountNumber.string' => 'Độ dài số tài khoản không hợp lệ',
          'data.bankId.required' => 'Vui lòng chọn ngân hàng',
          'data.bankId.string' => 'Ngân hàng không nằm trong danh sách hỗ trợ',
        ];
    }

	public function getVmmcBankAccountHolder(): string {
		return Str::of($this->json('data.bankAccountHolder'))->slug(' ')->upper()->trim()->__toString();
	}
}
