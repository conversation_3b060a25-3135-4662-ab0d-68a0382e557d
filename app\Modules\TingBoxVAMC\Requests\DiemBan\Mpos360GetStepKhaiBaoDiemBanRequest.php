<?php

namespace App\Modules\TingBoxVAMC\Requests\DiemBan;

use App\Modules\Merchant\Requests\MerchantRequest;
use Illuminate\Foundation\Http\FormRequest;

class Mpos360GetStepKhaiBaoDiemBanRequest extends MerchantRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.merchantId' => ['required', 'string'],
			'data.username' => ['nullable'],
			'data.muId' => ['nullable', 'string']
    ];
  }
}
