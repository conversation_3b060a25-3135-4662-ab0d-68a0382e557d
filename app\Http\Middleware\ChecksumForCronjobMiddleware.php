<?php

namespace App\Http\Middleware;

use Closure;
use App\Traits\ApiResponser;
use App\Exceptions\BusinessException;

class ChecksumForCronjobMiddleware
{
	use ApiResponser;

	public function handle($request, Closure $next, $fieldHash)
	{
		throw_if(
			empty($request->json('lang')),
			new BusinessException(vmsg('ValidateHashMiddleware_NgonNguLaBatBuoc'), 422)
		);

		throw_if(
			!in_array($request->json('lang'), ['vi', 'en']),
			new BusinessException(vmsg('ValidateHashMiddleware_HeThongChiHoTroNgonNguViVaEn'), 422)
		);

		throw_if(
			empty($request->json('time_request')),
			new BusinessException(vmsg('ValidateHashMiddleware_TimeRequestLaBatBuoc'), 422)
		);

		$apiKey = $request->json('api_key');

		if (empty($apiKey)) {
			throw new BusinessException(vmsg('ValidateHashMiddleware_ApiKeyLaBatBuoc'), 422);
		}
		
		$apiSecret = env('MPOS360_CRONJOB_SECRET_KEY');

		$fieldExplode = explode('|', $fieldHash);
		if (empty($fieldExplode)) {
			throw new BusinessException(vmsg('ValidateHashMiddleware_LoiApiHashField'), 422);
		}

		$combineString = '';

		foreach ($fieldExplode as $key) {
			$combineString .= sprintf('%s:%s+', $key, $request->json('data.' . $key));
		}

		$combineChecksum = [
			$request->method(),
			$request->path(),
			$request->json('time_request'),
			$request->json('lang'),
			$combineString,
			$apiKey
		];

		$stringBeforeHash = implode('|', $combineChecksum);

		$checksum = hash_hmac('sha512', $stringBeforeHash, $apiSecret);

		$clientCheckSum = $request->json('checksum');

		if (empty($clientCheckSum) || empty($checksum)) {
			throw new BusinessException('Mã tham chiếu là bắt buộc', 422);
		}

		if ($clientCheckSum == $checksum) {
			return $next($request);
		}

		throw new BusinessException('Mã tham chiếu không hợp lệ', 412);
	}
} // End class
