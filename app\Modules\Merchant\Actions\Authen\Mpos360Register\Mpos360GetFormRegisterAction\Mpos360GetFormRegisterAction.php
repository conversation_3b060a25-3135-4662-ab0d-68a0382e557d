<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360GetFormRegisterAction;

use App\Modules\Merchant\Model\Setting;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360AuthRegisterRequest;

class Mpos360GetFormRegisterAction
{
	/**
	 * Không có form đăng ký, thay vào đó return link
	 *
	 * @param Mpos360AuthRegisterRequest $request
	 * @return void
	 */
	public function run(): array
	{
		$setting = Setting::query()->firstWhere(['key' => 'HOST_ASSET_MAIL_TEMPLATE']);
		if ($setting) {
			$settingValue = json_decode($setting->value, true);
		}

		$returnData = [
			'form' => [
				['field' => 'username', 'label' => 'Số điện thoại'],
				['field' => 'password', 'label' => 'Mật khẩu'],
				['field' => 'password_confirmation', 'label' => 'Nhập lại mật khẩu'],
			],

			'checkbox' => 'Tôi đồng ý với {DieuKhoanChinhSach}',
			'other_data' => [
				'DieuKhoanChinhSach' => [
					'text' => 'điều khoản và chính sách',
					'text_color' => '#018bf4',
					'webViewUrl' => $settingValue['common']['mposDieuKhoanChinhSachDangKyTaiKhoanUrl']
				]
			]
		];

		return $returnData;
	}
}
