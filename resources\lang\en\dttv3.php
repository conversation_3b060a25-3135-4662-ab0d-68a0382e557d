<?php

return [
  '<PERSON>ail là bắt buộc' => 'Email is required',
  'Email phải là kiểu chuỗi' => 'Email must be a string',
  'Email không đúng định dạng' => 'Invalid email format',
  'SĐT là bắt buộc' => 'Phone number is required',
  'SĐT phải là kiểu chuỗi' => 'Phone number must be a string',
  'SĐT có độ dài tối thiểu phải là 10 ký tự số' => 'Phone number must have at least 10 digits',
  'SĐT có độ dài tối đa phải là 12 ký tự số' => 'Phone number must have at most 12 digits',
  'Trường chọn thay thế tài khoản mpos là bắt buộc' => 'The mpos account replacement field is required',
  'Trường chọn thay thế tài khoản mpos phải là kiểu mảng' => 'The mpos account replacement field must be an array',
  'Trườ<PERSON> thay thế TKĐN mpos phải là kiểu chuỗi' => 'The mpos login account replacement field must be a string',
  'Trường thay thế TKĐN mpos không thuộc các giá trị đã chỉ định' => 'The mpos login account replacement field does not match the specified values',

  '*Email đã tồn tại, vui lòng nhập email khác' => '*Email already exists, please enter another email',
  '*SĐT đã tồn tại, vui lòng nhập SĐT khác' => '*Phone number already exists, please enter another phone number',
  'Thành công' => 'Success',

  'Lỗi truy vấn đối tác MNP' => 'MNP partner query error',

  'Tạo yêu cầu đổi thông tin thành công' => 'Information change request created successfully',

  'Lỗi không tạo được bản ghi yêu cầu thay đổi thông tin' => 'Failed to create information change request',
  'Lỗi: không tạo được yc đổi thông tin TKNH' => 'Error: unable to create bank account information change request',
  'Lỗi: ít nhất bạn phải thay đổi Email hoặc SĐT' => 'Error: you must change at least the email or phone number',
  'CCCD mới là bắt buộc' => 'New ID card is required',
  'CCCD của người đại diện mới là bắt buộc' => 'Representative’s new ID card is required',
  'Email người đại diện là bắt buộc' => 'Representative’s email is required',
  'SĐT người đại diện là bắt buộc' => 'Representative’s phone number is required',
  'Lỗi không tạo được bản ghi yêu cầu thay đổi' => 'Failed to create change request record',
  'Bạn cần xác minh otp' => 'You need to verify OTP',


  'Id yêu cầu là bắt buộc' => 'Id id required',
  'Lỗi: YC đổi thông tin không tồn tại' => 'Err: Your request is not found',

  'Lỗi: không tìm thấy thông tin yêu cầu' => 'Error: request information not found',
  'Lỗi: Bạn không phải chủ sở hữu yc này' => 'Error: You are not the owner of this request',
  'Lỗi: CCCD không trùng khớp với thông tin của người đại diện hiện tại. Vui lòng sử dụng đúng CCCD còn hạn sử dụng và là bản mới nhất.' => 'Error: The ID card does not match the current representative’s information. Please use a valid and latest ID card.',
  'Ghi nhận kết quả thành công' => 'Successfully recorded result',
  'Lỗi: không tìm được cccd chứng thực' => 'Error: verification ID card not found',
  'Lỗi: bạn phải truyền thông tin qts id lên (passport)' => 'Error: you must provide QTS ID information (passport)',
  'Lỗi update thông tin hồ sơ vào bản ghi' => 'Error updating profile information into the record',

  /**
   * @param 'Mpos360RequestChangeInfoAllOtpProfileSuccessRequest'
   */
  'Id là bắt buộc' => 'Id is required',
  'Id phải là dạng số' => 'Id must be a number',
  'Id phải là số nguyên' => 'Id must be an integer',
  'Id phải có giá trị là 1' => 'Id must have a value of 1',

  'Trường xác thực là bắt buộc' => 'Validation field is required',
  'Trường xác thực phải là kiểu mảng' => 'Validation field must be an array',
  'profileKey là bắt buộc' => 'profileKey is required',
  'profileKey phải là kiểu chuỗi' => 'profileKey must be a string',
  'giá trị hồ sơ là bắt buộc' => 'Profile value is required',
  'giá trị hồ sơ phải là kiểu chuỗi' => 'Profile value must be a string',
  'giá trị hồ sơ phải có độ dài tối thiểu là 2 ký tự' => 'Profile value must have at least 2 characters',
  'StatusVerify là bắt buộc' => 'StatusVerify is required',
  'StatusVerify phải thuộc giá trị cho phép: 1' => 'StatusVerify must be an allowed value: 1',
  'Thời gian xác thực là bắt buộc' => 'Verification time is required',
  'Thời gian xác thực phải là kiểu số' => 'Verification time must be a number',
  'Thời gian xác thực phải là kiểu số nguyên' => 'Verification time must be an integer',

  'Thiếu thông tin Email cần thay đổi (authoriserEmail)' => 'Missing email information to be changed (authoriserEmail)',
  'Thiếu thông tin SĐT cần thay đổi (authoriserContactNumber)' => 'Missing phone number information to be changed (authoriserContactNumber)',
  'Trường chứng thực thông tin chỉ được phép có 1 phần tử duy nhất (passport)' => 'The information validation field can only have one element (passport)',
  'Thiếu thông tin passport (qtsRequestId)' => 'Missing passport information (qtsRequestId)',
  'passport phải là 1 uuid' => 'Passport must be a uuid',
  'Phải có ít nhất 1 thông tin (passport)' => 'There must be at least one piece of information (passport)',

	/**
	 * @param Mpos360RequestChangeInfoMarkAsDoneAction::class
	 */
	'Lỗi: không tìm thấy yêu cầu thay đổi' => 'Error: change request not found',
	'Lỗi: yêu cầu của bạn có trạng thái không hợp lệ' => 'Error: your request has an invalid status',
	'Thiếu thông tin QtsRequestId' => 'Missing QtsRequestId information',
	'Thiếu thông tin OtpId' => 'Missing OtpId information',
	'Thiếu thông tin tỷ lệ khớp khuôn mặt' => 'Missing face match ratio information',
	'Lỗi: không đánh dấu được yc là mới tạo' => 'Error: could not mark request as newly created',
	'Lỗi không tìm thấy bản ghi chứng thực' => 'Error: verification record not found',
	'Lỗi: xác thực người thay đổi không giống với số CMT/CCCD hiện tại của bạn' => 'Error: the person changing is not verified against your current ID number (CMT/CCCD)',
	'Lỗi cập nhật chứng thực' => 'Error updating verification',

	/**
	 * @param Mpos360RequestChangeInfoGetOtpAction
	 */
	'Lỗi: không tìm thấy bản ghi yc thay đổi thông tin' => 'Error: change information request record not found',
	'Mã xác thực OTP đã được gửi đến email. Vui lòng kiểm tra và xác thực' => 'OTP verification code has been sent to the email :email. Please check and verify',
	'Mã xác thực OTP đã được gửi đến SĐT. Vui lòng kiểm tra và nhập vào ô dưới đây' => 'OTP verification code has been sent to the phone number :mobile. Please check and enter it in the field below',

	/**
	 * @param Mpos360RequestChangeInfoVerifyProfileAction
	 */
	'Lỗi: không tìm thấy yêu cầu của bạn' => 'Error: your request not found',
	'Lỗi: sai lệch bản ghi yêu cầu' => 'Error: request record mismatch',
	'Mã OTP không đúng!' => 'Incorrect OTP code!',
	'Mã OTP không đúng hoặc đã hết hạn, vui lòng kiểm tra lại trong tin nhắn, hoặc lấy lại mã khác' => 'Incorrect or expired OTP code, please check your message or request a new code',
	'Lỗi: Otp đã được sử dụng' => 'Error: OTP has already been used',
	'Lỗi: sai thông tin otp của yêu cầu' => 'Error: incorrect OTP information for the request',
	'Lỗi: không cập nhật otp về trạng thái cuối' => 'Error: OTP could not be updated to the final status',
	'Lỗi: không thể đánh dấu hồ sơ là đã xác thực' => 'Error: profile could not be marked as verified',

	'Đổi người đại diện mới' => 'Change new representative',
	'Cập nhật CCCD mới' => 'Update new ID card (CCCD)',
	'Đổi thông tin liên hệ' => 'Change contact information',
	'Không xác định' => 'Undefined',
	'Đổi TKNH' => 'Change Banking Account Info',
	'Thông tin yêu cầu' => 'Request Info',
	'Cá nhân' => 'Individual',
	'Công ty' => 'Company',
	'Hộ kinh doanh' => 'Business household',
	'Đổi người đại diện mới' => 'Change new representative',
	'Đổi thông tin người đại diện hiện tại' => 'Change info current representative',

	'Đổi số TKNH khác của hộ kinh doanh' => 'Change to a different business household bank account number',
	'Đổi số TKNH cá nhân được HKD ủy quyền' => 'Change to an individual bank account number authorized by the business household',
	'Đổi số TKNH của doanh nghiệp' => 'Change to a business bank account number',
	'Đổi số TKNH cá nhân được doanh nghiệp ủy quyền' => 'Change to an individual bank account number authorized by the business',
	'Đổi TKNH' => 'Change bank account number',
	'Đổi người đại diện mới của HKD' => 'Change the new representative of the business household',
	'Đổi CCCD mới của HKD' => 'Change the new citizen ID of the business household',
	'Đổi thông tin liên hệ của HKD' => 'Change the contact information of the business household',
	'Đổi người đại diện mới của doanh nghiệp' => 'Change the new representative of the business',
	'Đổi CCCD mới của doanh nghiệp' => 'Change the new citizen ID of the business',
	'Đổi thông tin liên hệ của doanh nghiệp' => 'Change the contact information of the business',
	'Đổi thông tin người đại diện' => 'Change the representatives information',
	'Không xác định' => 'Unknow Case',

  'Tên ngân hàng' => 'Tên ngân hàng',
  'Chi nhánh ngân hàng' => 'Chi nhánh ngân hàng',
  'Số tài khoản' => 'Số tài khoản',
  'Tên người thụ hưởng' => 'Tên người thụ hưởng',
  'Tỉnh/Thành phố' => 'Tỉnh/Thành phố',
]; // End