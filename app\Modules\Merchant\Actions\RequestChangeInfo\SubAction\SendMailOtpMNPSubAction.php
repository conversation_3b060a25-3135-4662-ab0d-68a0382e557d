<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\SubAction;

use Exception;
use App\Lib\partner\MNP;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Model\Mpos360CodeOtp;

class SendMailOtpMNPSubAction
{
	public MNP $mnp;


	public function __construct(MNP $mnp)
	{
		$this->mnp = $mnp;
	}

	public function run(
		Mpos360CodeOtp $mpos360CodeOtp, 
		DeviceSession $deviceSessionWithToken, 
		$template='content',
		$subject = 'Mã OTP dùng để xác minh thông tin'
	) {
		$mpos360CodeOtp->load('mpos360User');
		$content = config('otp.' . $template);

		$customFields = [
			'[@M360_HoTen]' => $mpos360CodeOtp->mpos360User->getUserMcName(),
			'[@M360_Otp]' => $mpos360CodeOtp->otp
		];

		foreach ($customFields as $field => $value) {
			$content = str_replace($field, $value, $content);
		}

		$param = [
			'to' => [$mpos360CodeOtp->obj_value],
			'cc' => [],
			'html' => $content,
			'subject' => sprintf('[%s] - %s', $mpos360CodeOtp->otp, $subject)
		];

		$sendMailResult = $this->mnp->sentEmail($param, $deviceSessionWithToken->mnp_token);

		mylog([
			'param send mail' => $param,
			'result send mail' => $sendMailResult
		]);

		if (empty($sendMailResult['status'])) {
			$msg = $sendMailResult['message'] ?? 'không xác định';
			throw new BusinessException(vmsg('LoiGuiOtpQuaEmail') . $msg);
		}

		return true;
	} // End method
} // End clasds
