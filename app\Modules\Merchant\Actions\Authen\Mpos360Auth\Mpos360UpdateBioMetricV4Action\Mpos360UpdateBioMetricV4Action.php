<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360UpdateBioMetricV4Action;

use Exception;
use App\Lib\Helper;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Device;
use App\Modules\Merchant\Requests\Authen\Mpos360\V4\Mpos360UpdateBioMetricV4Request;

class Mpos360UpdateBioMetricV4Action
{

	public function run(Mpos360UpdateBioMetricV4Request $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();

		$device = Device::query()->whereRaw("UPPER(os) = ?", [trim($request->json('data.os'))])
														 ->where('token', $request->json('data.deviceToken'))
														 ->where('user_id', $deviceSession->user_id)
														 ->first();
		if (!$device) {
			// Đăng nhập lần đầu, sổ luôn vào màn hình setting và bật sinh trắc thì nên store lại luôn
			$device = Device::query()->forceCreate([
				'os' => Helper::chuanHoaOs($request->json('data.os')),
				'token' => $request->json('data.deviceToken'),
				'user_id' => $deviceSession->user_id,
				'time_created' => now()->timestamp,
				'time_updated' => now()->timestamp
			]);

			if (!$device) {
				throw new BusinessException('Lỗi: Không lưu được thông tin sinh trắc của thiết bị');
			}
		}

		if (!$request->isOffBioMetric()) {
			$bioMetricType = $request->json('data.bioMetricType');

			$newItemMetric = [
				'type' => 'BIO_METRIC',
				'time_modified' => now()->timestamp,
				'data' => [
					'bioMetricType' => $bioMetricType,
					'bioMetricData' => $request->json('data.bioMetricData'),
				]
			];

			$otherData = json_decode($device->other_key, true);

			// loại bỏ các phương thức sinh trắc hiện tại để lấy item mới nhất
			$otherData = collect($otherData)->filter(function ($item) use ($bioMetricType) {
				return $item['data']['bioMetricType'] != $bioMetricType;
			})
			->values()
			->all();
			
			$otherData[] = $newItemMetric;

			$device->other_key = json_encode($otherData);
		}

		if ($request->isOffBioMetric()) {
			$device->other_key = null;
		}

		
		$r = $device->save();
		
		if (!$r) {
			throw new BusinessException('Lỗi không lưu được thông tin sinh trắc');
		}

		return $device;
	}
} // End class
