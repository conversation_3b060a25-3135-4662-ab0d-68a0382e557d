<?php

namespace App\Modules\Merchant\Actions\TingBox\Mpos360CheckTknhKhaiBaoStep3Action\SubAction;

use Exception;
use App\Lib\partner\VMMC;
use Illuminate\Support\Str;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Setting;

class KiemTraStkLaThatSubAction
{
	const STK_KHONG_DUNG = 'STK_KHONG_DUNG';
	const STK_KHONG_XAC_DINH = 'STK_KHONG_XAC_DINH';
	const STK_HOP_LE = 'STK_HOP_LE';

	public VMMC $vmmc;

	public function __construct(VMMC $vmmc)
	{
		$this->vmmc = $vmmc;
	}

	public function run($vmmcParamCheckStk=[])
	{
		$checkBankInfoResult = $this->vmmc->checkBankAccountMerchant($vmmcParamCheckStk);
		mylog(['Ket qua check' => $checkBankInfoResult]);

		if (empty($checkBankInfoResult['status_code_partner'])) {
			throw new BusinessException(vmsg('Lỗi không kiểm tra được thông tin ngân hàng'));
		}

		$code = $checkBankInfoResult['status_code_partner'];

		// Thành công
		if ($code == '00') {
			if (
				!empty($checkBankInfoResult['data'])
				&& Str::contains($checkBankInfoResult['data']['bank_account_holder'], $vmmcParamCheckStk['bank_account_holder'])
			) {
				$returnData = [
					'status' => self::STK_HOP_LE,
					'msg'    => vmsg('Số tài khoản hợp lệ'),
					'desc' => 'Để tránh sai sót khi nhận tiền. Vui lòng kiểm tra lại thông tin trước khi tiếp tục',
					'bankAccountHolderChecked' => $checkBankInfoResult['data']['bank_account_holder']
				];
			} else {
				$returnData = $this->__handleWrongInfo($checkBankInfoResult['data']['bank_account_holder']);
			}
		} elseif ($code == '99') {
			$returnData = $this->__handleKhongXacDinhInfo();
		} else {
			$returnData = $this->__handleWrongInfo();
		}


		$setting = Setting::query()->firstWhere(['key' => 'FAKE_CHECK_BANKING_INFO']);

		switch (optional($setting)->value) {
			case 'HOP_LE';
				$returnData['status'] = self::STK_HOP_LE;
				$returnData['msg'] = vmsg('Số tài khoản hợp lệ');
				$returnData['desc'] = 'Để tránh sai sót khi nhận tiền. Vui lòng kiểm tra lại thông tin trước khi tiếp tục';
				break;

			case 'TAI_KHOAN_KHONG_DUNG';
				$returnData['status'] = self::STK_KHONG_DUNG;
				$returnData['msg'] = vmsg('Số tài khoản không chính xác. Vui lòng kiểm tra lại');
				$returnData['desc'] = '';
				break;

			case 'KHONG_XAC_DINH';
				$returnData['status'] = self::STK_KHONG_XAC_DINH;
				$returnData['msg'] = vmsg('Số tài khoản chưc được xác minh bởi ngân hàng. Vui lòng kiểm tra lại thông tin trước khi tiếp tục!');
				$returnData['desc'] = '';
				break;

			default;
				// code: dùng thật
				break;
		}

		return $returnData;
	}

	private function __handleWrongInfo($bankAccountHolderChecked='')
	{
		return [
			'status' => self::STK_KHONG_DUNG,
			'msg'    => vmsg('Số tài khoản không hợp lệ'),
			'desc' => '',
			'bankAccountHolderChecked' => $bankAccountHolderChecked
		];
	}

	private function __handleKhongXacDinhInfo()
	{
		return [
			'status' => self::STK_KHONG_XAC_DINH,
			'msg'    => vmsg('Số tài khoản không hợp lệ'),
			'desc' => '',
			'bankAccountHolderChecked' => ''
		];
	}
} // End class
