<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransactionListNormalAction;

use Carbon\Carbon;
use App\Lib\Helper;
use Illuminate\Support\Arr;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionListNormalRequest;
use App\Modules\Merchant\Actions\Transaction\subAction\Mpos360TransactionDefineConfigSubAction;

class Mpos360TransactionListNormalAction
{


	
	public function run(Mpos360TransactionListNormalRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();

		$params = [
			'typeTransaction' => 'STATISTIC_TRANSACTION',
			'merchantFk' => $deviceSession->getMerchantId(),
			'startDate' => $request->getStartDate(),
			'endDate' => $request->getEndDate(),
			'tokenLogin' => $deviceSession->getMposToken(),
			'pageIndex' =>  $request->json('data.start', '1'),
			'pageSize' =>  $request->json('data.limit', '100'),
			'statusTransaction'=> $request->getTranStatus(),
			'paymentMethod'=> $request->getPaymentMethod(),
		];
		
		$listTransaction = app(Mpos360TransactionDefineConfigSubAction::class)->requestTransList($params);
		
		$data = [];
		
		$params['rangeTime'] = '';

		if (!empty($listTransaction['data']['transactionStatisticList'])) {
			$trans = collect($listTransaction['data']['transactionStatisticList'])->sortByDesc(function ($item) {
				return strtotime($item['createdDate']);
			})
			->values()
			->all();

			$listTransaction['data']['transactionStatisticList'] = $trans;

			if (isset($listTransaction['data']['transactionStatisticList'])) {
				$data = $this->__convertDataReturn($listTransaction['data']['transactionStatisticList']);
				
			}

			$params['rangeTime'] = collect($listTransaction['data']['transactionStatisticList'])->map(function ($item) {
				return Carbon::createFromFormat('d-m-Y H:i:s', $item['createdDate'])->format('d-m-Y');
			})
			->unique()
			->sortBy(function ($item) {
				return strtotime($item);
			})
			->implode(',');
		}

		$params['email'] = $deviceSession->getMerchantEmail();

		$returnData = [
			'rows' => 0,
			'data' => $data,
			'other_data' => [
				'status' => $this->__getStatusDefault(),
				'status_installment'=> $this->__getStatusInstallMent(),
				'filter' => $this->__getFilterDefault(),
				'countSumFilter' => Arr::only($params, [
					'typeTransaction',
					'merchantFk',
					'statusTransaction',
					'paymentMethod',
					'rangeTime',
					'email'
				])
			]
		];

		return $returnData;
	}

	private function __getStatusDefault()
	{
		return (new Mpos360TransactionDefineConfigSubAction())->getStatusTrans(); 
	}
	private function __getFilterDefault()
	{
		
		$data = $this->__getStatusDefault();
		$statusArr[] = ['value' => 'ALL', 'label' => trans_choice_fallback('trans.all')];
		foreach ($data as $key => $value) {
			$statusArr[] = [
				'value' => $value['value'],
				'label' => $value['label']
			];
		}
		return  [
			[
				'key' => 'transaction_time',
				'name' => trans_choice_fallback('trans.title.createdDate','Thời gian giao dịch'),
				'list' => [
					['value' => 'TODAY', 'label' =>trans_choice_fallback('trans.title.TODAY','Tháng này')],
					['value' => 'THIS_MONTH', 'label' => trans_choice_fallback('trans.title.THIS_MONTH','Tháng này')],
					['value' => 'LAST_MONTH', 'label' => trans_choice_fallback('trans.title.LAST_MONTH','Tháng trước')],
				]
			],

			[
				'key' => 'transaction_method',
				'name' => trans_choice_fallback('trans.title.paymentMethod','Hình thức giao dịch'),
				'list' => $this->__getTransMethodName(),
			],
			[
				'key' => 'transaction_status',
				'name' =>trans_choice_fallback('trans.title.status','Trạng thái giao dịch'),
				'list' => $statusArr
			]
		];
	}
	
	protected function __getStatusInstallMent()
	{
		return (new Mpos360TransactionDefineConfigSubAction())->getStatusInstallment(); 
	}
	
	private function __convertDataReturn($data)
	{
		if ($data) {
			$dataReturn = [];
			$mapData = $this->__getTransMethodName();
			foreach ($mapData as $key1 => $value1) {
				$transMethodNameArr[$value1['value']] = $value1['label'];
			}
			foreach ($data as $key => $value) {
				$timestamp = strtotime($value['createdDate']);
				$timeD = date('d-m-Y', $timestamp);
				if($timeD == date('d-m-Y')) {
					$timeD = __('trans.title.Hôm nay');
				}
				$timeKey = date('Ymd', $timestamp);
				$transaction_method = $this->__getTransMethodCode($value);
				
				$transaction_method_name = isset($transMethodNameArr[$transaction_method])?$transMethodNameArr[$transaction_method]:$transaction_method;
				$cardType = $this->__getCardType($value);
				$dataArr[$timeD][] = [
					'icon' => cumtomAsset('images/transaction/transaction-list-normal/CARDS_WIPE.png'),
					'transaction_id' => $value['txid'],
					'transaction_method' => $transaction_method, // thẻ
					'transaction_method_name' => $transaction_method_name,
					'card_number' => ucfirst(strtolower($cardType)) . ': **** ' . substr($value['pan'], -4),
					'time_created' => date('H:i',$timestamp),
					'amount' => Helper::numberFormat($value['amountTransaction']) . ' VND',
					'status' => $value['status'],
					'note' => '',
					'other_data' => [
						'note' => ['text_color' => '', 'bg_color' => '']
					],
				];
			}
			foreach ($dataArr as $key => $value) {
				$dataReturn[] = [
					'date' => $key,
					'total_transaction' => 0,
					'list_transaction' => $value,
				];
			}
			return $dataReturn;
		}
		return [];
	}


	private function __getCardType($value)
	{
		if (isset($value['issuerCode'])) {
			return (new Mpos360TransactionDefineConfigSubAction())->getTypeCard($value['issuerCode']); 
		}
		return '';
	}
	private function __getTransMethodCode($value)
	{
		if ($value) {
			return (new Mpos360TransactionDefineConfigSubAction())->getPaymentMethodCode($value); 
		}
		return '';
	}
	private function __getTransMethodName()
	{
		return (new Mpos360TransactionDefineConfigSubAction())->getPaymentMethod();
	}
}
