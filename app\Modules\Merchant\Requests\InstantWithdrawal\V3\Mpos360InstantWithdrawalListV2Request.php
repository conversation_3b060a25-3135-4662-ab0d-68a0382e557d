<?php

namespace App\Modules\Merchant\Requests\InstantWithdrawal\V3;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360InstantWithdrawalListV2Request extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.username' => ['required', 'string'],
			'data.merchantId' => ['required', 'numeric'],
			'data.currentPage' => ['required', 'numeric'],
			'data.status' => ['present', 'string'],
			'data.time' => [
				'present', 
				'string',
				'in:ALL,CHO_DUYET,NH_DANG_CHI,DA_THANH_TOAN,CHI_THAT_BAI'
			]
		];
	}

	public function getStartDateEndDate() {
		$time = $this->json('data.time');
		switch ($time) {
			case 'THIS_MONTH': 
				return [
					'startDate' => now()->startOfMonth()->format('d/m/Y H:i:s'),
					'endDate' => now()->endOfMonth()->format('d/m/Y H:i:s'),
				];

			case 'TODAY': 
				return [
					'startDate' => now()->startOfDay()->format('d/m/Y H:i:s'),
					'endDate' => now()->endOfDay()->format('d/m/Y H:i:s'),
				];

			case 'YESTERDAY': 
				return [
					'startDate' => now()->subDay()->startOfDay()->format('d/m/Y H:i:s'),
					'endDate' => now()->subDay()->endOfDay()->format('d/m/Y H:i:s'),
				];

			case 'THIS_WEEK': 
				return [
					'startDate' => now()->startOfWeek()->startOfDay()->format('d/m/Y H:i:s'),
					'endDate' => now()->endOfWeek()->endOfDay()->format('d/m/Y H:i:s'),
				];

			default: 
				return [];
		}

		return [];
	}
} // End class
