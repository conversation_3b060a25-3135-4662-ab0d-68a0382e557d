<?php

namespace App\Http\Middleware;

use App\Exceptions\BusinessException;
use App\Exceptions\ValidateAsJsonException;
use Closure;

class MakeSureThatRequestIsJsonMiddleware
{
	public function handle($request, Closure $next)
	{
		$header = $request->header();

		// enter url của api vào trình duyệt thì redirect luôn
		if (empty($header['content-type'])) {
			return redirect()->to('/');
		}

		$isExistApplicationJson = collect($header['content-type'])->contains(function ($item) {
			return $item == 'application/json';
		});

		if (!$isExistApplicationJson) {
			throw new BusinessException('your request is not json request', 416);
		}

		return $next($request);
	}
}
