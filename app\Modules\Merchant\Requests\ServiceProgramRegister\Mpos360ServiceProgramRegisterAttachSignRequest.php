<?php

namespace App\Modules\Merchant\Requests\ServiceProgramRegister;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360ServiceProgramRegisterAttachSignRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.email' => ['required', 'email'],
			'data.request_id' => ['required', 'numeric'],
			'data.signatureUrl' => ['required', 'string', 'url']
		];
	}
} // End class
