<?php

namespace App\Modules\WebBackend\Controllers\Setting;

use App\Exceptions\BusinessException;
use Illuminate\Http\Request;
use App\Modules\Merchant\Model\Setting;
use App\Modules\WebBackend\Controllers\Controller;
use App\Modules\WebBackend\Requests\Setting\UpdateSettingRequest;

class SettingController extends Controller
{
	public function __construct()
	{
		echo "404 Not Found";
		exit;
	}

	public function GetAllSetting(Request $request)
	{
		$settings = Setting::query();

		$settingId = trim($request->json('data.settingId', ''));

		if (!empty($settingId)) {
			$settings->where(function ($q) use ($settingId) {
				return $q->where('id', $settingId)->orWhere('key', $settingId);
			});
		}

		$settings = $settings->latest('id')->get();
		return $this->successResponse([
			'rows' => $settings->count(),
			'data' => $settings->toArray()
		], $request);
	}

	public function GetDetailSetting(Request $request)
	{
		$id = $request->json('data.settingId', '');
		$setting = Setting::query()->find($id);
		if (!$setting) {
			throw new BusinessException('Setting not found');
		}

		return $this->successResponse($setting->toArray(), $request);
	}

	public function UpdateSetting(UpdateSettingRequest $request)
	{
		$key = $request->json('data.key');

		$settingId = $request->json('data.id');
		$setting = Setting::query()->find($settingId);
		if (!$setting) {
			throw new BusinessException('Setting không tồn tại');
		}

		$otherSettingSameKey = Setting::query()->where('key', $key)->where('id', '!=', $settingId)->first();
		if ($otherSettingSameKey) {
			throw new BusinessException('Đã có setting khác sử dụng key này. Bạn cần đổi key cấu hình');
		}

		$setting->name = $request->json('data.name');
		$setting->key = trim($request->json('data.key'));
		$setting->value = trim($request->json('data.value'));
		$setting->description = trim($request->json('data.description'));
		$setting->time_updated = now()->timestamp;
		$r = $setting->save();

		if (!$r) {
			throw new BusinessException('Lỗi: không cập nhật được bản ghi setting');
		}
		return $this->successResponse(['id' => $setting->id], $request, 200, __('Cập nhật setting thành công'));
	}
} // End class
