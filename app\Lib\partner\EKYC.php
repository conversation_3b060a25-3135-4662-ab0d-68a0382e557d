<?php
namespace App\Lib\partner;

use App\Lib\Helper;
use GuzzleHttp\Client;
use App\Lib\TelegramAlertWarning;
use GuzzleHttp\Exception\RequestException;

class EKYC {
    private $baseUrl;
    private $client;
    private $pgUserCode;
    private $channelName;
    private $username;
    private $password ;
    private $secretKey;
    private $privateKey;
    private $licenName;
    public function __construct() {
        $this->pgUserCode = env('API_EKYC_USER_CODE');
        $this->channelName = env('API_EKYC_CHANEL_NAME');
        $this->username = env('API_EKYC_USERNAME');
        $this->password = env('API_EKYC_PASSWORD');
        $this->secretKey = env('API_EKYC_SERECKEY');
        $this->baseUrl = env('API_EKYC_URL');
        $this->licenName = env('API_EKYC_LICENSE_NAME');
        // $privateKey = json_decode(env('API_EKYC_PRIVATEKEY'),true);
        $this->privateKey = env('API_EKYC_PRIVATEKEY');//$privateKey['key_private'];  
        $this->client = new Client(['base_uri' => $this->baseUrl]);
        // var_dump( $this->privateKey);die();
    }

    public function verifyCert($licenseName, $requestId) {
        $licenseName = $this->licenName;
        $key = $this->formatPrivateKey($this->privateKey);
        $data = [
            'licenseName' => $licenseName,
            'privateKey' =>$key,
            'request_id' => $requestId
        ];
        
        $jsonData =json_encode($data, JSON_UNESCAPED_SLASHES);
        $requestBody = [
            'pg_user_code' => $this->pgUserCode,
            'channel_name' => $this->channelName,
            'fnc' => 'VerifyCert',
            'data' =>$jsonData ,
            'checksum' => $this->generateChecksum($jsonData)
        ];
        try {
            $response = $this->call('POST', '', $requestBody);
            return json_decode($response->getBody(), true);
        } catch (\Exception $e) {
            TelegramAlertWarning::sendMessage(Helper::traceError($e));
            return [
                'status' => false,
                'error_code' => '400',
                'message' => $e->getMessage()
            ];
        }
    }
    public function getLogDetail($requestId) {
        $key = $this->formatPrivateKey($this->privateKey);
        $data = [
            'requestId' => $requestId
        ];
        
        $jsonData =json_encode($data, JSON_UNESCAPED_SLASHES);
        $requestBody = [
            'pg_user_code' => $this->pgUserCode,
            'channel_name' => $this->channelName,
            'fnc' => 'GetLogDetail',
            'data' =>$jsonData ,
            'checksum' => $this->generateChecksum($jsonData)
        ];
        try {
            $response = $this->call('POST', '', $requestBody);
            return json_decode($response->getBody(), true);
        } catch (\Exception $e) {
            TelegramAlertWarning::sendMessage(Helper::traceError($e));
            return [
                'status' => false,
                'error_code' => '400',
                'message' => $e->getMessage()
            ];
        }
    }

    
    private function formatPrivateKey($key) {
        // Loại bỏ các dòng đầu và cuối
        $lines = explode("\n", trim($key));
        array_shift($lines);
        array_pop($lines);
    
        // Nối các dòng còn lại thành một chuỗi duy nhất
        return implode('', array_map('trim', $lines));
    }
    private function call($method, $endpoint, $data = []) {
        $options = [
            'headers' => [
                'Content-Type' => 'application/json',
                'Authorization' => 'Basic ' . base64_encode($this->username . ':' . $this->password)
            ],
            'json' => $data
        ];

        try {
            $response = $this->client->request($method, $endpoint, $options);
            return $response;
        } catch (RequestException $e) {
            TelegramAlertWarning::sendMessage(Helper::traceError($e));
            throw new \Exception("API call failed: " . $e->getMessage());
        }
    }

    private function generateChecksum($data) {
        return md5($data . $this->secretKey);
    }
}