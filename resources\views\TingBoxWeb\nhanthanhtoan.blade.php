<html lang="zxx">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title><PERSON><PERSON>u c<PERSON>u đổi thông tin</title>
    <link rel="icon" type="image/png" href="{{cumtomAsset('assets/img/favicon-mpos.svg')}}">
    <!-- <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
    integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous"> -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">

		<script src="https://cdn.jsdelivr.net/npm/promise-polyfill"></script>
		<script src="https://cdn.jsdelivr.net/npm/fetch-polyfill"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.0/jquery.min.js"></script>
		
    <link rel="stylesheet" href="{{cumtomAsset('assets/css/style.css')}}">
    <link rel="stylesheet" href="{{cumtomAsset('assets/css/style-other.css')}}">
    <link href="
https://cdn.jsdelivr.net/npm/sweetalert2@11.15.5/dist/sweetalert2.min.css
" rel="stylesheet">


</head>
<body>

<section class="ftco-section login-wap">

    <div class="wrapper-page">
        <div class="login-wrap mt-3">
            <div class="mpos360-head">
                <h1>Bắt đầu nhận thanh toán</h1>
                <p>Để bắt đầu sử dụng loa TingBox và nhận thanh toán, vui lòng thực hiện lần lượt theo 3 bước dưới
                    đây</p>
            </div>
            <div class="mpos360-form_reg mb-4">
                <ul id="box-full" class=" hide-haft">
                    <li class="step-success">
                        <span class="mfr-numbe">1</span>
                        <span class="mfr-check"><img src="assets/img/icon-check.svg"></span>
                        <div class="ss-content">
                            <p>Khai báo cửa hàng <a href="" data-bs-toggle="modal" data-bs-target="#khaibao">Sửa</a>
                            </p>
                            <div class="box-acc">
                                <label>Tạp hóa Minh Phương phú đô tên dài</label>
                                <p>18 tam trinh, Quận Hai Bà Trưng, Hà nội</p>
                            </div>
                        </div>
                    </li>
                    <li class="step-success">
                        <span class="mfr-numbe">1</span>
                        <span class="mfr-check"><img src="assets/img/icon-check.svg"></span>
                        <div class="ss-content">
                            <p>Loa Tingbox <a href="">Thêm</a></p>
                            <div class="box-success mb-2">
                                <article>
                                    <label>TB02141110202</label>
                                    <p class="bs-warning">Đang kích hoạt</p>
                                </article>
                                <img src="assets/img/img-thietbi.svg">
                            </div>
                            <div class="box-success">
                                <article>
                                    <label>TB02141110202</label>
                                    <p class="bs-success">Đang kích hoạt</p>
                                </article>
                                <img src="assets/img/img-thietbi.svg">
                            </div>
                        </div>
                    </li>
                    <li class="step-success">
                        <span class="mfr-numbe">1</span>
                        <span class="mfr-check"><img src="assets/img/icon-check.svg"></span>
                        <div class="ss-acc">
                            <p>Tài khoản ngân hàng nhận tiền</p>
                            <div class="acc-show">
                                <label>**************</label>
                                <p>TRAN DUC ANH</p>
                                <p>TECHCOMBANK</p>
                            </div>
                        </div>
                    </li>
                </ul>

                <a id="showLess" class="btn btn-link m-auto w-100 text-center mt-3" href="javascript:;">Thu gọn</a>
                <a id="showMore" class="btn btn-link m-auto w-100 text-center mt-3" href="javascript:;">Xem thêm</a>
            </div>

            <a class="btn btn-link m-auto w-100 text-center my-3" href="javascript:;"> + Thêm cửa hàng</a>

            <div class="mpos360-form_reg">
                <ul>
                    <li class="active">
                        <span class="mfr-numbe">1</span>
                        <span class="mfr-check"><img src="assets/img/icon-check.svg"></span>
                        <a href="" data-bs-toggle="modal" data-bs-target="#khaibao"><img
                                    src="assets/img/favicon-shop.svg"> Khai báo cửa hàng</a>
                    </li>
                    <li class="">
                        <span class="mfr-number">2</span>
                        <span class="mfr-check"><img src="assets/img/icon-check.svg"></span>
                        <a href="" data-bs-toggle="modal" data-bs-target="#assi-speaker"><img
                                    src="assets/img/favicon-void.svg"> Gán loa TingBox</a>
                    </li>
                    <li>
                        <span class="mfr-number">3</span>
                        <span class="mfr-check"><img src="assets/img/icon-check.svg"></span>
                        <a href="" data-bs-toggle="modal" data-bs-target="#acc-receiving"><img
                                    src="assets/img/favicon-bank.svg"> Khai báo tài khoản nhận tiền</a>
                    </li>
                </ul>
            </div>
            <div class="mpos360-footer">
                <a href="#" class="text-center btn-blue w-100 d-block">Hoàn tất</a>
            </div>
        </div>
    </div>

</section>
<!-- Modal Thông tin cửa hàng -->
<div class="modal fade action-sheet" id="khaibao" tabindex="-1" role="dialog" style="display: none;">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                <h5>Thông tin cửa hàng</h5>
            </div>
            <div class="modal-body">

                <div class="mpos360-form">
                    <div class="notifi-item">
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control name_cua_hang" id="floatingInputValue" placeholder=""
                                   value="">
                            <label for="floatingInputValue">Tên cửa hàng</label>
                        </div>

                        <div class="form-floating mb-3">
                            <select class="form-select job" id="floatingSelect"
                                    aria-label="Floating label select example">
                                <option selected="" value="">-- Chọn ngành nghề --</option>
                                @foreach($dataNganhNgheNganHangThanhPho['industries'] as $dataNganhNghe)
                                    <option value="{{$dataNganhNghe['industryId']}}">{{$dataNganhNghe['industryName']}}</option>
                                @endforeach
                            </select>
                            <label for="floatingSelect">Ngành nghề kinh doanh</label>
                            <div class="error-message text-danger mt-1" id="error-job"></div>
                        </div>
                        <div class="form-floating mb-3">
                            <select class="form-select cities" id="floatingSelect "
                                    aria-label="Floating label select example "
                                    {{--                                        data-bs-toggle="modal" data-bs-target="#modal-select" data-bs-dismiss="modal"--}}
                            >
                                <option selected="" value="">-- Chọn tỉnh/Thành phố --</option>
                                @foreach($dataNganhNgheNganHangThanhPho['cities'] as $dataThanhPho)
                                    <option value="{{$dataThanhPho['cityId']}}">{{$dataThanhPho['cityName']}}</option>
                                @endforeach
                            </select>
                            <label for="floatingSelect">Tỉnh/Thành phố</label>
                            <div class="error-message text-danger mt-1" id="error-cities"></div>
                        </div>
                        <div class="form-floating mb-3">
                            <select class="form-select district" id="floatingSelect"
                                    aria-label="Floating label select example">
                                <option selected="" value="">-- Chọn quận/Huyện --</option>
                            </select>
                            <label for="floatingSelect">Quận/Huyện</label>
                            <div class="error-message text-danger mt-1" id="error-district"></div>
                        </div>

                        <div class="form-floating mb-3">
                            <input type="text" class="form-control address" id="floatingInputValue" placeholder=""
                                   value="">
                            <label for="floatingInputValue">Địa chỉ (không bắt buộc)</label>
                        </div>

                        <p class="mt-4 mb-0">
                            <a href="#" class="text-center btn-blue w-100 d-block submit_cua_hang">
                                Tiếp tục
                                <span class="spinner-border spinner-border-sm d-none" role="status"
                                      aria-hidden="true"></span>
                            </a>
                        </p>


                    </div>
                </div>

            </div>
        </div>
    </div>
</div>


<!-- Modal Thông tin cửa hàng -->
{{--    <div class="modal fade action-sheet" id="modal-select" tabindex="-1" role="dialog">--}}
{{--        <div class="modal-dialog" role="document">--}}
{{--            <div class="modal-content">--}}
{{--                <div class="modal-header">--}}
{{--                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"> </button>--}}
{{--                    <h5>Chọn Tỉnh/Thành phố</h5>--}}
{{--                </div>--}}
{{--                <div class="modal-body">--}}
{{--                    <div class="modal-search">--}}
{{--                        <span><img src="assets/img/icon-search.svg"></span><input type="" name=""--}}
{{--                            placeholder="Tìm kiếm">--}}
{{--                    </div>--}}
{{--                    <ul class="list-box">--}}
{{--                        <li>--}}
{{--                            <a href="" data-bs-toggle="modal" data-bs-target="#khaibao" >--}}
{{--                                An Giang <span><img src="assets/img/icon-check.svg"></span>--}}
{{--                            </a>--}}
{{--                        </li>--}}
{{--                        <li>--}}
{{--                            <a href="" data-bs-toggle="modal" data-bs-target="#khaibao" >--}}
{{--                                Bà Rịa - Vũng Tàu--}}
{{--                            </a>--}}
{{--                        </li>--}}
{{--                        <li>--}}
{{--                            <a href="" data-bs-toggle="modal" data-bs-target="#khaibao" >--}}
{{--                                Bắc Giang--}}
{{--                            </a>--}}
{{--                        </li>--}}
{{--                        <li>--}}
{{--                            <a href="" data-bs-toggle="modal" data-bs-target="#khaibao" >--}}
{{--                                Bắc Kạn--}}
{{--                            </a>--}}
{{--                        </li>--}}
{{--                        <li>--}}
{{--                            <a href="" data-bs-toggle="modal" data-bs-target="#khaibao" >--}}
{{--                                Bạc Liêu--}}
{{--                            </a>--}}
{{--                        </li>--}}
{{--                        <li>--}}
{{--                            <a href="" data-bs-toggle="modal" data-bs-target="#khaibao" >--}}
{{--                                Bắc Ninh--}}
{{--                            </a>--}}
{{--                        </li>--}}
{{--                        <li>--}}
{{--                            <a href="" data-bs-toggle="modal" data-bs-target="#khaibao" >--}}
{{--                                Bến Tre--}}
{{--                            </a>--}}
{{--                        </li>--}}
{{--                        <li>--}}
{{--                            <a href="" data-bs-toggle="modal" data-bs-target="#khaibao" >--}}
{{--                                Bình Định--}}
{{--                            </a>--}}
{{--                        </li>--}}
{{--                        <li>--}}
{{--                            <a href="" data-bs-toggle="modal" data-bs-target="#khaibao">--}}
{{--                                Bình Dương--}}
{{--                            </a>--}}
{{--                        </li>--}}
{{--                    </ul>--}}

{{--                </div>--}}
{{--            </div>--}}
{{--        </div>--}}
{{--    </div>--}}

<!-- Gán loa TingBox  -->
<div class="modal" id="assi-speaker" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Gán loa TingBox</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="text-center"><a href="#">Hướng dẫn lấy mã thiết bị</a></p>
                {{--                <div class="mpos360-scan">--}}
                {{--                    <a href="" class="ms-qr">--}}
                {{--                        <span><img src="{{cumtomAsset('assets/img/qr-code.svg')}}"></span>--}}
                {{--                        <p>Quét mã QR Serial trên thiết bị TingBox</p>--}}
                {{--                    </a>--}}
                {{--                </div>--}}
                {{--                <p class="cl890 text-center mt-3">Hoặc</p>--}}
                <div class="form-floating mb-3">
                    <input type="text" class="form-control serialId" id="floatingtk" placeholder=""
                           value="">
                    <label for="floatingtk">Nhập mã Serial thiết bị TingBox</label>
                    <div class="error-message text-danger mt-1" id="error-serialId"></div>
                </div>

                <p class="mt-4 mb-0"><a href="#" class="text-center btn-blue w-100 d-block submit_ting_box">
                        Gán thiết bị
                        <span class="spinner-border spinner-border-sm d-none" role="status"
                              aria-hidden="true"></span>
                    </a>
                </p>
            </div>

        </div>
    </div>
</div>

<!-- Kích hoạt thành công  -->
<div class="modal fade" id="active-succs" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">

            <div class="modal-body">
                <p class="text-center"><img src="assets/img/icon-success.svg"></p>
                <p class="text-center f16">Loa TingBox của Quý khách đã được kích hoạt thành công. Để bắt đầu nhận thanh
                    toán,
                    vui
                    lòng bật nguồn thiết bị và kết nối loa TingBox với mạng Internet. Sau khi hoàn tất, Quý khách có thể
                    ngay
                    lập tức sử dụng loa TingBox để nhận thanh toán một cách tiện lợi và hiệu quả.</p>


                <p class="mt-4 mb-0">
                    <a href="#" class="text-center btn-blue w-100 d-block reload_tingbox">OK</a>
                </p>
            </div>

        </div>
    </div>
</div>

<!-- Không thể kích hoạt  -->
<div class="modal fade" id="cannot-active" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">

            <div class="modal-body">
                <p class="text-center"><img src="assets/img/waring.svg"></p>
                <p class="text-center f16">Thiết bị TingBox <span class="BE-bold"> TB02141110202 </span>không thể kích
                    hoạt do
                    không đáp ứng điều kiện hoặc
                    đã thuộc đơn vị khác. Vui lòng kiểm tra lại mã thiết bị hoặc liên hệ CSKH để được hỗ trợ.</p>


                <p class="mt-4 mb-0"><a href="#" class="text-center btn-blue w-100 d-block"
                                        data-bs-dismiss="modal">OK</a></p>
            </div>

        </div>
    </div>
</div>

<!--Tài khoản NH nhận tiền  -->
<div class="modal fade" id="acc-receiving" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Tài khoản NH nhận tiền</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="text-center">Để đảm bảo tính hợp lệ Họ tên Chủ tài khoản ngân hàng phải khớp với Họ tên
                    trên căn
                    cước/CCCD đã đăng ký mở tài khoản.</p>
                <div class="mpos360-scan">
                    {{--                        <a href="" class="ms-qr">--}}
                    {{--                            <span><img src="assets/img/qr-code.svg"></span>--}}
                    {{--                            <p>Quét mã QR tài khoản ngân hàng</p>--}}
                    {{--                        </a>--}}
                    {{--                        <p class="cl890 text-center mt-4 mb-4">Hoặc</p>--}}
                    <a href="mPOS360-QuetQR_nhaptay.html" data-bs-toggle="modal" data-bs-target="#acc-receiving1"
                       data-bs-dismiss="modal" class="mpos360-btn w-100">Nhập tay</a>
                </div>
            </div>
        </div>
    </div>
</div>

<!--Tài khoản NH nhận tiền 1  -->
<div class="modal fade" id="acc-receiving1" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Tài khoản NH nhận tiền <a href=""
                                                                                         class="float-right"><img
                                src="assets/img/icon-qr.svg"></a></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="text-center">Để đảm bảo tính hợp lệ Họ tên Chủ tài khoản ngân hàng phải khớp với Họ tên
                    trên căn
                    cước/CCCD đã đăng ký mở tài khoản.</p>
                <div class="notifi-item">
                    <div class="form-floating mb-3">
                        <input type="text" class="form-control bankHolder" id="floatingInputValue" placeholder="" value="">
                        <label for="floatingInputValue">Tên chủ tài khoản</label>
                         <div class="error-message text-danger mt-1" id="error-bankHolder"></div>
                    </div>
                    <div class="form-floating mb-3">
                        <input type="text" class="form-control bankNumber" id="floatingInputValue" placeholder=""
                               value="">
                        <label for="floatingInputValue">Số tài khoản</label>
                         <div class="error-message text-danger mt-1" id="error-bankNumber"></div>
                    </div>

                    <div class="form-floating mb-3">
                        <select class="form-select floatingSelectBank bankName" id="floatingSelectBank"
                                aria-label="Floating label select example" data-bs-toggle="modal"
                                data-bs-target="#list-all-bank" data-bs-dismiss="modal">
                            <option value="">--Chọn ngân hàng--</option>
                        </select>
                        <label for="floatingSelect">Tên ngân hàng</label>
                         <div class="error-message text-danger mt-1" id="error-bankName"></div>
                    </div>

                    <div class="d-flex mt-5">
                        <a href="#" class="text-center btn-blue w-100 submit_check_ngan_hang">Tiếp tục</a>
                    </div>
{{--                    <div class="d-flex mt-5">--}}
{{--                        <a href="#" class="text-center btn-blue w-100" data-bs-toggle="modal"--}}
{{--                           data-bs-target="#acc-receiving2" data-bs-dismiss="modal">Đang xử lý...</a>--}}
{{--                        <a href="#" class="text-center btn-blue w-100" data-bs-toggle="modal"--}}
{{--                           data-bs-target="#acc-receiving3" data-bs-dismiss="modal">Đang xử lý...</a>--}}
{{--                    </div>--}}
                </div>
            </div>
        </div>
    </div>
</div>

<!--Tài khoản NH nhận tiền 2 -->
<div class="modal fade" id="acc-receiving2" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Tài khoản NH nhận tiền </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex align-items-start mb-3">
                    <img src="{{cumtomAsset('assets/img/small-succs.svg')}}" class="me-2">
                    <div class="cleaxfjx">
                        <span class="d-block f16 BE-bold cl1DA">Số tài khoản hợp lệ</span>
                        <span class="cl890">Để tránh sai sót khi nhận tiền, Vui lòng kiểm tra lại thông tin trước
                                khi tiếp
                                tục!</span>
                    </div>
                </div>
                <div class="box-edit">
                    <div class="info">
                        <span class="f20 BE-bold save_bank_number"></span>
                        <span class="text-uppercase BE-bold mb-1 save_bank_holder"></span>
                        <span class="cl890 save_bank_name" ></span>
                        <span class="cl890 save_bank_id" hidden></span>
                        <span class="cl890 verify" hidden>1</span>
                    </div>
                    <a href="#" class="f12">Sửa</a>
                </div>

               <div class="d-flex mt-5">
                    <a href="#" class="text-center btn-blue w-100 submit_ngan_hang" data-bs-toggle="modal"
                       data-bs-target="#linkbank-succ" data-bs-dismiss="modal" >Lưu thông tin</a>
                </div>

            </div>

        </div>
    </div>
</div>

<!--Tài khoản NH nhận tiền 3 -->
<div class="modal fade" id="acc-receiving3" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Tài khoản NH nhận tiền </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex align-items-start mb-3">
                    <img src="{{cumtomAsset('assets/img/small-waring.svg')}}" class="me-2">
                    <div class="cleaxfjx">
                            <span class="clE99">Số tài khoản chưa được xác minh bởi ngân hàng. Vui lòng kiểm tra lại
                                trước khi lưu
                                thông tin !</span>
                    </div>
                </div>
                <div class="box-edit">
                    <div class="info">
                         <span class="f20 BE-bold save_bank_number"></span>
                        <span class="text-uppercase BE-bold mb-1 save_bank_holder"></span>
                        <span class="cl890 save_bank_name" ></span>
                        <span class="cl890 save_bank_id" hidden></span>
                        <span class="cl890 verify" hidden>2</span>

                    </div>
                    <a href="#" class="f12">Sửa</a>
                </div>

                <div class="d-flex mt-5">
                    <a href="#" class="text-center btn-blue w-100 submit_ngan_hang" data-bs-toggle="modal"
                       data-bs-target="#linkbank-succ" data-bs-dismiss="modal" >Lưu thông tin</a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Popup: liên kết tài khoản NH thành công  -->
<div class="modal fade" id="linkbank-succ" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">

            <div class="modal-body">
                <p class="text-center"><img src="assets/img/icon-success.svg"></p>
                <p class="text-center f16">Tài khoản ngân hàng đã được liên kết thành công. mPOS sẽ sử dụng để
                    chuyển tiền từ các giao dịch hàng ngày vào tài khoản này.</p>
                <p class="mt-4 mb-0"><a href="#" class="text-center btn-blue w-100 d-block">OK</a></p>
            </div>

        </div>
    </div>
</div>

<!-- Modal Chọn ngân hàng -->
<div class="modal fade action-sheet" id="list-all-bank" tabindex="-1" role="dialog" style="display: none;">
    <div class="modal-dialog modal-dialog-scrollable" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                <h5>Chọn ngân hàng</h5>
            </div>
            <div class="modal-body">
                <div class="modal-search">
                    <span><img src="{{cumtomAsset('assets/img/icon-search.svg')}}"></span><input type="" name=""
                                                                                                 placeholder="Tìm kiếm">
                </div>
                <ul class="list-bank">
{{--                    <li>--}}
{{--                        <a href="" data-bs-toggle="modal" data-bs-target="#acc-receiving1">--}}
{{--                            <figure><img src="assets/img/favicon-vietcombank.svg"></figure>--}}
{{--                            <article>--}}
{{--                                <label>Vietcombank</label>--}}
{{--                                <p>NH TMCP Ngoai Thuong VN</p>--}}
{{--                            </article>--}}
{{--                        </a>--}}
{{--                    </li>--}}
{{--                    <li>--}}
{{--                        <a href="" data-bs-toggle="modal" data-bs-target="#acc-receiving1">--}}
{{--                            <figure><img src="assets/img/favicon-mbbank.svg"></figure>--}}
{{--                            <article>--}}
{{--                                <label>MBBank</label>--}}
{{--                                <p>NH TMCP Quan Doi</p>--}}
{{--                            </article>--}}
{{--                        </a>--}}
{{--                    </li>--}}
{{--                    <li>--}}
{{--                        <a href="" data-bs-toggle="modal" data-bs-target="#acc-receiving1">--}}
{{--                            <figure><img src="assets/img/favicon-vietcombank.svg"></figure>--}}
{{--                            <article>--}}
{{--                                <label>Vietcombank</label>--}}
{{--                                <p>NH TMCP Ngoai Thuong VN</p>--}}
{{--                            </article>--}}
{{--                        </a>--}}
{{--                    </li>--}}
{{--                    <li>--}}
{{--                        <a href="" data-bs-toggle="modal" data-bs-target="#acc-receiving1">--}}
{{--                            <figure><img src="assets/img/favicon-mbbank.svg"></figure>--}}
{{--                            <article>--}}
{{--                                <label>MBBank</label>--}}
{{--                                <p>NH TMCP Quan Doi</p>--}}
{{--                            </article>--}}
{{--                        </a>--}}
{{--                    </li>--}}
{{--                    <li>--}}
{{--                        <a href="" data-bs-toggle="modal" data-bs-target="#acc-receiving1">--}}
{{--                            <figure><img src="assets/img/favicon-vietcombank.svg"></figure>--}}
{{--                            <article>--}}
{{--                                <label>Vietcombank</label>--}}
{{--                                <p>NH TMCP Ngoai Thuong VN</p>--}}
{{--                            </article>--}}
{{--                        </a>--}}
{{--                    </li>--}}
{{--                    <li>--}}
{{--                        <a href="" data-bs-toggle="modal" data-bs-target="#acc-receiving1">--}}
{{--                            <figure><img src="assets/img/favicon-mbbank.svg"></figure>--}}
{{--                            <article>--}}
{{--                                <label>MBBank</label>--}}
{{--                                <p>NH TMCP Quan Doi</p>--}}
{{--                            </article>--}}
{{--                        </a>--}}
{{--                    </li>--}}
                </ul>

            </div>
        </div>
    </div>
</div>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.0/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.min.js"></script>
<script src="{{cumtomAsset('assets/js/style.js')}}"></script>
<script src="
https://cdn.jsdelivr.net/npm/sweetalert2@11.15.5/dist/sweetalert2.all.min.js
"></script>
<script type="text/javascript">
    $(document).ready(function () {
        // Khi nhấp vào nút "Xem thêm"
        $('#showMore').on('click', function () {
            $('#box-full').addClass('hide-full').removeClass('hide-haft'); // Thêm class 'active'
            $('#showMore').hide(); // Ẩn nút "Xem thêm"
            $('#showLess').show(); // Hiển thị nút "Thu gọn"
        });

        // Khi nhấp vào nút "Thu gọn"
        $('#showLess').on('click', function () {
            $('#box-full').removeClass('hide-full').addClass('hide-haft'); // Thêm class 'inactive'
            $('#showLess').hide(); // Ẩn nút "Thu gọn"
            $('#showMore').show(); // Hiển thị nút "Xem thêm"
        });

        // Khởi tạo: ẩn nút "Thu gọn"
        $('#showLess').hide();
    });
</script>
<script>
    $(document).ready(function () {
        const modal1 = document.getElementById('modal1');
        modal1.addEventListener('hidden.bs.modal', function () {
            console.log('Modal 1 đã đóng');
        });

    });
</script>
<script>


    $(document).ready(function () {
        $('#active-succs').modal({
            backdrop: 'static', // Prevent closing by clicking outside the modal
            keyboard: false,     // Prevent closing with the "Escape" key
            // focus: true
        });
        $('#cannot-active').modal({
            backdrop: 'static', // Prevent closing by clicking outside the modal
            keyboard: false     // Prevent closing with the "Escape" key
        });


        $('.cities').on('change', function () {
            let selectedValue = $(this).val();
            let optionSelect2 = $('.district');

            // Gọi AJAX
            $.ajax({
                url: "{{ route('getQuanHuyenTuMaTinhThanh') }}",
                type: 'GET',
                data: {
                    id_city: selectedValue,
                },
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function (response) {
                    optionSelect2.html(response.data); // Gán dữ liệu trả về vào input
                },
                error: function () {
                    alert('Có lỗi xảy ra. Vui lòng thử lại.');
                }
            });
        });
    });
</script>
<script type="text/javascript">

    $(document).ready(function () {
        // Khi nhấp vào nút "Xem thêm"
        $('#showMore').on('click', function () {
            $('#box-full').addClass('hide-full').removeClass('hide-haft'); // Thêm class 'active'
            $('#showMore').hide(); // Ẩn nút "Xem thêm"
            $('#showLess').show(); // Hiển thị nút "Thu gọn"
        });

        // Khi nhấp vào nút "Thu gọn"
        $('#showLess').on('click', function () {
            $('#box-full').removeClass('hide-full').addClass('hide-haft'); // Thêm class 'inactive'
            $('#showLess').hide(); // Ẩn nút "Thu gọn"
            $('#showMore').show(); // Hiển thị nút "Xem thêm"
        });

        // Khởi tạo: ẩn nút "Thu gọn"
        $('#showLess').hide();
    });


    $(document).ready(function () {

        const $job = $(".job");
        const $errorJob = $("#error-job");

        function validateJob() {
            const job = $job.val().trim();
            if (job === "") {
                $errorJob.text("Vui lòng chọn ngành nghề");
                $job.addClass("is-invalid");
            } else {
                $errorJob.text("");
                $job.removeClass("is-invalid");
            }
        }

        const $cities = $(".cities");
        const $errorCities = $("#error-cities");

        function validateCities() {
            const cities = $cities.val().trim();
            if (cities === "") {
                $errorCities.text("Vui lòng chọn tỉnh/ thành phố");
                $cities.addClass("is-invalid");
            } else {
                $errorCities.text("");
                $cities.removeClass("is-invalid");
            }
        }

        const $district = $(".district");
        const $errorDistrict = $("#error-district");

        function validateDistrict() {
            const district = $district.val().trim();
            if (district === "") {
                $errorDistrict.text("Vui lòng chọn quận/huyện");
                $district.addClass("is-invalid");
            } else {
                $errorDistrict.text("");
                $district.removeClass("is-invalid");
            }
        }

        const $serialId = $('.serialId');
        const $errorSerialId = $('#error-serialId');

        function validateSerial() {
            const serialId = $serialId.val().trim();
            if (serialId === "") {
                $errorSerialId.text("Mã QR không hợp lệ hoặc không phải mã thiết bị TingBox");
                $serialId.addClass("is-invalid");
            } else {
                $errorSerialId.text("");
                $serialId.removeClass("is-invalid");
            }
        }


        $('.submit_cua_hang').on('click', function (e) {
            e.preventDefault();

            validateJob();
            validateCities();
            validateDistrict();

            if (
                $errorJob.text() === "" &&
                $errorCities.text() === "" &&
                $errorDistrict.text() === ""
            ) {
                const $buttonCuaHang = $(this);

                // Disable the button and show spinner
                $buttonCuaHang.addClass('disabled');
                $buttonCuaHang.find('.button-text').addClass('d-none'); // Hide button text
                $buttonCuaHang.find('.spinner-border').removeClass('d-none'); // Show spinner
                // Gather form data
                let nameCuaHang = $('.name_cua_hang').val();
                let nganhNghe = $('.job').val();
                let city = $('.cities').val();
                let district = $('.district').val();
                let address = $('.address').val();

                // Validate the inputs (optional)
                // if (!nameCuaHang || !nganhNghe || !city || !district) {
                //     alert('Vui lòng nhập đầy đủ thông tin!');
                //     return;
                // }

                // AJAX request
                $.ajax({
                    url: "{{route('TBMpos360SubmitThongTinCuaHang')}}", // Replace with your backend route
                    method: 'POST',
                    data: JSON.stringify({
                        data: {
                            ten_cua_hang: nameCuaHang,
                            nganh_nghe_kinh_doanh: nganhNghe,
                            tinh_thanh_pho: city,
                            quan_huyen: district,
                            dia_chi: address
                        }
                    }),
                    success: function (response) {
                        if (response.code == 1000) {
                            // $("#myModalAjax").modal('show')
                            // .find("#myModalContent")
                            // .load($(this).attr('value'));
                            alert('Thành công');
                            window.location.reload();
                        } else {
                            alert(response.message);
                        }
                    },
                    error: function (error) {
                        alert("Có lỗi xảy ra, Hãy thông báo kỹ thuật");
                    },

                    complete: function () {
                        // Re-enable the button and hide spinner after the request
                        $buttonCuaHang.removeClass('disabled');
                        $buttonCuaHang.find('.button-text').removeClass('d-none'); // Show button text
                        $buttonCuaHang.find('.spinner-border').addClass('d-none'); // Hide spinner
                    }
                    // Handle error response
                });
            }
        });

        $('.submit_ting_box').on('click', function (e) {
            e.preventDefault();
            validateSerial();
            if ($errorSerialId.text() === "") {
                let serialId = $('.serialId').val();
                const $buttonSerial = $(this);

                // Disable the button and show spinner
                $buttonSerial.addClass('disabled');
                $buttonSerial.find('.button-text').addClass('d-none'); // Hide button text
                $buttonSerial.find('.spinner-border').removeClass('d-none'); // Show spinner
                $.ajax({
                    url: "{{route('TBMpos360SubmitLoaTingBox')}}", // Replace with your backend route
                    method: 'POST',
                    data: JSON.stringify({
                        data: {
                            serialId: serialId,
                        }
                    }),
                    success: function (response) {
                        if (response.code == 1000) {

                            $('#active-succs').modal('show');
                            $('.reload_tingbox').on('click', function (e) {
                                e.preventDefault(); // Prevent default anchor behavior
                                window.location.reload(); // Reload the current page
                            });

                        } else {
                            $('#cannot-active').modal('show');
                        }
                    },
                    error: function (error) {
                        alert("Có lỗi xảy ra, Hãy thông báo kỹ thuật");
                    },

                    complete: function () {
                        // Re-enable the button and hide spinner after the request
                        $buttonSerial.removeClass('disabled');
                        $buttonSerial.find('.button-text').removeClass('d-none'); // Show button text
                        $buttonSerial.find('.spinner-border').addClass('d-none'); // Hide spinner
                    }
                    // Handle error response
                });

            }

        })

        if ($('#list-all-bank').length > 0) {
            // Trigger the AJAX call when the modal is shown
            $('#list-all-bank').on('show.bs.modal', function () {
                $.ajax({
                    url: "{{route('getAllBank')}}", // Laravel route URL
                    method: 'GET', // Change to POST if necessary
                    success: function (response) {
                        // Assume the response is an array of banks
                        const bankList = response.data; // Adjust as per your response structure
                        const bankListContainer = $('.list-bank'); // Target the modal body

                        // Clear previous content
                        bankListContainer.empty();

                        // Populate the modal with bank data
                        bankListContainer.html(bankList);
                    },
                    error: function () {
                        alert('Failed to fetch bank data. Please try again later.');
                    }
                });
            });
        }

        $('.modal-search input').on('input', function () {
            const searchTerm = $(this).val().toLowerCase();

            $('.list-bank li').each(function () {
                const bankName = $(this).find('p').text().toLowerCase();

                if (bankName.includes(searchTerm)) {
                    $(this).show(); // Show matching items
                } else {
                    $(this).hide(); // Hide non-matching items
                }
            });
        });

        $(document).on('click', '.click_bank', function (e) {
            e.preventDefault();

            // Debugging to ensure this is executed
            console.log('Bank clicked!');

            // Get the data from the clicked item
            const bankValue = $(this).data('id');
            const bankLabel = $(this).data('label');
            const bankVimo = $(this).data('bankvimo');

            console.log('Bank Value:', bankValue);
            console.log('Bank Label:', bankLabel);
            console.log('Bank Vimo:', bankVimo);

            if (bankValue && bankLabel) {
                // Find the select element
                const $select = $('#floatingSelectBank'); // Ensure this ID matches your dropdown

                // Update the select dropdown
                $select.empty(); // Remove all existing options
                $select.append(`<option selected value="${bankValue}-${bankVimo}">${bankLabel}</option>`);

                // Close the modal
                $('#list-all-bank').modal('hide');
            } else {
                console.error('Bank data missing! Check the data attributes in the HTML.');
            }
        });

        const $bankHolder = $('.bankHolder');


        // Automatically convert to uppercase on input
        $bankHolder.on('input', function () {
            const value = $(this).val();
            $(this).val(value.toUpperCase()); // Convert to uppercase
        });


        const $bankNumber = $('.bankNumber');
        const $bankName = $('.bankName');
        const $errorBankHolder = $("#error-bankHolder");
        const $errorBankNumber = $("#error-bankNumber");
        const $errorBankName = $("#error-bankName");

        function validateBankHolder() {
            const bankHolder = $bankHolder.val().trim();
            if (bankHolder === "") {
                $errorBankHolder.text("Tên chủ tài khoản không hợp lệ");
                $bankHolder.addClass("is-invalid");
            } else {
                $errorBankHolder.text("");
                $bankHolder.removeClass("is-invalid");
            }
        }
        function validateBankNumber() {
            const bankNumber = $bankNumber.val().trim();
            if (bankNumber === "") {
                $errorBankNumber.text("Số tài khoản không hợp lệ");
                $bankNumber.addClass("is-invalid");
            } else {
                $errorBankNumber.text("");
                $bankNumber.removeClass("is-invalid");
            }
        }
        function validateBankName() {
            const bankName = $bankName.val().trim();
            if (bankName === "") {
                $errorBankName.text("Tên ngân hàng không hợp lệ");
                $bankName.addClass("is-invalid");
            } else {
                $errorBankName.text("");
                $bankName.removeClass("is-invalid");
            }
        }


        $('.submit_check_ngan_hang').on('click', function (e) {
            e.preventDefault();

            validateBankHolder();
            validateBankNumber();
            validateBankName();

            if (
                $errorBankName.text() === "" &&
                $errorBankNumber.text() === "" &&
                $errorBankHolder.text() === ""
            ) {
                const $buttonBank = $(this);

                // Disable the button and show spinner
                $buttonBank.addClass('disabled');
                $buttonBank.text('Đang xử lý');
                $buttonBank.find('.button-text').addClass('d-none'); // Hide button text
                $buttonBank.find('.spinner-border').removeClass('d-none'); // Show spinner
                // // Gather form data
                let bankName = $('.bankName').val();
                let bankHolder = $('.bankHolder').val();
                let bankNumber = $('.bankNumber').val();
                let bank = bankName.split('-');
                let label = $('.click_bank').data('label');


                // AJAX request
                $.ajax({
                    url: "{{route('TBMpos360CheckThongTinTaiKhoanNhanTien')}}", // Replace with your backend route
                    method: 'POST',
                    data: JSON.stringify({
                        data: {
                            ten_ngan_hang: bankName,
                            so_tai_khoan: bankNumber,
                            ten_tai_khoan: bankHolder,
                        }
                    }),
                    success: function (response) {
                        if (response.status == 'STK_HOP_LE') {
                            $('#acc-receiving2').modal('show');
                            $('.save_bank_number').text(bankNumber);
                            $('.save_bank_holder').text(bankHolder);
                            $('.save_bank_name').text(label);
                            $('.save_bank_id').text(bank[0]);

                        }else{
                            $('#acc-receiving3').modal('show');
                            $('.save_bank_number').text(bankNumber);
                            $('.save_bank_holder').text(bankHolder);
                            $('.save_bank_name').text(label);
                            $('.save_bank_id').text(bank[0]);
                        }


                    },
                    error: function (error) {
                        alert("Có lỗi xảy ra, Hãy thông báo kỹ thuật");
                    },

                    complete: function () {
                        // Re-enable the button and hide spinner after the request
                        $buttonBank.removeClass('disabled');
                        $buttonBank.text('Tiếp tục');
                        $buttonBank.find('.button-text').removeClass('d-none'); // Show button text
                        $buttonBank.find('.spinner-border').addClass('d-none'); // Hide spinner
                    }
                    // Handle error response
                });
            }
        });




        $('.submit_ngan_hang').on('click', function (e) {
            e.preventDefault();

                const $buttonSubmitBank = $(this);

                // Disable the button and show spinner
                // $buttonSubmitBank.addClass('disabled');
                // $buttonSubmitBank.text('Đang xử lý');
                // $buttonSubmitBank.find('.button-text').addClass('d-none'); // Hide button text
                // $buttonSubmitBank.find('.spinner-border').removeClass('d-none'); // Show spinner
                // // Gather form data

                let save_bank_id = $('.save_bank_id').text();
                let save_bank_holder = $('.save_bank_holder').text();
                let save_bank_number = $('.save_bank_number').text();
                let verify = $('.verify').text();
                console.log(save_bank_id)
                console.log(save_bank_holder)
                console.log(save_bank_number)
                console.log(verify)

                // AJAX request
                $.ajax({
                    url: "{{route('TBMpos360SubmitTaiKhoanNhanTien')}}", // Replace with your backend route
                    method: 'POST',
                    data: JSON.stringify({
                        data: {
                            id_ngan_hang: save_bank_id,
                            so_tai_khoan: save_bank_number,
                            ten_tai_khoan: save_bank_holder,
                            verify: verify,
                        }
                    }),
                    success: function (response) {
                       if(response.code == 1000){

                       }else{

                       }

                    },
                    error: function (error) {
                        alert("Có lỗi xảy ra, Hãy thông báo kỹ thuật");
                    },

                    complete: function () {
                        // Re-enable the button and hide spinner after the request
                        // $buttonSubmitBank.removeClass('disabled');
                        // $buttonSubmitBank.text('Tiếp tục');
                        // $buttonSubmitBank.find('.button-text').removeClass('d-none'); // Show button text
                        // $buttonSubmitBank.find('.spinner-border').addClass('d-none'); // Hide spinner
                    }
                    // Handle error response
                });

        });


    });

</script>
</body>

</html>