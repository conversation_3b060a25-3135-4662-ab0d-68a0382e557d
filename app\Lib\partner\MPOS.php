<?php

namespace App\Lib\partner;

use Exception;
use App\Lib\Helper;
use GuzzleHttp\Client;
use Illuminate\Support\Str;
use App\Lib\TelegramAlertWarning;
use App\Exceptions\BusinessException;
use GuzzleHttp\Exception\RequestException;

class MPOS
{
    public $baseUrl;
    private $client;
    private $lang = 'vi';
    private $keyMakeCheckSum;

    public function __construct()
    {
        $this->baseUrl = env('API_PARTNER_MPOS_URL');
        $this->keyMakeCheckSum = env('API_PARTNER_MPOS_TRANS_KEY_MAKE_CHECKSUM');
        $this->client = new Client(['base_uri' => $this->baseUrl]);
    }
    /*
DỮ liệu test
"deviceIdentifier": "", //device tren app bắt buộc
  "os":"Android", // thiết bị  app bắt buộc
  "language":"vi", //ngôn ngữ string vi en
  "email": "<EMAIL>", //email đăng nhập app bắt buộc
  "password": "123456", //MK app bắt buộc
  "bundle": "" // id của app đăng ký với MPOS app bắt buộc
*/
    public function login($params)
    {
        $inputs = [
            "serviceName" => 'GATEWAY_MERCHANT_LOGIN',
        ];
        $accessParams = ['bundle', 'language', 'deviceIdentifier', 'os', 'email', 'password'];
        foreach ($accessParams as $key => $value) {
            if (isset($params[$value])) {
                $inputs[$value] = $params[$value];
            }
        }
        $response = $this->call('POST', '/gateway', $inputs);
        return $this->returnData($response);
    }

		public function loginWithMobileUser($params)
    {
			$inputs = [
					"serviceName" => 'GATEWAY_MERCHANT_LOGIN',
			];
			$accessParams = ['bundle', 'language', 'deviceIdentifier', 'os', 'username', 'password'];
			foreach ($accessParams as $key => $value) {
					if (isset($params[$value])) {
							$inputs[$value] = $params[$value];
					}
			}
			$response = $this->callCurl('POST', '/mc360/no-merchant', $inputs, false);
			return $this->returnData($response);
    }

    public function getNotifyCategory($params)
    {
        $inputs = [
            // "serviceName" => 'GET_TRANS_DAY_WITHDRAW_MASTER',
            // "saleId" => isset($params['sale_id']) ? (int) $params['sale_id'] : 0,
            // "dateFrom" => isset($params['from_date']) ? $params['from_date'] : ''
        ];
        $response = $this->callGetNoFormat('GET', '/topic/list-category', $inputs);
        return $this->returnData($response);
    }
    public function countTransSumByTime($params)
    {
        $inputs = [
            "serviceName" => 'DAILY_TRANSACTION_SUMMARY',
        ];
        $accessParams = ['merchantFk', 'language', 'startDate', 'endDate', 'txid','tokenLogin'];
        foreach ($accessParams as $key => $value) {
            if (isset($params[$value])) {
                $inputs[$value] = $params[$value];
            }
        }
        // $inputs['merchantFk'] = 'fdkslfklsdlfd';
        $response = $this->callCurl('POST', '/mc360', $inputs);
        return $this->returnData($response);
    }
    public function getTrans($params)
    {
        $inputs = [
            "serviceName" => 'STATISTIC_TRANSACTION',
        ];
        $accessParams = ['startDate', 'language', 'endDate', 'merchantFk','tokenLogin','typeTransaction','pageIndex','pageSize','withdrawStatus','statusTransaction','paymentMethod','installmentSendStatus','quickWithdrawStatus'];
        foreach ($accessParams as $key => $value) {
            if (isset($params[$value])) {
                $inputs[$value] = $params[$value];
                if($value == 'statusTransaction' ) {
                    $inputs[$value] = (int)$params[$value];
                }
            }
        }
        if(isset($inputs['pageIndex']) && $inputs['pageIndex'] < 1) {
            $inputs['pageIndex'] = 1;
        }
        // var_dump($inputs);
        // die();
        $response = $this->callCurl('POST', '/mc360', $inputs);
        return $this->returnData($response);
    }
    public function CHECK_READER_DEVICE_OF_MERCHANT($params)
    {
        $inputs = [
            "serviceName" => 'CHECK_READER_DEVICE_OF_MERCHANT',
        ];
        $accessParams = ['language', 'merchantFk','tokenLogin','readerSerial'];
        foreach ($accessParams as $key => $value) {
            if (isset($params[$value])) {
                $inputs[$value] = $params[$value];
            }
        }
        $response = $this->callCurl('POST', '/mc360', $inputs);
        
        return $this->returnData($response);
    }
    public function getTransDetail($params = [])
    {
        $inputs = [
            "serviceName" => 'STATISTIC_TRANSACTION',
        ];
        $accessParams = ['merchantFk', 'language', 'id','tokenLogin','typeTransaction', 'txid'];
        foreach ($accessParams as $key => $value) {
            if (isset($params[$value])) {
                $inputs[$value] = $params[$value];
            }
        }
        // var_dump($inputs);
        $response = $this->callCurl('POST', '/mc360', $inputs);
        return $this->returnData($response);
    }
    public function changePassword($params = [])
    {
        $inputs = [
            "serviceName" => 'CHANGE_PASSWORDS',
        ];
        $accessParams = ['merchantFk', 'language', 'currentPass','newPass','confirmPass','tokenLogin'];
        foreach ($accessParams as $key => $value) {
            if (isset($params[$value])) {
                $inputs[$value] = $params[$value];
            }
        }
        $response = $this->callCurl('POST', '/mc360', $inputs);
        return $this->returnData($response);
    }
    private function call($method, $endpoint, $data = [])
    {
        $options = [
            'headers' => [
                'Content-Type' => 'application/json',
                // 'Authorization' => 'Bearer ' . $this->authenKey,
            ],
        ];

        if (!empty($data)) {
            $options['json'] = $data;
        }
        try {
            $response = $this->client->request($method, $endpoint, $options);
            $data = json_decode($response->getBody(), true);
            if (isset($data['error']['code'])) {
                return $data;
            } else {
                return [
                    'error' => [
                        'code' => 'ERORR FORMAT RETURN',
                        'message' => 'Lỗi format cấu trúc'
                    ]
                ];
            }
        } catch (RequestException $e) {
			TelegramAlertWarning::sendMessage(Helper::traceError($e));
            return [
                'error' => [
                    'code' => 'ERROR NOTFOUD',
                    'message' => 'Lỗi không xác định',

                ]
            ];
        }
    }
    private function callGetNoFormat($method, $endpoint, $data = [])
    {
        $options = [
            'headers' => [
                'Content-Type' => 'application/json',
                // 'Authorization' => 'Bearer ' . $this->authenKey,
            ],
        ];

        if (!empty($data)) {
            $options['json'] = $data;
        }
        try {
            $response = $this->client->request($method, $endpoint, $options);
            $data = json_decode($response->getBody(), true);
            $data['error'] = [
                'code' => 1000,
                'message' => 'success'
            ];
            // $data['code']=1000;
            return $data;
        } catch (RequestException $e) {
			TelegramAlertWarning::sendMessage(Helper::traceError($e));
            return [
                'error' => [
                    'code' => 'ERROR NOTFOUD',
                    'message' => 'Lỗi không xác định',
                ]
            ];
        }
    }
    private function callCurl($method, $endpoint = '', $data = [], $isCheckTokenLogin=true)
    {
			$startTime = microtime(true);

			$key = sprintf('ApiMpos-%s-%s', $data['serviceName'], mt_rand(1, 9999999999));
			$log[$key]['request']['data'] = $data;
			$log[$key]['method'] = $method;
			$log[$key]['startTime'] = $startTime;

			if (empty($data['tokenLogin']) && $isCheckTokenLogin) {
				throw new BusinessException('Chức năng cần mã tham chiếu mpos');
			}

			$curl = curl_init();

        try {
            $checksum = $this->createCheckSumData($data);
						// mylog(['mpos_checksum' => $checksum]);
            
            $url = $this->baseUrl;
            if ($endpoint) {
                $url = $this->baseUrl . $endpoint;
            }

						$log[$key]['url'] = $url;
            
            curl_setopt_array($curl, array(
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
								CURLOPT_MAXREDIRS => 10,
								CURLOPT_CONNECTTIMEOUT => 30,
								CURLOPT_TIMEOUT => 30,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => $method,
                CURLOPT_POSTFIELDS => json_encode($data),
                CURLOPT_HTTPHEADER => array(
                    'checkSum: ' . $checksum,
                    'Content-Type: application/json'
                ),
            ));

						$log[$key]['checksum'] = $checksum;
            $response = curl_exec($curl);
            $data = json_decode($response,true);
            curl_close($curl);
						$log[$key]['response'] = $data;
						
						$endTime = microtime(true);
						$log[$key]['endTime'] = $endTime;
						$log[$key]['mposDuration'] = $endTime - $startTime;
						mylog($log);
            if (isset($data['error']['code'])) {
                return $data;
            } else {
                return [
                    'error' => [
                        'code' => 'ERORR FORMAT RETURN',
                        'message' => 'Lỗi format cấu trúc'
                    ]
                ];
            }
        } catch (RequestException $e) {
			    TelegramAlertWarning::sendMessage(Helper::traceError($e));
						curl_close($curl);
						$endTime = microtime(true);
						$log[$key]['endTime'] = $endTime;
						$log[$key]['mposDuration'] = $endTime - $startTime;
						mylog($log);
            return [
                'error' => [
                    'code' => 'ERROR NOTFOUD',
                    'message' => 'Lỗi không xác định',
                ]
            ];
        }
    }
    

    private function makeApiCall($method, $endpoint, $inputs)
    {
        $response = $this->call($method, $endpoint, $inputs);
        return $this->returnData($response);
    }
    public function returnData($result)
    {
        // Xử lý và trả về dữ liệu theo định dạng mong muốn
        // Ví dụ:isset($data['error']['code'])
        if (isset($result['error']['code']) && $result['error']['code']) {
            return [
                'status' => true,
                'data' => $result ?? null,
                'status_code_partner' => $result['error']['code'],
                'message' => isset($result['error']['message']) ? $result['error']['message'] : '',
            ];
        } else {
            return [
                'status' => false,
                'status_code_partner' => 99,
                'data' => null,
                'message' => isset($result['error']['message']) ? $result['error']['message'] : '',
            ];
        }
    }
    public function createCheckSumData($data) {
        return md5(json_encode($data).$this->keyMakeCheckSum);
    }
   
    public function getPaymentNowRQStatusWitdraw($requestId,$merchantFk,$tokenLogin)
    {
        $inputs = [
            "serviceName" => 'PAYMENT_NOW_RQ_CHECK_STATUS',
            'merchantFk'=>$merchantFk, 
            'tokenLogin' => $tokenLogin,
            'requestId'=> $requestId
        ];
        $response = $this->callCurl('POST', '/mc360', $inputs);
        return $this->returnData($response);
    }
    public function getDetailWithdrawNow($requestId,$merchantFk,$tokenLogin)
    {
        $inputs = [
            "serviceName" => 'PAYMENT_NOW_RQ_GET_DETAIL',
            'tokenLogin' => $tokenLogin,
            'merchantFk'=>$merchantFk, 
            'requestId'=> $requestId
        ];
        $response = $this->callCurl('POST', '/mc360', $inputs);
        return $this->returnData($response);
    }

    public function getPaymentNowList($params = [])
    {
        $inputs = [
            "serviceName" => 'STATISTIC_TRANSACTION',
            "typeTransaction" => 'STATISTIC_PAYMENT_NOW'
        ];
        $accessParams = ['merchantFk'];
        foreach ($accessParams as $key => $value) {
            if (isset($params[$value])) {
                $inputs[$value] = $params[$value];
            }
        }
        $response = $this->callCurl('POST', '/mc360', $inputs);
        return $this->returnData($response);
    }

    public function paymentNowCreate($params = [])
    {
        $inputs = [
            "serviceName" => 'PAYMENT_NOW'
        ];
        $accessParams = ['merchantFk', 'transactionIDList', 'tokenLogin'];
        foreach ($accessParams as $key => $value) {
            if (isset($params[$value])) {
                $inputs[$value] = $params[$value];
            }
        }
        $response = $this->callCurl('POST', '/mc360', $inputs);
        return $this->returnData($response);
    }

    public function paymentNowCheckStatus($params = [])
    {
        $inputs = [
            "serviceName" => 'PAYMENT_NOW_RQ_CHECK_STATUS'
        ];
        $accessParams = ['merchantFk', 'requestId'];
        foreach ($accessParams as $key => $value) {
            if (isset($params[$value])) {
                $inputs[$value] = $params[$value];
            }
        }
        $response = $this->callCurl('POST', '/mc360', $inputs);
        return $this->returnData($response);
    }

    public function paymentNowGetDetail($params = [])
    {
        $inputs = [
            "serviceName" => 'PAYMENT_NOW_RQ_GET_DETAIL'
        ];
        $accessParams = ['merchantFk', 'requestId', 'tokenLogin'];
        foreach ($accessParams as $key => $value) {
            if (isset($params[$value])) {
                $inputs[$value] = $params[$value];
            }
        }
        $response = $this->callCurl('POST', '/mc360', $inputs);
        return $this->returnData($response);
    }

    public function paymentNowPreview($params = [])
    {
        $inputs = [
            "serviceName" => 'PAYMENT_NOW_REQUEST_PREVIEW'
        ];
        $accessParams = ['merchantFk', 'tokenLogin'];
        foreach ($accessParams as $key => $value) {
            if (isset($params[$value])) {
                $inputs[$value] = $params[$value];
            }
        }
        $response = $this->callCurl('POST', '/mc360', $inputs);
        return $this->returnData($response);
    }

    public function getTransQRList($params = [])
    {
        $inputs = [
            "serviceName" => 'STATISTIC_TRANSACTION',
            'typeTransaction' => 'STATISTIC_QR',
        ];
        $accessParams = ['merchantFk', 'typeTransaction', 'startDate', 'endDate', 'transactionType', 'transactionStatus', 'tokenLogin', 'pageIndex', 'pageSize', 'transactionPushType', 'issuerCode'];
        foreach ($accessParams as $key => $value) {
            if (isset($params[$value])) {
                $inputs[$value] = $params[$value];
            }
        }
       
        $response = $this->callCurl('POST', '/mc360', $inputs);
        return $this->returnData($response);
    }


    public function getListPaymentNow($params = [])
    {
        $inputs = [
            "serviceName" => 'GET_DATA_PAGE'
        ];
        $accessParams = ['merchantFk', 'tokenLogin', 'pageIndex', 'pageSize','dateFrom','dateTo'];
        foreach ($accessParams as $key => $value) {
            if (isset($params[$value])) {
                $inputs[$value] = $params[$value];
            }
        }
        if (isset($inputs['pageIndex']) && $inputs['pageIndex'] < 1) {
            $inputs['pageIndex'] = 0;
        }
        $response = $this->callCurl('POST', '/mc360', $inputs);
        return $this->returnData($response);
    }

		public function countSumTransByDate($params = [])
    {
			$inputs = ["serviceName" => 'DAILY_COUNT_SUM_TRANSACTION'];
			$accessParams = ['merchantFk', 'tokenLogin', 'rangeTime', 'typeTransaction'];
			foreach ($accessParams as $key => $value) {
					if (isset($params[$value])) {
							$inputs[$value] = $params[$value];
					}
			}
			if (isset($inputs['pageIndex']) && $inputs['pageIndex'] < 1) {
					$inputs['pageIndex'] = 0;
			}
			$response = $this->callCurl('POST', '/mc360', $inputs);
			return $this->returnData($response);
    }

		public function getMcInfoByUserName($params = [])
    {
			$response = $this->callCurl('POST', '/mc360/no-merchant', $params, false);
			return $this->returnData($response);
    }

		public function getOtpForgotPassword($params = [])
    {
			$response = $this->callCurl('POST', '/mc360/no-merchant', $params, false);
			return $this->returnData($response);
    }

		public function forgotGetNewPassword($params = [])
    {
			$response = $this->callCurl('POST', '/mc360/no-merchant', $params, false);
			return $this->returnData($response);
    }
}
