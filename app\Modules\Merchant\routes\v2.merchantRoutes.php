<?php

use Illuminate\Support\Facades\Route;
use App\Http\Middleware\MakeSureThatRequestIsJsonMiddleware;
use App\Modules\Merchant\Controllers\RequestChangeInfo\V2\Mpos360RequestChangeInfoController;

Route::group([
	'prefix' => 'v2', 
	'middleware' => [MakeSureThatRequestIsJsonMiddleware::class]
], function () {
	// add thêm tài liệu đính kèm
	Route::any('/Mpos360RequestChangeInfoAdditionalAttachment', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoAdditionalAttachment',
		'as' => 'Mpos360RequestChangeInfoAdditionalAttachmentVer2Action'
	])->middleware('validateHash:email|id');

	// đánh dấu all profile success
	Route::any('/Mpos360RequestChangeInfoAllOtpProfileSuccess', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoAllOtpProfileSuccess',
		'as' => 'Mpos360RequestChangeInfoAllOtpProfileSuccessV2Action'
	])->middleware('validateHash:id');

	// pick phương thức ký
	Route::any('/Mpos360RequestChangeInfoPickSignMethod', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoPickSignMethod',
		'as' => 'Mpos360RequestChangeInfoPickSignMethodAction'
	])->middleware('validateHash:id');

	// finish luồng chọn ký (mc phải upload hoặc xác nhận ok)
	Route::any('/Mpos360RequestChangeInfoUploadPhuLucKy', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoUploadPhuLucKy',
		'as' => 'Mpos360RequestChangeInfoUploadPhuLucKyAction'
	])->middleware('validateHash:id|signature_url');

	// nhét chữ ký vào yêu cầu
	Route::any('/Mpos360RequestChangeInfoAttachSignature', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoAttachSignature',
		'as' => 'Mpos360RequestChangeInfoAttachSignatureAction'
	])->middleware('validateHash:signature_id|request_id');
});



