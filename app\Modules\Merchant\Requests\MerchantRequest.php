<?php

namespace App\Modules\Merchant\Requests;

use App\Lib\DeviceSessionManualHelper;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\Merchant\Model\DeviceSession;

class MerchantRequest extends FormRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			//
		];
	}

	public function getCurrentDeviceSession(): DeviceSession
	{
		// $this->input('api_key') dùng trong trường hợp upload
		$apiKey = $this->json('api_key', $this->input('api_key'));
		if (strlen($apiKey) > env('MPOS360_DO_DAI_KEY_PHIEN_CU', 36)) {
			$dv = DeviceSessionManualHelper::makeInstance($apiKey);
			return $dv;
		}else {
			$deviceSession = DeviceSession::query()
																	 ->with('mpos360User:id,username,merchant_id,core_id,data_users,last_ip,status,last_time,time_created,time_updated,data_merchant')
																	 ->firstWhere('api_key', $apiKey);
		}
		
		return $deviceSession;
	}

	public function getMposUserMobileToken(): string {
		$deviceSession = $this->getCurrentDeviceSession();
		return $deviceSession->mpos_token;
	}

	protected function prepareForValidation()
	{
		$params = $this->all();
		if (!empty($params['data']['email'])) {
			if (!filter_var($params['data']['email'], FILTER_VALIDATE_EMAIL)) {
				$params['data']['email'] = $params['data']['email'] . '@mpos360.vn';
			}
		}
		
		$this->merge($params);
	}
} // End class
