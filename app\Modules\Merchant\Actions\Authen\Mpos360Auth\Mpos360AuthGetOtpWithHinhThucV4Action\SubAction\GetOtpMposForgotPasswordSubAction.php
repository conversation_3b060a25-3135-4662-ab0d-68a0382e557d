<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthGetOtpWithHinhThucV4Action\SubAction;

use App\Exceptions\BusinessException;
use App\Lib\partner\MPOS;
use Carbon\Carbon;

class GetOtpMposForgotPasswordSubAction
{
	public MPOS $mpos;

	public function __construct(MPOS $mpos)
	{
		$this->mpos = $mpos;
	}

	public function run($params)
	{
		$result = $this->mpos->getOtpForgotPassword($params);
		if (empty($result['data']['data']['otp'])) {
			$msg = sprintf('Mpos Err - Lỗi không tạo được otp (Code: %s)', $result['data']['error']['code'] ?? '00');
			throw new BusinessException($msg);
		}

		$timeExpiredAsTimestamp = Carbon::createFromTimestampMs($result['data']['data']['timeExpire'])->timestamp;
		
		return [
			'otp' => $result['data']['data']['otp'],
			'countdown_time_get_new_otp' => $timeExpiredAsTimestamp - now()->timestamp
		];
	}
}
