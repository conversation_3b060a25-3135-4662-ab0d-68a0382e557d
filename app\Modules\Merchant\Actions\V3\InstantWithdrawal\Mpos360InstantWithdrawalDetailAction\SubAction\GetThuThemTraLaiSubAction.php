<?php

namespace App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalDetailAction\SubAction;

use App\Lib\Helper;
use App\Modules\Merchant\Enums\Mpos360Enum;

class GetThuThemTraLaiSubAction
{
	public function run($rc)
	{
		$thuThem = [
			'key' => 'MaYc',
			'label' => __('rtn.Thu thêm'),
			'value' => Helper::priceFormat(0),
			'other_data' => [
				'text_color' => '#018bf4',

			]
		];

		if (!empty($rc['collectTransList'])) {
			$thuThem['value'] = Helper::priceFormat($rc['totalCollectAmount']);

			$thuThem['other_data']['linking'] = [
				'app_screen' => 'LIST_VIETQR_TRANSACTION_EXCLUDING_DETAIL',
				'title' => __('rtn.Giao dịch thu thêm'),
				'params' => [
					'order_code' => $rc['id'],
					'type' => Mpos360Enum::MPOS360_RTN_TYPE_GD_THU_THEM
				]
			];
		}

		$traLai = [
			'key' => 'MaYc',
			'label' => __('rtn.Trả lại'),
			'value' =>  Helper::priceFormat(0),
			'other_data' => [
				'text_color' => '#018bf4',
			]
		];

		if (!empty($rc['refundTransList'])) {
			$traLai['value'] = Helper::priceFormat($rc['totalRefundAmount']);
			$traLai['other_data']['linking'] = [
				'app_screen' => 'LIST_VIETQR_TRANSACTION_EXCLUDING_DETAIL',
				'title' => __('rtn.Giao dịch trả lại'),
				'params' => [
					'order_code' => $rc['id'],
					'type' => Mpos360Enum::MPOS360_RTN_TYPE_GD_TRA_LAI
				]
			];
		}

		return [ 'thuThem' => $thuThem, 'traLai' => $traLai ];
	} // End method
} // End class
