<?php

namespace App\Modules\Merchant\Actions\TingBox\Mpos360SaveTknhStep3Action;

use App\Lib\partner\MPOS;
use App\Lib\MnpOnboardNewMcHelper;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Requests\TingBox\Mpos360SaveTknhStep3Request;
use App\Modules\Merchant\Actions\TingBox\Mpos360CheckTknhKhaiBaoStep3Action\SubAction\KiemTraStkLaThatSubAction;
use App\Modules\Merchant\Model\CacheAction;

class Mpos360SaveTknhStep3Action
{
	public MPOS $mpos;
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public function __construct(MPOS $mpos, MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mpos = $mpos;
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run(Mpos360SaveTknhStep3Request $request)
	{
		// Check STK đầu api trước, không nên tin tưởng client
		$listBank = $this->mnpOnboardNewMcHelper->getNganhNgheNganHangThanhPho(true);
		$bankItem = collect($listBank['banks'])->where('bankId', $request->json('data.bankId'))->first();
		
		if (!$bankItem) {
			throw new BusinessException('Không tìm được thông tin ngân hàng mà bạn đã cung cấp');
		}

		$paramCheckStk = [
			'bank_account_holder' => $request->json('data.bankAccountHolder'),
			'bank_account' => $request->json('data.bankAccountNumber'),
			'bank_id' => $bankItem['bankVimoId']
		];

		/**
		 * array:3 [
				"status" => "STK_KHONG_DUNG"
				"msg" => "Số tài khoản không hợp lệ"
				"desc" => ""
			]
		 */
		$check = app(KiemTraStkLaThatSubAction::class)->run($paramCheckStk);
		
		if ($check['status'] != 'STK_HOP_LE') {
			throw new BusinessException('Lỗi không thể xác minh tài khoản ngân hàng nhận tiền của bạn là hợp lệ');
		}

		$inputs = [
			'bankId' => $request->json('data.bankId'),
			'mposMcId' => $request->json('data.merchantId'),
			'accountNo' => $request->json('data.bankAccountNumber'), 
			'holderName' => $request->json('data.bankAccountHolder'),
			'bankVerify' => true
		];

		// Gọi sang bên anh Hiếu để update STK vào cửa hàng
		$updateBank = $this->mnpOnboardNewMcHelper->updateThongTinBanking($inputs);

		if (empty($updateBank['result']) || $updateBank['code'] != Mpos360Enum::API_SUCCESS_CODE) {
			$msg = sprintf('Lỗi không lưu được tài khoản ngân hàng nhận tiền: %s (Code: %s)', $updateBank['message'], $updateBank['code'] ?? '-1');
			throw new BusinessException($msg);
		}

		@CacheAction::query()->forceCreate([
			'step' => 1,
			'reference_id' => $request->json('data.merchantId'),
			'time_created' => time(),
			'other_data' => '',
			'step_code' => 'KHAI_BAO_TKNH'
		]);

		$returnData = [
			'status' => 'SUCCESS',
			'msg' => 'Tài khoản ngân hàng đã được liên kết thành công. mPOS sẽ sử dụng để chuyển tiền từ các giao dịch hàng ngày vào tài khoản này.'
		];

		return $returnData;
	}
} // End class
