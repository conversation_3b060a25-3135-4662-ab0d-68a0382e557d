<?php

namespace App\Lib;

use App\Lib\Helper;
use App\Lib\partner\MNP;
use App\Exceptions\BusinessException;
use Illuminate\Support\Facades\Cache;
use App\Lib\partner\MnpDangKyTaiKhoan;
use App\Modules\Merchant\Actions\TingBox\Mpos360CheckTknhKhaiBaoStep3Action\SubAction\KiemTraStkLaThatSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoGetConfigAction\Mpos360RequestChangeInfoGetConfigAction;

class MnpOnboardNewMcHelper
{
	public MNP $mnp;

	const CACHE_NGANHNGHE_NGANHANG_THANHPHO = 'CACHE_NGANHNGHE_NGANHANG_THANHPHO';

	public function __construct(MNP $mnp)
	{
		$this->mnp = $mnp;
	}

	public function getAccessTokenMcDefault()
	{
		$merchantDefaultId = env('MNP_MERCHANT_DEFAULT_ID');

		$cacheKey = sprintf('mnp_merchant_default_accessToken_%s', $merchantDefaultId);
		$cacheTime = 20 * 60 * 60;

		$cacheData = Cache::remember($cacheKey, $cacheTime, function () use ($merchantDefaultId) {
			$getToken = $this->mnp->getToken($merchantDefaultId);

			if (empty($getToken['status'])) {
				throw new BusinessException('Lỗi không lấy được thông tin MC MNP');
			}

			return $getToken['data'];
		});

		return $cacheData;
	}

	public function getNganhNgheNganHangThanhPho(bool $useCache=false) {
		if ($useCache) {
			if (Cache::has(self::CACHE_NGANHNGHE_NGANHANG_THANHPHO)) {
				return Cache::get(self::CACHE_NGANHNGHE_NGANHANG_THANHPHO);
			}
		}

		$mnpGetConfig = app(Mpos360RequestChangeInfoGetConfigAction::class)->run();
		
		$industries = [];
		
		foreach ($mnpGetConfig['data']['mcc'] as $industryId => $industryName) {
			$industries[] = [
				'industryId' => $industryId,
				'industryName' => $industryName
			];
		}

		$cities = [];
		foreach ($mnpGetConfig['data']['cities'] as $cityId => $cityName) {
			$cities[] = [
				'cityId' => $cityId,
				'cityName' => $cityName
			];
		}

		$banks = [];
		foreach ($mnpGetConfig['data']['banks'] as $bankId => $bankName) {
			$item = [
				'bankId' => $bankId,
				'bankName' => $bankName,
				'bankVimoId' => $mnpGetConfig['data']['bankMnpVimo'][$bankId] ?? '',
				'bankCode' => Helper::getBankCode($bankName)
			];

			$banks[] = $item;
		}

		$returnData = [
			'banks' => $banks,
			'cities' => $cities,
			'industries' => $industries
		];

		if ($useCache) {
			Cache::put(self::CACHE_NGANHNGHE_NGANHANG_THANHPHO, $returnData, 30*24*60*60);
		}

		return $returnData;
	}

	
	public function getQuanHuyenTuMaTinhThanh(string $idTinhThanh='vn-kha', bool $useCache=false) {
		$cacheKey = 'cache_quan_huyen_by_ma_tinh_thanh_' . $idTinhThanh;
		if ($useCache && Cache::has($cacheKey)) {
			return Cache::get($cacheKey);
		}

		$getAccessToken = $this->getAccessTokenMcDefault();
		$mnpDangKy = new MnpDangKyTaiKhoan($getAccessToken);
		$r = $mnpDangKy->sendRequest('/cnps-cstm-360/cfg/district-list/' . $idTinhThanh, 'GET', []);

		// Cache quận huyện theo từng mã tỉnh thành
		if ($useCache && !empty($r['data']) && $r['code'] == 1000) {
			Cache::put($cacheKey, $r, 30*24*60*60);
		}

		return $r;
	}
	
	/**	
	 *  "{\"result\":true,\"code\":1000,\"message\":\"DO_SERVICE_SUCCESS\",\"data\":null,\"meta\":null}"
	
	 */
	public function checkSdtHopLe(string $mobileNumber='', $password='') {
		$getAccessToken = $this->getAccessTokenMcDefault();
		$mnpDangKy = new MnpDangKyTaiKhoan($getAccessToken);
		$r = $mnpDangKy->sendRequest('/cnps-cstm-360/reg-check', 'POST', [
			'mobile' => $mobileNumber,
			'password' => $password
		]);
		return $r;
	}

	/**
	 * [THAT BAI] {
				"result": false,
				"code": 1029,
				"message": "Tạo tài khoản thất bại. Vui lòng thử lại hoặc liên hệ <EMAIL>",
				"data": null,
				"meta": null
		}

		[THANH CONG] {
				"result": true,
				"code": 1000,
				"message": "DO_SERVICE_SUCCESS",
				"data": {
						"mposMcId": ********
				},
				"meta": null
		}
	 */
	public function taoTaiKhoanMerchant(string $mobileNumber, string $password, $refCodeSaleBank='') {
		$getAccessToken = $this->getAccessTokenMcDefault();
		$mnpDangKy = new MnpDangKyTaiKhoan($getAccessToken);
		$p = ['mobile' => $mobileNumber, 'password' => $password];
		
		if (!empty($refCodeSaleBank)) {
			$p['refCodeSaleBank'] = $refCodeSaleBank;
		}

		$r = $mnpDangKy->sendRequest( '/cnps-cstm-360/new-pending-info-mc', 'POST', $p );
		return $r;
	}

	/**
	 * [THAT BAI] 
	 * {
				"result": false,
				"code": 1028,
				"message": "Không tìm thấy tài khoản đã đăng kí",
				"data": null,
				"meta": null
		}

		[THANH CONG]
		{
				"result": true,
				"code": 1000,
				"message": "Tạo MC: thành công. Tạo HĐ: thành công",
				"data": null,
				"meta": null
		}
	 */
	public function updateThongTinMc(array $params=[]) {
		$getAccessToken = $this->getAccessTokenMcDefault();
		$mnpDangKy = new MnpDangKyTaiKhoan($getAccessToken);

		$postData = [
			'address' => trim($params['address']),
			'customerName' => $params['customerName'],
			'gender' => $params['gender'], // MALE, FEMALE
			'merchantName' => $params['merchantName'],
			'mposMcId' => $params['mposMcId'],
			'passport' => $params['passport'],
			'birthDay' => $params['birthDay'],
			'passportRepresentFrontUrl' => $params['passportRepresentFrontUrl'],
			'passportRepresentBackUrl' => $params['passportRepresentBackUrl'],
		];

		if (!empty($params['qrDisplayName'])) {
			$postData['qrDisplayName'] = $params['qrDisplayName'];
		}

		$r = $mnpDangKy->sendRequest('/cnps-cstm-360/verify-info-mc', 'POST', $postData);
		return $r;
	}

	// Thêm cửa hàng mới
	public function taoCuaHangMoi(array $params=[]) {
		$getAccessToken = $this->getAccessTokenMcDefault();
		$mnpDangKy = new MnpDangKyTaiKhoan($getAccessToken);

		$postData = [
			'mcc' => $params['mcc'], // id nganh nghe
			'areaName' => trim($params['areaName']), // ten cua hang
			'areaAddress' => trim($params['areaAddress']), // Dia chi cua hang
			'mposMcId' => $params['mposMcId'], // id mpos mc
			'areaCityCode' => $params['areaCityCode'], // id tỉnh thành
			'areaDistrictCode' => $params['areaDistrictCode'], // id quận huyện,
			'qrDisplayName' => isset($params['qrDisplayName']) ? $params['qrDisplayName'] : ''
		];

		$r = $mnpDangKy->sendRequest('/cnps-cstm-360/new-default-loc', 'POST', $postData);
		return $r;
	}

	// Sửa thông tin cửa hàng
	public function updateThongTinCuaHang(array $params=[]) {
		$getAccessToken = $this->getAccessTokenMcDefault();
		$mnpDangKy = new MnpDangKyTaiKhoan($getAccessToken);

		$postData = [
			'areaId' => $params['areaId'], // id cua hang
			'mcc' => $params['mcc'], // id nganh nghe
			'areaName' => $params['areaName'], // ten cua hang
			'areaAddress' => trim($params['areaAddress']), // Dia chi cua hang
			'mposMcId' => $params['mposMcId'], // id mpos mc
			'areaCityCode' => $params['areaCityCode'], // id tỉnh thành
			'areaDistrictCode' => $params['areaDistrictCode'], // id quận huyện,
			'qrDisplayName' => isset($params['qrDisplayName']) ? $params['qrDisplayName'] : ''
		];

		$r = $mnpDangKy->sendRequest('/cnps-cstm-360/update-default-loc', 'POST', $postData);
		return $r;
	}

	// Gán thiết bị tingbox
	/**
	 * {
				"result": true,
				"code": 1000,
				"message": "DO_SERVICE_SUCCESS",
				"data": {
						"mposMcId": ********
				},
				"meta": null
		}
	 */
	public function ganThietBiTingBox(array $params=[]) {
		$getAccessToken = $this->getAccessTokenMcDefault();
		$mnpDangKy = new MnpDangKyTaiKhoan($getAccessToken);

		$postData = [
			'areaId' => $params['areaId'], // id cua hang
			'mposMcId' => $params['mposMcId'], // mpos merchantId
			'serial' => $params['serial'], // serial tingbox
		];

		if (!empty($params['refDeviceCode'])) {
			$postData['refDeviceCode'] = $params['refDeviceCode'];
		}

		if (!empty($params['partnerCode'])) {
			$postData['partnerCode'] = $params['partnerCode'];
		}

		$r = $mnpDangKy->sendRequest('/cnps-cstm-360/setup-tingbox', 'POST', $postData);
		return $r;
	}

	public function checkThongTinBanking(array $params=[]) {
		$vmmcParamCheckStk = [
			'bank_account_holder' => $params['bank_account_holder'],
			'bank_account' => $params['bank_account'],
			'bank_id' => $params['bank_id'], // vimo bankId
		];

		$checkBankInfoResult = app(KiemTraStkLaThatSubAction::class)->run($vmmcParamCheckStk);
		return $checkBankInfoResult;
	}

	// Update thông tin banking
	/**
	 * {
					"result": true,
					"code": 1000,
					"message": "DO_SERVICE_SUCCESS",
					"data": null,
					"meta": null
			}
	 */
	public function updateThongTinBanking(array $params=[]) {
		$getAccessToken = $this->getAccessTokenMcDefault();
		$mnpDangKy = new MnpDangKyTaiKhoan($getAccessToken);

		$postData = [
			'bankId' => $params['bankId'],
			'mposMcId' => $params['mposMcId'],
			'accountNo' => $params['accountNo'], 
			'holderName' => $params['holderName'],
			'bankVerify' => $params['bankVerify'], // true: nếu stk xác minh được, false: nếu không xác minh được stk
			'saveForce' => true
		];

		$r = $mnpDangKy->sendRequest('/cnps-cstm-360/setup-bank-acc', 'POST', $postData);
		return $r;
	}

	/**
	 * {
				"result": true,
				"code": 1000,
				"message": "DO_SERVICE_SUCCESS",
				"data": {
						"mposMcId": ********,
						"locations": [
								{
										"id": "67736fadebf12321de3e3dd3",
										"areaName": "nhanh mot",
										"areaAddress": "dia chi mot",
										"areaCity": "Hà Nội",
										"areaDistrict": "Hoài Đức",
										"areaCityCode": "vn-hni",
										"areaDistrictCode": "vn-hni-hoc",
										"deviceDTOs": [
												{
														"serialNumber": "TEST0110T184",
														"status": "ACTIVE",
														"mobileUserId": "utgj756886"
												}
										]
								}
						]
				},
				"meta": null
		}
	 */
	public function getTingBoxList(array $params=[]) {
		$getAccessToken = $this->getAccessTokenMcDefault();
		$mnpDangKy = new MnpDangKyTaiKhoan($getAccessToken);

		$postData = [
			'mposMcId' => $params['mposMcId'], // mpos merchantId
		];

		$r = $mnpDangKy->sendRequest('/cnps-cstm-360/tingbox-list', 'POST', $postData);
		return $r;
	}

	public function getTknh($params=[]) {
		$getAccessToken = $this->getAccessTokenMcDefault();
		$mnpDangKy = new MnpDangKyTaiKhoan($getAccessToken);

		$postData = [
			'mposMcId' => $params['mposMcId'], // mpos merchantId
		];

		$r = $mnpDangKy->sendRequest('/cnps-cstm-360/bank-info', 'POST', $postData);
		return $r;
	}

	public function detailMc($params=[]) {
		$getAccessToken = $this->getAccessTokenMcDefault();
		$mnpDangKy = new MnpDangKyTaiKhoan($getAccessToken);

		$postData = [
			'mposMcId' => $params['mposMcId'], // mpos merchantId
		];

		$r = $mnpDangKy->sendRequest('/cnps-cstm-360/detail-mc', 'POST', $postData);
		return $r;
	}

	public function detailMcV2($params=[]) {
		$getAccessToken = $this->getAccessTokenMcDefault();
		$mnpDangKy = new MnpDangKyTaiKhoan($getAccessToken);

		$postData = [
			'mposMcId' => $params['mposMcId'], // mpos merchantId
		];

		$r = $mnpDangKy->sendRequest('/cnps-cstm-360/detail-mc-v2', 'POST', $postData);
		return $r;
	}

	public static function handleSecurity(string $merchantId, $signed='') {
		// return true;
		// if (empty($signed)) {
		// 	if (!session()->has('DangKyMcId')) {
		// 		echo view('abort', ['message' => 'Không tìm thấy thông tin merchant đã đăng ký trước đó']);
		// 		exit;
		// 	}
	
		// 	if (session()->get('DangKyMcId') != $merchantId) {
		// 		echo view('abort', ['message' => 'Lỗi không khớp thông tin merchant']);
		// 		exit;
		// 	}
		// }
		

		// if (!empty($signed)) {
			try {
				$signedDecrypt = decrypt($signed);
				if (is_array($signedDecrypt)) {
					$signedDecryptAsArray = $signedDecrypt;
				}else {
					$signedDecryptAsArray = json_decode($signedDecrypt, true);
				}
				
				if (!isset($signedDecryptAsArray['merchantId'])) {
					echo view('abort', ['message' => 'Không tồn tại thông tin MC']);
					exit;
				}

				if ($signedDecryptAsArray['merchantId'] != $merchantId) {
					echo view('abort', ['message' => 'Lỗi sai thông tin merchant']);
					exit;
				}

			}catch(\Throwable $th) {
				echo view('abort', ['message' => 'Lỗi không giải mã được thông tin merchant.']);
				exit;
			}
		}
	// }
} // End class