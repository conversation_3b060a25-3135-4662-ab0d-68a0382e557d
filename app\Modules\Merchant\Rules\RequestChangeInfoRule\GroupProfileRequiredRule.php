<?php

namespace App\Modules\Merchant\Rules\RequestChangeInfoRule;

use App\Exceptions\BusinessException;
use Illuminate\Contracts\Validation\Rule;
use App\Exceptions\ValidateAsJsonException;

class GroupProfileRequiredRule implements Rule
{
	public string $mnpGroupCode = '';

	public string $errorMessage = 'Lỗi: ';

	public function __construct(string $mnpGroupCode)
	{
		$this->mnpGroupCode = $mnpGroupCode;
	}

	/**
	 * Determine if the validation rule passes.
	 *
	 * @param  string  $attribute
	 * @param  mixed  $mnpGroupProfileCode: CHANGE_BANK_ACCOUN_INFO
	 * @return bool
	 */
	public function passes($attribute, $profiles): bool
	{
		$expression = false;

		// Thong tin ngan hang
		if ($this->mnpGroupCode == 'CHANGE_BANK_ACCOUN_INFO') {
			foreach ($profiles as $p) {
				
				if (!$p['profileKey']) {
					throw new BusinessException('Lỗi cấu trúc json', 422);
				}

				$expression = in_array($p['profileKey'], [
					'branch',
					'bank',
					'accountNo',
					'bankAccType',
					'holderName',
					'bankCity',
				]);

				if (!$expression) {
					$this->errorMessage .= sprintf('%s không phải là hồ sơ hợp lệ trong nhóm %s', $p['label'], $this->mnpGroupCode);
					return false;
				}
			}

			return $expression;
		} // endif

		if ($this->mnpGroupCode == 'CHANGE_REPRESENT_INFO') {
			foreach ($profiles as $p) {
				
				if (!$p['profileKey']) {
					throw new BusinessException('Lỗi cấu trúc json', 422);
				}

				$expression = in_array($p['profileKey'], [
					'authoriserContactNumber',
					'placeOfIssue',
					'passport',
					'authoriserAddress1',
					'authoriserAddressPresent',
					'authoriserEmail',
					'customerName',
				]);

				if (!$expression) {
					$this->errorMessage .= sprintf('"%s" không phải là hồ sơ hợp lệ trong nhóm "%s"', $p['label'], $this->mnpGroupCode);
					return false;
				}
			}

			return $expression;
		} // endif
	}

	/**
	 * Get the validation error message.
	 *
	 * @return string
	 */
	public function message()
	{
		return $this->errorMessage;
	}
}
