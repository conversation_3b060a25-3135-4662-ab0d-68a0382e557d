<?php

namespace App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360GetDsDiemBanMuidAction;

use App\Lib\Helper;
use App\Lib\Logs;
use App\Lib\partner\SoundBox;
use App\Lib\MnpOnboardNewMcHelper;
use App\Modules\TingBoxVAMC\Requests\Transaction\Mpos360GetDsDiemBanMuidRequest;

class Mpos360GetDsDiemBanMuidTingBoxMcAction
{
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;	
	}

	public function run(Mpos360GetDsDiemBanMuidRequest $request)
	{
		Logs::writeInfo("Mpos360GetDsDiemBanMuidTingBoxMcAction", $request->json('data'));

		$listDiemBan = [];
		$listDiemBan[] = [ 'muid' => '', 'name' => 'Tất cả cửa hàng' ];

		$returnData = [
			'merchantId' => $request->json('data.merchantId'),
			'listDiemBan' => $listDiemBan
		];

		$listStore = (new SoundBox())->getLocationByMcId([
			"mcId" => $request->json('data.merchantId'),
			"partnerCode" => Helper::getPartnerCode($request->json('data.merchantId'))
		]);

		if (empty($listStore['data']['mcStores'])) {
			return $returnData;
		}

		foreach ($listStore['data']['mcStores'] as $s) {
			if (isset($s['name']) && $s['mobileUser']['muName'] != 'MOBILE_USER_DEFAULT') {
				$listDiemBan[] = [
					'muid' => $s['mobileUser']['muName'],
					'name' => sprintf('%s (%s)', $s['name'], $s['mobileUser']['muName'])
				];
			}
		}


		$returnData = [
			'merchantId' => $request->json('data.merchantId'),
			'listDiemBan' => $listDiemBan
		];

		return $returnData;
	}
} // End class
