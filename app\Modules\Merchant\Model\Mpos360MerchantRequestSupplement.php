<?php

namespace App\Modules\Merchant\Model;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Traits\Mpos360McRequestSupportLoaiYeuCauV3Trait;

class Mpos360MerchantRequestSupplement extends Model
{
	use Mpos360McRequestSupportLoaiYeuCauV3Trait;
	
	protected $connection = 'mpos360_data';

	protected $table      = 'merchant_request_supplement';

	protected $guarded    = [];

	public $timestamps    = false;

	protected $dates      = [];

	protected $hidden     = [];

	public function mpos360McRequest() {
		return $this->belongsTo(Mpos360MerchantRequest::class, 'merchant_request_id', 'id');
	}

	public function isHienThiBoSungThongTinManHinhChiTiet(): bool {
		return !in_array($this->status, [
			Mpos360Enum::MPOS360_BO_SUNG_HO_SO_MOI_TAO,
			Mpos360Enum::MPOS360_BO_SUNG_HO_SO_KHONG_CAN_XU_LY,
		]);
	}
} // End class
