<?php

namespace App\Modules\Merchant\Actions\ServiceProgramRegister\Mpos360ServiceProgramRegisterCheckAction;

use App\Exceptions\BusinessException;
use App\Lib\partner\MNP;
use App\Modules\Merchant\Enums\ServiceProgramRegisterEnum;
use App\Modules\Merchant\Model\Mpos360MerchantRequestServiceProgramRegister;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\GetMNPAccessTokenSubAction;
use App\Modules\Merchant\Requests\ServiceProgramRegister\Mpos360ServiceProgramRegisterCheckRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction\MnpGetMerchantProfileSubAction;
use App\Modules\Merchant\Actions\ServiceProgramRegister\Mpos360ServiceProgramRegisterListAction\SubAction\MappingServiceProgramSubAction;

class Mpos360ServiceProgramRegisterCheckAction
{
	public MNP $mnp;

	public function __construct(MNP $mnp)
	{
		$this->mnp = $mnp;
	}

	public function run(Mpos360ServiceProgramRegisterCheckRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		$deviceSessionWithToken = app(GetMNPAccessTokenSubAction::class)->run($deviceSession);
		$merchantId = $deviceSession->getMerchantId();

		$mnpMerchantDetail = app(MnpGetMerchantProfileSubAction::class)->run(
			$merchantId,
			$deviceSessionWithToken->mnp_token
		);

		$mnpLoaiMc = $mnpMerchantDetail['data']['customerType']['value'] ?? '';

		if (empty($mnpLoaiMc)) {
			throw new BusinessException('Mnp Err: Lỗi không xác định được loại MC');
		}

		$mapping = MappingServiceProgramSubAction::run();

		if ($mnpLoaiMc == 'INDIVIDUAL' || $mnpLoaiMc == 'HOUSEHOLD') {
			$returnData = [
				'can' => ServiceProgramRegisterEnum::CAN_SPR_GOTO_XEM_PHU_LUC,
				'data' => (object) [
					'request_id' => -1,
					'merchant_id' => $merchantId,
					'merchant_type' => $mnpLoaiMc,
					'service_program_id' => $request->json('data.service_program_id'),
					'service_detail_url' => $mapping['services']['HOME_PAY_LATER']['service_detail_url']
				],
				'msg' => 'Bạn cần xem phụ lục trước khi ký'
			];
		}else {
			// Yêu cầu là công ty, bắn lỗi ra cho người dùng
			throw new BusinessException('Hệ thống chưa hỗ trợ đăng ký dịch vụ với Merchant Doanh nghiệp. Bạn vui lòng liên hệ nhân viên hỗ trợ.');
			
			// tạo ra yêu cầu nếu là công ty
			$r = Mpos360MerchantRequestServiceProgramRegister::query()->forceCreate([
				'merchant_id' => $merchantId,
				'service_program_id' => $request->json('data.service_program_id'),
				'username' => $deviceSession->getMerchantEmail(),
				'data_request' => json_encode([
					'merchantType' => $mnpLoaiMc,
					'merchantId' => $merchantId,
				]),
				'time_created' => now()->timestamp
			]);

			if (!$r) {
				throw new BusinessException('Lỗi không tạo được yêu cầu đăng ký dịch vụ');
			}

			$returnData = [
				'can' => ServiceProgramRegisterEnum::CAN_SPR_GOTO_HOAN_THANH,
				'data' => (object) [
					'request_id' => $r->id,
					'merchant_id' => $merchantId,
					'merchant_type' => $mnpLoaiMc,
					'service_program_id' => $request->json('data.service_program_id'),
					'service_detail_url' => $mapping['services']['HOME_PAY_LATER']['service_detail_url']
				],
				'msg' => 'Tạo yêu cầu thành công'
			];
		}

		return $returnData;
	}
} // End class