<?php
namespace App\Modules\TingBoxVAMC\Requests\MerchantBank;

use Illuminate\Foundation\Http\FormRequest;


class ActiveQrNoDeviceRequest extends FormRequest
{
    /**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data' => ['required', 'array'],
			'data.userMobileId' => ['required', 'string'],
			'data.merchantId' => ['required', 'string'],
			'data.username' => ['required', 'string'],
			'data.serial' => ['required', 'string'],
		];
	}

	public function messages()
	{
		return [
			'data.userMobileId.required' => 'Mobile User là bắt buộc',
			'data.userMobileId.string' => 'Mobile User phải là kiểu chuỗi',

			'data.merchantId.required' => 'Mã MC là bắt buộc',
			'data.merchantId.string' => 'Mã MC phải là kiểu chuỗi',

			'data.serial.required' => 'Mã serial là bắt buộc',
			'data.serial.string' => 'Mã serial phải là kiểu chuỗi',

			'data.username.required' => 'username là bắt buộc',
			'data.username.string' => 'username phải là kiểu chuỗi',

		];
	}
}