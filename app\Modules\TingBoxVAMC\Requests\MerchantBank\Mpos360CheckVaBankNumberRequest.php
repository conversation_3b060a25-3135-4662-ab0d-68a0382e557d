<?php

namespace App\Modules\TingBoxVAMC\Requests\MerchantBank;

use Illuminate\Foundation\Http\FormRequest;

class Mpos360CheckVaBankNumberRequest extends FormRequest
{
	public function rules(): array
	{
		return [
			'data.username' => ['required', 'string'],
			'data.merchantId' => ['required', 'numeric'],
			'data.qrCode' => ['required', 'string'],
		];
	}

	public function messages(): array
	{
		return [
			'data.username.required' => 'User đăng nhập là bắt buộc',
			'data.username.string' => 'User đăng nhập phải là kiểu chuỗi',
			'data.merchantId.required' => 'Mã MC là bắt buộc',
			'data.merchantId.numeric' => 'Mã MC phải là kiểu số',
			'data.qrCode.required' => 'Mã QR là bắt buộc',
			'data.qrCode.string' => 'Mã QR phải là kiểu chuỗi',
		];
	}

	protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
	{
		echo '';
		exit;
	}
}

