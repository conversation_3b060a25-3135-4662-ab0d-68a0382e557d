<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction;

use Exception;
use App\Exceptions\BusinessException;

class DamBaoCoCccdSubAction
{
	/**
	 * array:4 [
  "name" => "Người đại diệ<PERSON> k<PERSON>"
  "code" => "CHANGE_REPRESENT_INFO"
  "choice" => array:3 [
    0 => array:2 [
      "code" => "DOI_NGUOI_DAI_DIEN_MOI"
      "name" => "Đổi người đại diện mới"
    ]
    1 => array:2 [
      "code" => "DOI_CCCD_MOI"
      "name" => "Đổi CCCD mới"
    ]
    2 => array:2 [
      "code" => "DOI_THONG_TIN_LIEN_HE"
      "name" => "Đổi thông tin liên hệ"
    ]
  ]
  "profiles" => array:7 [
    0 => array:11 [
      "editable" => 1
      "display" => "0973927223"
      "format" => "TEXT"
      "position" => 0
      "label" => "Số điện thoại người đại diện"
      "type" => "TEL"
      "value" => "0973927223"
      "group" => "CHANGE_REPRESENT_INFO"
      "isChanged" => "0"
      "other_data" => {#372}
      "profileKey" => "authoriserContactNumber"
    ]
	 */
	public function run($groupData): bool
	{
		$profiles = $groupData['profiles'];
		$hasCccd = collect($profiles)->contains(function ($item) {
			return $item['profileKey'] == 'passport' && !empty($item['value']);
		});

		if (!$hasCccd) {
			throw new BusinessException('Lỗi: Hồ sơ MC không có thông tin CCCD');
		}

		return true;
	}
} // End class