<?php

namespace App\Modules\Merchant\Requests\TingBox;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360GetFormKhaiBaoTknhStep3Request extends MerchantRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data.username' => ['required', 'string'],
      'data.merchantId' => ['required', 'string'],
    ];
  }

	public function messages() {
		return [
			'data.username.required' => 'Username là bắt buộc',
			'data.username.string' => 'Username phải là kiểu chuỗi',
			'data.merchantId.required' => 'MerchantID là bắt buộc',
			'data.merchantId.string' => 'MerchantID phải là kiểu chuỗi',
		];
	}
} // End class
