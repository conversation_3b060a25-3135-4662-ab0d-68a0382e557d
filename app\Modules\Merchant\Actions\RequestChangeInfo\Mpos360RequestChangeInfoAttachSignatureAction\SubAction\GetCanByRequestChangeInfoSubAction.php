<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAttachSignatureAction\SubAction;

use Exception;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360ChungThuc;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;

class GetCanByRequestChangeInfoSubAction
{
	public function run(Mpos360MerchantRequest $mpos360McRequest): string
	{
		if ($mpos360McRequest->isDoiThongTinNganHang() || $mpos360McRequest->isDoiThongTinLienHe()) {
			return Mpos360Enum::MPOS360_CAN_GOTO_STEP3;
		}


		if ($mpos360McRequest->isYeuCauDoiCccdMoi() || $mpos360McRequest->isYeuCauDoiNguoiDaiDienMoi()) {
			$dataRequest = json_decode($mpos360McRequest->data_request, true);
			$cccd = $dataRequest[0]['profiles']['representPassport'];

			/**
			 * Đoạn này cần so khớp thông tin chứng thực
			 * -Nếu khớp thì không cần làm bước 3
			 * -Nếu không khớp thì phải làm bước 3
			 */
			if ($mpos360McRequest->isYcVer3()) {
				$mpos360ChungThuc = Mpos360ChungThuc::query()->firstWhere([
					'merchant_id' => $mpos360McRequest->merchant_id,
					'key_code' => 'CCCD'
				]);

				if (!$mpos360ChungThuc) {
					throw new BusinessException('Lỗi: không tìm thấy thông bản ghi CCCD chứng thực');
				}
			}else {
				$mpos360ChungThuc = Mpos360ChungThuc::getChungThucCCCD($mpos360McRequest->merchant_id);
				if (!$mpos360ChungThuc) {
					throw new BusinessException('Lỗi: không tìm thấy thông tin CCCD đã chứng thực');
				}
			}

			if ($mpos360ChungThuc->isKhopThongTinChungThuc($cccd)) {
				return Mpos360Enum::MPOS360_CAN_MARK_DONE_REQUEST;
			}
		}

		return Mpos360Enum::MPOS360_CAN_GOTO_STEP3;
	} // End method
} // End class
