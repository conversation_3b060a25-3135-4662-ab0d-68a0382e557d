<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\SubAction;

use Exception;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Setting;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAttachSignatureAction\SubAction\GetCanByRequestChangeInfoV3SubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessAction\SubActionV2\LietKePhuongThucKyNeuLaCongTySubAction;

class KiemTraPhuongThucKyV3SA
{
	public string $can;

	public array $signMethod = [];

	public int $hasChuKy = 0; // không có chữ ký

	public $isDaXacThuc = false;

	public function __construct()
	{
		$this->can = '';
	}

	public function run(Mpos360MerchantRequest $mpos360McRequest, string $settingSignKey='REDIRECT_SIGNING_SCREEN_IF_IS_COMPANY_V3')
	{
		$dataRequest = json_decode($mpos360McRequest->data_request, true);

		/**
		 * Các trường hợp phải ký vẽ tay:
		 * 1-YC đổi TKNH của HKD
		 * 2-YC đổi TKNH của cá nhân mà HKD đó ủy quyền
		 */
		if (
			$mpos360McRequest->isYcDoiTknhHkd() 
			|| $mpos360McRequest->isYcDoiTknhCaNhanMaHkdUyQuyen()
			|| $mpos360McRequest->isYcDoiCccdHKDV3()
			|| $mpos360McRequest->isYcDoiThongTinLienHeHKDV3()
			|| $mpos360McRequest->isYcDoiNguoiDaiDienMoiHKDV3()
		) {
			
			$dataRequest[0]['signProcess'] = [
				'mpos360_sign_code' => 'KY_VE_TAY',
				'code' => 'E_CONTRACT',
				'name' => 'Ký vẽ tay',
				'signature_url' => ''
			];

			$this->can = Mpos360Enum::MPOS360_CAN_CREATE_SIGNATURE;
		}

		/**
		 * Các trường hợp phải cân nhắc cho chọn phương thức ký
		 * $phuongThucKyCuThe: Đây là biến on/off để điều hướng trong DB, thực hiện các thao tác
		 * 1-có thể chọn pt ký
		 * 2-có thể đi luôn vào 1 pt ký cụ thể
		 * 3-passed luôn mà không cần ký tá gì cả (sét/ép mặc định là ký điện tử megadoc)
		 */
		if (
			$mpos360McRequest->isYcDoiTknhDoanhNghiep() 
			|| $mpos360McRequest->isYcDoiTknhCaNhanMaDoanhNghiepUyQuyen()
			|| $mpos360McRequest->isYcDoiThongTinLienHeDoanhNghiepV3()
			|| $mpos360McRequest->isYcDoiCccdDoanhNghiepV3()
			|| $mpos360McRequest->isYcDoiNguoiDaiDienMoiDoanhNghiepV3()
		) {
			$this->can = Mpos360Enum::MPOS360_CAN_PICK_SIGN_METHOD;
			$this->signMethod = app(LietKePhuongThucKyNeuLaCongTySubAction::class)->run();

			$phuongThucKyCuThe = Setting::query()->firstWhere(['key' => $settingSignKey]);

			if (optional($phuongThucKyCuThe)->value == 'KY_DIEN_TU_MEGADOC') {
				$this->can = Mpos360Enum::MPOS360_CAN_GOTO_KY_DIEN_TU_MEGADOC;
			}

			if (optional($phuongThucKyCuThe)->value == 'KY_GIAY') {
				$this->can = Mpos360Enum::MPOS360_CAN_GOTO_UPLOAD_PHU_LUC_GIAY;
			}

			if (optional($phuongThucKyCuThe)->value == 'SALE_HO_TRO') {
				$this->can = Mpos360Enum::MPOS360_CAN_GOTO_SALE_HO_TRO;
			}

			if (optional($phuongThucKyCuThe)->value == 'NONE') {
				$this->can = app(GetCanByRequestChangeInfoV3SubAction::class)->run($mpos360McRequest);
				if ($this->can == Mpos360Enum::MPOS360_CAN_MARK_DONE_REQUEST) {
					$dataRequest[0]['scan_method']['QTS'] = [
						'status' => 'DONE',
						'other_data' => [
							'is_matching_facescan' => 1,
							'matching_percent' => 100
						]
					];
				}
			}

			// Nếu mà passed thì quy luôn cho là ký điện tử
			if (optional($phuongThucKyCuThe)->value == 'PASSED') {
				$this->can = app(GetCanByRequestChangeInfoV3SubAction::class)->run($mpos360McRequest);

				// $dataRequest[0]['signProcess'] = [
				// 	'mpos360_sign_code' => 'KY_DIEN_TU_MEGADOC',
				// 	'code' => 'E_CONTRACT',
				// 	'name' => 'Ký điện tử',
				// 	'signature_url' => ''
				// ];

				$this->hasChuKy = 0; // Đánh dấu luôn là có chữ ký
			}
		}

		$paramUpdate = [
			'data_request' => json_encode($dataRequest),
			'status_sign' => $this->hasChuKy
		];

		if ($this->can == Mpos360Enum::MPOS360_CAN_MARK_DONE_REQUEST) {
			$paramUpdate['status_verify'] = Mpos360Enum::MPOS360_MC_VERIFY_STT_DA_THUC_HIEN_BUOC3;
		}
		
		
		$wasUpdatedRequest = $mpos360McRequest->forceFill($paramUpdate)->update();

		if (!$wasUpdatedRequest) {
			throw new BusinessException('Lỗi không cập nhật được loại chữ ký');
		}

		return [
			'can' => $this->can,
			'sign_method' => $this->signMethod
		];
	}
} // End class