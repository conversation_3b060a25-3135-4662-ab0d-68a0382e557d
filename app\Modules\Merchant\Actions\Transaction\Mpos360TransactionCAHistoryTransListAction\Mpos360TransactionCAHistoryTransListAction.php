<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransactionCAHistoryTransListAction;

use Illuminate\Support\Str;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionCAHistoryTransListRequest;

class Mpos360TransactionCAHistoryTransListAction
{
	public function run(Mpos360TransactionCAHistoryTransListRequest $request)
	{
		$returnData = [
			'rows' => 100,
			'data' => [
				[
					'date' => '2024-08-14',
					'total_transaction' => 5,
					'list_transaction' => [
						[
							'order_code' => strtoupper(sprintf('RTN-%s', Str::random(8))),
							'transaction_id' => '',
							'time_created' => time(),
							'amount' => '3.000.000 vnd',
							'customer' => 'Nguyen Van A: 0987654321',
							'status' => 'SUCCESS',
							'note' => 'Nếu bạn không kết toán, GD sẽ tự động hủy',
							'other_data' => (object) [
								'note' => [
									'text_color' => '#d22e2a',
									'bg_color' => '#fbeaea'
								]
							],
							'remaining_settle_date' => 1,
						],


						[
							'order_code' => strtoupper(sprintf('RTN-%s', Str::random(8))),
							'transaction_id' => '',
							'time_created' => time(),
							'amount' => '3.000.000 vnd',
							'customer' => 'Nguyen Van A: 0987654321',
							'status' => 'SUCCESS',
							'note' => 'Nếu bạn không kết toán, GD sẽ tự động hủy',
							'other_data' => (object) [
								'note' => [
									'text_color' => '#d22e2a',
									'bg_color' => '#fbeaea'
								]
							],
							'remaining_settle_date' => 1,
						],
					]
				], // end item

				[
					'date' => '2024-08-13',
					'total_transaction' => 2,
					'list_transaction' => [
						[
							'order_code' => strtoupper(sprintf('RTN-%s', Str::random(8))),
							'transaction_id' => '',
							'time_created' => time(),
							'amount' => '3.000.000 vnd',
							'customer' => 'Nguyen Van A: 0987654321',
							'status' => 'NEAR_EXPIRED',
							'note' => 'Nếu bạn không kết toán, GD sẽ tự động hủy',
							'other_data' => (object) [
								'note' => [
									'text_color' => '#dd8523',
									'bg_color' => '#fcf3e9'
								]
							],
							'remaining_settle_date' => 1,
						],
					]
				], // end item

			],
			'other_data' => [
				'status' => [
					'NEAR_EXPIRED' => ['label' => 'Sắp hết hạn', 'text_color' => '#ffffff', 'bg_color' => '#018bf4'],
					'SUCCESS' => ['label' => 'Thành công', 'text_color' => '#ffffff', 'bg_color' => '#e99323'],
				],


				'filter' => [
					// thoi gian giao dich
					'transaction_time' => [
						['label' => 'Hôm nay', 'value' => 'TODAY'],
						['label' => 'Tháng này', 'value' => 'THIS_MONTH'],
						['label' => '01 tháng trước', 'value' => 'LAST_MONTH'],
					],


					// trang thai xu ly
					'transaction_status' => [
						['label' => 'Tất cả', 'value' => 'ALL'],
						['label' => 'Thành công', 'value' => 'SUCCESS'],
						['label' => 'Sắp hết hạn', 'value' => 'NEAR_EXPIRED'],
		
					]
				]
			]
		];

		return $returnData;
	}
}
