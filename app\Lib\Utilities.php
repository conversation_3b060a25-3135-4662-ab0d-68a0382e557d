<?php
namespace App\Lib;

class Utilities {

    public function makeTransactionId($code = '', $service_code = '') {
        $tranId = date('ymd') . $this->generateRandomNumber(9);

        return $tranId;
    }
    
    public function makeUniqid($number_add = 9) {
        $uniqid = date('ymd') . $this->generateRandomNumber($number_add);
        
        return $uniqid;
    }

    public static function roundDecimalUp($value, $userfee = '') {
        $amount = round($value, 3, PHP_ROUND_HALF_UP);
        if (isset($userfee) && !empty($userfee) && $userfee == $GLOBALS['MERCHANT_FEE_USERFEE_CODE']['SENDER']) {
            $amount = round($value, 2, PHP_ROUND_HALF_UP);
        }

        return $amount;
    }

    public static function roundDecimalDown($value, $userfee = '') {
        $amount = round($value, 3, PHP_ROUND_HALF_DOWN);
        if (isset($userfee) && !empty($userfee) && $userfee == $GLOBALS['MERCHANT_FEE_USERFEE_CODE']['SENDER']) {
            $amount = round($value, 2, PHP_ROUND_HALF_DOWN);
        }

        return $amount;
    }

    public static function roundDecimalEven($value, $userfee = '') {
        $amount = round($value, 3, PHP_ROUND_HALF_EVEN);
        if (isset($userfee) && !empty($userfee) && $userfee == $GLOBALS['MERCHANT_FEE_USERFEE_CODE']['SENDER']) {
            $amount = round($value, 2, PHP_ROUND_HALF_EVEN);
        }

        return $amount;
    }

    public static function roundDecimalOdd($value, $userfee = '') {
        $amount = round($value, 3, PHP_ROUND_HALF_ODD);
        if (isset($userfee) && !empty($userfee) && $userfee == $GLOBALS['MERCHANT_FEE_USERFEE_CODE']['SENDER']) {
            $amount = round($value, 2, PHP_ROUND_HALF_ODD);
        }

        return $amount;
    }

    public static function roundVND($value, $userfee = '') {
        return round($value, 0, PHP_ROUND_HALF_UP);
    }

    public static function roundCurrency($value, $userfee = '') {
        $amount = round($value, 3, PHP_ROUND_HALF_UP);
        if (isset($userfee) && !empty($userfee) && $userfee == $GLOBALS['MERCHANT_FEE_USERFEE_CODE']['SENDER']) {
            $amount = round($value, 2, PHP_ROUND_HALF_UP);
        }

        return $amount;
    }

    public static function roundExcCurrency($value) {
        $amount = round($value, 5, PHP_ROUND_HALF_UP);

        return $amount;
    }

    public static function roundRefundCurrency($value, $userfee = '') {
        return round($value, 2, PHP_ROUND_HALF_DOWN);
    }

    public static function supplirtRoundDecimalUp($value) {
        return round($value, 2, PHP_ROUND_HALF_UP);
    }

    public function getErrorCode($errorCode) {
        if (isset($errorCode) && !empty($errorCode)) {
            $exp = explode('_', $errorCode, 2);
            if (isset($exp[1]) && !empty($exp[1])) {
                return strtolower($exp[1]);
            }
        }

        return strtolower('SYS_ERROR_EXCEPTION');
    }

    public function getErrorDescription($errorCode, $language, $descriptionCustom = '') {
        $description = (new GetString($errorCode))->get($language);
        if (!isset($description) || empty($description)) {
            $description = strtolower($errorCode);
        }
        if (!empty(trim($descriptionCustom))) {
            $description = $descriptionCustom;
        }

        return $description;
    }

    /*
     * Get partner for mobile Number
     * @return VMS,VTEL,VNP, ....
     */

    public static function telcoCode($mobile) {
        $telco = false;
        $VIETTEL = array('096', '097', '098', '0163', '0164', '0165', '0166', '0167', '0168', '0169', '086', '032', '033', '034', '035', '036', '037', '038', '039');
        $MOBIFONE = array('090', '093', '0120', '0121', '0122', '0126', '0128', '089', '070', '079', '077', '076', '078');
        $VINAPHONE = array('091', '094', '0123', '0124', '0125', '0127', '0129', '088', '083', '084', '085', '081', '082');
        $VIETNAMMOBILE = array('092', '0188', '056', '058');
        $BEELINE = array('099', '0199', '082', '059');
        $SFONE = array('095');

        //kiem tra nha mang
        if (strlen($mobile) == 10) {
            $subCode = substr($mobile, 0, 3);
        } elseif (strlen($mobile) == 11) {
            $subCode = substr($mobile, 0, 4);
        } else {
            $subCode = $mobile;
        }

        if (in_array($subCode, $VIETTEL)) {
            $telco = 'VTEL';
        } elseif (in_array($subCode, $MOBIFONE)) {
            $telco = 'VMS';
        } elseif (in_array($subCode, $VINAPHONE)) {
            $telco = 'VNP';
        } elseif (in_array($subCode, $VIETNAMMOBILE)) {
            $telco = 'VNM';
        } elseif (in_array($subCode, $BEELINE)) {
            $telco = 'GTEL';
        } elseif (in_array($subCode, $SFONE)) {
            $telco = 'SFONE';
        }

        return $telco;
    }

    /*
     * Số điện thoại đầy đủ theo quốc gia
     * Nếu là VN thì bổ số 0 và thêm 84 ở đầu
     * 
     * ex, 849x, 841x
     * 
     * @return mobile number
     */

    public static function mobileNumberFull($mobilenumber = '') {
        $mobilenumber = self::setMobileNumber($mobilenumber);

        return '84' . $mobilenumber;
    }

    /*
     * Số điện thoại ngắn
     * fomat mobile = 09x hoặc 01x
     * 
     * @return mobinumber string;
     */

    public static function mobileNumberSub($mobilenumber) {
        $mobilenumber = self::setMobileNumber($mobilenumber);

        return '0' . $mobilenumber;
    }

    /*
     * Chuan hoa so dien thoai
     * Bo so 0 và 84 ở đầu
     * mobile number format 9x hoặc 1x
     * 
     * @return mobile number format
     */

    public static function setMobileNumber($phone) {
        $oneNumberFirst = substr($phone, 0, 1);
        $twoNumberFirst = substr($phone, 0, 2);
        $length = strlen($phone);
        if (trim($oneNumberFirst) == '0' || trim($oneNumberFirst) === 0) {
            $phone = substr($phone, 1, ($length - 1));
        } elseif (trim($twoNumberFirst) == '84' || trim($twoNumberFirst) == 84) {
            $phone = substr($phone, 2, ($length - 2));
        }

        return $phone;
    }

    /*
     * Tạo string ngẫu nhiên bao gồm cả chữ và số
     * @input độ dài string muốn lấy
     * 
     * @return string;
     */

    public static function generateRandomString($length = 10) {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, strlen($characters) - 1)];
        }

        return $randomString;
    }

    /*
     * Tạo string ngẫu nhiên chỉ có số
     * @input độ dài dãy số muốn lấy
     * 
     * @return string number
     */

    public static function generateRandomNumber($length = 10) {
        $characters = '0123456789';
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, strlen($characters) - 1)];
        }

        return $randomString;
    }

    /*
     * Lấy địa chi IP client gọi vào theo thứ tự ưu tiên lấy
     * 1-HTTP_X_FORWARDED_FOR
     * 2-HTTP_CLIENT_IP
     * 3-REMOTE_ADDR
     * 
     * @return string
     */

    public static function getIpClient() {
        $ip = '';
        //to check ip is pass from proxy
        if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            //check ip from share internet 
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } else if (isset($_SERVER['REMOTE_ADDR'])) {
            $ip = $_SERVER['REMOTE_ADDR'];
        }

        return $ip;
    }

    /*
     * Lấy họ, tên của 1 string
     */

    public static function getFirstAndName($string) {
        $string = (new StringData)->_convertToSMS($string);

        $firstName = $string;
        $lastName = $string;
        if (!empty($string)) {
            $exp = @explode(' ', $string, 2);
            if (count($exp) == 2) {
                $firstName = $exp[0];
                $lastName = $exp[1];
            }
        }

        return array(
            'first_name' => $firstName,
            'last_name' => $lastName
        );
    }

    public static function getCardTypeNameByCardNumber($card_number) {
        if (isset($card_number) && !empty($card_number)) {
            if (substr($card_number, 0, 1) == 4) {
                return $GLOBALS['TYPE_CARD_PAYMENT_CODE']['VISA'];
            } else if (substr($card_number, 0, 1) == 5) {
                return $GLOBALS['TYPE_CARD_PAYMENT_CODE']['MASTERCARD'];
            } else if (substr($card_number, 0, 2) == 35) {
                return $GLOBALS['TYPE_CARD_PAYMENT_CODE']['JCB'];
            } else if (substr($card_number, 0, 2) == 37) {
                return $GLOBALS['TYPE_CARD_PAYMENT_CODE']['AMERICANEXPRESS'];
            } else if (substr($card_number, 0, 2) == 62) {
                return $GLOBALS['TYPE_CARD_PAYMENT_CODE']['UNIONPAY'];
            }
        }

        return false;
    }

    public static function formatCardNumber($card_number = '', $bin_number = 6) {
        $formatCardNumber = false;
        if (!empty($card_number)) {
            $card_number = trim($card_number);
            $card_number = str_replace(' ', '', $card_number);
            $formatCardNumber = substr($card_number, 0, $bin_number) . 'XXXXXX' . substr($card_number, -4, 4);
            if (!empty($bin_number) && $bin_number == 8) {
                $formatCardNumber = substr($card_number, 0, $bin_number) . 'XXXX' . substr($card_number, -4, 4);
            }
        }

        return $formatCardNumber;
    }

    public static function formatCardNumberByBank($card_number = '') {
        $formatCardNumber = false;
        if (!empty($card_number)) {
            $card_number = trim($card_number);
            $card_number = str_replace(' ', '', $card_number);
            $formatCardNumber = 'xxxx-' . substr($card_number, -4, 4);
        }

        return $formatCardNumber;
    }

    public static function getBinCardByNumber($card_number = '', $length = 6) {
        $formatCardNumber = false;
        if (!empty($card_number)) {
            $card_number = trim($card_number);
            $card_number = str_replace(' ', '', $card_number);
            $formatCardNumber = substr($card_number, 0, $length);
        }

        return $formatCardNumber;
    }

    public static function getNameCardByNumber($card_number = '') {
        $cardName = '';
        if (!empty($card_number)) {
            $card_number = trim($card_number);
            $card_number = str_replace(' ', '', $card_number);
            $subKey = '';
            if (array_key_exists(substr($card_number, 0, 4), $GLOBALS['SUB_CARD_PAYMENT_TYPE_NAME'])) {
                $subKey = substr($card_number, 0, 4);
            } else if (array_key_exists(substr($card_number, 0, 2), $GLOBALS['SUB_CARD_PAYMENT_TYPE_NAME'])) {
                $subKey = substr($card_number, 0, 2);
            } else if (array_key_exists(substr($card_number, 0, 1), $GLOBALS['SUB_CARD_PAYMENT_TYPE_NAME'])) {
                $subKey = substr($card_number, 0, 1);
            }

            if (isset($GLOBALS['SUB_CARD_PAYMENT_TYPE_NAME'][$subKey]) && !empty($GLOBALS['SUB_CARD_PAYMENT_TYPE_NAME'][$subKey])) {
                $cardName = $GLOBALS['SUB_CARD_PAYMENT_TYPE_NAME'][$subKey];
            }
        }

        return $cardName;
    }

    /*
     * Lấy số điện thoại theo định dạng mới
     */

    public static function newMobileNumberSub($mobilenumber) {
        $mobilenumber = self::mobileNumberSub($mobilenumber);
        $subNumber = substr($mobilenumber, 0, 4);
        $newSubNumber = '';

        //Viettel
        if ($subNumber == '0162')
            $newSubNumber = '032';
        if ($subNumber == '0163')
            $newSubNumber = '033';
        if ($subNumber == '0164')
            $newSubNumber = '034';
        if ($subNumber == '0165')
            $newSubNumber = '035';
        if ($subNumber == '0166')
            $newSubNumber = '036';
        if ($subNumber == '0167')
            $newSubNumber = '037';
        if ($subNumber == '0168')
            $newSubNumber = '038';
        if ($subNumber == '0169')
            $newSubNumber = '039';

        //Mobiphone
        if ($subNumber == '0120')
            $newSubNumber = '070';
        if ($subNumber == '0121')
            $newSubNumber = '079';
        if ($subNumber == '0122')
            $newSubNumber = '077';
        if ($subNumber == '0126')
            $newSubNumber = '076';
        if ($subNumber == '0128')
            $newSubNumber = '078';

        //Vinaphone
        if ($subNumber == '0123')
            $newSubNumber = '083';
        if ($subNumber == '0124')
            $newSubNumber = '084';
        if ($subNumber == '0125')
            $newSubNumber = '085';
        if ($subNumber == '0127')
            $newSubNumber = '081';
        if ($subNumber == '0129')
            $newSubNumber = '082';

        //Vietnammobile
        if ($subNumber == '0186')
            $newSubNumber = '056';
        if ($subNumber == '0188')
            $newSubNumber = '058';

        //Gmobile
        if ($subNumber == '0199')
            $newSubNumber = '059';


        if (!isset($newSubNumber) || empty($newSubNumber)) {
            return $mobilenumber;
        }

        $mobilenumber = trim($newSubNumber . substr($mobilenumber, 4, 10));

        return $mobilenumber;
    }

    /*
     * Lấy số điện thoại theo định dạng cũ
     */

    public static function oldMobileNumberSub($mobilenumber) {
        $mobilenumber = self::mobileNumberSub($mobilenumber);
        $subNumber = substr($mobilenumber, 0, 3);
        $newSubNumber = '';

        //Viettel
        if ($subNumber == '032')
            $newSubNumber = '0162';
        if ($subNumber == '033')
            $newSubNumber = '0163';
        if ($subNumber == '034')
            $newSubNumber = '0164';
        if ($subNumber == '035')
            $newSubNumber = '0165';
        if ($subNumber == '036')
            $newSubNumber = '0166';
        if ($subNumber == '037')
            $newSubNumber = '0167';
        if ($subNumber == '038')
            $newSubNumber = '0168';
        if ($subNumber == '039')
            $newSubNumber = '0169';

        //Mobiphone
        if ($subNumber == '070')
            $newSubNumber = '0120';
        if ($subNumber == '079')
            $newSubNumber = '0121';
        if ($subNumber == '077')
            $newSubNumber = '0122';
        if ($subNumber == '076')
            $newSubNumber = '0126';
        if ($subNumber == '078')
            $newSubNumber = '0128';

        //Vinaphone
        if ($subNumber == '083')
            $newSubNumber = '0123';
        if ($subNumber == '084')
            $newSubNumber = '0124';
        if ($subNumber == '085')
            $newSubNumber = '0125';
        if ($subNumber == '081')
            $newSubNumber = '0127';
        if ($subNumber == '082')
            $newSubNumber = '0129';

        //Vietnammobile
        if ($subNumber == '056')
            $newSubNumber = '0186';
        if ($subNumber == '058')
            $newSubNumber = '0188';

        //Gmobile
        if ($subNumber == '059')
            $newSubNumber = '0199';


        if (!isset($newSubNumber) || empty($newSubNumber)) {
            return $mobilenumber;
        }

        $mobilenumber = trim($newSubNumber . substr($mobilenumber, 3, 10));

        return $mobilenumber;
    }

    public static function writeOrRecord($data, $path, $file) {
        if (!is_array($data)) {
            if (!is_dir($path)) {
                @mkdir($path, 0700);
            }
            if (file_exists($path . DS . $file)) {
                unlink($path . DS . $file);
                $fp = fopen($path . DS . $file, "w");
                fputs($fp, $data);
                fclose($fp);
            } else {
                $fp = fopen($path . DS . $file, "w");
                fputs($fp, $data);
                fclose($fp);
            }
            return true;
        }
        return FALSE;
    }

    /*
     * Lay ngay truoc ngay truyen vao (- them so ngay)
     * Ngay truyen vao format yyyymmdd, yyyy-mm-dd, yyyy/mm/dd
     * @input: date, xDay
     * 
     * return time
     */

    public static function getTimeBeforeXDay($date = '', $xDay = 1) {
        $timeReturn = '';
        if (!empty($date)) {
            $date = str_replace(' ', '', $date);
            $date = str_replace('/', '', $date);
            $date = str_replace('-', '', $date);
            $date = str_replace(':', '', $date);

            $timeOfDate = strtotime($date);
            if (isset($xDay) && !empty($xDay) && Validation::isOnlyNumber($xDay)) {
                $timeReturn = strtotime("-" . $xDay . " days", $timeOfDate);
            }
        }

        return $timeReturn;
    }

    /*
     * Lay ngay sau ngay truyen vao (+ them so ngay)
     * Ngay truyen vao format yyyymmdd, yyyy-mm-dd, yyyy/mm/dd
     * @input: date, xDay
     * 
     * return time
     */

    public static function getTimeAfterXDay($date = '', $xDay = 1) {
        $timeReturn = '';
        if (!empty($date)) {
            $date = str_replace(' ', '', $date);
            $date = str_replace('/', '', $date);
            $date = str_replace('-', '', $date);
            $date = str_replace(':', '', $date);

            $timeOfDate = strtotime($date);
            if (isset($xDay) && !empty($xDay) && Validation::isOnlyNumber($xDay)) {
                $timeReturn = strtotime("+" . $xDay . " days", $timeOfDate);
            }
        }

        return $timeReturn;
    }

    public function makeContractCode($fkey = '', $step = '') {
        $code = 'LDHD-' . date('ymdHi') . rand(100, 999);
        if (!empty($fkey)) {
            $code = $fkey . '-' . date('ymdHi') . rand(100, 999);
        }
        if (!empty($step)) {
            $code .= '-' . $step;
        }

        return $code;
    }

    private static function __cal_business_areas_id($business_areas_id){
        if(in_array($business_areas_id, $GLOBALS['business-area-1'])){
            return 1;
        }elseif (in_array($business_areas_id, $GLOBALS['business-area-2'])){
            return 0.8;
        }elseif (in_array($business_areas_id, $GLOBALS['business-area-3'])){
            return 0.6;
        }else{
            return 0;
        }
    }
    private static function __cal_city($city, $district){
        switch ($city) {
            case "vn-hni":
                if (in_array($district, $GLOBALS['vn-hni-1'])) {
                    return 1;
                } elseif (in_array($district, $GLOBALS['vn-hni-2'])) {
                    return 0.8;
                } else {
                    return 0.7;
                }
            case "vn-hcm":
                if (in_array($district, $GLOBALS['vn-hcm-1'])) {
                    return 1;
                } elseif (in_array($district, $GLOBALS['vn-hcm-2'])) {
                    return 0.8;
                } else {
                    return 0.7;
                }
            default:
                if(in_array($city, $GLOBALS['country-1'])){
                    return 0.8;
                }
                elseif(in_array($city, $GLOBALS['country-2'])){
                    if (in_array($district, $GLOBALS['district-1'])) {
                        return 0.8;
                    }
                }
                return 0.7;
        }
    }
    private static function __cal_belong_group($belong_group, $avg_trans_value){
        $unit = 1000000;
        switch ($belong_group) {
            case 1:
                if (20 * $unit <= $avg_trans_value && $avg_trans_value <= 1000 * $unit) {
                    return [10 * $unit, 100 * $unit];
                }
                return [10 * $unit, 10 * $unit];
            case 2:
                if (20 * $unit <= $avg_trans_value && $avg_trans_value < 300 * $unit) {
                    return [10 * $unit, 150 * $unit];
                }
                return [10 * $unit, 10 * $unit];
            case 3:
                if (300 * $unit <= $avg_trans_value && $avg_trans_value < 500 * $unit) {
                    return [10 * $unit, 200 * $unit];
                } elseif (500 * $unit <= $avg_trans_value && $avg_trans_value < 1000 * $unit) {
                    return [10 * $unit, 250 * $unit];
                } elseif (1000 * $unit <= $avg_trans_value && $avg_trans_value <= 1000000 * $unit) {
                    return [10 * $unit, 300 * $unit];
                }
                return [10 * $unit, 10 * $unit];
            case 4:
                if (0 <= $avg_trans_value && $avg_trans_value < 20 * $unit) {
                    return [10 * $unit, 40 * $unit];
                }
                return [10 * $unit, 10 * $unit];
            default:
                return [10 * $unit, 10 * $unit];
        }
    }
    private static function __cal_time_loan($belong_group){
        switch ($belong_group){
            case 1:
                return 1;
            case 2:
                return 1;
            case 3:
                return 1;
            case 4:
                return 0.3;
            default:
                return 0;
        }
    }
    public function loan_money($merchant){
        $business_areas_id = $merchant['business_areas_id'];
        $city_code = $merchant['city_code'];
        $district_code = $merchant['district_code'];
        $belong_group = $merchant['belong_group'];
        $avg_trans_value = $merchant['avg_trans_value'];
        $percent_business_areas = $this->__cal_business_areas_id($business_areas_id);
        $percent_city = $this->__cal_city($city_code, $district_code);
        $amount_range = $this->__cal_belong_group($belong_group, $avg_trans_value);
        $time_load = $this->__cal_time_loan($belong_group);
        $amount = (floor($percent_business_areas*$percent_city*10)/10)*$time_load*$avg_trans_value;
        if($amount <= $amount_range[0]){
            return $amount_range[0];
        }
        if($amount >= $amount_range[1]){
            return $amount_range[1];
        }
        // Thoi gian vay: fix cung 90 ngay ~ 80%
        return round($amount * 0.8, -5);
    }
}

?>