<?php

namespace App\Utils;

class Result
{
    const OK = 200;
    const ERROR = '0002';
    const INVALID_PARAMETERS = 0003;
    const UNAUTHORIZED = 0004;
    const BAD_REQUEST = 0005;
    const VERIFICATION_CODE_EXPIRED = 1011;
    const ALREADY_VERIFIED = 1012;
    const UNKNOWN_MOBILE_NUMBER = 1013;
    const MOBILE_NUMBER_ALREADY_OCCUPIED = 1014;
    const VERIFICATION_REQUIRED = 1015;
    const VALID_PERIOD_EXPIRED = 1016;
    const MALFORMED_DATA_SUSPICOUS = 1017;
    const INVALID_VERIFICATION_CODE = 1018;
    const INVALID_ACCESS_KEY = 1019;
    const TOO_MANY_REQUESTS = 1020;
    const ACCESS_IS_EXPIRED = 1021;
    const NOT_MATCHED_USERNAME_AND_MOBILE_NUMBER = 1022;
    const ERROR_FROM_SMS_PROVIDER = 1023;
    const NO_CHANGES = 1101;
    const WRONG_PASSWORD = 1102;
    const ALREADY_REQUESTED = 1201;
    const CANNOT_PURCHASE_OWN_PRODUCT = 1202;
    const UNKNOWN_USER = 1203;
    const ALREADY_SOLD_PRODUCT = 1204;
    const SALES_STOPPED = 1205;
    const CANNOT_REPORT_OWN_PRODUCTS = 1207;
    const CANNOT_LOAN_OWN_PRODUCT = 1206;
    const USER_ALREADY_WROTE_REVIEW = 1301;
    const LOCKED_PRODUCT = 6003;
    const CANNT_ABORT = 6004;
    const PRODUCT_ARE_BEING_EVALUATED = 6005;
    const NONE_LOAN_BECAUSE_STORE_NOT_ALLOW = 6007;
    const EXCEEDED_THE_TIME_SPECIFIED = 6008;
    const BLOCKED_CONTENT = 6001;
    const USER_SUSPENDED = 6002;
    const REQUEST_SUCCESS = 200;
    const REQUEST_FALSE = 201;
    const VALIDATE_ERROR = 203;
    const TOKEN_IS_EXPIRED = 300;
    const TOKEN_IS_INVALID = 301;
    const TOKEN_IS_NOT_FOUND = 302;
    const TOKEN_BLACKLISTED_EXCEPTION = 303;
    const EVENT_DRAFT = 7001;
    const EVENT_EXPIRED = 7000;

    const GOOGLE_FIREBASE = 5010;
    const VIETTEL_SMS = 5011;
    const VN_CODE = 84;
    const INVALID_NUMBER_PHONE = 5012;
    const UPLOAD_FALSE = 8000;

    const PROFILE_CREATE_FAILED = 17001;
    const PROFILE_UPDATE_FAILED = 17002;
    const PROFILE_DELETE_FAILED = 17003;
    const PROFILE_SET_STATUS_FAILED = 17004;

    const PROFILE_LABEL_CREATE_FAILED = 17101;
    const PROFILE_LABEL_UPDATE_FAILED = 17102;
    const PROFILE_LABEL_DELETE_FAILED = 17103;
    const PROFILE_LABEL_SET_STATUS_FAILED = 17104;
    const PROFILE_LABEL_CANNOT_UPDATE_BECAUSE_OF_STATUS = 17105;
    const PROFILE_LABEL_CANNOT_SEND_BECAUSE_OF_STATUS = 17106;
    const PROFILE_LABEL_SEND_REGISTER_SUCCESS = 17107;
    const PROFILE_LABEL_SEND_REGISTER_FAILED = 17108;
    const PROFILE_LABEL_CANNOT_VERIFY_BECAUSE_OF_STATUS = 17109;
    const PROFILE_LABEL_VERIFY_REGISTER_FAILED = 17110;
    const PROFILE_LABEL_REGISTER_SUCCESS = 17111;
    const PROFILE_LABEL_REGISTER_FAILED = 17112;

    const PROFILE_LABEL_GROUP_CREATE_FAILED = 17201;
    const PROFILE_LABEL_GROUP_UPDATE_FAILED = 17202;
    const PROFILE_LABEL_GROUP_DELETE_FAILED = 17203;
    const PROFILE_LABEL_GROUP_SET_STATUS_FAILED = 17204;

    const PROFILE_LABEL_CONFIG_CREATE_FAILED = 17301;
    const PROFILE_LABEL_CONFIG_UPDATE_FAILED = 17302;
    const PROFILE_LABEL_CONFIG_DELETE_FAILED = 17303;
    const PROFILE_LABEL_CONFIG_SET_STATUS_FAILED = 17304;

    const PROFILE_LABEL_INFO_UPDATE_ACTION_FAILED = 17401;
    const PROFILE_LABEL_INFO_UPDATE_CARE_FAILED = 17402;

    const CANNOT_OPERATE_DUE_TO_INVALID_STATUS = 17900;
    const RECORDS_NOT_FOUND = 17901;
    const ID_EMPTY = 17902;
    const PROFILE_LABEL_GROUP_ID_EMPTY = 17903;
    const PROFILE_ID_EMPTY = 17904;
    const ID_AND_PROFILE_ID_EMPTY = 17905;
    const STATUS_EMPTY = 17906;
    const STATUS_INVALID = 17907;
    const GROUP_CODE_EMPTY = 17908;
    const CID_EMPTY = 17909;
    const LIST_LABEL_ERROR = 17910;
    const INPUT_LABEL_EMPTY = 17911;
    const BANK_CODE_EMPTY = 17912;
    const PAYMENT_CHANNEL_EMPTY = 17913;
    const PAYMENT_CHANNEL_INVALID = 17914;

    public static $resultMessage = [
        // self::OK => 'Ok',
        self::ERROR => 'Error',
        self::INVALID_PARAMETERS => 'Invalid paramters.',
        self::UNAUTHORIZED => 'No authorization.',
        self::BAD_REQUEST => 'Bad request.',
        self::VERIFICATION_CODE_EXPIRED => 'Verification code has been expired.',
        self::ALREADY_VERIFIED => 'Verification code has been already verified.',
        self::UNKNOWN_MOBILE_NUMBER => 'No verification code matching mobile number.',
        self::MOBILE_NUMBER_ALREADY_OCCUPIED => 'Mobile number has been already occupied.',
        self::VERIFICATION_REQUIRED => 'Mobile number verification is required.',
        self::VALID_PERIOD_EXPIRED => 'Valid period of verification has been expired.',
        self::MALFORMED_DATA_SUSPICOUS => 'Malformed verification is suspicious.',
        self::INVALID_VERIFICATION_CODE => 'Invalid verification code.',
        self::INVALID_ACCESS_KEY => 'Invalid access key.',
        self::TOO_MANY_REQUESTS => 'Too many requests.',
        self::ACCESS_IS_EXPIRED => 'Access key is expired.',
        self::NOT_MATCHED_USERNAME_AND_MOBILE_NUMBER => 'Username doesn\'t match with mobile number.',
        self::ERROR_FROM_SMS_PROVIDER => 'Error occured from SMS provider.',
        self::NO_CHANGES => 'No changes detected.',
        self::WRONG_PASSWORD => 'Password is wrong.',
        self::ALREADY_REQUESTED => 'Already requested.',
        self::CANNOT_PURCHASE_OWN_PRODUCT => 'Cannot purchase own product.',
        self::CANNOT_REPORT_OWN_PRODUCTS => 'Cannot report own product.',
        self::UNKNOWN_USER => 'Unknown user.',
        self::ALREADY_SOLD_PRODUCT => 'Already sold product.',
        self::SALES_STOPPED => 'Sales stopped.',
        self::USER_ALREADY_WROTE_REVIEW => 'User already wrote the review',
        self::BLOCKED_CONTENT => 'This resource has been blocked for abnormal use',
        self::CANNOT_LOAN_OWN_PRODUCT => 'Cannot loan own product',
        self::USER_SUSPENDED => 'Your account has been blocked for abnormal use. If you have any objection, please contact customer service.',
        self::LOCKED_PRODUCT => 'The product has been ordered by someone',
        self::CANNT_ABORT => 'Loan already send for svfc or you already abort, Should can not abort',
        self::PRODUCT_ARE_BEING_EVALUATED => ' Products are being evaluated',
        self::NONE_LOAN_BECAUSE_STORE_NOT_ALLOW => 'none loan because store not allow',
        self::EXCEEDED_THE_TIME_SPECIFIED => 'Exceeded the time specified',
        self::REQUEST_SUCCESS => 'Request success',
        self::REQUEST_FALSE => 'Request false',
        self::VALIDATE_ERROR => 'Validate error',
        self::TOKEN_IS_EXPIRED => 'Token is expired',
        self::TOKEN_IS_INVALID => 'Token is invalid',
        self::TOKEN_BLACKLISTED_EXCEPTION => 'Token can not be used, get new one',
        self::TOKEN_IS_NOT_FOUND => 'Authorization Token not found',
        self::EVENT_DRAFT => 'Event draft',
        self::EVENT_EXPIRED => 'Event already expired',
        self::GOOGLE_FIREBASE => 'Send sms otp by google firabase',
        self::VIETTEL_SMS => 'Send sms otp by viettel',
        self::INVALID_NUMBER_PHONE => 'Invalid phone unused format',
        self::UPLOAD_FALSE => 'Upload false',

        self::ID_EMPTY => 'Request id cannot be blank',
        self::RECORDS_NOT_FOUND => 'Records not found',
        self::PROFILE_ID_EMPTY => 'Profile id cannot be blank',
        self::ID_AND_PROFILE_ID_EMPTY => 'Request id and profile id is empty',
        self::STATUS_EMPTY => 'Status cannot be empty',
        self::STATUS_INVALID => 'Status invalid',
        self::GROUP_CODE_EMPTY => 'Group code cannot be empty',
        self::CID_EMPTY => 'Request cid cannot be blank',
        self::LIST_LABEL_ERROR => 'List label must be an array and cannot be empty',
        self::INPUT_LABEL_EMPTY => 'Label cannot be empty',
        self::BANK_CODE_EMPTY => 'Bank code cannot be empty',
        self::CANNOT_OPERATE_DUE_TO_INVALID_STATUS => 'Cannot perform action because current status is invalid',
        self::PROFILE_LABEL_GROUP_ID_EMPTY => 'Profile label group id cannot be blank',
        self::PAYMENT_CHANNEL_EMPTY => 'Payment channel cannot be empty',
        self::PAYMENT_CHANNEL_INVALID => 'Payment channel is invalid',
        
        self::PROFILE_LABEL_GROUP_CREATE_FAILED => 'Create profile label group failed',
        self::PROFILE_LABEL_GROUP_UPDATE_FAILED => 'Update profile label group failed',
        self::PROFILE_LABEL_GROUP_DELETE_FAILED => 'Delete profile label group failed',
        self::PROFILE_LABEL_GROUP_SET_STATUS_FAILED => 'Set status for profile label group failed',
        
        self::PROFILE_LABEL_CONFIG_CREATE_FAILED => 'Create profile label config failed',
        self::PROFILE_LABEL_CONFIG_UPDATE_FAILED => 'Update profile label config failed',
        self::PROFILE_LABEL_CONFIG_SET_STATUS_FAILED => 'Set status for profile label config failed',
        
        self::PROFILE_LABEL_CREATE_FAILED => 'Create profile label failed',
        self::PROFILE_LABEL_UPDATE_FAILED => 'Update profile label failed',
        self::PROFILE_LABEL_DELETE_FAILED => 'Delete profile label failed',
        self::PROFILE_LABEL_CANNOT_UPDATE_BECAUSE_OF_STATUS => 'Cannot update profile label because status is not valid',
        self::PROFILE_LABEL_CANNOT_SEND_BECAUSE_OF_STATUS => 'Cannot send register of profile because status is invalid',
        self::PROFILE_LABEL_CANNOT_VERIFY_BECAUSE_OF_STATUS => 'Cannot verify register of profile because status is invalid',
        self::PROFILE_LABEL_SET_STATUS_FAILED => 'Set status for profile label failed',
        self::PROFILE_LABEL_SEND_REGISTER_SUCCESS => 'Send register of profile label success',
        self::PROFILE_LABEL_SEND_REGISTER_FAILED => 'Send register of profile label failed',
        self::PROFILE_LABEL_VERIFY_REGISTER_FAILED => 'Verify register of profile label failed',
        self::PROFILE_LABEL_REGISTER_SUCCESS => 'Register profile label success',
        self::PROFILE_LABEL_REGISTER_FAILED => 'Register profile label failed',
        
        self::PROFILE_CREATE_FAILED => 'Create profile failed',
        self::PROFILE_UPDATE_FAILED => 'Update profile failed',
        self::PROFILE_DELETE_FAILED => 'Delete profile failed',
        self::PROFILE_SET_STATUS_FAILED => 'Set status for profile failed',
        self::PROFILE_LABEL_INFO_UPDATE_ACTION_FAILED => 'Update profile action info failed',
        self::PROFILE_LABEL_INFO_UPDATE_CARE_FAILED => 'Update profile care info failed',
    ];
}
