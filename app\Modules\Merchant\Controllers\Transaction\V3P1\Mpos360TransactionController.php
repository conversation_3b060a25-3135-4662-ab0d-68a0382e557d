<?php

namespace App\Modules\Merchant\Controllers\Transaction\V3P1;

use App\Lib\Helper;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionHistoryRequest;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionHistoryForHomeRequest;
use App\Modules\Merchant\Actions\Transaction\V3P1\Mpos360TransactionHistoryAction\Mpos360TransactionHistoryAction;
use App\Modules\Merchant\Actions\Transaction\V3P1\Mpos360TransactionHistoryForHomeAction\Mpos360TransactionHistoryForHomeAction;

class Mpos360TransactionController extends Controller
{
	public function Mpos360TransactionHistory(Mpos360TransactionHistoryRequest $request)
	{
		try {
			$result = app(Mpos360TransactionHistoryAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360TransactionHistoryForHome(Mpos360TransactionHistoryForHomeRequest $request)
	{
		try {
			$result = app(Mpos360TransactionHistoryForHomeAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
} // End class
