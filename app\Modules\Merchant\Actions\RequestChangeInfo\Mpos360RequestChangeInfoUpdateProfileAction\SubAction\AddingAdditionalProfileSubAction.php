<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\SubAction;

use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileRequest;

class AddingAdditionalProfileSubAction
{
	public function run(Mpos360RequestChangeInfoUpdateProfileRequest $request)
	{
		$additionalProfiles = [];
/* -------------------- additionanl for đổi hẳn người đại diện mới -------------------- */
		if ($request->isDoiNguoiDaiDienMoi()) {
			$profilesFromRequest = $request->json('data.request_change_info.profiles');

			// add thêm các trường cần show ra form đổi người đại diện mới
			$additionalProfiles = collect($profilesFromRequest)->filter(function ($item) {
				return $item['profileKey'] == 'authoriserContactNumber' || $item['profileKey'] == 'authoriserEmail';
			})->map(function ($item) {
				$mapItem = [
					'profileKey' => $item['profileKey'],
					'value' => $item['value'],
					'label' => $item['label'],
					'other_data' => (object) [],
				];
				return $mapItem;
			})->push([
				'profileKey' => 'representPosition',
				'value' => '',
				'label' => 'Chức vụ của người đại diện mới',
				'other_data' => (object) [],
			])->push([
				'profileKey' => 'representMutualRelation',
				'value' => '',
				'label' => 'Mối quan hệ giữa người đại diện và người ủy quyền.',
				'other_data' => (object) [],
			])->push([
				'profileKey' => 'identificationDocument',
				'value' => '',
				'label' => 'Giấy ủy quyền (nếu có)',
				'other_data' => [
					// Quản lý slot update và có tên của từng slot trên mobile app
					'list' => [
						[
							'value' => 'giay_uy_quyen',
							'label' => 'Giấy ủy quyền',
							'slot' => [
								[
									'profileKey' => 'substituteCertUrls',
									'label' => 'Chụp giấy ủy quyền'
								],
							]
						],
					]
				]
			])
				->values()
				->toArray();
		}
/* -------------------- additionanl for đổi thông tin ngân hàng -------------------- */
		if ($request->isChangeThongTinNganHang()) {
			$additionalProfiles = collect($additionalProfiles)->push([
				'profileKey' => 'positionAuthBank',
				'value' => '',
				'label' => vmsg('Chức vụ của người ủy quyền tại ngân hàng'),
				'other_data' => (object) []
			])->push([
				'profileKey' => 'bankMutualRelation',
				'value' => '',
				'label' => vmsg('Mối quan hệ giữa chủ tài khoản và người có thẩm quyền tại ngân hàng'),
				'other_data' => (object) []
			])->push([
				'profileKey' => 'identificationDocument',
				'value' => '',
				'label' => vmsg('CCCD/GP lái xe/Hộ khẩu/Hộ chiếu'),
				'other_data' => [
					// Quản lý slot update và có tên của từng slot trên mobile app
					'list' => [
						[
							'value' => 'cccd',
							'label' => vmsg('CCCD'),
							'slot' => [
								[
									'profileKey' => 'passportRepresentFrontUrl',
									'label' => vmsg('Ảnh chụp CCCD mặt trước')
								],

								[
									'profileKey' => 'passportRepresentBackUrl',
									'label' => vmsg('Ảnh chụp CCCD mặt sau')
								],
							]
						],

						[
							'value' => 'gp_lai_xe',
							'label' => vmsg('GP lái xe'),
							'slot' => [
								[
									'substituteCertUrls' => 'substituteCertUrls',
									'label' => vmsg('GPLX mặt trước')
								],

								[
									'substituteCertUrls' => 'substituteCertUrls',
									'label' => vmsg('GPLX mặt sau')
								],
							],
						],

						[
							'value' => 'ho_chieu',
							'label' => vmsg('Hộ chiếu'),
							'slot' => [
								[
									'profileKey' => 'substituteCertUrls',
									'label' => vmsg('Ảnh 1')
								],

								[
									'profileKey' => 'substituteCertUrls',
									'label' => vmsg('Ảnh 2')
								],

								[
									'profileKey' => 'substituteCertUrls',
									'label' => vmsg('Ảnh 3')
								],
								[
									'profileKey' => 'substituteCertUrls',
									'label' => vmsg('Ảnh 4')
								],
							]
						],

						[
							'value' => 'ho_khau',
							'label' => vmsg('Hộ khẩu'),
							'slot' => [
								[
									'profileKey' => 'substituteCertUrls',
									'label' => vmsg('Ảnh 1')
								],

								[
									'profileKey' => 'substituteCertUrls',
									'label' => vmsg('Ảnh 2')
								],

								[
									'profileKey' => 'substituteCertUrls',
									'label' => vmsg('Ảnh 3')
								],
								[
									'profileKey' => 'substituteCertUrls',
									'label' => vmsg('Ảnh 4')
								],
							]
						],
					]
				]
			])->values()->toArray();
		}

		return $additionalProfiles;
	} // End method
} // End class
