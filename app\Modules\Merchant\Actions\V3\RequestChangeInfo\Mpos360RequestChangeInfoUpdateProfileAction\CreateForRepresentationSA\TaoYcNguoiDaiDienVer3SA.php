<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForRepresentationSA;

use App\Exceptions\BusinessException;
use App\Lib\partner\MNPExtend;
use Exception;
use Illuminate\Support\Facades\DB;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\DTOs\RequestChangeInfo\CreateMerchantRequestDto;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoUpdateProfileRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\SubAction\CreateMerchantRequestSubAction;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForRepresentationSA\SA\AddAdditionalProfileV3SA;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForRepresentationSA\SubAction\ValidateHoSoRiengLeSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\GetMNPAccessTokenSubAction;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForRepresentationSA\SA\GetThongTinLienHeThucSuThayDoiSA;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForRepresentationSA\SA\TaoThayDoiTaiKhoanMposSA;

class TaoYcNguoiDaiDienVer3SA
{
	public MNPExtend $mnpExtend;

	public $thongTinLienHeThucSuThayDoi;

	public function __construct(MNPExtend $mnpExtend)
	{
		$this->mnpExtend = $mnpExtend;	
	}

	public function run(
		CreateMerchantRequestDto $createMerchantRequestDto,
		Mpos360RequestChangeInfoUpdateProfileRequest $request
	) {
		$verificationProfiles = $request->json('data.request_change_info.profiles');
/* --------------------------- Đổi thông tin liên hệ -------------------------- */
		if ($request->isDoiThongTinLienHe()) {
			$isThayDoiEmail = collect($verificationProfiles)->contains(function ($item) {
				return $item['profileKey'] == 'authoriserEmail';
			});

			$isThayDoiMobile = collect($verificationProfiles)->contains(function ($item) {
				return $item['profileKey'] == 'authoriserContactNumber';
			});

			if (empty($isThayDoiEmail) && empty($isThayDoiMobile)) {
				throw new BusinessException(__('dttv3.Lỗi: ít nhất bạn phải thay đổi Email hoặc SĐT'));
			}

			$dataThayDoiThongTin = [
				'typeChangeRepresent' => 'CHANGE_CURRENT_REPRESENT_INFO' // vẫn là ng đại diện cũ
			];

			if ($isThayDoiEmail) {
				$emailProfile = collect($verificationProfiles)->where('profileKey', 'authoriserEmail')->first();
				app(ValidateHoSoRiengLeSubAction::class)->run('authoriserEmail', $emailProfile['value'] ?? '');
			}

			if ($isThayDoiMobile) {
				$mobileProfile = collect($verificationProfiles)->where('profileKey', 'authoriserContactNumber')->first();
				app(ValidateHoSoRiengLeSubAction::class)->run('authoriserContactNumber', $mobileProfile['value'] ?? '');
			}

			$deviceSession = $request->getCurrentDeviceSession();
			$deviceSessionWithToken = app(GetMNPAccessTokenSubAction::class)->run($deviceSession);
			$thongTinLienHeThucSuThayDoi = app(GetThongTinLienHeThucSuThayDoiSA::class)->run(
				$deviceSessionWithToken,
				$emailProfile ?? [],
				$mobileProfile ?? []
			);


			$dataThayDoiThongTin = array_merge($dataThayDoiThongTin, $thongTinLienHeThucSuThayDoi['dataThayDoiThongTin']);
			$createMerchantRequestDto->setProfiles($dataThayDoiThongTin);
			$createMerchantRequestDto->setRequestVerifyIntoDataRequest($thongTinLienHeThucSuThayDoi['requestVerifies']);

			$verificationProfiles = collect($verificationProfiles)->filter(function ($item) {
				return $item['profileKey'] == 'authoriserEmail' || $item['profileKey'] == 'authoriserContactNumber';
			})->values()->all();

			// Kiểm tra bắt validate
			if (!empty($request->json('data.replace_mpos_account_field'))) {
				$this->thongTinLienHeThucSuThayDoi = $thongTinLienHeThucSuThayDoi;
				$r = $this->mnpExtend->validateEmailAndMobile(
					$deviceSessionWithToken->mnp_token,
					$thongTinLienHeThucSuThayDoi['isChangeEmail'] ?? '',
					$thongTinLienHeThucSuThayDoi['isChangeMobile'] ?? ''
				);

				if (empty($r['result'])) {
					$msg =  $r['message'] ?? 'Can not query to mnp system';
					$err = sprintf('MNP Err: %s (Code: %s)', $msg, $r['code'] ?? '00');
					throw new BusinessException($err);
				}
			}
		}
/* --------------------------- Đổi CCCD mới (người đại diện vẫn thế) -------------------------- */
		if ($request->isDoiCccdMoi()) {
			$verificationProfiles = collect($verificationProfiles)->filter(function ($item) {
				return $item['profileKey'] == 'passport';
			})->values()->toArray();

			if (empty($verificationProfiles)) {
				throw new BusinessException(__('dttv3.CCCD mới là bắt buộc'));
			}

			$createMerchantRequestDto->setProfiles([
				'typeChangeRepresent' => 'CHANGE_CURRENT_REPRESENT_INFO' // vẫn là người đại diện cũ
			]);
		}
/* --------------------------- Đổi hẳn người đại diện mới -------------------------- */
		if ($request->isDoiNguoiDaiDienMoi()) {
			$hasPassport = collect($verificationProfiles)->first(function ($item) {
				return $item['profileKey'] == 'passport';
			});

			if (empty($hasPassport['value'])) {
				throw new BusinessException(__('dttv3.CCCD của người đại diện mới là bắt buộc'));
			}

			$hasEmail = collect($verificationProfiles)->first(function ($item) {
				return $item['profileKey'] == 'authoriserEmail';
			});

			if (empty($hasEmail['value'])) {
				throw new BusinessException(__('dttv3.Email người đại diện là bắt buộc'));
			}

			$hasMobile = collect($verificationProfiles)->first(function ($item) {
				return $item['profileKey'] == 'authoriserContactNumber';
			});

			if (empty($hasMobile['value'])) {
				throw new BusinessException(__('dttv3.SĐT người đại diện là bắt buộc'));
			}

			$verificationProfiles = collect($verificationProfiles)->filter(function ($item) {
				return $item['profileKey'] == 'authoriserEmail' || $item['profileKey'] == 'authoriserContactNumber';
			})->values()->all();

			$createMerchantRequestDto->setProfiles([
				'typeChangeRepresent' => 'CHANGE_NEW_REPRESENT' // type đổi người mới
			]);
		}
/* --------------------------- Vào luồng tạo yc -------------------------- */
		DB::beginTransaction();
		try {
			// Tạo bản ghi yêu cầu
			$mpos360McRequest = app(CreateMerchantRequestSubAction::class)->run($createMerchantRequestDto->toArray());
			$savedResult = $mpos360McRequest->save();

			if (!$savedResult) {
				throw new BusinessException(__('dttv3.Lỗi không tạo được bản ghi yêu cầu thay đổi'));
			}

			// Xử lý case tài khoản đăng nhập mpos
			$replaceMposFields = $request->json('data.replace_mpos_account_field');
			
			if (!empty($replaceMposFields)) {
				$replaceMposFields = [];

				if ($this->thongTinLienHeThucSuThayDoi['isChangeEmail']) {
					$replaceMposFields[] = 'authoriserEmail';
				}

				if ($this->thongTinLienHeThucSuThayDoi['isChangeMobile']) {
					$replaceMposFields[] = 'authoriserContactNumber';
				}

				if (empty($replaceMposFields)) {
					throw new BusinessException('Hệ thống không phát hiện ra thông tin Email mới hoặc SĐT mới');
				}
			}

			$mpos360McRequest = app(TaoThayDoiTaiKhoanMposSA::class)->run(
				$mpos360McRequest,
				$replaceMposFields,
				$request->json('data.request_change_info.profiles')
			);

			$can = '';

			if ($request->isDoiThongTinLienHe()) {
				$can = Mpos360Enum::MPOS360_CAN_NEED_VERIFY_OTP;
			}

			if ($request->isDoiCccdMoi()) {
				$can = Mpos360Enum::MPOS360_CAN_GOTO_SDK_FLOW;
			}

			if ($request->isDoiNguoiDaiDienMoi()) {
				$can = Mpos360Enum::MPOS360_CAN_GOTO_SDK_FLOW;
			}

			$returnData = [
				'id' => $mpos360McRequest->id,
				'request_change_info_id' => $mpos360McRequest->mynextpay_id,
				'status' => 'SUCCESS',
				'msg' => __('dttv3.Bạn cần xác minh otp'),
				'can' => $can,
				'additional_profiles' => app(AddAdditionalProfileV3SA::class)->run($mpos360McRequest),
				'verification_profiles' => $verificationProfiles,
				'list_sign_method' => [],
				'scan_method' => []
			];

			DB::commit();
			return $returnData;
		} catch (\Throwable $th) {
			DB::rollBack();
			throw $th;
		}
	} // end method
} // End class