<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoGetOtpAction\SubAction;

use Exception;
use App\Lib\OtpHelper;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Model\Mpos360CodeOtp;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoGetOtpRequest;

class CreateOtpSubAction
{
	public function run(
		Mpos360MerchantRequest $merchantRequest,
		DeviceSession $deviceSession,
		Mpos360RequestChangeInfoGetOtpRequest $request
	): Mpos360CodeOtp {

		$otp = false;

		while (!$otp) {
			$otpGenerate = generateRandomNumber();

			$otpGenerateIsExist = Mpos360CodeOtp::query()
				->where('otp', $otpGenerate)
				->where('time_out', '>=', now()->timestamp)
				->first();

			if (!$otpGenerateIsExist) {
				$otp = $otpGenerate;
			}
		}

		$mpos360CodeOtp = Mpos360CodeOtp::query()->forceCreate([
			'command_code' => $request->json('data.command_code'),
			'service_code' => $request->json('data.service_code'),
			'user_id'      => $deviceSession->user_id,
			'obj_value'    => $request->json('data.object_value'),
			'message'      => base64_encode('Noi dung'),
			'otp'          => $otp,
			'reference_id' => $merchantRequest->id,
			'status'       => 1,
			'time_out'     => now()->addSeconds(OtpHelper::getSoGiayCountdown())->timestamp,
			'time_created' => now()->timestamp,
			'time_updated' => now()->timestamp,
		]);

		if (!$mpos360CodeOtp) {
			throw new BusinessException('Lỗi không tạo được bản ghi OTP');
		}

		// Đoạn này bắn sang bên OTP

		return $mpos360CodeOtp;
	}
} // End clas