<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\SubAction;

use Exception;
use App\Lib\partner\VMMC;
use Illuminate\Support\Str;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoGetConfigAction\Mpos360RequestChangeInfoGetConfigAction;

class ValidateBankAccountSubAction
{

	public VMMC $vmmc;

	public function __construct(VMMC $vmmc)
	{
		$this->vmmc = $vmmc;
	}

	public function run(string $mnpBankId, string $accountNo, string $holderName)
	{
		$mnpConfig = app(Mpos360RequestChangeInfoGetConfigAction::class)->run();
		
		
		$vmmcBankId = $mnpConfig['data']['bankMnpVimo'][$mnpBankId] ?? '';
		
		$params = [
			'bank_account_holder' => $holderName,
			'bank_account' => $accountNo,
			'bank_id' => $vmmcBankId
		];

		$checkBankInfoResult = $this->vmmc->checkBankAccountMerchant($params);
		mylog(['Ket qua check' => $checkBankInfoResult]);

		if (empty($checkBankInfoResult['status_code_partner'])) {
			throw new BusinessException('Lỗi không kiểm tra được thông tin ngân hàng');
		}

		$code = $checkBankInfoResult['status_code_partner'];

		if ($code == '00') {
			if (
				!empty($checkBankInfoResult['data'])
				&& Str::contains($checkBankInfoResult['data']['bank_account_holder'], $params['bank_account_holder'])
			) {
				return true;
			}
		}

		throw new BusinessException('Lỗi không tìm được thông tin TKNH');
	}
} // End class