<?php

namespace App\Modules\Merchant\Requests\RequestChangeInfo\V3;

use Illuminate\Validation\Rule;
use App\Modules\Merchant\Requests\MerchantRequest;
use App\Modules\Merchant\Rules\RequestChangeInfoRule\GroupProfileRequiredRule;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Traits\UpdateProfileValidateTrait;

class Mpos360RequestChangeInfoUpdateProfileRequest extends MerchantRequest
{
	use UpdateProfileValidateTrait;

	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}


	public function rules()
	{
		$code = $this->json('data.request_change_info.code');

		$rules = [
			'data.email' => ['required', 'string', 'email'],
			'data.request_change_info' => ['required', 'array'],
			'data.request_change_info.code' => [
				'required',
				'string',
				'max:255',
				Rule::in([
					'CHANGE_BANK_ACCOUN_INFO',
					'CHANGE_REPRESENT_INFO'
				])
			],
			'data.request_change_info.name' => ['required', 'string', 'max:500'],
			'data.request_change_info.profiles' => ['required', 'array', new GroupProfileRequiredRule($code)],
			'data.request_change_info.profiles.*.profileKey' => ['required', 'string', 'max:255'],
			'data.request_change_info.profiles.*.value' => ['present', 'string', 'max:255'],
		];

		// Rule cho đổi TKNH
		if ($code == 'CHANGE_BANK_ACCOUN_INFO') {
			$rules['data.choice'] = [
				'required',
				'string',
				Rule::in([
					// HKD
					'DOI_STK_KHAC_CUA_HKD',
					'DOI_SO_TKNH_CA_NHAN_DUOC_HKD_UY_QUYEN',

					// Doanh nghiep
					'DOI_STK_KHAC_CUA_DOANH_NGHIEP',
					'DOI_SO_TKNH_CA_NHAN_DUOC_DOANH_NGHIEP_UY_QUYEN'
				])
			];

			$rules['data.request_vefify'] = ['array', 'required'];
			$rules['data.request_vefify.*.field'] = ['required', 'string'];
			$rules['data.request_vefify.*.value'] =  ['required', 'string'];
			$rules['data.request_vefify.*.status_verify'] = ['required', 'numeric', Rule::in(['1', '0', 1, 0])];
			$rules['data.request_vefify.*.date_verify'] = ['required', 'numeric', 'integer'];
		}

		// Rule đổi người đại diện
		if ($code == 'CHANGE_REPRESENT_INFO') {
			$rules['data.choice'] = [
				'required',
				'string',
				Rule::in([
					'DOI_NGUOI_DAI_DIEN_MOI',
					'DOI_CCCD_MOI',
					'DOI_THONG_TIN_LIEN_HE',
				])
			];

			$rules['data.sub_choice'] = [
				'required',
				'string',
				Rule::in([
					'DOI_NGUOI_DAI_DIEN_MOI_HKD',
					'DOI_CCCD_MOI_HKD',
					'DOI_THONG_TIN_LIEN_HE_HKD',

					'DOI_NGUOI_DAI_DIEN_MOI_DN',
					'DOI_CCCD_MOI_DN',
					'DOI_THONG_TIN_LIEN_HE_DN',
				])
			];

			$rules['data.replace_mpos_account_field'] = ['present', 'array'];

			$rules['data.replace_mpos_account_field.*'] = [
				'nullable',
				'string',
				Rule::in(['authoriserContactNumber', 'authoriserEmail'])
			];
		}

		return $rules;
	}

	public function messages()
	{
		return [
			'data.choice.in' => __('Loại thay đổi không đúng định dạng'),
			'data.request_vefify.required' => __('Danh sách trường đã xác thực là bắt buộc'),
			'data.request_vefify.*.field.required' => __('Trường dữ liệu xác thực là bắt buộc'),
			'data.request_vefify.*.value.required' => __('Giá trị dữ liệu xác thực là bắt buộc'),
			'data.request_vefify.*.status_verify.required' => __('Trạng thái xác thực là bắt buộc'),
			'data.request_vefify.*.date_verify.required' => __('Thời gian xác thực là bắt buộc'),
			'data.sub_choice.required' => __('Subchoice là bắt buộc'),
		];
	}

/*---------------------------TKNH-------------------------*/
	public function isChangeThongTinNganHang(): bool
	{
		return $this->json('data.request_change_info.code') == 'CHANGE_BANK_ACCOUN_INFO';
	}

	public function isDoiSTKKhacCuaHKD(): bool
	{
		return $this->isChangeThongTinNganHang() && $this->json('data.choice') == 'DOI_STK_KHAC_CUA_HKD';
	}

	public function isDoiSTKCuaCaNhanDuocHKDUyQuyen(): bool
	{
		return $this->isChangeThongTinNganHang() && $this->json('data.choice') == 'DOI_SO_TKNH_CA_NHAN_DUOC_HKD_UY_QUYEN';
	}

	public function isDoiSTKKhacCuaDoanhNghiep(): bool
	{
		return $this->isChangeThongTinNganHang() && $this->json('data.choice') == 'DOI_STK_KHAC_CUA_DOANH_NGHIEP';
	}

	public function isDoiSTKCuaCaNhanDuocDoanhNghiepUyQuyen(): bool
	{
		return $this->isChangeThongTinNganHang() && $this->json('data.choice') == 'DOI_SO_TKNH_CA_NHAN_DUOC_DOANH_NGHIEP_UY_QUYEN';
	}
/*---------------------------Người đại diện-------------------------*/
	public function isChangeNguoiDaiDien(): bool
	{
		return $this->json('data.request_change_info.code') == 'CHANGE_REPRESENT_INFO';
	}

	public function isDoiThongTinLienHe(): bool
	{
		return $this->isChangeNguoiDaiDien() && $this->json('data.choice') == 'DOI_THONG_TIN_LIEN_HE';
	}

	public function isDoiCccdMoi(): bool
	{
		return $this->isChangeNguoiDaiDien() && $this->json('data.choice') == 'DOI_CCCD_MOI';
	}

	public function isDoiNguoiDaiDienMoi(): bool
	{
		return $this->isChangeNguoiDaiDien() && $this->json('data.choice') == 'DOI_NGUOI_DAI_DIEN_MOI';
	}

	public function isDoiNguoiDaiDienMoiHKD(): bool
	{
		return $this->isDoiNguoiDaiDienMoi() && $this->json('data.sub_choice') == 'DOI_NGUOI_DAI_DIEN_MOI_HKD';
	}

	public function isDoiNguoiDaiDienMoiDoanhNghiep(): bool
	{
		return $this->isDoiNguoiDaiDienMoi() && $this->json('data.sub_choice') == 'DOI_NGUOI_DAI_DIEN_MOI_DN';
	}
/*--------------------------Method tiện ích--------------------------*/
	public function getMnpGroupCode(): string
	{
		return $this->json('data.request_change_info.code');
	}

	public function getMnpProfiles(): array
	{
		return $this->json('data.request_change_info.profiles');
	}

	public function getEmailNguoiDaiDienMoi(): string
	{
		$collect = collect($this->json('data.request_change_info.profiles'));
		return $collect->where('profileKey', 'authoriserEmail')->first()['value'];
	}

	public function getMobileNguoiDaiDienMoi(): string
	{
		$collect = collect($this->json('data.request_change_info.profiles'));
		return $collect->where('profileKey', 'authoriserContactNumber')->first()['value'];
	}

	protected function passedValidation()
	{
		$params = $this->all();

		foreach ($params['data']['request_change_info']['profiles'] as &$profile) {
			if (is_string($profile['value']) && $profile['type'] == 'INPUT') {
				$profile['value'] = cleanXSS($profile['value']);
			}
		}

		if (!empty($params['data']['request_vefify'])) {
			foreach ($params['data']['request_vefify'] as &$p) {
				if (is_string($p['value'])) {
					$p['value'] = cleanXSS($p['value']);
				}
			}
		}

		$this->merge($params);
	}
} // End class
