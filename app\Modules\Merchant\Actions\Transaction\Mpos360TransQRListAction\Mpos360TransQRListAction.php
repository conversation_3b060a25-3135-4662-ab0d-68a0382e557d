<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransQRListAction;

use Carbon\Carbon;
use App\Lib\partner\MPOS;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use App\Lib\DBConnectionHelper;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransQRListRequest;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransQRListAction\Mpos360TransQRListSubAction\TransQRGetDataSubAction;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransQRListAction\Mpos360TransQRListSubAction\TransQRMappingSubAction;

class Mpos360TransQRListAction
{
	public MPOS $mpos;

	public array $rangeTime = [];

	public function __construct(MPOS $mpos)
	{
		$this->mpos = $mpos;
	}

	public function run(Mpos360TransQRListRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		DBConnectionHelper::closeIfExist();

		$d = $request->getStartDateEndDate();
		$returnData = [];

		
		$params = [
			'typeTransaction' => 'STATISTIC_QR',
			"serviceName" => "STATISTIC_TRANSACTION",
			'merchantFk' => $deviceSession->getMerchantId(),
			'tokenLogin' => $deviceSession->getMposToken(),
			"startDate" => $d['startDate'],
			"endDate" => $d['endDate'],
			"pageSize" => $request->json('data.limit', 20),
			"pageIndex" => $request->getPageIndex(),
		];

		if ($params['pageIndex'] == 0) {
			$params['pageIndex'] = 1;
		}

		$transactionMethod = $request->json('data.transaction_method');
		// not empty và không có option Tất cả thì mới truyền lên
		if (!empty($transactionMethod) && !Str::contains($transactionMethod, 'ALL')) {
			$params['issuerCode'] = $transactionMethod;		
		}

		$transaction_status = $request->json('data.transaction_status', '');
		if (!empty($transaction_status) && $transaction_status != 'ALL') {
			$params['transactionStatus'] = $transaction_status;
		}

		$listTransaction = app(TransQRGetDataSubAction::class)->getData($params);
		$returnData['filter'] = $this->getFilter();
		$returnData['data'] = $listTransaction;
		
		if (!empty($listTransaction)) {
			foreach ($listTransaction as $tranByDate) {
				foreach ($tranByDate['list'] as $tr) {
					$this->rangeTime[] = Carbon::createFromFormat('d/m/Y H:i:s', $tr['raw_date'])->format('d-m-Y');
				}
			}
		}

		$params['rangeTime'] = implode(',', array_unique($this->rangeTime));

		$params['email'] = $deviceSession->getMerchantEmail();
		$returnData['other_data']['countSumFilter'] = Arr::only($params, [
			'typeTransaction',
			'merchantFk',
			'issuerCode',
			'transactionStatus',
			'rangeTime',
			'email'
		]);
		return $returnData;
	}

	public function mappingStatus()
	{
		$statusArr = app(TransQRMappingSubAction::class)->mappingStatusTransaction();
		$filterStatus = [
			'key' => 'transaction_status',
			'name' => __('gdqr.Trạng thái giao dịch'),
			'list' => [
				[
					'value' => 'ALL',
					'label' => __('gdqr.Tất cả')
				],
			]
		];
		$mapStatus = collect($statusArr)->values()->toArray();
		foreach ($mapStatus as $it) {
			$filterStatus['list'][] = [
				'value' => $it['key'],
				'label' => $it['label']
			];
		}

		$filter = $filterStatus;
		return $filter;
	}

	public function mappingType()
	{
		$filterType = [
			'key' => 'transaction_method',
			'name' => __('gdqr.Phương thức thanh toán'),
			'list' => [
				["label" => "Tất cả", "value" => "ALL"],
				// ["label" => "ZALOPAY", "value" => "ZALOPAY"],
				// ["label" => "MOMO", "value" => "MOMO"],
				// ["label" => "VAQR", "value" => "VAQR"],
				// ["label" => "AIRPAY", "value" => "AIRPAY"],
				// ["label" => "BANKQR", "value" => "BANKQR"],
				// ["label" => "VIETTELQR", "value" => "VIETTELQR"],
				// ["label" => "NHBQR", "value" => "NHBQR"],
				// ["label" => "WECHAT", "value" => "WECHAT"],
				// ["label" => "VISA", "value" => "VISA"],
				// ["label" => "VISA_LOCAL", "value" => "VISA_LOCAL"],
				// ["label" => "MASTER", "value" => "MASTER"],
				// ["label" => "MASTER_LOCAL", "value" => "MASTER_LOCAL"],
				// ["label" => "VIMOQR", "value" => "VIMOQR"],
				// ["label" => "VAYMUONQR", "value" => "VAYMUONQR"],
				// ["label" => "SMARTPAY", "value" => "SMARTPAY"],
				// ["label" => "TCB", "value" => "TCB"],
				// ["label" => "SHOPEE_PAY", "value" => "SHOPEE_PAY"],
				// ["label" => "PAYON_QR", "value" => "PAYON_QR"],
				// ["label" => "VCBQR", "value" => "VCBQR"],
				// ["label" => "EXIMQR", "value" => "EXIMQR"],
				// ["label" => "MPOS", "value" => "MPOS"],
				// ["label" => "VINID", "value" => "VINID"],
				// ["label" => "MSBQR", "value" => "MSBQR"],
				// ["label" => "VIETQR", "value" => "VIETQR"],
				// ["label" => "HALOOQR", "value" => "HALOOQR"],
				// ["label" => "HANAQR", "value" => "HANAQR"]
			]
		];
		
		return $filterType;
	}

	public function mappingDate()
	{
		$transTypeArr = app(TransQRMappingSubAction::class)->mappingDate();
		return $transTypeArr;
	}

	public function getFilter()
	{
		$filter[] = $this->mappingDate();
		$filter[] = $this->mappingType();
		$filter[] = $this->mappingStatus();

		$filter = collect($filter)->map(function ($fil) {
			$fil['other_data'] = [];
			if ($fil['key'] == 'transaction_method') {
				$fil['other_data'] = [
					'multiple_select' => '1'
				];
			}

			$fil['other_data'] = (object) $fil['other_data'];
			return $fil;
		})->values()->all();
		
		return $filter;
	}
} // End class
