<?php

namespace App\Modules\Merchant\Actions\TingBox\Mpos360GetStepKhaiBaoTingBoxAction;

use App\Lib\Mpos360UrlHelper;
use App\Lib\MnpOnboardNewMcHelper;
use App\Modules\TingBoxVAMC\Models\MerchantBank;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use App\Modules\Merchant\Requests\TingBox\Mpos360GetStepKhaiBaoTingBoxRequest;
use App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360ListDiemBanAction\SubAction\DongBoTknhTrungGianSubAction\DongBoTknhSubAction;

class Mpos360GetStepKhaiBaoTingBoxVamcAction
{
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run(Mpos360GetStepKhaiBaoTingBoxRequest $request)
	{
		$merchantId = $request->json('data.merchantId');

		$detailMc = $this->mnpOnboardNewMcHelper->detailMc(['mposMcId' => $merchantId]);
		app(DongBoTknhSubAction::class)->run($detailMc);
		
		$returnData = [
			'shopInfo' =>(object) [],
			'title' => '',
			'steps' => [
				[
					'stepNumber' => '1',
					'stepCode' => 'KhaiBaoCuaHang',
					'stepAction' => 'YES',
					'name' => 'Khai báo cửa hàng',
					'active' => 'YES',
					'values' => [],
					'listBank' => (object) []
				],
				[
					'stepNumber' => '2',
					'stepCode' => 'GanLoaTingBox',
					'stepAction' => 'YES',
					'name' => 'Gán loa TingBox',
					'active' => 'NO',
					'values' => [],
					'listBank' => (object) []
				],
				[
					'stepNumber' => '3',
					'stepCode' => 'KhaiBaoStkNhanTien',
					'stepAction' => 'YES',
					'name' => 'Khai báo tài khoản nhận tiền',
					'active' => 'NO',
					'values' => [],
					'listBank' => [
						'direct' => [],
						'inter' => []
					]
				],
			],
			'other_data' => (object) [],
		];

		if (!empty($detailMc['data']['bankId'])) {
			$returnData['steps'] = collect($returnData['steps'])->map(function ($item) use ($detailMc) {
				if ($item['stepNumber'] == '3') {
					$item['icon'] = cumtomAsset('images/tingbox/stepKhaiBao/stepActive.svg');
					$item['active'] = 'YES';
					$item['stepAction'] = 'NO';
					$item['values'] = [
						[
							'title' => $detailMc['data']['accountNo'],
							'desc' => sprintf('%s%s%s', $detailMc['data']['holderName'], PHP_EOL, $detailMc['data']['bankName']),
							'icon' => '',
							'other_data' => (object) []
						]
					];
				}
				return $item;
			})->values()->all();
		}

		if (!empty($detailMc['data']['locations'])) {
			$defaultLocation = $detailMc['data']['locations'][0];
			$returnData['shopInfo'] = [
				'shopId' => $defaultLocation['id'],
				'shopName' => $defaultLocation['areaName'],
				'address' => $defaultLocation['areaAddress'],
				'cityId' => $defaultLocation['areaCityCode'],
				'districtId' => $defaultLocation['areaDistrictCode'],
				'industryId' => $defaultLocation['mcc'],
				'qrDisplayName' => $detailMc['data']['qrDisplayName'] ?? ''
			];

			$returnData['steps'] = collect($returnData['steps'])->map(function ($item) use ($defaultLocation) {
				if ($item['stepNumber'] == '1') {
					$item['active'] = 'YES';
					$item['values'] = [
						[
							'title' => $defaultLocation['areaName'],
							'desc' => sprintf('%s, %s, %s', $defaultLocation['areaAddress'], $defaultLocation['areaDistrict'], $defaultLocation['areaCity']),
							'icon' => '',
							'other_data' => (object) []
						]
					];

					// Kiểm tra xem đã có thiết bị hay chưa? Có rồi thì sẽ không có action Sửa nữa
					if (!empty($defaultLocation['deviceDTOs'])) {
						$item['stepAction'] = 'NO';
					}
				}
				return $item;
			})->values()->all();

			// Bắt có thiết bị hay không
			$listTingBoxDevice = $defaultLocation['deviceDTOs'];
			
			if (!empty($listTingBoxDevice)) {
				$stepValues = [];

				foreach ($listTingBoxDevice as $dv) {
					$url = Mpos360UrlHelper::buildTingBoxDetailUrl('Mpos360TingBoxDeviceHuongDanSuDungAction', $dv);
					$stepValues[] = [
						'title' => $dv['serialNumber'],
						'desc' => $dv['status'] == 'ACTIVE' ? 'Đã kích hoạt' : 'Đang kích hoạt',
						'icon' => Mpos360UrlHelper::getTingBoxThumbnail($dv['serialNumber']),
						'other_data' => [
							'desc' => [
								'text_color' => $dv['status'] == 'ACTIVE' ? '#73ae4a' : '#fdb62f'
							],
							'title' => [
								'webviewUrl' => Mpos360UrlHelper::convertToHttps($url)
							]
						],
					];
				}

				$returnData['steps'] = collect($returnData['steps'])->map(function ($item) use ($stepValues) {
					if ($item['stepNumber'] == '2') {
						$item['icon'] = cumtomAsset('images/tingbox/stepKhaiBao/stepActive.svg');
						$item['active'] = 'YES';
						$item['values'] = $stepValues;
					}

					return $item;
				})->values()->all();
			}

			// Xử lý banking mặc định
			$listMerchantShopBank = MerchantShopBank::query()
																							->with(['merchantBank', 'switching'])
																							->where('shop_id', $defaultLocation['id'])
																							->get();

			$listBank = [];
			if ($listMerchantShopBank->isNotEmpty()) {
				$listBank['direct'] = $listMerchantShopBank->filter(function (MerchantShopBank $mcShopBank) {
					return $mcShopBank->account_type == MerchantShopBank::LOAI_TK_TRUC_TIEP;
				})->map(function ($mcShopBank) {
					return [
						'account_number' => $mcShopBank->merchantBank->account_number, 
						'account_holder' => $mcShopBank->merchantBank->account_holder, 
						'account_qr'  => $mcShopBank->account_qr,
						'account_qr_display' => $mcShopBank->account_qr_display,
						'status' => $mcShopBank->status,
						'bank_branch' => $mcShopBank->merchantBank->bank_branch,
						'bank_code' =>  $mcShopBank->merchantBank->bank_code,
						'bank_id' => $mcShopBank->merchantBank->id,
						'request_id' => $mcShopBank->request_id,
						'partner_request_id' => $mcShopBank->partner_request_id,
						'is_default' => $mcShopBank->has('switching') ? 'YES' : 'NO'
					];
				})->values()
				  ->toArray();

				$listBank['inter'] = $listMerchantShopBank->filter(function (MerchantShopBank $mcShopBank) {
					return $mcShopBank->account_type == MerchantBank::LOAI_TK_TRUNG_GIAN;
				})->map(function ($mcShopBank) {
					return [
						'account_number' => $mcShopBank->merchantBank->account_number, 
						'account_holder' => $mcShopBank->merchantBank->account_holder, 
						'account_qr'  => $mcShopBank->account_qr,
						'account_qr_display' => $mcShopBank->account_qr_display,
						'status' => $mcShopBank->status,
						'bank_branch' => $mcShopBank->merchantBank->bank_branch,
						'bank_code' =>  $mcShopBank->merchantBank->bank_code,
						'bank_id' => $mcShopBank->merchantBank->id,
						'request_id' => $mcShopBank->request_id,
						'partner_request_id' => $mcShopBank->partner_request_id,
						'is_default' => $mcShopBank->has('switching') ? 'YES' : 'NO'
					];
				})->values()
					->toArray();
			}
		}

		$returnData['steps'][2]['listBank'] = (object) [];
		
		if (!empty($listBank)) {
			$returnData['steps'][2]['listBank'] = (object) $listBank;
		}
		
		return $returnData;
	}
}
