<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessAction\SubActionV2;

use Exception;
use App\Lib\Helper;
use App\Lib\partner\MNP;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Setting;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction\MnpGetMerchantProfileSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAttachSignatureAction\SubAction\GetCanByRequestChangeInfoSubAction;

class KiemTraPhuongThucKySubAction
{
	public MNP $mnp;

	public string $signProcess;

	public string $can;

	public array $signMethod = [];

	public int $hasChuKy = 0;

	public function __construct(MNP $mnp)
	{
		$this->mnp = $mnp;
		$this->can = Mpos360Enum::MPOS360_CAN_CHOOSE_SIGNATURE;
	}

	/**
	 * Dựa vào "loại MC" để quyết định xem MC sẽ được áp dụng phương thức ký gì
	 * 1-Nếu là HKD: ký vẽ tay
	 * 2-Nếu là công ty: gọi sang MNP kiểm tra xem phương thức hỗ trợ ký là gì?
	 * 		+Nếu mnp trả empty => Auto ký giấy
	 * 		+Nếu mnp trả ký điện tử => Ghi nhận ký điện tử
	 */
	public function run(Mpos360MerchantRequest $mpos360McRequest, DeviceSession $deviceSessionWithToken)
	{
		$mnpMerchantDetail = app(MnpGetMerchantProfileSubAction::class)->run(
			$deviceSessionWithToken->getMerchantId(),
			$deviceSessionWithToken->mnp_token
		);

		$mnpLoaiMc = $mnpMerchantDetail['data']['customerType']['value'] ?? '';
		if (empty($mnpMerchantDetail['data']['customerType']['value'])) {
			throw new BusinessException('Lỗi MNP: không có thông tin loại MC');
		}

		
		$dataRequest = json_decode($mpos360McRequest->data_request, true);
		
		// HKD thì auto vẽ tay
		if ($mnpLoaiMc == 'INDIVIDUAL' || $mnpLoaiMc == 'HOUSEHOLD') {
			$dataRequest[0]['signProcess'] = [
				'mpos360_sign_code' => 'KY_VE_TAY',
				'code' => 'E_CONTRACT',
				'name' => 'Ký vẽ tay nhưng MNP tính là ký điện tử',
				'signature_url' => ''
			];
		}

		

		$isGiaLapPhuongThucKy = Setting::query()->firstWhere(['key' => 'FAKE_LIST_SIGN_COMPANY', 'value' => '1']);

		if ($mnpLoaiMc == 'COMPANY' || optional($isGiaLapPhuongThucKy)->value) {
			/**
			 * Call sang MNP để lấy danh sách phương thức
			 * 1-Nếu chỉ trả về 1 phương thức thì điều hướng thẳng sang phương thức đó
			 * 
			 * 2-Nếu trả về nhiều hơn 1 phương thức thì điều hướng dang màn list
			 * Trong thời điểm này thì ÉP chọn luôn 1 phương thức và set vào bản ghi của mpos360 luôn, sau thì mới trả ra sau
			 */
			$this->can = Mpos360Enum::MPOS360_CAN_PICK_SIGN_METHOD;
			$this->signMethod = app(LietKePhuongThucKyNeuLaCongTySubAction::class)->run();

			$phuongThucKyCuThe = Setting::query()->firstWhere(['key' => 'REDIRECT_SIGNING_SCREEN_IF_IS_COMPANY']);
			
			if (optional($phuongThucKyCuThe)->value == 'KY_DIEN_TU_MEGADOC') {
				$this->can = Mpos360Enum::MPOS360_CAN_GOTO_KY_DIEN_TU_MEGADOC;
			}

			if (optional($phuongThucKyCuThe)->value == 'KY_GIAY') {
				$this->can = Mpos360Enum::MPOS360_CAN_GOTO_UPLOAD_PHU_LUC_GIAY;
			}

			if (optional($phuongThucKyCuThe)->value == 'SALE_HO_TRO') {
				$this->can = Mpos360Enum::MPOS360_CAN_GOTO_SALE_HO_TRO;
			}

			if (optional($phuongThucKyCuThe)->value == 'KY_VE_TAY') {
				$dataRequest[0]['signProcess'] = [
					'mpos360_sign_code' => 'KY_VE_TAY',
					'code' => 'E_CONTRACT',
					'name' => 'Ký vẽ tay nhưng MNP tính là ký điện tử',
					'signature_url' => ''
				];

				$this->can = Mpos360Enum::MPOS360_CAN_CHOOSE_SIGNATURE;
			}

			// Nếu mà passed thì quy luôn cho là ký điện tử
			if (optional($phuongThucKyCuThe)->value == 'PASSED') {
				$this->can = app(GetCanByRequestChangeInfoSubAction::class)->run($mpos360McRequest);
				
				$dataRequest[0]['signProcess'] = [
					'mpos360_sign_code' => 'KY_DIEN_TU_MEGADOC',
					'code' => 'E_CONTRACT',
					'name' => 'Ký điện tử',
					'signature_url' => ''
				];

				$this->hasChuKy = 1; // Đánh dấu luôn là có chữ ký
			}
		}

		$paramUpdate = [
			'data_request' => json_encode($dataRequest),
			'status_sign' => $this->hasChuKy
		];
		
		$wasUpdatedRequest = $mpos360McRequest->forceFill($paramUpdate)->update();
		
		if (!$wasUpdatedRequest) {
			throw new BusinessException('Lỗi không cập nhật được loại chữ ký');
		}

		return [
			'can' => $this->can,
			'sign_method' => $this->signMethod
		];
	}
} // End class