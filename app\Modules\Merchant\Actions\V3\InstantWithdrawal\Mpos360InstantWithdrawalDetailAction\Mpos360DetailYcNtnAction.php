<?php

namespace App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalDetailAction;

use Carbon\Carbon;
use App\Lib\Helper;
use App\Lib\partner\MPOS;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Requests\InstantWithdrawal\V3\Mpos360InstantWithdrawalDetailRequest;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalDetailAction\SubAction\GetThuThemTraLaiSubAction;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalDetailAction\SubAction\GetTrangThaiDetailNTNDungLuonCuaMposSA;

class Mpos360DetailYcNtnAction
{
	public MPOS $mpos;

	public function __construct(MPOS $mpos)
	{
		$this->mpos = $mpos;
	}

	public function run(Mpos360InstantWithdrawalDetailRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();

		$params = [
			'serviceName' => 'PAYMENT_NOW_RQ_GET_DETAIL',
			'requestId' => $request->json('data.order_code'),
			'tokenLogin' => $deviceSession->getMposToken(),
			'merchantFk' => $deviceSession->getMerchantId()
		];

		$detail = $this->mpos->paymentNowGetDetail($params);

		if (empty($detail['data']['data'])) {
			throw new BusinessException('Record not found');
		}

		$rc = $detail['data']['data'];

		$mapTrangThaiDetail = app(GetTrangThaiDetailNTNDungLuonCuaMposSA::class)->run($rc);

		$thuThemTraLai = app(GetThuThemTraLaiSubAction::class)->run($rc);

		$returnData = [
			'warning' => [],
			'data' => [
				[
					'key' => 'ThongTinChung',
					'name' => 'Rút tiền nhanh',
					'list' => [
						[
							'key' => 'MaYc',
							'label' => 'Mã yêu cầu',
							'value' => $rc['id'],
							'other_data' => (object) [
								'font_weight' => 'bold'
							]
						],

						[
							'key' => 'SoGDVietQRYeuCau',
							'label' => 'Số giao dịch rút',
							'value' => sprintf('%02d', count($rc['transactionQRList'])),
							'other_data' => (object) [
								'text_color' => '#018bf4',
								'linking' => [
									'app_screen' => 'LIST_VIETQR_TRANSACTION_EXCLUDING_DETAIL',
									'title' => __('rtn.Yêu cầu NTN VietQR'),
									'params' => [
										'order_code' => $rc['id'],
										'type' => Mpos360Enum::MPOS360_RTN_TYPE_GD_VIETQR_TAO_YC
									]
								]
							]
						],
					]
				]
			]
		];

		if ($rc['status'] == 'APPROVED' || $rc['withdrawStatus'] == 'HAS_BALANCE') {
			$soGiaoDichDuocDuyet = [
				'key' => 'MaYc',
				'label' => __('rtn.Số GD được duyệt'),
				'value' => sprintf('%02d', count($rc['transactionQRList'])),
				'other_data' => (object) [
					'text_color' => '#018bf4',
					'linking' => [
						'app_screen' => 'LIST_VIETQR_TRANSACTION_EXCLUDING_DETAIL',
						'title' => __('rtn.Yêu cầu NTN được duyệt'),
						'params' => [
							'order_code' => $rc['id'],
							'type' => Mpos360Enum::MPOS360_RTN_TYPE_GD_VIETQR_DUOC_DUYET
						]
					]
				]
			];

			$returnData['data'][0]['list'][] = $soGiaoDichDuocDuyet;
			
			// Mã phiếu chi
			if (!empty($rc['withdrawNumber'])) {
				$returnData['data'][0]['list'][] = [
					'key' => 'MaPhieuChi',
					'label' => __('rtn.Mã phiếu chi'),
					'value' => $rc['withdrawNumber'],
					'other_data' => (object) [
						'font_weight' => 'bold'
					]
				];
			}
		}

		$returnData['data'][0]['list'][] = 	[
			'key' => 'ThoiGianYeuCau',
			'label' => 'Thời gian yêu cầu',
			'value' => Carbon::parse($rc['createdDate'])->format('H:i, d/m/Y'),
			'other_data' => (object) []
		];

		if (!empty($rc['approvedDate'])) {
			$returnData['data'][0]['list'][] = [
				'key' => 'ThoiGianDuyet',
				'label' => 'Thời gian duyệt',
				'value' => Carbon::parse($rc['approvedDate'])->format('H:i, d/m/Y'),
				'other_data' => (object) []
			];
		}

		if (!empty($rc['paymentDate'])) {
			$returnData['data'][0]['list'][] = [
				'key' => 'ThoiGianThanhToan',
				'label' => 'Thời gian thanh toán',
				'value' => Carbon::parse($rc['paymentDate'])->format('H:i, d/m/Y'),
				'other_data' => (object) []
			];
		}
		

		$returnData['data'][0]['list'][] = [
			'key' => 'TrangThaiYc',
			'label' => __('rtn.Trạng thái yêu cầu'),
			'value' => $mapTrangThaiDetail['text'],
			'other_data' => (object) [
				'text' => $mapTrangThaiDetail['text'],
				'text_color' => $mapTrangThaiDetail['text_color'],
				'bg_color' => $mapTrangThaiDetail['bg_color'],
				'display_type' => 'pills',
			]
		];


		// Xử lý phần lý do chi tiền lỗi
		if (!empty($rc['payoutErrorDescription'])) {
			$returnData['data'][0]['list'][] = [
				'key' => 'LyDoLoiChiTien',
				'label' => $rc['payoutErrorDescription'],
				'value' => '',
				'other_data' => (object) [
					'label_text_color' => '#da2128',
					'label_font_style' => 'italic',
					'alignment' => 'center',
				]
			];
		}


		$chiTietYc = [
			'key' => 'ChiTietYeuCau',
			'name' => 'Giao dịch',
			'list' => collect([
				[
					'key' => 'TongTienRut',
					'label' => 'Tổng tiền rút',
					'value' => Helper::priceFormat($rc['totalAmount']),
					'other_data' => (object) [
						'font_weight' => 'bold'
					]
				],
				[
					'key' => 'PhiRTN',
					'label' => 'Phí Rút tiền nhanh',
					'value' => Helper::priceFormat($rc['quickFeeAmount']),
					'other_data' => (object) []
				],

				[
					'key' => 'PhiChuyenTien',
					'label' => 'Phí chuyển tiền',
					'value' => Helper::priceFormat($rc['feeTransfer']),
					'other_data' => (object) []
				],



				$thuThemTraLai['thuThem'],

				$thuThemTraLai['traLai'],

				[
					'key' => 'ThucNhan',
					'label' => 'Tổng nhận',
					'value' =>  Helper::priceFormat($rc['recieveAmount']),
					'other_data' => (object) [
						'font_weight' => 'bold',
						'text_color' => '#73ae4a',
					]
				]
			])->filter()->values()->all()
		];

		// Số tiền thực nhận bằng chữ
		if (!empty($rc['recieveAmountString'])) {
			$chiTietYc['list'][] = [
				'key' => 'ThucNhanBangChu',
				'label' => '',
				'value' => trim($rc['recieveAmountString']),
				'other_data' => (object) [
					'display_type' => 'block',
					'font_style' => 'italic',
				]
			];
		}


		$returnData['data'][] = $chiTietYc;

		// Có thông tin tknh thì mới hiển thị group này
		if (!empty($rc['bankAccountNo'])) {
			$label = sprintf('%s:  %s - %s', $rc['bankName'], $rc['bankAccountNo'], $rc['holderName']);

			$returnData['data'][] = [
				'key' => 'TaiKhoanNhanTien',
				'name' => 'Tài khoản nhận',
				'list' => [
					[
						'key' => 'NganHang',
						'label' => $this->__formatBankString($label),
						'value' => '',
						'other_data' => (object) [
							'alignment' => 'center',
							'label_text_color' => '#404041'
						]
					],
				]
			];
		}


		return $returnData;
	} // End method

	// AI
	public function __formatBankString($input) {
    // Kiểm tra nếu chuỗi có dấu () tức là có bankCode
    if (preg_match('/\((.*?)\):\s*(\d+)\s*-\s*(.*)/', $input, $matches)) {
        $bankCode = $matches[1];   // BIDV
        $accountNumber = $matches[2]; // **********
        $beneficiary = $matches[3]; // DUNG

        return "$bankCode: $accountNumber - $beneficiary";
    }
    
    // Nếu không có dấu () thì giữ nguyên
    return $input;
	}
} // End clas
