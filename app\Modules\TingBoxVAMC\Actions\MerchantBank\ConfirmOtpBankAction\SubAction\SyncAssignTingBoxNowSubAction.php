<?php

namespace App\Modules\TingBoxVAMC\Actions\MerchantBank\ConfirmOtpBankAction\SubAction;

use App\Lib\Helper;
use App\Lib\partner\SoundBox;
use App\Lib\TelegramAlertWarning;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;

class SyncAssignTingBoxNowSubAction
{
	public SoundBox $soundBox;

	public int $tries = 1;

	public function __construct(SoundBox $soundBox)
	{
		$this->soundBox = $soundBox;	
	}

	public function run(MerchantShopBank $merchantShopBank)
	{
		$result = false;


		$params = [
			"mcId" => $merchantShopBank->merchant_id,
			"mobileUserName" => $merchantShopBank->shop_id,
			"qrCode" => $merchantShopBank->account_qr,
			"qrType" => "VIETQR",
			"bankCode" => $merchantShopBank->merchantBank->bank_code,
			"accountNumber" => $merchantShopBank->account_number_partner,   // stk ngân hàng vaBankNumber
			"accountName" => $merchantShopBank->account_qr_display, // Tên hiển thị Qr cửa hàng
			'vaNextPayNumber' => $merchantShopBank->getVaNextPayNumber(),
			"mcBankAccountHolderName" => $merchantShopBank->merchantBank->account_holder, // Họ tên chủ tài khoản
			"mcBankAccountNumber" => $merchantShopBank->merchantBank->account_number, // STK ngân hàng mà MC liên kết
			"mcBankAccountMobile" => $merchantShopBank->merchantBank->bank_mobile, // SĐT mà MC đã đăng ký với NH
			"integratedMethod" => 'VAMC',
			'partnerCode' => Helper::getPartnerCode($merchantShopBank->merchant_id),
			'assignDefault' => true
		];



		while (!$result && $this->tries < 3) {
			$sendSoundBox = $this->soundBox->createVAMC($params);

			if (empty($sendSoundBox['result'])) {
				++$this->tries;
				$message = 'Lỗi tạo VA sang tingbox: ' . $params['vaNextPayNumber'];
				$merchantShopBank->is_sync_tingbox = 1;
				$merchantShopBank->save();
				@TelegramAlertWarning::sendMessage($message);
			}else {
				// Tạo VAMC thành công
				$merchantShopBank->is_sync_tingbox = 2;
				$merchantShopBank->save();
				return $sendSoundBox;
			}
		}

		return;
	}
}
