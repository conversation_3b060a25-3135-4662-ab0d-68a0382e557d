<?php

namespace App\Modules\Merchant\Controllers\ChungThuc;

use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\ChungThuc\Mpos360ChungThucCheckRequest;
use App\Modules\Merchant\Requests\ChungThuc\Mpos360ChungThucCreateRequest;
use App\Modules\Merchant\Requests\ChungThuc\Mpos360ChungThucGetOtpRequest;
use App\Modules\Merchant\Requests\ChungThuc\Mpos360ChungThucResendOtpRequest;
use App\Modules\Merchant\Requests\ChungThuc\Mpos360ChungThucVerifyOtpRequest;
use App\Modules\Merchant\Requests\ChungThuc\Mpos360ChungThucGetFieldInfoRequest;
use App\Modules\Merchant\Actions\ChungThuc\Mpos360PushChungThucAction\Mpos360PushChungThucAction;
use App\Modules\Merchant\Actions\ChungThuc\Mpos360ChungThucCheckAction\Mpos360ChungThucCheckAction;
use App\Modules\Merchant\Actions\ChungThuc\Mpos360ChungThucCreateAction\Mpos360ChungThucCreateAction;
use App\Modules\Merchant\Actions\ChungThuc\Mpos360ChungThucDetailAction\Mpos360ChungThucDetailAction;
use App\Modules\Merchant\Actions\ChungThuc\Mpos360ChungThucGetOtpAction\Mpos360ChungThucGetOtpAction;
use App\Modules\Merchant\Actions\ChungThuc\Mpos360ChungThucResendOtpAction\Mpos360ChungThucResendOtpAction;
use App\Modules\Merchant\Actions\ChungThuc\Mpos360ChungThucVerifyOtpAction\Mpos360ChungThucVerifyOtpAction;
use App\Modules\Merchant\Actions\ChungThuc\Mpos360ChungThucGetFieldInfoAction\Mpos360ChungThucGetFieldInfoAction;
use App\Modules\Merchant\Actions\ChungThuc\Mpos360ChungThucGetFieldInfoAction\Mpos360ChungThucGetFieldInfoImproveAction;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Model\Mpos360Logs\LogRequest;

class Mpos360ChungThucController extends Controller
{
	// get các thông tin thể hiện chứng thực
	public function Mpos360ChungThucGetFieldInfo(Mpos360ChungThucGetFieldInfoRequest $request)
	{
		try {
			$result = app(Mpos360ChungThucGetFieldInfoImproveAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360ChungThucCreate(Mpos360ChungThucCreateRequest $request)
	{
		try {
			$result = app(Mpos360ChungThucCreateAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360ChungThucCheck(Mpos360ChungThucCheckRequest $request)
	{
		try {
			$result = app(Mpos360ChungThucCheckAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360ChungThucGetOtp(Mpos360ChungThucGetOtpRequest $request)
	{
		try {
			$result = app(Mpos360ChungThucGetOtpAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360ChungThucResendOtp(Mpos360ChungThucResendOtpRequest $request)
	{
		try {
			$result = app(Mpos360ChungThucResendOtpAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360ChungThucVerifyOtp(Mpos360ChungThucVerifyOtpRequest $request)
	{
		try {
			$result = app(Mpos360ChungThucVerifyOtpAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360ChungThucDetail(Request $request)
	{
		try {
			$result = app(Mpos360ChungThucDetailAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360PushChungThuc(Request $request)
	{
		try {
			$result = app(Mpos360PushChungThucAction::class)->run();
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360CronRemoveOldSession(Request $request)
	{
		$timming = now()->subHours(6)->timestamp;
		try {
			$r = DeviceSession::query()->where('time_expired', '<=', $timming)
																 ->limit(10000)
																 ->delete();

			$timelineDeleteLogRequest = now()->subDays(10)->startOfDay();
			$r2 = LogRequest::query()->whereDate('created_at', $timelineDeleteLogRequest)
															 ->select(['id'])
															 ->orderBy('id', 'DESC')
															 ->limit(1)
															 ->first();
			if ($r2) {
				LogRequest::query()->where('id', '<', $r2->id)
													 ->limit(1000)
													 ->delete();
			}
															 


			return $r;
		} catch (\Throwable $th) {
			return 'empty';
		}
	}
} // End class
