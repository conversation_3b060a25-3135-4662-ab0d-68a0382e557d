<?php

namespace App\Modules\Merchant\Controllers\Notification;

use App\Lib\Helper;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\Notification\Mpos360ListTopicRequest;
use App\Modules\Merchant\Requests\Notification\Mpos360GetTotalUnreadRequest;
use App\Modules\Merchant\Requests\Notification\Mpos360MaskAsReadByIdsRequest;
use App\Modules\Merchant\Requests\Notification\Mpos360NotifySubOrUnsubRequest;
use App\Modules\Merchant\Requests\Notification\Mpos360GetMyNotificationsRequest;
use App\Modules\Merchant\Requests\Notification\Mpos360GetNotificationDetailRequest;
use App\Modules\Merchant\Requests\Notification\Mpos360MaskNotificationAsReadRequest;
use App\Modules\Merchant\Requests\Notification\Mpos360GetGroupWithUnreadItemsRequest;
use App\Modules\Merchant\Actions\Notification\Mpos360ListTopicAction\Mpos360ListTopicAction;
use App\Modules\Merchant\Actions\Notification\Mpos360MaskAsReadByIdsAction\Mpos360MaskAsReadByIdsAction;
use App\Modules\Merchant\Actions\Notification\Mpos360NotifySubOrUnsubAction\Mpos360NotifySubOrUnsubAction;
use App\Modules\Merchant\Actions\Notification\Mpos360GetMyNotificationsAction\Mpos360GetMyNotificationsAction;
use App\Modules\Merchant\Actions\Notification\Mpos360GetNotificationDetailAction\Mpos360GetNotificationDetailAction;
use App\Modules\Merchant\Actions\Notification\Mpos360MaskNotificationAsReadAction\Mpos360MaskNotificationAsReadAction;
use App\Modules\Merchant\Actions\Notification\Mpos360GetGroupWithUnreadItemsAction\Mpos360GetGroupWithUnreadMposAction;
use App\Modules\Merchant\Actions\Notification\Mpos360GetGroupWithUnreadItemsAction\Mpos360GetGroupWithUnreadItemsAction;

class Mpos360NotificationController extends Controller
{
	public function Mpos360GetMyNotifications(Mpos360GetMyNotificationsRequest $request)
	{
		try {
			$getCategories = app(Mpos360GetMyNotificationsAction::class)->run($request);
			return $this->successResponse($getCategories, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360GetNotificationDetail(Mpos360GetNotificationDetailRequest $request)
	{
		try {
			$result = app(Mpos360GetNotificationDetailAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360GetGroupWithUnreadItems(Mpos360GetGroupWithUnreadItemsRequest $request)
	{
		try {
			$result = app(Mpos360GetGroupWithUnreadMposAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360GetTotalUnread(Mpos360GetGroupWithUnreadItemsRequest $request)
	{
		try {
			$mpos360GetGroupWithUnreadItemsDto = app(Mpos360GetGroupWithUnreadItemsAction::class)->run($request);
			return $this->successResponse([
				'total_unread' => $mpos360GetGroupWithUnreadItemsDto->data->count
			], $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}


	// Đánh dấu là read all theo user
	public function Mpos360MaskNotificationAsRead(Mpos360MaskNotificationAsReadRequest $request)
	{
		try {
			$result = app(Mpos360MaskNotificationAsReadAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360MaskAsReadByIds(Mpos360MaskAsReadByIdsRequest $request)
	{
		try {
			$result = app(Mpos360MaskAsReadByIdsAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360NotifySubOrUnsub(Mpos360NotifySubOrUnsubRequest $request)
	{
		try {
			$result = app(Mpos360NotifySubOrUnsubAction::class)->run($request);
			return $this->successResponse([
				'status' => 'SUCCESS',
				'listTopic' => $result['data']
			], $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360ListTopic(Mpos360ListTopicRequest $request)
	{
		try {
			$result = app(Mpos360ListTopicAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
} // End class
