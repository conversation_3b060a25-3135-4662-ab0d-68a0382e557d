<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class AppendApiRequestIdMiddleware
{
  public $timeStart;

  /**
   * Handle an incoming request.
   *
   * @param  \Illuminate\Http\Request  $request
   * @param  \Closure  $next
   * @return mixed
   */

  public function handle($request, Closure $next)
  {
    if (!$request->has('api_request_id')) {
      $requestId = sprintf('M360_%s_%s', date('mdHi') ,Str::random(6));
      $request->request->add(['api_request_id' => $requestId]);

			$traceId = $request->header('X-Trace-Id') ?? Str::uuid()->toString();
			$request->headers->set('X-Trace-Id', $traceId);
    }

    return $next($request);
  }
} // End class
