<?php

namespace App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360TransactionQRListByDiemBanAction;

use App\Modules\TingBoxVAMC\Requests\Transaction\Mpos360TransactionQRListByDiemBanRequest;
use App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360TransactionQRListByDiemBanAction\SubAction\GetGiaoDichQrVaModeSubAction;

class Mpos360TransactionQRListByDiemBanAction
{

	public function run(Mpos360TransactionQRListByDiemBanRequest $request)
	{
		$d = $request->getStartDateEndDate();

		$params = [
			"serviceName" => "STATISTIC_TRANSACTION",
			'typeTransaction' => 'HISTORY_QR',
			'merchantFk' => $request->json('data.merchantId'),
			'vaMode' => $request->json('data.vaMode'),
			'muid' => $request->json('data.muid'),
			"startDate" => $d['startDate'],
			"endDate" => $d['endDate'],
			"pageIndex" => $request->json('data.pageIndex'),
			"pageSize" => 10,
		];

		$listTransaction = app(GetGiaoDichQrVaModeSubAction::class)->getData($params);
		
		$returnData['data'] = $listTransaction;
		$returnData['filter'] = $this->getFilter();
		return $returnData;
	}


	public function getFilter()
	{
		$filter = [
			[
				"key" => "transaction_time",
				"name" => "Thời gian giao dịch",
				"list" => [
					["value" => "ALL", "label" => "Tất cả"],
					["value" => "TODAY", "label" => "Hôm nay"],
					["value" => "YESTERDAY", "label" => "Hôm qua"],
					["value" => "THIS_WEEK", "label" => "Tuần này"],
					["value" => "THIS_MONTH", "label" => "Tháng này"],
				],
				"other_data" => (object) []
			],

			[
				"key" => "startDate",
				"name" => "Thời gian bắt đầu",
				"list" => [],
				"other_data" => (object) []
			],

			[
				"key" => "endDate",
				"name" => "Thời gian kết thúc",
				"list" => [],
				"other_data" => (object) []
			],
		];

		if (request()->json('data.vaMode') == 'VANP') {
			$filter[] = [
				"key" => "transaction_status",
				"name" => "Trạng thái giao dịch",
				"list" => [
					["value" => "ALL", "label" => "Tất cả"],
					["value" => "CANCEL", "label" => "Đã huỷ"],
					["value" => "SUCCESS", "label" => "Thành công"],
					["value" => "SETTLEMENT", "label" => "Đã kết toán"],
					["value" => "PAY", "label" => "Đã thanh toán"],
					["value" => "PENDING", "label" => "Đang xử lý"],
					["value" => "FAIL", "label" => "Thất bại"],
					["value" => "REFUND", "label" => "Hoàn trả"]
				],
				"other_data" => (object) []
			];
		}else {
			// VAMC
			$filter[] = [
				"key" => "transaction_status",
				"name" => "Trạng thái giao dịch",
				"list" => [
					["value" => "ALL", "label" => "Tất cả"],
					["value" => "SUCCESS", "label" => "Thành công"],
				],
				"other_data" => (object) []
			];
		}

		return $filter;
	}
} // End class
