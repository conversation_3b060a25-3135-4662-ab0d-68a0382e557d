<?php

namespace App\Modules\TingBoxVAMC\Addon;

class ChiTietCuaHangAddon
{
	public function run()
	{
		$data = [
			"result" => true,
			"code" => 1000,
			"message" => "DO_SERVICE_SUCCESS",
			"data" => [
				"passportObject" => [
					"birthDay" => "1990-02-05T17:00:00Z",
					"passportRepresentFrontUrl" => "",
					"address" => "Vũ <PERSON>ư, Thái B<PERSON>nh",
					"gender" => "FEMALE",
					"passport" => "************",
					"customerName" => "Trần Thị Hu<PERSON>ền",
					"passportRepresentBackUrl" => ""
				],
				"bankId" => null,
				"holderName" => null,
				"businessShortName" => "MC360_0904565598",
				"passport" => "************",
				"mposMcId" => ********,
				"accountNo" => null,
				"bankVerify" => null,
				"bankName" => null,
				"locations" => [
					[
						"id" => "67a469b7edb1ac3d9f26c7ed",
						"areaName" => "Tran Huyen Coffee",
						"areaAddress" => "18 Tam Trinh, HBT, Hà Nội",
						"areaCity" => "Hà Nội",
						"areaDistrict" => "Hai Bà Trưng",
						"areaCityCode" => "vn-hni",
						"areaDistrictCode" => "vn-hni-haa",
						"mcc" => "BAN_LE",
						"deviceDTOs" => [
							[
								"serialNumber" => "TBTEST0000080",
								"status" => null,
								"mobileUserId" => "25A100345"
							]
						]
					]
				],
				"qrDisplayName" => "TH CAFE",
				"customerName" => "Trần Thị Huyền"
			],
			"meta" => null
		];

		return $data;
	}
}
