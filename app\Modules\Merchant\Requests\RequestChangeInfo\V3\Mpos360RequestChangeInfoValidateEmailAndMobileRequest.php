<?php

namespace App\Modules\Merchant\Requests\RequestChangeInfo\V3;

use App\Modules\Merchant\Requests\MerchantRequest;
use Illuminate\Validation\Rule;

class Mpos360RequestChangeInfoValidateEmailAndMobileRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.authoriserEmail' => ['required', 'string', 'email'],
			'data.authoriserContactNumber' => ['required', 'string', 'min:10', 'max:12'],
			'data.replace_mpos_account_field' => ['present', 'array'],
			'data.replace_mpos_account_field.*' => [
				'nullable', 
				'string', 
				Rule::in(['authoriserContactNumber', 'authoriserEmail'])
			],
		];
	}

	public function messages() {
		return [
			'data.authoriserEmail.required' => __('dttv3.Email là bắt buộc'),
			'data.authoriserEmail.string' => __('dttv3.Email phải là kiểu chuỗi'),
			'data.authoriserEmail.email' => __('dttv3.Email không đúng định dạng'),

			'data.authoriserContactNumber.required' => __('dttv3.SĐT là bắt buộc'),
			'data.authoriserContactNumber.string' => __('dttv3.SĐT phải là kiểu chuỗi'),
			'data.authoriserContactNumber.min' => __('dttv3.SĐT có độ dài tối thiểu phải là 10 ký tự số'),
			'data.authoriserContactNumber.max' => __('dttv3.SĐT có độ dài tối đa phải là 12 ký tự số'),

			'data.replace_mpos_account_field.present' => __('dttv3.Trường chọn thay thế tài khoản mpos là bắt buộc'),
			'data.replace_mpos_account_field.array' => __('dttv3.Trường chọn thay thế tài khoản mpos phải là kiểu mảng'),

			'data.replace_mpos_account_field.*.string' => __('dttv3.Trường thay thế TKĐN mpos phải là kiểu chuỗi'),
			'data.replace_mpos_account_field.*.in' => __('dttv3.Trường thay thế TKĐN mpos không thuộc các giá trị đã chỉ định'),
		];
	}

	public function hasThayTheTkDangNhapMpos(): bool {
		return !empty($this->json('data.replace_mpos_account_field'));
	}

	public function getThayTheTkDangNhapMpos(): array {
		return $this->json('data.replace_mpos_account_field');
	}
} // End class
