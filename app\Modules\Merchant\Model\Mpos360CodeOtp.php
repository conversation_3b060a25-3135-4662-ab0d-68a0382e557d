<?php

namespace App\Modules\Merchant\Model;

use Illuminate\Database\Eloquent\Model;

class Mpos360CodeOtp extends Model
{
	protected $connection = 'mpos360_data';

	protected $table      = 'otp_transactions';
	
	protected $guarded    = [];
	
	public $timestamps    = false;

	protected $dates      = [];

	protected $hidden     = [];

	public function mpos360User() {
		return $this->belongsTo(Mpos360User::class, 'user_id', 'id');
	}
	
	public function isFinalStatus(): bool {
		return $this->status == 2;
	}

	public function isExpiredOtp(): bool {
		return now()->timestamp >= $this->time_out;
	}

	public function isSmsOtp(): bool {
		return $this->service_code == 'SMS';
	}

	public function isEmailOtp(): bool {
		return $this->service_code == 'EMAIL';
	}

	public function isZaloOtp(): bool {
		return $this->service_code == 'ZALO';
	}
} // End class
