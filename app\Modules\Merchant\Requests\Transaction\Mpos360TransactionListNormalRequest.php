<?php

namespace App\Modules\Merchant\Requests\Transaction;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360TransactionListNormalRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.transaction_time' => ['present', 'string'],
			'data.transaction_method' => ['present', 'string'],
			'data.transaction_status' => ['present', 'string'],
			'data.start' => ['required', 'numeric', 'integer', 'min:0'],
			'data.limit' => ['required', 'numeric', 'integer', 'min:5'],
		];
	}

	public function getStartDate(): string
	{
		$transactionTime = $this->json('data.transaction_time', 'TODAY');
		$transactionTimeArray = explode(',', $transactionTime);

		if (in_array('THIS_MONTH', $transactionTimeArray)) {
			return now()->startOfMonth()->format('d-m-Y');
		}

		if (in_array('LAST_MONTH', $transactionTimeArray)) {
			return now()->subMonth()->startOfMonth()->format('d-m-Y');
		}

		if (in_array('YESTERDAY', $transactionTimeArray)) {
			return now()->yesterday()->format('d-m-Y');
		}

		if (in_array('TODAY', $transactionTimeArray)) {
			return now()->subDays(3)->format('d-m-Y');
		}

		if (in_array('ALL', $transactionTimeArray)) {
			return now()->subDays(10)->format('d-m-Y');
		}

		return now()->format('d-m-Y');
	}
	public function getEndDate(): string
	{
		$transactionTime = $this->json('data.transaction_time', 'TODAY');
		$transactionTimeArray = explode(',', $transactionTime);

		if (in_array('THIS_MONTH', $transactionTimeArray)) {
			return now()->endOfMonth()->format('d-m-Y');
		}

		if (in_array('LAST_MONTH', $transactionTimeArray)) {
			return now()->subMonth()->endOfMonth()->format('d-m-Y');
		}

		if (in_array('YESTERDAY', $transactionTimeArray)) {
			return now()->yesterday()->endOfDay()->format('d-m-Y');
		}

		if (in_array('TODAY', $transactionTimeArray)) {
			return now()->format('d-m-Y');
		}

		return now()->format('d-m-Y');
	}
	public function getPaymentMethod(): string
	{
		$value = $this->json('data.transaction_method', '');
		if ($value) {
			if ($value == 'CARDS_WIPE') {
				return 'Thẻ';
			} elseif ($value == 'QR_CODE') {
				return 'QR Code';
			} elseif ($value == 'REMOTE_GENRERATE_LINK') {
				return 'Nhập thẻ';
			}elseif ($value == 'ALL') {
				return '';
			} else {
				return 'Số dư ví DV';
			}
		}
		return '';
	}
	public function getTranStatus(): string
	{
		$value = $this->json('data.transaction_status', '');
		if ($value) {
			if($value == 'ALL') {
				return '';
			}
			return  $value;
		}
		return '';
	}
} // End class
