<?php

namespace App\Modules\Merchant\Model\Mpos360Logs;

use Illuminate\Database\Eloquent\Model;

class LogRequest extends Model
{
	protected $connection = 'mpos360_logs';
	protected $table      = 'log_request';
	protected $guarded    = [];
	public $timestamps    = true;

	public $id_refer;
	public string $model_refer = '';
	public string $partner = 'mpos';
	public $request = '{}';
	public $response = '{}';
	public $func = '';
	public $other_data = '{}';

	public function setData(string $property, $value=[])
	{
		$this->$property = $value;
	}


	public function createLog($params = [])
	{
		$create = [
			'id_refer' => $this->id_refer,
			'model_refer' => $this->model_refer,
			'partner' => $this->partner,
			'request' => is_string($this->request) ? $this->request : json_encode($this->request),
			'response' => is_string($this->response) ? $this->response : json_encode($this->response),
			'func' => $this->func,
			'other_data' => is_string($this->other_data) ? $this->other_data : json_encode($this->other_data),
		];

		$create = array_merge($create, $params);

		return $this->forceCreate($create);
	}
} // End class
