<?php

namespace App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360ListDiemBanAction\SubAction\DongBoTknhTrungGianSubAction;

use App\Exceptions\BusinessException;
use App\Modules\TingBoxVAMC\Models\MerchantBank;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use Illuminate\Support\Arr;

class DongBoTknhSubAction
{
	/**
	 * array:5 [
			"result" => true
			"code" => 1000
			"message" => "DO_SERVICE_SUCCESS"
			"data" => array:12 [
				"passportObject" => array:7 [
					"birthDay" => "1990-02-05T17:00:00Z"
					"passportRepresentFrontUrl" => ""
					"address" => "V<PERSON>, Thái B<PERSON>nh"
					"gender" => "FEMALE"
					"passport" => "************"
					"customerName" => "Trần Th<PERSON> Hu<PERSON>"
					"passportRepresentBackUrl" => ""
				]
				"bankId" => "5ff931d9959d58591556ced3"
				"holderName" => "TRAN THI HUYEN"
				"businessShortName" => "MC360_0904565598"
				"passport" => "************"
				"mposMcId" => ********
				"accountNo" => "*********"
				"bankVerify" => true
				"bankName" => "VPB - VPBANK - NH TMCP VN THỊNH VƯỢNG"
				"locations" => array:1 [
					0 => array:9 [
						"id" => "67a469b7edb1ac3d9f26c7ed"
						"areaName" => "Tran Huyen Coffee"
						"areaAddress" => "18 Tam Trinh, HBT, Hà Nội"
						"areaCity" => "Hà Nội"
						"areaDistrict" => "Hai Bà Trưng"
						"areaCityCode" => "vn-hni"
						"areaDistrictCode" => "vn-hni-haa"
						"mcc" => "BAN_LE"
						"deviceDTOs" => array:1 [
							0 => array:3 [
								"serialNumber" => "TB01230800003"
								"status" => "ACTIVE"
								"mobileUserId" => "tbdemo123"
							]
						]
					]
				]
				"qrDisplayName" => "TH CAFE"
				"customerName" => "Trần Thị Huyền"
			]
			"meta" => null
		]
	 */
	public function run($detailMc) {

		if (!empty($detailMc['data']['bankId'])) {
			// Tạm thời lấy phần ký tự đầu insert vào, sau hỏi anh Thông sau
			$bankName = $detailMc['data']['bankName'];
			$explode = explode(' - ', $bankName);

			$paramCreateMerchantBank = [
				'merchant_id' => $detailMc['data']['mposMcId'],
				'bank_code' => trim(Arr::first($explode)),
				'account_cat' => 'ATM',
				'account_number' => $detailMc['data']['accountNo'],
				'account_holder' => $detailMc['data']['holderName'],
				'bank_branch' => '',
				'partner_code' => 'MYNEXTPAY',
				'status' => '1', // 1: kích hoạt
				'time_created' => now()->timestamp,
				'time_updated' => now()->timestamp,
			];

			$merchantBank = MerchantBank::query()->firstOrCreate([
				'merchant_id' => $paramCreateMerchantBank['merchant_id'],
				'account_holder' => $paramCreateMerchantBank['account_holder'],
				'account_number' => $paramCreateMerchantBank['account_number']
			], $paramCreateMerchantBank);

			if (!$merchantBank) {
				throw new BusinessException('Lỗi không đồng bộ được thông tin TKNH của bạn');
			}

			// Lưu thông tin merchantShopBank
			foreach ($detailMc['data']['locations'] as $location) {
				if (!empty($location['deviceDTOs'])) {
					foreach ($location['deviceDTOs'] as $dv) {
						$paramCreateShopBank = [
							'merchant_id' => $detailMc['data']['mposMcId'],
							'merchant_bank_id' => $merchantBank->id,
							'shop_id' => $dv['mobileUserId'],
							'account_type' => MerchantShopBank::LOAI_TK_TRUNG_GIAN // Trung gian
						];
		
						$mcShopBank = MerchantShopBank::query()->firstOrCreate(
							$paramCreateShopBank, 
							array_merge($paramCreateShopBank, [
								'is_sync_tingbox' => 0,
								'time_created' => time()
							])
						);
		
						if (!$mcShopBank) {
							throw new BusinessException('Lỗi không đồng bộ được thông tin TKNH trong cửa hàng');
						}
					}
				}
			}
		}

		return true;
	}
}
