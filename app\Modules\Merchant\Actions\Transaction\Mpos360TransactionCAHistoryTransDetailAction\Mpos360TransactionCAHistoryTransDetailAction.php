<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransactionCAHistoryTransDetailAction;

use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionCAHistoryTransDetailRequest;


class Mpos360TransactionCAHistoryTransDetailAction
{
	public function run(Mpos360TransactionCAHistoryTransDetailRequest $request)
	{
		$returnData = [
			'content' => [
				[
					[
						'label' => 'Số tiền đã đặt cọc', 
						'value' => '3.000.000 vnd', 
						'other_data' => (object) [
							'text_color' => '#dd8523', 'bg_color' => '#dd8523'
						]
					],

					[
						'label' => 'Số tiền thanh toán', 
						'value' => '3.000.000 vnd', 
						'other_data' => (object) [
							'text_color' => '#dd8523', 'bg_color' => '#dd8523'
						]
					],

					[
						'label' => 'Trạng thái',
						'value' => 'Thành công',
						'other_data' => [
							'text_color' => '#ffffff', 
							'bg_color' => '#d12e29'
						]
					],
				], 

				
				[
					['label' => 'Mã giao dịch', 'value' => '2024040114193999361', 'other_data' => (object) []],
					['label' => 'Số tham chiếu', 'value' => '408706794666', 'other_data' => (object) []],
				], // customer_payment

				
				[
					['label' => 'Mã tạm ứng', 'value' => 'MA01', 'other_data' => (object) []],
					['label' => 'Tên chủ thẻ', 'value' => 'TRINH MINH TRANG', 'other_data' => (object) []],
					['label' => 'Thời gian giao dịch', 'value' => '2024/03/07 - 13:50', 'other_data' => (object) []],
					['label' => 'Thời gian kết toán còn lại', 'value' => '02 ngày', 'other_data' => (object) []],
				], // payment_info

				
				[
					['label' => 'Số điện thoại', 'value' => '0912345678', 'other_data' => (object) []],
					['label' => 'Email', 'value' => '<EMAIL>', 'other_data' => (object) []],
					['label' => 'Mô tả', 'value' => 'abcxyz', 'other_data' => (object) []],
				], // promotions
			],

			'other_data' => (object) []
		];

		return $returnData;
	}
}
