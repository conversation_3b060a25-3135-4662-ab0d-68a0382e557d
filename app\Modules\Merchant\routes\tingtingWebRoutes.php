<?php

use Illuminate\Support\Facades\Route;
use App\Modules\Merchant\Controllers\TingBox\Web\Mpos360TingBoxWebController;
use App\Modules\Merchant\Controllers\TingBox\Web\Mpos360TingBoxKhaiBaoController;
use App\Modules\Merchant\Controllers\TingBox\Web\Mpos360TingBoxPaymentController;

Route::middleware(['web'])->group(function () {
	// Route::any('/form-register', function () {
	// 	return redirect()->route('TBWebMpos360FormDangKyAction');
	// });
	
	// Route::any('/Mpos360FormDangKyTaiKhoan', [
	// 	'uses' => Mpos360TingBoxWebController::class . '@TBWebMpos360FormDangKy',
	// 	'as' => 'TBWebMpos360FormDangKyAction'
	// ]);
	
	// Route::any('dang-ky-thanh-cong', function () {
	// 	return view('finish');
	// });
	
	// Route::post('/TBWebMpos360SubmitDangKy', [
	// 	'uses' => Mpos360TingBoxWebController::class . '@TBWebMpos360SubmitDangKy',
	// 	'as' => 'TBWebMpos360SubmitDangKyAction'
	// ]);
	
	// Route::post('/TBWebMpos360SendOtp', [
	// 	'uses' => Mpos360TingBoxWebController::class . '@TBWebMpos360SendOtp',
	// 	'as' => 'TBWebMpos360SendOtpAction'
	// ]);
	
	// Route::post('/TBWebMpos360VerifyOtp', [
	// 	'uses' => Mpos360TingBoxWebController::class . '@TBWebMpos360VerifyOtp',
	// 	'as' => 'TBWebMpos360VerifyOtpAction'
	// ]);
	
	// Route::any('/form-authen-account', [
	// 	'uses' => Mpos360TingBoxWebController::class . '@TBWebMpos360FormAuthenAccount',
	// 	'as' => 'TBWebMpos360FormAuthenAccountAction'
	// ]);
	
	// Route::post('/TBWebMpos360SubmitAuthenAccountStep1', [
	// 	'uses' => Mpos360TingBoxWebController::class . '@TBWebMpos360SubmitAuthenAccountStep1',
	// 	'as' => 'TBWebMpos360SubmitAuthenAccountStep1tAction'
	// ]);
	
	// Route::post('/TBWebMpos360SubmitAuthenAccountStep2', [
	// 	'uses' => Mpos360TingBoxWebController::class . '@TBWebMpos360SubmitAuthenAccountStep2',
	// 	'as' => 'TBWebMpos360SubmitAuthenAccountStep2tAction'
	// ]);
	
	// Route::post('/TBWebMpos360EditAuthenAccount', [
	// 	'uses' => Mpos360TingBoxWebController::class . '@TBWebMpos360EditAuthenAccount',
	// 	'as' => 'TBWebMpos360EditAuthenAccountAction'
	// ]);
	
	// // Phuong support
	// Route::any('Mpos360TingBoxKhaiBaoListInfo', [
	// 	'uses' => Mpos360TingBoxKhaiBaoController::class . '@Mpos360TingBoxKhaiBaoListInfo',
	// 	'as' => 'Mpos360TingBoxKhaiBaoListInfoAction'
	// ]);
	
	// Route::post('Mpos360TingBoxKhaiBaoThietBi', [
	// 	'uses' => Mpos360TingBoxKhaiBaoController::class . '@Mpos360TingBoxKhaiBaoThietBi',
	// 	'as' => 'Mpos360TingBoxKhaiBaoThietBiAction'
	// ]);
	
	// Route::post('Mpos360TingBoxGetFormSuaCuaHang', [
	// 	'uses' => Mpos360TingBoxKhaiBaoController::class . '@Mpos360TingBoxGetFormSuaCuaHang',
	// 	'as' => 'Mpos360TingBoxGetFormSuaCuaHangAction'
	// ]);
	
	// Route::post('Mpos360TingBoxUpdateCuaHang', [
	// 	'uses' => Mpos360TingBoxKhaiBaoController::class . '@Mpos360TingBoxUpdateCuaHang',
	// 	'as' => 'Mpos360TingBoxUpdateCuaHangAction'
	// ]);
	
	// Route::post('Mpos360TingBoxOnChangeTinhThanh', [
	// 	'uses' => Mpos360TingBoxKhaiBaoController::class . '@Mpos360iTngBoxOnChangeTinhThanh',
	// 	'as' => 'Mpos360iTngBoxOnChangeTinhThanhAction'
	// ]);
	
	// Route::post('Mpos360iTngBoxOnCheckTknh', [
	// 	'uses' => Mpos360TingBoxKhaiBaoController::class . '@Mpos360iTngBoxOnCheckTknh',
	// 	'as' => 'Mpos360iTngBoxOnCheckTknhAction'
	// ]);
	
	// Route::post('Mpos360iTngBoxOnUpdateTknh', [
	// 	'uses' => Mpos360TingBoxKhaiBaoController::class . '@Mpos360iTngBoxOnUpdateTknh',
	// 	'as' => 'Mpos360iTngBoxOnUpdateTknhAction'
	// ]);
	// // End phuong support
	
	// Route::any('TBWebMpos360NhanThanhToan', [
	// 	'uses' => Mpos360TingBoxPaymentController::class . '@TBMpos360NhanThanhToan',
	// 	'as' => 'TBMpos360NhanThanhToan'
	// ]);
	
	// Route::any('getQuanHuyenTuMaTinhThanh', [
	// 	'uses' => Mpos360TingBoxPaymentController::class . '@getQuanHuyenTuMaTinhThanh',
	// 	'as' => 'getQuanHuyenTuMaTinhThanh'
	// ]);
	
	// Route::any('TBMpos360SubmitThongTinCuaHang', [
	// 	'uses' => Mpos360TingBoxPaymentController::class . '@TBMpos360SubmitThongTinCuaHang',
	// 	'as' => 'TBMpos360SubmitThongTinCuaHang'
	// ]);
	
	// Route::any('TBMpos360SubmitLoaTingBox', [
	// 	'uses' => Mpos360TingBoxPaymentController::class . '@TBMpos360SubmitLoaTingBox',
	// 	'as' => 'TBMpos360SubmitLoaTingBox'
	// ]);
	
	// Route::any('getAllBank', [
	// 	'uses' => Mpos360TingBoxPaymentController::class . '@getAllBank',
	// 	'as' => 'getAllBank'
	// ]);
	// Route::any('TBMpos360CheckThongTinTaiKhoanNhanTien', [
	// 	'uses' => Mpos360TingBoxPaymentController::class . '@TBMpos360CheckThongTinTaiKhoanNhanTien',
	// 	'as' => 'TBMpos360CheckThongTinTaiKhoanNhanTien'
	// ]);
	// Route::any('TBMpos360SubmitTaiKhoanNhanTien', [
	// 	'uses' => Mpos360TingBoxPaymentController::class . '@TBMpos360SubmitTaiKhoanNhanTien',
	// 	'as' => 'TBMpos360SubmitTaiKhoanNhanTien'
	// ]);
});
