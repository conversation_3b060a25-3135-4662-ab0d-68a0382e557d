<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForRepresentationSA\SubAction;

use App\Exceptions\BusinessException;
use Exception;

class ValidateHoSoRiengLeSubAction
{
	public function run(string $profileKey, string $value = '')
	{
		if (empty($value)) {
			throw new BusinessException(sprintf('Giá trị của hồ sơ `%s` là bắt buộc', $profileKey));
		}

		if ($profileKey == 'authoriserEmail' || $profileKey == 'representEmail') {
			if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
				throw new BusinessException('Email mới không đúng định dạng');
			}
		} // End check email

		if ($profileKey == 'authoriserContactNumber' || $profileKey == 'representMobile') {
			$regex = '/^(0|\+?84)(\d{9}|\d{2}\s?\d{3}\s?\d{3})$/';
			if (!preg_match($regex, $value)) {
				throw new BusinessException('SĐT mới không đúng định dạng');
			}
		} // End check email
	} // End method
}  // End class