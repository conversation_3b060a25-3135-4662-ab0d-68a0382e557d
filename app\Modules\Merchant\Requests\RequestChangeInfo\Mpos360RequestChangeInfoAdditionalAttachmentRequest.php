<?php

namespace App\Modules\Merchant\Requests\RequestChangeInfo;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360RequestChangeInfoAdditionalAttachmentRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.id' => ['required', 'numeric', 'integer', 'min:1'],

			'data.email' => ['required', 'string', 'email'],
			
			'data.additional_profiles' => ['required', 'array'],
			'data.additional_profiles.*.profileKey' => ['required', 'string'],
			'data.additional_profiles.*.value' => ['required', 'string'],

			'data.attachments' => ['required', 'array'],

			'data.attachments.id_documents' => ['required', 'array'],
			
			'data.attachments.other_documents' => ['present', 'array'],
			'data.attachments.other_documents.*' => ['string', 'url'],
		];
	}

	protected function passedValidation()
	{
		$params = $this->all();
		foreach ($params['data']['additional_profiles'] as &$profile) {
			
			if ( is_string($profile['value']) ) {
				$profile['value'] = cleanXSS($profile['value']);
			}
		}

		$this->merge($params);
	}
} // End class
