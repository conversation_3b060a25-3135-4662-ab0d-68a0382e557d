<?php

namespace App\Modules\Merchant\Requests\Authen\Mpos360;

use Illuminate\Foundation\Http\FormRequest;

class Mpos360AuthRegisterRequest extends FormRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.is_getlink_register' => ['required', 'numeric'],
		];
	}

	public function messages()
	{
		return [
			'data.is_getlink_register.required' => vmsg('ThamSoGetLinkRegisterLaBatBuoc'),
			'data.is_getlink_register.numeric' => vmsg('ThamSoGetLinkRegisterPhaiLaKieuSo'),
		];
	}
}
