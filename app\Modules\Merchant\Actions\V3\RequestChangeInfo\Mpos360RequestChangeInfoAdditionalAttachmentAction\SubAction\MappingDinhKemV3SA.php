<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\SubAction;

use Illuminate\Support\Arr;

class MappingDinhKemV3SA
{
	/**
	 * {
      "id_documents": {
        "cccd": [
          "url1",
          "url2"
        ],
        "gp_lai_xe": [
          "url3",
          "url4"
        ],
        "ho_chieu": [
          "page1",
          "p2",
          "p3",
          "p4"
        ],
        "ho_khau": [
          "hk1",
          "hk2",
          "hk3",
          "hk4"
        ]
      },
      "other_documents": [
        "https://mpos.vn/",
        "https://nextlend.vn/"
      ]
    }
	 */
	public function run(array $attachments)
	{
		$profilesImgUrl['substituteCertUrls'] = [];

		// xu ly hinh anh
		if (!empty($attachments)) {
			foreach ($attachments['id_documents'] as $keyHoSoDefine => $arrayUrlImage) {
				// ăn vào key cccd bên mnp
				if ($keyHoSoDefine == 'cccd') {
					$profilesImgUrl['passportRepresentFrontUrl'] = Arr::first($arrayUrlImage);
					$profilesImgUrl['passportRepresentBackUrl'] = Arr::last($arrayUrlImage);
				} else {
					// Các giấy tờ khác được coi là `CHỨNG TỪ THAY THẾ`
					foreach ($arrayUrlImage as $url) {
						$profilesImgUrl['substituteCertUrls'][] = $url;
					}
				}
			}

			// giấy tờ khác thì cho vào: `URL của giấy chứng nhận đi kèm`
			foreach ($attachments['other_documents'] as $url) {
				$profilesImgUrl['attachedCertUrls'][] = $url;
			}
		} // End xu ly hinh anh

		return $profilesImgUrl;
	}
} // End class
