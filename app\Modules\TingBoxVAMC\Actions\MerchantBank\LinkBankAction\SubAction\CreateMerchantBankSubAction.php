<?php

namespace App\Modules\TingBoxVAMC\Actions\MerchantBank\LinkBankAction\SubAction;

use App\Modules\TingBoxVAMC\Models\MerchantBank;

class CreateMerchantBankSubAction
{
	public function run($params = []): MerchantBank
	{

		$p = [
			'merchant_id'    => $params['merchant_id'],
			'bank_code'      => $params['bank_code'],
			'account_number' => $params['account_number'],
			'account_holder' => $params['account_holder'],
			'account_cat'    => 'ACCOUNT',
			'bank_branch'    => '',
			'status'         => 1,
			'time_created'   => time(),
			'time_updated'   => time(),
			'bank_identity'  => $params['bank_identity'],
			'bank_mobile'    => $params['bank_mobile'],
			'bank_email'     => $params['bank_email'],
		];

		$r = MerchantBank::query()->updateOrCreate([
			'merchant_id' => $p['merchant_id'],
			'bank_code' => $p['bank_code'],
			'account_number' => $p['account_number']
		], $p);

		return $r;
	}
}
