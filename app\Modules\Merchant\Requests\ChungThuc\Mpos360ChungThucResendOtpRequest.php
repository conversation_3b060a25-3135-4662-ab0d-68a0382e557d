<?php

namespace App\Modules\Merchant\Requests\ChungThuc;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360ChungThucResendOtpRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.otp_id' => ['required', 'string', 'max:10'],
		];
	}

	public function messages()
	{
		return [
			'data.otp_id.required' => vmsg('ChungThucResendOtpOtpIdLaBatBuoc'),
			'data.otp_id.string' => vmsg('ChungThucResendOtpOtpPhaiLaKieuChuoi'),
			'data.otp_id.max' => vmsg('ChungThucResendOtpOtpPhaiCoDoDaiDuoi10KyTu'),
		];
	}
}
