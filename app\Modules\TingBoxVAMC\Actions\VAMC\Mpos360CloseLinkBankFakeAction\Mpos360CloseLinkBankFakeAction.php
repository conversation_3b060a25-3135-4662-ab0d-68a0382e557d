<?php

namespace App\Modules\TingBoxVAMC\Actions\VAMC\Mpos360CloseLinkBankFakeAction;

use App\Lib\partner\SoundBox;
use App\Exceptions\BusinessException;
use App\Modules\TingBoxVAMC\Enums\TingBoxVAMCEnum;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\CloseLinkBankRequest;

class Mpos360CloseLinkBankFakeAction
{
	protected SoundBox $soundBox;
	protected $isCan = '';

	public function __construct(SoundBox $soundBox)
	{
		$this->soundBox = $soundBox;
	}

	public function run(CloseLinkBankRequest $request)
	{
		$where = [
			'id' => $request->json('data.merchantShopBankId'),
			'merchant_id' => $request->json('data.merchantId'),
			'shop_id' => $request->json('data.userMobileId'),
		];

		$merchantShopBank = MerchantShopBank::query()->where($where)->first();

		if (!$merchantShopBank) {
			throw new BusinessException('Không tìm thấy thông tin ngân hàng.');
		}

		$can = $request->get('fakeCan');

		if ($can == 'SUCCESS') {
			$can = TingBoxVAMCEnum::CAN_GO_TO_TINGBOX_VAMC_SUCCESS;
		}elseif ($can == 'OTP') {
			$can = TingBoxVAMCEnum::CAN_GO_TO_TINGBOX_VAMC_SEND_OTP;
		}else {
			throw new BusinessException('Lỗi nghiệp vụ: ' . $can);
		}

		return [
			'msg' => 'Huỷ liên kết tài khoản ngân hàng thành công.',
			'can' => $can,
			'merchantShopBankId'         => $merchantShopBank->merchant_bank_id,
			'partnerRequestId'       => $merchantShopBank->partner_request_id,
			'requestId'              => $merchantShopBank->request_id,
			'countdownTimeGetNewOtp' => isset($closeLinkBank['data']) && isset($closeLinkBank['data']['expired']) ? $closeLinkBank['data']['expired'] : '',
			'deepLinkConfirm' => isset($closeLinkBank['data']) && isset($closeLinkBank['data']['deepLinkConfirm']) ? $closeLinkBank['data']['deepLinkConfirm'] : '',
			'urlConffirm' => isset($closeLinkBank['data']) && isset($closeLinkBank['data']['urlConffirm']) ? $closeLinkBank['data']['urlConffirm'] : '',
			'bankCode'               => $merchantShopBank->merchantBank->bank_code,
			'merchantShopBankIdAssign' => $request->json('data.merchantShopBankIdAssign')
		];
	}
} // End class
