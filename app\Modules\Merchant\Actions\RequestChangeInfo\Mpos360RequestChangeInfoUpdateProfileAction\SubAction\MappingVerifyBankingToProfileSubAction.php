<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\SubAction;

use App\Modules\Merchant\DTOs\RequestChangeInfo\CreateMerchantRequestDto;

// Thông tin trong key verify sẽ được map và nhét vào key `profiles`
class MappingVerifyBankingToProfileSubAction
{
	public function run(array $request_vefify = [], CreateMerchantRequestDto $dto)
	{
		$saveData = $dto->toArray();

		if (empty($request_vefify)) {
			return $saveData;
		}

		$dataRequest = json_decode($saveData['data_request'], true);
		
		foreach ($request_vefify as $item) {
			$dataRequest[0]['profiles'][$item['field']] = $item['value'];
		}

		$saveData['data_request'] = json_encode($dataRequest);
		return $saveData;
	}
}
