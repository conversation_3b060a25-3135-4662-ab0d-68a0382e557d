<?php

namespace App\Modules\TingBoxVAMC\Actions\MerchantBank\GetInfoBankV2Action;

use App\Lib\Helper;
use App\Lib\partner\SoundBox;
use App\Lib\MnpOnboardNewMcHelper;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Setting;
use App\Modules\TingBoxVAMC\Models\BankVAMC;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\Mpos360GetInfoBankV2Request;


class GetInfoBankV2Action
{
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public string $isShowTrungGian = 'YES';

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run(Mpos360GetInfoBankV2Request $request)
	{
		$nganhNgheNganHangThanhPho = $this->mnpOnboardNewMcHelper->getNganhNgheNganHangThanhPho(true);

		$listBankVamc = BankVAMC::query()->where('isDisplay', '!=', BankVAMC::DISPLAY_NONE)
		  ->where('blockOldVersion', 0)
			->orderBy('sort', 'DESC')
			->get();

		$listDisplay = $listBankVamc->filter(function (BankVAMC $it) {
			return $it->isDisplay == BankVAMC::DISPLAY_PROD;
		});

		$settingDungThuVamc = Setting::query()->firstWhere(['key' => 'LIST_USERNAME_DUNG_THU_VAMC']);

		$listBanhThuNghiem = Collection::make([]);

		if ($settingDungThuVamc) {
			$username = $request->json('data.username');
			$listUsernameDungThu = json_decode($settingDungThuVamc->value, true);
			$listBanhThuNghiem = $listBankVamc->filter(function (BankVAMC $b) use ($username, $listUsernameDungThu) {
				return $b->isDisplay == BankVAMC::DISPLAY_DEV_MODE && in_array($username, $listUsernameDungThu);
			});
		}

		$warningText = 'Khi xác nhận thay đổi, mã QR trên loa Tingbox sẽ tự động cập nhật, mã QR dán giấy cũ (nếu có) sẽ không được sử dụng được. Vui lòng tải mã QR mới tại menu \'QR cửa hàng\', in và dán lại';

		$listBankVamc = $listDisplay->concat($listBanhThuNghiem);

		$listBankVamc = $listBankVamc->map(function (BankVAMC $it) use ($request) {
			$it->termsLink = $it->getTermLinkByVersionApp($request->get('versionApp'));
			return $it;
		});

		$partnerCode = Helper::getPartnerCode($request->json('data.merchantId'));
		
		$r = (new SoundBox())->getDetailMcPartner([
			'code' => $partnerCode,
		]);

		$config = $r['data']['partnerConfigBankRes'];
		$configBank = collect($config)->where('partnerCode', $partnerCode)
																	->whereIn('vaMode', ['VAMC', 'ALL'])
																	->pluck('bankCode')
																	->toArray();
		
		$listBankVamc = $listBankVamc->filter(function (BankVAMC $it) use ($configBank) {
			return in_array($it->bankCode, $configBank) || in_array('ALL', $configBank);
		})->values()->toArray();
		
		$returnData = [
			'listBankCommon' => $nganhNgheNganHangThanhPho['banks'],
			'listBankDirect' => $listBankVamc,
			'holderName' => '',
			'cardIdentity' => '',
			'isShowTrungGian' => $this->isShowTrungGian,
			'warningText' => $warningText
		];

		return $returnData;
	}
}
