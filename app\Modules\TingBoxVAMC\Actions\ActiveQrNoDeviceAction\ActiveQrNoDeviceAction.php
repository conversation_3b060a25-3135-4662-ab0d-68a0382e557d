<?php
namespace App\Modules\TingBoxVAMC\Actions\ActiveQrNoDeviceAction;


use Illuminate\Http\Request;
use App\Lib\partner\SoundBox;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Mpos360Logs\LogRequest;

class ActiveQrNoDeviceAction
{

    protected SoundBox $soundBox;

	public function __construct(SoundBox $soundBox)
	{
		$this->soundBox = $soundBox;
	}


    public function run(Request $request)
    {
        $params = [
			"mcId" => $request->json('data.merchantId'), 
			"mobileUserName" => $request->json('data.userMobileId'),
			"serial" => $request->json('data.serial'),
		];

        $logRequest = LogRequest::query()->forceCreate([
			'merchant_id' => $request->json('data.merchantId'),
			'partner' => 'tingbox',
			'func' => 'activeQrNoDevice',
			'request' => json_encode($params, JSON_UNESCAPED_UNICODE),
			'created_at' => now()->format('Y-m-d H:i:s'),
			'updated_at' => now()->format('Y-m-d H:i:s'),
		]);
        
        $sendSoundBox = $this->soundBox->acticeDeviceApp($params);

        $logRequest->update(['response' => json_encode($sendSoundBox, JSON_UNESCAPED_UNICODE), 'updated_at' => now()->format('Y-m-d H:i:s')]);

        if (empty($sendSoundBox['result'])) {
            throw new BusinessException('Kích hoạt QR trên thiết bị thất bại. Hãy thử lại hoặc liên hệ chuyên viên tư vấn.');
        }

        return [
            'msg' => 'Kích hoạt QR trên thiết bị thành công',
            "merchantId" => $request->json('data.merchantId'), 
			"userMobileId" => $request->json('data.userMobileId'),
			"serial" => $request->json('data.serial'),
			"username" => $request->json('data.username'),
        ];
    }
}