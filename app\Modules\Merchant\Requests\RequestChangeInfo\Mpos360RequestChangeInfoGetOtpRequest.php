<?php

namespace App\Modules\Merchant\Requests\RequestChangeInfo;

use App\Modules\Merchant\Requests\MerchantRequest;
use Illuminate\Validation\Rule;

class Mpos360RequestChangeInfoGetOtpRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}


	public function rules()
	{
		return [
			'data.request_id' => ['required', 'numeric', 'integer', 'min:0'],
			'data.service_code' => [
				'required',
				'string',
				Rule::in(['SMS', 'EMAIL', 'ZALO'])
			],
			'data.object_value' => [
				'required',
				'string',
				function ($attribute, $value, $fail) {
					if ($this->json('data.service_code') === 'EMAIL' && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
						$fail('Gia tri phai la 1 email');
					}
				}
			],
			'data.command_code' => [
				'required',
				'string',
				Rule::in(['MERCHANT', 'MERCHANT_SCAN3'])
			]
		];
	}

	public function isGetEmailOtp(): bool
	{
		return $this->json('data.service_code') == 'EMAIL';
	}

	public function isGetMobileOtp(): bool
	{
		return $this->json('data.service_code') == 'SMS';
	}
} // End class
