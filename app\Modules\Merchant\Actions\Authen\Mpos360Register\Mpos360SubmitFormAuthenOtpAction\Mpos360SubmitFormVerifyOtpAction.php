<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360SubmitFormAuthenOtpAction;

use App\Lib\Helper;
use App\Lib\MnpOnboardNewMcHelper;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Setting;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Enums\OtpResCodeEnum;
use App\Modules\Merchant\Model\Mpos360CodeOtp;
use App\Modules\Merchant\Model\MerchantOnboard;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360Register\Mpos360SubmitFormVerifyOtpRequest;
use App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360SubmitFormAuthenOtpAction\SubAction\CreateCacheAction;

/**
 * Sau khi xác minh sđt thành công, cần làm:
 * 	B1: <PERSON><PERSON><PERSON> thực otp
 * 	B2: <PERSON><PERSON>n gọi api sang bên mnp để thực hiện tạo tài khoản
 */
class  Mpos360SubmitFormVerifyOtpAction
{
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run(Mpos360SubmitFormVerifyOtpRequest $request): array
	{
		if (Helper::isLocalOrDevEnv() && !empty(env('APP_DEBUG'))) {
			goto CREATE_USER;
		}

		// B1: Xác thực OTP
		$otp = trim($request->json('data.otp'));
		$otpId = $request->json('data.otp_id');

		$mpos360CodeOtp = Mpos360CodeOtp::query()->where([
			'id' => $otpId,
			'otp' => $otp,
			'command_code' => 'REGISTER'
		])->first();

		$msg = 'Mã OTP không chính xác hoặc đã hết thời gian hiệu lực. Xin vui lòng kiểm tra lại mã trong tin nhắn hoặc yêu cầu một mã mới';

		if (!$mpos360CodeOtp) {
			$msg = 'Mã OTP không chính xác';
			throw new BusinessException($msg, OtpResCodeEnum::OTP_ERR_KHONG_TIM_THAY);
		}

		if ($mpos360CodeOtp->isFinalStatus()) {
			throw new BusinessException($msg, OtpResCodeEnum::OTP_ERR_DA_DUOC_SU_DUNG);
		}

		if ($mpos360CodeOtp->isExpiredOtp()) {
			throw new BusinessException($msg, OtpResCodeEnum::OTP_ERR_HET_HAN);
		}

		$mpos360CodeOtp->status = Mpos360Enum::MPOS360_OTP_DA_SU_DUNG;
		$r = $mpos360CodeOtp->save();

		if (!$r) {
			throw new BusinessException('Lỗi không xử lý được bản ghi otp');
		}

		CREATE_USER:
		// B2: Gọi sang Mynextpay để tạo yêu cầu mở tài khoản
		$createAccount = $this->mnpOnboardNewMcHelper->taoTaiKhoanMerchant(
			$request->json('data.username'), 
			$request->json('data.password')
		);

		if (!$createAccount['result'] || $createAccount['code'] != 1000 || (!isset($createAccount['data']['mposMcId']) || empty($createAccount['data']['mposMcId']))) {
			$message = isset($createAccount['message']) && !empty($createAccount['message']) ? $createAccount['message'] : "Có lỗi xảy ra";
			$message = sprintf('MNP Err: %s (Code: %s)', $message, $createAccount['code']);
			throw new BusinessException($message);
		}

		// B3: Tạo bản ghi merchant onboard - AI
		// $merchantOnboard = MerchantOnboard::query()->create([
		// 	'merchantId' => $createAccount['data']['mposMcId'],
		// 	'username' => $request->json('data.username'),
		// 	'partnerInviteCode' => $request->json('data.partnerInviteCode', ''), // Thêm mã 
		// 	'createdAt' => now(),
		// 	'updatedAt' => now()
		// ]);

		// if (!$merchantOnboard) {
		// 	throw new BusinessException('Lỗi không tạo được bản ghi merchant onboard');
		// }

		return [
			'can' => Mpos360Enum::CAN_GO_TO_FORM_XAC_THUC_TAI_KHOAN,
			'merchantId' => $createAccount['data']['mposMcId'],
			'status' => 'SUCCESS'
		];
	}
} // End class