<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoDetailAction\SubAction;

use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestCronPushRecordAction\SubAction\GetCccdInfoByQtsRequestIdSubAction;
use Carbon\Carbon;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoGetConfigAction\Mpos360RequestChangeInfoGetConfigAction;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForRepresentationSA\SA\AddAdditionalProfileV3SA;
use Illuminate\Support\Arr;
use PhpParser\Node\Stmt\Break_;

class GenGroupMnpDetailForMobileV3SubAction
{
	public $qtsRequestIdNguoiDuocUyQuyen = false;

	public array $ignoreProfiles = [
		'passportRepresentFrontUrl',
		'passportRepresentBackUrl',
		'passportAuthoriserFrontUrl',
		'passportAuthoriserBackUrl',
		'substituteCertUrls',
		'attachedCertUrls',
		'lostEmailUrls',
		'lostPassportUrls',
		'identificationDocument'
	];

	public bool $isVerifiedCccd = false;

	public function run(Mpos360MerchantRequest $mpos360McRequest)
	{

		$this->isVerifiedCccd = $mpos360McRequest->isVerifiedProfile('', ['passport', 'representPassport'], true);

		$returnData = [];

		$dataRequest = json_decode($mpos360McRequest->data_request, true);

		if (!empty($dataRequest[0]['qts_request_id_nguoi_dc_uy_quyen'])) {
			$this->qtsRequestIdNguoiDuocUyQuyen = $dataRequest[0]['qts_request_id_nguoi_dc_uy_quyen'];
		}

		foreach ($dataRequest as $item) {
			$list = [];

			// Đổi TKNH
			if ($item['type'] == 'CHANGE_BANK_ACCOUN_INFO') {
				$returnData[$item['type']] = [
					'key' => $item['type'],
					'name' => vmsg('Đổi thông tin TKNH'),
					'list' => [],
					'other_data' => (object) [
						'action' => (object) [],
					],
				];


				foreach ($item['profiles'] as $mnpProfikey => $mnpProfileValue) {
					$otherData = [];
					$label = $mnpProfikey;

					switch ($mnpProfikey) {
						case 'bankName':
							$label = __('dttv3.Tên ngân hàng');
							$explode = explode(' - ', $mnpProfileValue);
							$mnpProfileValue = Arr::last($explode);
							break;
						case 'branch':
							$label = __('dttv3.Chi nhánh ngân hàng');
							break;
						case 'accountNo':
							$label = __('dttv3.Số tài khoản');
							$otherData = [
								'is_verified' => 1,
								'icon_verifired_url' => cumtomAsset('images/requestChangeInfo/detail/verified.png')
							];
							break;
						case 'holderName':
							$label = __('dttv3.Tên người thụ hưởng');
							break;
						case 'bankCityName':
							$label = __('dttv3.Tỉnh/Thành phố');
							break;
						default:
							continue 2;
							break;
					}
					
					$list[] = [
						'label' => $label,
						'value' => is_array($mnpProfileValue) ? json_encode($mnpProfileValue) : $mnpProfileValue,
						'other_data' => (object) $otherData
					];
				}

				$list = collect($list)->sortBy(function ($item) {
					return array_search($item['label'], [
						'Tên ngân hàng',
						'Số tài khoản',
						'Tên người thụ hưởng',
						'Chi nhánh ngân hàng',
						'Tỉnh/Thành phố'
					]);
				})->values()->all();
				$returnData[$item['type']]['list'] = $list;
			}

			// Đổi ng đại diện
			if ($item['type'] == 'CHANGE_REPRESENT_INFO') {
				if ($item['choice'] == 'DOI_NGUOI_DAI_DIEN_MOI') {
					$returnData[$item['type']] = [
						'key' => $item['type'],
						'name' => vmsg('Đổi người đại diện mới'),
						'list' => [],
						'other_data' => (object) [
							'action' => (object) [],
						]
					];
				}

				if ($item['choice'] == 'DOI_CCCD_MOI') {
					$returnData[$item['type']] = [
						'key' => $item['type'],
						'name' => vmsg('Đổi CCCD mới'),
						'list' => [],
						'other_data' => (object) [
							'action' => (object) [],
						]
					];
				}
				
				if ($item['choice'] != 'DOI_THONG_TIN_LIEN_HE') {
					foreach ($item['profiles'] as $mnpProfikey => $mnpProfileValue) {
						$label = $mnpProfikey;
	
						if ($mnpProfikey == 'substituteCertUrls') {
							continue;
						}
	
						$value = $mnpProfileValue;
						$otherData = [];
	
						switch ($mnpProfikey) {
							case 'representName':
								$label = vmsg('Tên người đại diện');
								if ($this->isVerifiedCccd) {
									$otherData = [
										'is_verified' => 0,
									];
								}
								break;
							case 'representPassport':
								$label = vmsg('Số CCCD');
								if ($this->isVerifiedCccd) {
									$otherData = [
										'is_verified' => 1,
										'icon_verifired_url' => cumtomAsset('images/requestChangeInfo/detail/verified.png')
									];
								}
								break;
							case 'representIssuePlace':
								$label = vmsg('Nơi cấp');
								if ($this->isVerifiedCccd) {
									$otherData = [
										'is_verified' => 0,
									];
								}
								break;
							default :
								continue 2;
								break;
						}
						
						$list[] = [
							'label' => $label,
							'value' => is_array($value) ? json_encode($value) : $value,
							'other_data' => (object) $otherData
						];
					}

					$returnData[$item['type']]['list'] = $list;
				}

				// Đoạn này, nếu có thay đổi thông tin email/sđt thì cho vô đây
				if (!empty($dataRequest[0]['profiles']['representEmail'])) {
					$returnData['LIEN_HE_GROUP']['list'][] = [
						'label' => 'Email',
						'value' => $dataRequest[0]['profiles']['representEmail'],
						'other_data' => (object) [
							'is_verified' => in_array('representEmail', $dataRequest[0]['otp_success'] ?? []) ? 1 : 0,
							'icon_verifired_url' => cumtomAsset('images/requestChangeInfo/detail/verified.png')
						]
					];
				}

				if (!empty($dataRequest[0]['profiles']['representMobile'])) {
					$returnData['LIEN_HE_GROUP']['list'][] = [
						'label' => 'Số điện thoại',
						'value' => $dataRequest[0]['profiles']['representMobile'],
						'other_data' => (object) [
							'is_verified' => in_array('representMobile', $dataRequest[0]['otp_success'] ?? []) ? 1 : 0,
							'icon_verifired_url' => cumtomAsset('images/requestChangeInfo/detail/verified.png')
						]
					];
				}

				if (!empty($returnData['LIEN_HE_GROUP'])) {
					$returnData['LIEN_HE_GROUP']['key'] = 'LIEN_HE_GROUP';
					$returnData['LIEN_HE_GROUP']['name'] = 'Thông tin liên hệ';
					$returnData['LIEN_HE_GROUP']['other_data'] = (object) [
						'action' => (object) []
					];
				}
			} // End đổi người đại diện

			// Đổi tài khoản đăng nhập mpos
			if ($item['type'] == 'CHANGE_MPOS_ACCOUNT_INFO') {
				$returnData[$item['type']] = [
					'key' => $item['type'],
					'name' => 'Thay thế tài khoản đăng nhập MPOS',
					'list' => [],
					'other_data' => (object) [
						'action' => (object) []
					]
				];
				foreach ($item['profiles'] as $mnpProfikey => $mnpProfileValue) {
					$label = $mnpProfikey;

					if ($mnpProfikey == 'substituteCertUrls') {
						continue;
					}

					$value = $mnpProfileValue;

					switch ($mnpProfikey) {
						case 'accountEmail':
							$label = 'Email đăng nhập';
							break;
						case 'accountMobile':
							$label = 'SĐT đăng nhập';
							break;
						
						default :
							$label = $mnpProfikey;
							break;
					}
					
					$list[] = [
						'label' => $label,
						'value' => is_array($value) ? json_encode($value) : $value,
						'other_data' => (object) []
					];
				}

				$returnData[$item['type']]['list'] = $list;
			}
		} // End foreach

		// Thông tin CCCD của cá nhân được ủy quyền nếu có
		if ($this->qtsRequestIdNguoiDuocUyQuyen) {
			$detailQtsResultDto = app(GetCccdInfoByQtsRequestIdSubAction::class)->run($this->qtsRequestIdNguoiDuocUyQuyen);
			$returnData['CCCD_NGUOI_DUOC_UY_QUYEN'] = [
				'key' => 'CCCD_NGUOI_DUOC_UY_QUYEN',
				'name' => 'CCCD người được ủy quyền',
				'list' => [
					[
						'label' => 'Họ tên',
						'value' => $detailQtsResultDto->fullName,
						'other_data' => (object)[]
					],
					[
						'label' => 'Số CCCD',
						'value' => $detailQtsResultDto->currentCardId,
						'other_data' => (object) [
							'is_verified' => 1,
							'icon_verifired_url' => cumtomAsset('images/requestChangeInfo/detail/verified.png')
						]
					],
					[
						'label' => 'Nơi cấp',
						'value' => 'Cục cảnh sát quản lý hành chính về trật tự xã hội',
						'other_data' => (object) []
					],
				],

				'other_data' => (object) [
					'action' => (object) []
				]
			];
		}

		// Xử lý chứng từ đính kèm
		if (!empty($mpos360McRequest->getRawAttachmentsV3())) {
			$listAtm = [];
			$returnData['ATTACHMENTS'] = [
				'key' => 'ATTACHMENTS',
				'name' => 'Chứng từ đính kèm',
				'list' => [],
				'other_data' => (object) [
					'action' => (object) []
				]
			];

			if ($mpos360McRequest->isDoiThongTinNganHang()) {
				if (!empty($dataRequest[0]['profiles']['positionAuthBank'])) {
					$listAtm[] = [
						'label' => 'Vai trò/Vị trí của người được ủy quyền',
						'value' => $dataRequest[0]['profiles']['positionAuthBank'],
						'other_data' => (object)[]
					];
				}

				if (!empty($dataRequest[0]['profiles']['bankMutualRelation'])) {
					$listAtm[] = [
						'label' => 'Mối quan hệ giữa hai bên',
						'value' => $dataRequest[0]['profiles']['bankMutualRelation'],
						'other_data' => (object)[]
					];
				}
			}
			
			if ($mpos360McRequest->isYeuCauDoiNguoiDaiDienMoi()) {
				if (!empty($dataRequest[0]['profiles']['representPosition'])) {
					$listAtm[] = [
						'label' => 'Chức vụ/Vị trí của người đại diện mới',
						'value' => $dataRequest[0]['profiles']['representPosition'],
						'other_data' => (object)[]
					];
				}

				if (!empty($dataRequest[0]['profiles']['representMutualRelation'])) {
					$listAtm[] = [
						'label' => 'Mối quan hệ giữa 2 bên',
						'value' => $dataRequest[0]['profiles']['representMutualRelation'],
						'other_data' => (object)[]
					];
				}
			}

			$rawAttachments = $mpos360McRequest->getRawAttachmentsV3();

			$additionalProfile = app(AddAdditionalProfileV3SA::class)->run($mpos360McRequest);
			$identificationDocument = collect($additionalProfile)->where('profileKey', 'identificationDocument')->first();
			$listGiayTo = collect($identificationDocument['other_data']['list'])->keyBy('value')->all();
			foreach ($rawAttachments['id_documents'] as $loaiDinhKem => $listUrlDinhKem) {

				$listAtm[] = [
					'label' => $listGiayTo[$loaiDinhKem]['label'] ?? $loaiDinhKem,
					'value' => $loaiDinhKem,
					'other_data' => [
						'display_type' => 'attachments',
						'editable' => 0,
						'listAttachments' => $listUrlDinhKem
					],
				];
			}

			

			$returnData['ATTACHMENTS']['list'] = $listAtm;
		}

		if ($mpos360McRequest->mpos360McSupplements->isNotEmpty()) {
			foreach ($mpos360McRequest->mpos360McSupplements as $index => $supp) {
				
				if (!$supp->isHienThiBoSungThongTinManHinhChiTiet()) {
					continue;
				}

				$keyChungTuBoSung = 'ATTACHMENTS_' . ($index+1);
				$returnData[$keyChungTuBoSung] = [
					'key' => $keyChungTuBoSung,
					'name' => sprintf('[%s] - Bổ sung thông tin lần thứ %s', Carbon::createFromTimestamp($supp->time_created)->format('d/m/Y H:i'), $index+1),
					'list' => []
				];

				$rawAttachments = json_decode($supp->data_request, true);
				$listAtm = [];
				if (!empty($rawAttachments['raw_attachments']['id_documents'])) {
					foreach ($rawAttachments['raw_attachments']['id_documents'] as $loaiDinhKem => $listUrlDinhKem) {
						$listAtm[] = [
							'label' => $listGiayTo[$loaiDinhKem]['label'] ?? $loaiDinhKem,
							'value' => $loaiDinhKem,
							'other_data' => [
								'display_type' => 'attachments',
								'editable' => 0,
								'listAttachments' => $listUrlDinhKem
							],
						];
					}
					
					$returnData[$keyChungTuBoSung]['list'] = $listAtm;
				}else {
					$listAtm[] = [
						'label' => $supp->supplement_reason,
						'value' => '',
						'other_data' => (object) [],
					];
					$returnData[$keyChungTuBoSung]['list'] = $listAtm;
				}
			}
		}

		// Xử lý chữ
		if ($mpos360McRequest->status_sign != Mpos360Enum::MPOS360_MC_SIGN_STT_KHONG_CO_CHU_KY) {
			$returnData['SIGNATURE'] = [
				'key' => 'SIGNATURE',
				'name' => vmsg('Chữ ký xác nhận'),
				'list' => [
					[
						'label' => '',
						'value' => $mpos360McRequest->getSignatureUrlV3(),
						'other_data' => [
							'display_type' => 'signature',
							'editable' => 0
						]
					]
				],

				'other_data' => (object) [
					'action' => (object) []
				]
			];
		}

		return $returnData;
	}
} // End class
