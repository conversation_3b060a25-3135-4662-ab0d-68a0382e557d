<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUploadPhuLucKyAction;

use Exception;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\V2\Mpos360RequestChangeInfoUploadPhuLucKyRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAttachSignatureAction\SubAction\GetCanByRequestChangeInfoSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessAction\SubActionV2\KiemTraPhuongThucKySubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\SubAction\GetPhuongThucQuetB3Ver2SubAction;

class Mpos360RequestChangeInfoUploadPhuLucKyAction
{
	public function run(Mpos360RequestChangeInfoUploadPhuLucKyRequest $request)
	{
		$id = $request->json('data.id');
		$mpos360McRequest = Mpos360MerchantRequest::query()->find($id);

		if (!$mpos360McRequest) {
			throw new BusinessException('Lỗi: không tìm thấy thông tin yêu cầu');
		}

		mylog(['Yeu cau duoc xu ly la' => $mpos360McRequest->only(['id', 'mynextpay_id', 'order_code'])]);

		$dataRequest = json_decode($mpos360McRequest->data_request, true);
		$signatureUrl = trim($request->json('data.signature_url'));

		if ($mpos360McRequest->isKyGiay()) {
			if (empty($signatureUrl)) {
				throw new BusinessException('Bạn cần upload file chữ ký đính kèm lên');
			}
		}

		$dataRequest[0]['signProcess']['signature_url'] = $signatureUrl;
		$mpos360McRequest->data_request = json_encode($dataRequest);
		$mpos360McRequest->status_sign = Mpos360Enum::MPOS360_MC_SIGN_STT_CHUA_KY;
		$r = $mpos360McRequest->save();

		if (!$r) {
			throw new BusinessException('Lỗi lưu thông tin chữ ký');
		}

		// Liệt kê ra các phương thức chứng thực => đề xuất xác thực tương ứng
		$scanStep3 = app(GetPhuongThucQuetB3Ver2SubAction::class)->run($mpos360McRequest->merchant_id);

		$can = app(GetCanByRequestChangeInfoSubAction::class)->run($mpos360McRequest);

		return [
			'id' => $mpos360McRequest->id,
			'msg' => 'Xác nhận ký thành công',
			'status' => '1',
			'can' => $can,
			'scan_method' => $scanStep3
		];
	}
} // End class
