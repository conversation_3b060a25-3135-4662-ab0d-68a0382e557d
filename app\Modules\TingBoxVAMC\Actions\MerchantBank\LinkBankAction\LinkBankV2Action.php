<?php

namespace App\Modules\TingBoxVAMC\Actions\MerchantBank\LinkBankAction;

use App\Lib\Logs;
use App\Lib\Helper;
use Illuminate\Support\Str;
use App\Lib\partner\SoundBox;
use App\Lib\MnpOnboardNewMcHelper;
use Illuminate\Support\Facades\DB;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Setting;
use App\Modules\TingBoxVAMC\Enums\TingBoxVAMCEnum;
use App\Modules\Merchant\Model\Mpos360Logs\LogRequest;
use App\Modules\TingBoxVAMC\Actions\VAMC\RequestLinkVAMCAction;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\Mpos360LinkBankV2Request;
use App\Modules\TingBoxVAMC\Actions\MerchantBank\LinkBankAction\SubAction\CheckRuleLienKetSubAction;
use App\Modules\TingBoxVAMC\Actions\MerchantBank\LinkBankAction\SubAction\CreateMerchantBankSubAction;
use App\Modules\TingBoxVAMC\Actions\MerchantBank\LinkBankAction\SubAction\CreateMerchantShopBankSubAction;
use App\Modules\TingBoxVAMC\Actions\MerchantBank\LinkBankAction\SubAction\KiemTraDieuKienLienKetSubAction;
use App\Modules\TingBoxVAMC\Actions\MerchantBank\ConfirmOtpBankAction\SubAction\SyncAssignTingBoxNowSubAction;

class LinkBankV2Action
{
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;
	protected SoundBox $soundBox;
	protected $isCan = '';
	public $vaBankNumber = false;

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper, SoundBox $soundBox)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
		$this->soundBox = $soundBox;
	}

	public function run(Mpos360LinkBankV2Request $request)
	{
		Logs::writeInfo("LinkBankV2Action", $request->json('data'));

		if (!empty($request->json('data.qrCode'))) {
			$response = $this->soundBox->checkVaBankNumber([
				'mcId' => $request->json('data.merchantId'),
				'qrCode' => $request->json('data.qrCode')
			]);
	
			if (empty($response)) {
				throw new BusinessException('Không thể kết nối tới hệ thống kiểm tra VA Bank. Vui lòng thử lại sau.');
			}
	
			if (empty($response['data']['verify'])) {
				$errorMessage = 'Mã QR dán của bạn không hợp lệ hoặc đã được sử dụng';
				throw new BusinessException($errorMessage);
			}

			$this->vaBankNumber = $response['data']['vaBankNumber'];
		}
		
		$currentPartnerCode = Helper::getPartnerCode($request->json('data.merchantId'));

		$p = [
			'mcId' => $request->json('data.merchantId'),
			'muId' => $request->json('data.mobileUserId'),
			'partnerCode' => $currentPartnerCode,
		];

		$getDetailTingbox = $this->soundBox->getMcTingBox($p);

		if (empty($getDetailTingbox['data']['mcUserId'])) {
			throw new BusinessException('Không tìm thấy thông tin merchant Tingbox');
		}

		app(KiemTraDieuKienLienKetSubAction::class)->run(
			$request,
			$getDetailTingbox,
			$request->json('data.bankCode')
		);

		// Duy nhất gắn với mỗi VA
		$mcUserId = isset($getDetailTingbox['data']['mcUserId']) ? $getDetailTingbox['data']['mcUserId'] : ''; 
		
		$params = [
			'providerCode' => $request->json('data.channelCode'),
			'merchantType' => 'PERSONAL',
			'merchantName' =>  Str::title(Helper::removeVietnameseAccents($request->json('data.bankAccountName'))),
			'merchantAddress' => Str::ascii($getDetailTingbox['data']['merchantAddress']),
			'merchantMcc' => $getDetailTingbox['data']['merchantMcc'] ?? 'OTHER',
			'bankCode' => $request->json('data.bankCode'),
			'bankIssuerAccountNo' => $request->json('data.bankAccountNo'),
			'bankIssuerAccountName' => Str::title(Helper::removeVietnameseAccents($request->json('data.bankAccountName'))),
			'bankIssuerIdentity' => $request->json('data.bankIdentity'),
			'bankIssuerMobile' => $request->json('data.bankMobile'),
			'bankIssuerEmail' => $request->json('data.bankEmail', ''),

			// Bộ mpos
			'mposMcId' => $request->json('data.merchantId'),
			'mposUserId' => $getDetailTingbox['data']['mposUserId'] ?? '',
			'mposUserMobile' => $request->json('data.mobileUserId'),

			// Bộ tingbox
			'mcUserId' => $mcUserId, // unique tren moi lan tao
			'mcMcId' => isset($getDetailTingbox['data']['mcMcId']) ? $getDetailTingbox['data']['mcMcId'] : '', // ID của merchant (Cột id khóa chính hay cột nào)
			'mcUserMobile' => isset($getDetailTingbox['data']['mcUserMobile']) ? $getDetailTingbox['data']['mcUserMobile'] : '', // Mobile của merchant (Có phải SĐT của MC không?)
		];

		if (!empty($this->vaBankNumber)) {
			$params['vaBankNumber'] = $this->vaBankNumber;
		}

		$settingDungThuVAMC = Setting::query()->firstWhere(['key' => 'LIST_USERNAME_DUNG_THU_VAMC']);
		if ($settingDungThuVAMC) {
			$listUserName = json_decode($settingDungThuVAMC->value, true);

			if (in_array($request->json('data.username'), $listUserName)) {
				$params['bankIssuerEmail'] = '<EMAIL>';
			}
		}

		$logRequest = LogRequest::query()->forceCreate([
			'merchant_id' => $request->json('data.merchantId'),
			'partner' => 'va',
			'func' => 'linkBank',
			'request' => json_encode($params, JSON_UNESCAPED_UNICODE),
			'created_at' => now()->format('Y-m-d H:i:s'),
			'updated_at' => now()->format('Y-m-d H:i:s'),
		]);

		if (!$logRequest) {
			throw new BusinessException('Lỗi lưu trữ dữ liệu thất bại.');
		}


		$linkBank = app(RequestLinkVAMCAction::class)->run($params);
		
		

		$logRequest->update(['response' => json_encode($linkBank, JSON_UNESCAPED_UNICODE), 'updated_at' => now()->format('Y-m-d H:i:s')]);
		
		// Không có mã lỗi báo  về thì bắn lỗi liên kết
		if ( empty($linkBank['error_code']) ) {
			$msgDefault = sprintf('Đối tác ngân hàng thực hiện liên kết không có phản hồi. Vui lòng thử lại sau!');
			throw new BusinessException($msgDefault);
		}

		if ($linkBank['error_code'] == '00') {
			// Không cần OTP mà thành công luôn
			$this->isCan = TingBoxVAMCEnum::CAN_GO_TO_TINGBOX_VAMC_SUCCESS;
		}elseif ($linkBank['error_code'] == '475') {
			// Cần thực hiện otp
			$this->isCan = TingBoxVAMCEnum::CAN_GO_TO_TINGBOX_VAMC_SEND_OTP;
		}else {
			// Mã lỗi khác: Show message kèm thông tin lỗi 
			$listMaLoi = TingBoxVAMCEnum::DEFINE_ERROR_MESSAGE_VAMC;
			$currentMaLoi = $linkBank['error_code'] ?? -1;
			
			if (isset($listMaLoi[$currentMaLoi])) {
				$msgDefault = $listMaLoi[$currentMaLoi] . sprintf(' (Mã lỗi: %s)', $currentMaLoi);
			}else {
				$error = $linkBank['error_message'] ?? 'Liên kết tài khoản ngân hàng thất bại. Hãy liên hệ CSKH để được hỗ trợ';
				$msgDefault = sprintf('%s (Mã lỗi: %s)', $error, $currentMaLoi);
			}
			
			throw new BusinessException($msgDefault);
		}

		DB::beginTransaction();
		try {

			$params = [
				'merchant_id'        => $request->json('data.merchantId'),
				'bank_code'          => strtoupper($request->json('data.bankCode')),
				'account_number'     => $request->json('data.bankAccountNo'),
				'account_holder'     => $request->json('data.bankAccountName'),
				'bank_identity' => $request->json('data.bankIdentity'),
				'bank_mobile' => $request->json('data.bankMobile'),
				'bank_email' => $request->json('data.bankEmail'),
			
			];

			$merchantBank = app(CreateMerchantBankSubAction::class)->run($params);

			$params = [
				'username' => $request->json('data.username'),
				'merchant_id'        => $request->json('data.merchantId'),
				'merchant_bank_id'      => $merchantBank->id,
				'is_default'       		=> $request->json('data.default'),

				'account_type' => 1,
				'account_qr' => isset($linkBank['data']) && isset($linkBank['data']['qrCode']) ? $linkBank['data']['qrCode'] : '',
				'account_qr_display' => '',
				'data_linked'        => json_encode($linkBank, JSON_UNESCAPED_UNICODE),
				'request_id' => isset($linkBank['data']) && isset($linkBank['data']['mcRequestId']) ? $linkBank['data']['mcRequestId'] : '',
				'partner_request_id' => isset($linkBank['data']) && isset($linkBank['data']['vaNextpayNumber']) ? $linkBank['data']['vaNextpayNumber'] : '',
				'partner_code' => 'VA',
				'shop_id' => $request->json('data.mobileUserId'),
				'is_sync_tingbox' => 1,
				'status_link' => 0,
				'linkingMaxResend' => $linkBank['data']['maxResend'] ?? 3,
				'linkingMaxSubmit' => $linkBank['data']['maxSubmit'] ?? 3,
				'linkingTempLockedTime' => $linkBank['data']['tempLockedTime'] ?? 0,
				'linkingOtpExpired' => !empty($linkBank['data']['expired']) ? (now()->timestamp + $linkBank['data']['expired']) 
																																		: 0,
				'isNeedCreateTrungGian' => !empty($getDetailTingbox['data']['hasBankVanp']) ? 0 : 1, // nếu có bank trung gian rồi thì ko cần tạo nữa
			];

			$merchantShopBank = app(CreateMerchantShopBankSubAction::class)->run($params);
			DB::commit();
		} catch (\Throwable $th) {
			
			DB::rollBack();
			throw new BusinessException(
				'Liên kết tài khoản ngân hàng thất bại. Hãy liên hệ CSKH để được hỗ trợ',
				-1
			);
		} 

		if ($linkBank['error_code'] == '00') {
			try {
				app(SyncAssignTingBoxNowSubAction::class)->run($merchantShopBank);
				
			}catch(\Throwable $th) {
				throw new BusinessException('Tài khoản ngân hàng không thể liên kết sang Tingbox.');
			}
		}

		return [
			'merchantBankId'         => $merchantBank->id,
			'partnerRequestId'       => isset($linkBank['data']) && isset($linkBank['data']['vaNextpayNumber']) ? $linkBank['data']['vaNextpayNumber'] : '',
			'requestId'              => isset($linkBank['data']) && isset($linkBank['data']['mcRequestId']) ? $linkBank['data']['mcRequestId'] : '',
			'otpExpiredInSecond' => isset($linkBank['data']) && isset($linkBank['data']['expired']) ? $linkBank['data']['expired'] : '0',
			'deepLinkConfirm' => isset($linkBank['data']) && isset($linkBank['data']['deepLinkConfirm']) ? $linkBank['data']['deepLinkConfirm'] : '',
			'urlConffirm' => isset($linkBank['data']) && isset($linkBank['data']['urlConffirm']) ? $linkBank['data']['urlConffirm'] : '',
			'bankCode'               => $request->json('data.bankCode'),
			'can'                    => $this->isCan,
			'merchantShopBankId'         => $merchantShopBank ? $merchantShopBank->id : '',
			'numberOtpSendMax'         => isset($linkBank['data']) && isset($linkBank['data']['maxResend']) ? $linkBank['data']['maxResend'] : '',
			'timeLocked'         => isset($linkBank['data']) && isset($linkBank['data']['tempLockedTime']) ? $linkBank['data']['tempLockedTime'] : '',
			'methodConfirm' => isset($linkBank['data']['methodConfirm']) ? $linkBank['data']['methodConfirm'] : '',
			'countdownTimeGetNewOtp' => '0',
			'confirmOtpParams' => [
				'username' => $request->json('data.username'),
				'merchantId' => $merchantBank->merchant_id,
				'bankCode' => $merchantBank->bank_code,
				'mobileUserId' => $request->json('data.mobileUserId'),
				'merchantShopBankIdAssign' => '',
				'action' => 'LINK',
				'merchantBankId' => $merchantBank->id,
				'partnerRequestId' => $linkBank['data']['vaNextpayNumber'],
				'requestId' => $linkBank['data']['mcRequestId'],
				'merchantShopBankId' => optional($merchantShopBank)->id,
				'otp' => ''
			]
		];
	}
}
