<?php

namespace App\Modules\Merchant\Actions\ChungThuc\Mpos360ChungThucCreateAction;

use Exception;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360ChungThuc;
use App\Modules\Merchant\Requests\ChungThuc\Mpos360ChungThucCreateRequest;

class Mpos360ChungThucCreateAction
{
	public function run(Mpos360ChungThucCreateRequest $request)
	{
		$chungThucId = $request->json('data.id');
		$qtsRequestId = trim($request->json('data.qts_request_id'));

		$mpos360ChungThuc = Mpos360ChungThuc::query()->where(['id' => $chungThucId, 'method_code' => 'QTS'])->first();

		if (!$mpos360ChungThuc) {
			throw new BusinessException('Lỗi không tìm được bản ghi chứng thực');
		}

		$mpos360ChungThuc->qts_request_id = $qtsRequestId;
		$mpos360ChungThuc->status = Mpos360Enum::MPOS360_CHUNG_THUC_STT_DANG_XAC_NHAN;
		$mpos360ChungThuc->time_updated = now()->timestamp;
		$r = $mpos360ChungThuc->save();

		if (!$r) {
			throw new BusinessException('Lỗi không lưu được trạng thái xác nhận');
		}

		$returnData = [
			'id' => $mpos360ChungThuc->id,
			'msg' => 'Cập nhật bản ghi chứng thực thành công'
		];

		return $returnData;
	} // End method
} // End class
