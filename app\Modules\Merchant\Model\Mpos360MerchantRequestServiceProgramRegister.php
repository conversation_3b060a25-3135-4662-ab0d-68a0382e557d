<?php

namespace App\Modules\Merchant\Model;

use Illuminate\Database\Eloquent\Model;

class Mpos360MerchantRequestServiceProgramRegister extends Model
{
	protected $connection = 'mpos360_data';

	protected $table      = 'merchant_request_service_program_register';

	protected $guarded    = [];

	public $timestamps    = false;

	protected $dates      = [];

	protected $hidden     = [];

	public function getDataRequest(): array {
		return json_decode($this->data_request, true);
	}

	public function getSignatureUrl(): string {
		$dataRequest = $this->getDataRequest();
		return $dataRequest['signatureUrl'];
	}

	public function isLoaiMcHKD(): bool {
		$dataRequest = $this->getDataRequest();
		return $dataRequest['merchantType'] ==  'HOUSEHOLD' || $dataRequest['merchantType'] ==  'INDIVIDUAL';
	}

	public function isLoaiMcCongTy(): bool {
		return !$this->isLoaiMcHKD();
	}

	public function isMatchingCccd($currentCardId='', $oldCardId=''): bool {
		$dataRequest = $this->getDataRequest();
		$merchantPassport = $dataRequest['merchantPassport'];
		return $merchantPassport == $currentCardId || $merchantPassport == $oldCardId;
	}
} // End class
