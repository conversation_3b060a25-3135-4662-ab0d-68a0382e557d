<?php

namespace App\Modules\TingBoxVAMC\Actions\VAMC;

use App\Lib\Logs;
use App\Lib\partner\VA;

class CloseLinkVAMCAction
{
	public VA $va;

	public function __construct(VA $va)
	{
		$this->va = $va;
	}

	/**
	 * {
		"error_code": "00",
		"error_message": "Thành công",
		"checksum": "695ac96973e9e237cc3704701554a645",
		"data": {
			"mcRequestId": "MPOS-**********",
			"vaNextpayNumber": "NPCWIZGORXZOU2G",
			"vaBankNumber": "962VMS5071857738526",
			"qrCode": "38630010A000000727013300069704180119962VMS50718577385260208QRIBFTTA53037045802VN62280824MCDGXQ8HZSEJQNP Mua hang63047d3c",
			"qrImage": "",
			"status": "INACTIVE",
			"channelCode": "",
			"providerCode": "",
			"vaReference": "MCDGXQ8HZSEJQNP",
			"transferDesc": "MCDGXQ8HZSEJQNP Mua hang",
			"urlConffirm": "",
			"deepLinkConfirm": "",
			"methodConfirm": ""
		}
	}
	 */
	public function run($params = [])
	{
		$p =  [
			'app_id' => env('VAG_APP_ID'),
			'mcRequestId' => $params['mcRequestId'],
			'vaNextpayNumber' => $params['vaNextpayNumber']
		];
		Logs::writeInfo("CloseLinkVAMCAction", $p);
		mylog(['Param-VA-Raw' => $p]);

		$dataAsJson = json_encode($p);
		$encrypt = $this->va->encrypt($dataAsJson);

		$checksum = $this->va->createChecksum($encrypt);

		$dataPost = [
			'app_id' => VAG_APP_ID,
			'data' => $encrypt,
			'checksum' => $checksum,
		];

		$url = VAG_BASE_URL . '/v1/merchant/va-mc/close-link';
		try {
			$r = $this->va->call($url, $dataPost, 5);
		} catch (\Throwable $th) {
			$r = [];
		}
		Logs::writeInfo("CloseLinkVAMCAction-Response", $r);

		mylog(['Response-VA-Raw' => $r]);
		return $r;
	}
} // End class