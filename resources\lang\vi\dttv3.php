<?php

return [
  '<PERSON>ail là bắt buộc' => 'Email là bắt buộc',
  'Email phải là kiểu chuỗi' => 'Email phải là kiểu chuỗi',
  'Email không đúng định dạng' => 'Email không đúng định dạng',
  'SĐT là bắt buộc' => 'SĐT là bắt buộc',
  'SĐT phải là kiểu chuỗi' => 'SĐT phải là kiểu chuỗi',
  'SĐT có độ dài tối thiểu phải là 10 ký tự số' => 'SĐT có độ dài tối thiểu phải là 10 ký tự số',
  'SĐT có độ dài tối đa phải là 12 ký tự số' => 'SĐT có độ dài tối đa phải là 12 ký tự số',
  'Trường chọn thay thế tài khoản mpos là bắt buộc' => 'Trường chọn thay thế tài khoản mpos là bắt buộc',
  'Trường chọn thay thế tài khoản mpos phải là kiểu mảng' => 'Trường chọn thay thế tài khoản mpos phải là kiểu mảng',
  'Trường thay thế TKĐN mpos phải là kiểu chuỗi' => 'Trường thay thế TKĐN mpos phải là kiểu chuỗi',
  'Trường thay thế TKĐN mpos không thuộc các giá trị đã chỉ định' => 'Trường thay thế TKĐN mpos không thuộc các giá trị đã chỉ định',


  '*Email đã tồn tại, vui lòng nhập email khác' => '*Email đã tồn tại, vui lòng nhập email khác',
  '*SĐT đã tồn tại, vui lòng nhập SĐT khác' => '*SĐT đã tồn tại, vui lòng nhập SĐT khác',
  'Thành công' => 'Thành công',

  'Lỗi truy vấn đối tác MNP' => 'Lỗi truy vấn đối tác MNP',

  'Tạo yêu cầu đổi thông tin thành công' => 'Tạo yêu cầu đổi thông tin thành công',

  'Lỗi không tạo được bản ghi yêu cầu thay đổi thông tin' => 'Lỗi không tạo được bản ghi yêu cầu thay đổi thông tin',
  'Lỗi: không tạo được yc đổi thông tin TKNH' => 'Lỗi: không tạo được yc đổi thông tin TKNH',
  'Lỗi: ít nhất bạn phải thay đổi Email hoặc SĐT' => 'Lỗi: ít nhất bạn phải thay đổi Email hoặc SĐT',
  'CCCD mới là bắt buộc' => 'CCCD mới là bắt buộc',
  'CCCD của người đại diện mới là bắt buộc' => 'CCCD của người đại diện mới là bắt buộc',
  'Email người đại diện là bắt buộc' => 'Email người đại diện là bắt buộc',
  'SĐT người đại diện là bắt buộc' => 'SĐT người đại diện là bắt buộc',
  'Lỗi không tạo được bản ghi yêu cầu thay đổi' => 'Lỗi không tạo được bản ghi yêu cầu thay đổi',
  'Bạn cần xác minh otp' => 'Bạn cần xác minh otp',

  'Id yêu cầu là bắt buộc' => 'Id yêu cầu là bắt buộc',
  'Lỗi: YC đổi thông tin không tồn tại' => 'Lỗi: YC đổi thông tin không tồn tại',

  'Lỗi: không tìm thấy thông tin yêu cầu' => 'Lỗi: không tìm thấy thông tin yêu cầu',
  'Lỗi: Bạn không phải chủ sở hữu yc này' => 'Lỗi: Bạn không phải chủ sở hữu yc này',
  'Lỗi: CCCD không trùng khớp với thông tin của người đại diện hiện tại. Vui lòng sử dụng đúng CCCD còn hạn sử dụng và là bản mới nhất.' => 'Lỗi: CCCD không trùng khớp với thông tin của người đại diện hiện tại. Vui lòng sử dụng đúng CCCD còn hạn sử dụng và là bản mới nhất.',
  'Ghi nhận kết quả thành công' => 'Ghi nhận kết quả thành công',
  'Lỗi: không tìm được cccd chứng thực' => 'Lỗi: không tìm được cccd chứng thực',
  'Lỗi: bạn phải truyền thông tin qts id lên (passport)' => 'Lỗi: bạn phải truyền thông tin qts id lên (passport)',
  'Lỗi update thông tin hồ sơ vào bản ghi' => 'Lỗi update thông tin hồ sơ vào bản ghi',

  /**
   * @param 'Mpos360RequestChangeInfoAllOtpProfileSuccessRequest'
   */
  'Id là bắt buộc' => 'Id là bắt buộc',
  'Id phải là dạng số' => 'Id phải là dạng số',
  'Id phải là số nguyên' => 'Id phải là số nguyên',
  'Id phải có giá trị là 1' => 'Id phải có giá trị là 1',

  'Trường xác thực là bắt buộc' => 'Trường xác thực là bắt buộc',
  'Trường xác thực phải là kiểu mảng' => 'Trường xác thực phải là kiểu mảng',
  'profileKey là bắt buộc' => 'profileKey là bắt buộc',
  'profileKey phải là kiểu chuỗi' => 'profileKey phải là kiểu chuỗi',
  'giá trị hồ sơ là bắt buộc' => 'giá trị hồ sơ là bắt buộc',
  'giá trị hồ sơ phải là kiểu chuỗi' => 'giá trị hồ sơ phải là kiểu chuỗi',
  'giá trị hồ sơ phải có độ dài tối thiểu là 2 ký tự' => 'giá trị hồ sơ phải có độ dài tối thiểu là 2 ký tự',
  'StatusVerify là bắt buộc' => 'StatusVerify là bắt buộc',
  'StatusVerify phải thuộc giá trị cho phép: 1' => 'StatusVerify phải thuộc giá trị cho phép: 1',
  'Thời gian xác thực là bắt buộc' => 'Thời gian xác thực là bắt buộc',
  'Thời gian xác thực phải là kiểu số' => 'Thời gian xác thực phải là kiểu số',
  'Thời gian xác thực phải là kiểu số nguyên' => 'Thời gian xác thực phải là kiểu số nguyên',

  'Thiếu thông tin Email cần thay đổi (authoriserEmail)' => 'Thiếu thông tin Email cần thay đổi (authoriserEmail)',
  'Thiếu thông tin SĐT cần thay đổi (authoriserContactNumber)' => 'Thiếu thông tin SĐT cần thay đổi (authoriserContactNumber)',
  'Trường chứng thực thông tin chỉ được phép có 1 phần tử duy nhất (passport)' => 'Trường chứng thực thông tin chỉ được phép có 1 phần tử duy nhất (passport)',
  'Thiếu thông tin passport (qtsRequestId)' => 'Thiếu thông tin passport (qtsRequestId)',
  'passport phải là 1 uuid' => 'passport phải là 1 uuid',
  'Phải có ít nhất 1 thông tin (passport)' => 'Phải có ít nhất 1 thông tin (passport)',

	/**
	 * @param Mpos360RequestChangeInfoMarkAsDoneAction::class
	 */
	'Lỗi: không tìm thấy yêu cầu thay đổi' => 'Lỗi: không tìm thấy yêu cầu thay đổi',
	'Lỗi: yêu cầu của bạn có trạng thái không hợp lệ' => 'Lỗi: yêu cầu của bạn có trạng thái không hợp lệ',
	'Thiếu thông tin QtsRequestId' => 'Thiếu thông tin QtsRequestId',
	'Thiếu thông tin OtpId' => 'Thiếu thông tin OtpId',
	'Thiếu thông tin tỷ lệ khớp khuôn mặt' => 'Thiếu thông tin tỷ lệ khớp khuôn mặt',
	'Lỗi: không đánh dấu được yc là mới tạo' => 'Lỗi: không đánh dấu được yc là mới tạo',
	'Lỗi không tìm thấy bản ghi chứng thực' => 'Lỗi không tìm thấy bản ghi chứng thực',
	'Lỗi: xác thực người thay đổi không giống với số CMT/CCCD hiện tại của bạn' => 'Lỗi: xác thực người thay đổi không giống với số CMT/CCCD hiện tại của bạn',
	'Lỗi cập nhật chứng thực' => 'Lỗi cập nhật chứng thực',

	/**
	 * @param Mpos360RequestChangeInfoGetOtpAction
	 */
	'Lỗi: không tìm thấy bản ghi yc thay đổi thông tin' => 'Lỗi: không tìm thấy bản ghi yc thay đổi thông tin',
	'Mã xác thực OTP đã được gửi đến email. Vui lòng kiểm tra và xác thực' => 'Mã xác thực OTP đã được gửi đến email :email. Vui lòng kiểm tra và xác thực',
	'Mã xác thực OTP đã được gửi đến SĐT. Vui lòng kiểm tra và nhập vào ô dưới đây' => 'Mã xác thực OTP đã được gửi đến SĐT :mobile. Vui lòng kiểm tra và nhập vào ô dưới đây',

	/**
	 * @param Mpos360RequestChangeInfoVerifyProfileAction
	 */
	'Lỗi: không tìm thấy yêu cầu của bạn' => 'Lỗi: không tìm thấy yêu cầu của bạn',
	'Lỗi: sai lệch bản ghi yêu cầu' => 'Lỗi: sai lệch bản ghi yêu cầu',
	'Mã OTP không đúng!' => 'Mã OTP không đúng!',
	'Mã OTP không đúng hoặc đã hết hạn, vui lòng kiểm tra lại trong tin nhắn, hoặc lấy lại mã khác' => 'Mã OTP không đúng hoặc đã hết hạn, vui lòng kiểm tra lại trong tin nhắn, hoặc lấy lại mã khác',
	'Lỗi: Otp đã được sử dụng' => 'Lỗi: Otp đã được sử dụng',
	'Lỗi: sai thông tin otp của yêu cầu' => 'Lỗi: sai thông tin otp của yêu cầu',
	'Lỗi: không cập nhật otp về trạng thái cuối' => 'Lỗi: không cập nhật otp về trạng thái cuối',
	'Lỗi: không thể đánh dấu hồ sơ là đã xác thực' => 'Lỗi: không thể đánh dấu hồ sơ là đã xác thực',


	'Đổi người đại diện mới' => 'Đổi người đại diện mới',
	'Cập nhật CCCD mới' => 'Cập nhật CCCD mới',
	'Đổi thông tin liên hệ' => 'Đổi thông tin liên hệ',
	'Không xác định' => 'Không xác định',
	'Đổi TKNH' => 'Đổi TKNH',
	'Thông tin yêu cầu' => 'Thông tin yêu cầu',
	'Cá nhân' => 'Cá nhân',
	'Công ty' => 'Công ty',
	'Hộ kinh doanh' => 'Hộ kinh doanh',
	'Đổi người đại diện mới' => 'Đổi người đại diện mới',
	'Đổi thông tin người đại diện hiện tại' => 'Đổi thông tin người đại diện hiện tại',

	'Đổi số TKNH khác của hộ kinh doanh' => 'Đổi số TKNH khác của hộ kinh doanh',
	'Đổi số TKNH cá nhân được HKD ủy quyền' => 'Đổi số TKNH cá nhân được HKD ủy quyền',
	'Đổi số TKNH của doanh nghiệp' => 'Đổi số TKNH của doanh nghiệp',
	'Đổi số TKNH cá nhân được doanh nghiệp ủy quyền' => 'Đổi số TKNH cá nhân được doanh nghiệp ủy quyền',
	'Đổi TKNH' => 'Đổi TKNH',
	'Đổi người đại diện mới của HKD' => 'Đổi người đại diện mới của HKD',
	'Đổi CCCD mới của HKD' => 'Đổi CCCD mới của HKD',
	'Đổi thông tin liên hệ của HKD' => 'Đổi thông tin liên hệ của HKD',
	'Đổi người đại diện mới của doanh nghiệp' => 'Đổi người đại diện mới của doanh nghiệp',
	'Đổi CCCD mới của doanh nghiệp' => 'Đổi CCCD mới của doanh nghiệp',
	'Đổi thông tin liên hệ của doanh nghiệp' => 'Đổi thông tin liên hệ của doanh nghiệp',
	'Đổi thông tin người đại diện' => 'Đổi thông tin người đại diện',
	'Không xác định' => 'Không xác định',

  'Tên ngân hàng' => 'Tên ngân hàng',
  'Chi nhánh ngân hàng' => 'Chi nhánh ngân hàng',
  'Số tài khoản' => 'Số tài khoản',
  'Tên người thụ hưởng' => 'Tên người thụ hưởng',
  'Tỉnh/Thành phố' => 'Tỉnh/Thành phố',
]; // End