<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransactionBAFReceiveDetail\SubAction;

use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionBAFReceiveDetailRequest;
use App\Lib\Helper;
use App\Modules\Merchant\Actions\Transaction\subAction\Mpos360TransactionDefineConfigSubAction;

class Mpos360TransactionBAFReceiveDetailSubAction
{
	public $_type = 'DETAIL_WITHDRAW';
	public function __returnData($listTransaction)
	{
		$data = $this->__defaultReturn(); // $data['data'] = [];
		
		if ($listTransaction) {
			if (isset($listTransaction['data']['mc360DetailWithdrawDTO'])) {
				$dataList = $listTransaction['data']['mc360DetailWithdrawDTO'];
				$data['data'][] = $this->__commonInfo($dataList); //0
				$data['data'][] = $this->__customerPayment($dataList); //1
				$data['data'][] = $this->__customerInfo($dataList); //4
				$data['data'][] = $this->__paymentInfo($dataList); //2

			}
		}
		return $data;
	}
	

	
	private function __commonInfo($detail)
	{
		$mapData = (new Mpos360TransactionDefineConfigSubAction())->getWithdrawStatusTrans();
		$statusText = '';
		$statusOther = (object)[];
		if (isset($detail['status'])) {
			foreach ($mapData as $key => $value) {
				if ($value['value'] == $detail['status']) {
					$statusText = $value['label'];
					$statusOther = (object)$value;
					break;
				}
			}
		}
		$detail['transType'] = 'Thông tin chung';
		$data  = [
			'key' => 'common_info',
			'name' => trans_choice_fallback('trans.title.common_info'),
			'list' => [],
		];
		$data['list'] = [
			[
				'key' => 'withdrawId',
				'label' => trans_choice_fallback('trans.title.withdrawId'),
				'value' => $detail['withdrawId'],
				'extra' => "",
				'other_data' => (object) []
			],

			[
				'key' => 'createdDate',
				'label' => 'Ngày khởi tạo',
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'createdDate', 'datetime'),
				'extra' => "",
				'other_data' => (object) []
			],
			[
				'key' => 'status',
				'label' => trans_choice_fallback('trans.title.statusInfo'),
				'value' => $statusText,
				'extra' => "",
				'other_data' => $statusOther
			],

		];
		return $data;
	}
	private function __customerPayment($detail)
	{
		$data  = [
			'key' => 'customer_payment',
			'name' => trans_choice_fallback('trans.title.trans_recieve'),
			'list' => [],
		];
		$receivedAmountStringOther = (object)[];
		if($detail['receivedAmountString']) {
			$receivedAmountStringOther = (object)[
				"display_type"=> "block"
			];
		}

		$data['list'] = [
			[
				'key' => 'totalSettlement',
				'label' => trans_choice_fallback('trans.title.total_amount'),
				// 'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'totalSettlement', 'amount'),
				'value' => Helper::priceFormat($detail['amount'], ' VND'),
				'extra' => "",
				'other_data' => (object) []
			],

			[
				'key' => 'transactionFee',
				'label' => trans_choice_fallback('trans.title.trans_fee'),
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'transactionFee', 'amount'),
				'other_data' => (object) [],
			],

			[
				'key' => 'installmentFee',
				'label' => trans_choice_fallback('trans.title.installment_fee'),
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'installmentFee', 'amount'),
				'extra' => "",
				'other_data' => (object) []
			],

			[
				'key' => 'fee',
				'label' => trans_choice_fallback('trans.title.transfer_fee'),
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'fee', 'amount'),
				'extra' => "",
				'other_data' => (object) []
			],

			[
				'key' => 'totalRefund',
				'label' => trans_choice_fallback('trans.title.refund'),
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'totalRefund', 'amount'),
				'extra' => "",
				'other_data' => (object) []
			],
			[
				'key' => 'totalCollection',
				'label' => trans_choice_fallback('trans.title.collection'),
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'totalCollection', 'amount'),
				'extra' => "",
				'other_data' => (object) []
			],
			[
				'key' => 'receivedAmount',
				'label' => trans_choice_fallback('trans.title.receive_amount'),
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'receivedAmount', 'amount'),
				'extra' => "",
				'other_data' => (object) []
			],
			[
				'key' => 'receivedAmountString',
				'label' => trans_choice_fallback('trans.title.amount_string'),
				'value' => is_string($detail['receivedAmountString']) ? trim($detail['receivedAmountString']) : $detail['receivedAmountString'] ,
				'extra' => "",
				'other_data' => $receivedAmountStringOther
			]
		];
		return $data;
	}
	private function __paymentInfo($detail)
	{
		
		$data  = [
			'key' => 'payment_info',
			'name' =>trans_choice_fallback('trans.title.staic'),
			'list' => [],
		];
		
		$data['list'] = [
			[
				'key' => 'withdrawDate',
				'label' =>trans_choice_fallback('trans.title.perior'),
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'withdrawDate', 'datetime'),
				'other_data' => (object) [],
			],
			[
				'key' => 'totalSettlement',
				'label' => trans_choice_fallback('trans.title.total_trans'),
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'totalSettlement', 'amount'),
				'other_data' => (object) [
					'description'=> '('.$detail['countSettlement'].')',
				],
			],
			[
				'key' => 'totalRefund',
				'label' =>trans_choice_fallback('trans.title.refund'),
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'totalRefund', 'amount'),
				'other_data' => (object) [
					'description'=> '('.$detail['countRefund'].')',
				],
			],
			[
				'key' => 'totalCollection',
				'label' => trans_choice_fallback('trans.title.collection'),
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'totalCollection', 'amount'),
				'other_data' => (object) [
					'description'=> '('.$detail['countCollection'].')',
				],
			]
		];
		return $data;
	}

	private function __customerInfo($detail)
	{
		$data  = [
			'key' => 'customer',
			'name' => trans_choice_fallback('trans.title.account_recieve'),
			'list' => [],
		];
		$data['list'] =  [
			[
				'key' => 'holderName',
				'label' => $detail['holderName'],
				'value' => "",
				'extra' => "",
				'other_data' => (object) []
			],

			[
				'key' => 'accountNo',
				'label' => $detail['accountNo'],
				'value' => "",
				'extra' => "",
				'other_data' => (object) []
			],
			[
				'key' => 'bankName',
				'label' => $detail['bankName'],
				'value' => "",
				'extra' => "",
				'other_data' => (object)[]
			]
		];
		return $data;
	}
	private function __defaultReturn()
	{
		return [
			'warning' => [],
			'data' => [],
			'other_data' => (object)[],
		];
	}
}
