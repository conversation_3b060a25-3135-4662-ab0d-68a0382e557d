<?php

namespace App\Modules\Merchant\Actions\Home\Mpos360GetHomePageInfoAction;

use App\Lib\Mpos360UrlHelper;
use App\Modules\Merchant\Model\Setting;
use App\Modules\Merchant\Requests\Home\Mpos360GetHomePageInfoRequest;
use App\Modules\Merchant\Actions\Banner\Mpos360GetMyBannersAction\Mpos360GetMyBannersAction;
use App\Modules\Merchant\Actions\Home\Mpos360GetHomePageInfoAction\SubAction\GetMenuForAppSubAction;
use App\Modules\Merchant\Actions\Home\Mpos360GetHomePageInfoAction\SubAction\GetThongKeVietQrSubAction;

class Mpos360GetHomePageInfoAction
{
	public function run(Mpos360GetHomePageInfoRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		$listSetting = Setting::all();

		$returnData = [
			'revenues' => [
				'heading' => [
					'label' => 'Doanh số tháng 7',
					'value' => '// deeplink xem chi tiết'
				],

				'total_and_change' => [
					'total' => [
						'label' => 'Tổng số (VNĐ)',
						'value' => 'Chưa có thông tin'
					],

					'change' => [
						'label' => 'Thay đổi {revenue_icon} {percent}',
						'value' => 'Chưa có thông tin',
						'other_data' => [
							'revenue_icon' => [
								'text' => cumtomAsset('images/home/<USER>'),
								'text_color' => '',
								'bg_color' => ''
							],
							'percent' => ['text' => '-0%', 'text_color' => '#FF0000', 'bg_color']
						]
					]
				],

				'last_3months_avg' => [
					[
						'label' => 'Trung bình T5+T6',
						'value' => 'Chưa có thông tin'
					],

					[
						'label' => 'Tháng 6',
						'value' => 'Chưa có thông tin'
					],

					[
						'label' => 'Tháng 5',
						'value' => 'Chưa có thông tin'
					],
				]
			],

			
			'banners' => app(Mpos360GetMyBannersAction::class)->run($deviceSession)['banners'],

			'addons' => [
				[
					'name' => 'Thay đổi thông tin',
					'img' => 'https://mpos.vn/public/media?fpath=MjAyNDA5MDYtQURNSU4tbXBvczoxMDAwLTAtbXBvc194X3BtcWxfMg==.jpeg',
					'type' => 'deeplink',
					'target' => 'appScreen',
					'value' => [
						'appScreen' => [
							'screenName' => 'transactionDetail',
							'params' => [
								'transactionId' => '123'
							],
						],
						'webviewUrl' => 'https://mpos.vn/tin-tuc/mpos-x-phan-mem-quan-ly-giai-phap-tich-hop-hoan-hao-cho-moi-nha-ban-hang',
						'browserUrl' => 'https://mpos.vn/tin-tuc/mpos-x-phan-mem-quan-ly-giai-phap-tich-hop-hoan-hao-cho-moi-nha-ban-hang'
					]
				],
			],

			// ver 1.1
			'menus' => app(GetMenuForAppSubAction::class)->run($deviceSession, $listSetting),
			'notice' => [
				'text' => 'Dòng thông báo ở đây',
				'text_color' => '#73ae4a',
				'bg_color' => '#f1f7ed'
			],

			'vietQr' => app(GetThongKeVietQrSubAction::class)->run($deviceSession)
		];

		$settingNotice = $listSetting->where('key', 'HOME_NOTICE_MESSAGE')->first();
		
		if ($settingNotice) {
			$settingNoticeValue = json_decode($settingNotice->value, true);
			$returnData['notice']['text'] = $settingNoticeValue['text_via_lang'][app()->getLocale()];
			$returnData['notice']['text_color'] = $settingNoticeValue['text_color'];
			$returnData['notice']['bg_color'] = $settingNoticeValue['bg_color'];
		}

		

		// Ver 3.1 -> thêm hướng dẫn sử dụng
		$settingHuongDanSuDung = $listSetting->where('key', 'HUONG_DAN_SU_DUNG_MAN_HOME')->first();
		
		if ($settingHuongDanSuDung) {
			$settingHuongDanSuDungValue = json_decode($settingHuongDanSuDung->value, true);
			$returnData['tutorial'] = [
				'title' => $settingHuongDanSuDungValue['title_via_lang'][app()->getLocale()],
				'icon' => cumtomAsset($settingHuongDanSuDungValue['iconAsset']),
				'tutorialUrl' => $settingHuongDanSuDungValue['tutorialUrl'],
				'other_data' => $settingHuongDanSuDungValue['other_data']
			];
		}
		return $returnData;
	}
}
