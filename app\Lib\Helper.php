<?php

namespace App\Lib;

use App\Modules\Merchant\Model\MerchantOnboard;
use Illuminate\Support\Str;

class Helper
{
	public static function priceFormat($price, string $unit = ' VND')
	{
		$price = number_format($price, 0, '', '.');
		if (!empty($unit)) {
			$price .= $unit;
		}

		return $price;
	}

	public static function numberFormat($number = 0)
	{
		$number = (float) $number;

		$number = number_format($number, 0, '', '.');
		return $number;
	}


	public static function traceError($e): string
	{
		$getClassErrorAsString = get_class($e);
		$logError = sprintf('[Error: %s] - [Line: %s] - [File: %s]', $e->getMessage(), $e->getLine(), $e->getFile());
		mylog(['LogError' => $logError]);

		if (self::isLocal()) { 
			return $logError;
		}
		
		$message = 'Lỗi truy vấn thông tin';

		if ($getClassErrorAsString === 'App\Exceptions\BusinessException') {
			if (Str::contains(request()->path(), 'TBWebMpos') || Str::contains(request()->path(), 'Mpos360TingBox')) {
				$message = 'Lỗi truy vấn thông tin';
			}else {
				$message = $e->getMessage();
			}
		}

		return $message;
	}

	public static function isProduction(): bool
	{
		return env('APP_ENV') == 'production' && empty(env('APP_DEBUG'));
	}

	public static function isDevelopment(): bool
	{
		return !self::isProduction();
	}

	public static function isLocal(): bool
	{
		return env('APP_ENV') === 'local' && !empty(env('APP_DEBUG'));
	}

	public static function isLocalOrDevEnv() {
		return config('app.env') === 'local' || config('app.env') === 'development';
	}


	public static function getKeyWithSpecialChars(int $length = 32): string
	{
		$specialChars = '!@#$%^&*()[]';
		$subLength = $length - strlen($specialChars);
		$strRand = Str::random($subLength) . $specialChars;
		return str_shuffle($strRand);
	}

	/**
	 * {
        "profileKey": "passport",
        "value": "",
        "status_verify": "1",
        "qts_request_id": "4f7bea64-37a2-4d1f-baba-834230825cbf"
      },
	 */
	public static function isDayDuKey(array $requiredKeys = [], array $profileSauKhiXacThuc = [])
	{
		// Lấy danh sách các profileKey hiện có trong mảng
		$existingKeys = array_column($profileSauKhiXacThuc, 'profileKey');

		// Kiểm tra xem tất cả các key yêu cầu có nằm trong mảng hiện có không
		$missingKeys = array_diff($requiredKeys, $existingKeys);

		return empty($missingKeys);
	}

	public static function getLoaiChungTuDuocPhepUpload(): array {
		return [
			[
				'key' => 'anh_giay_uy_quyen',
				'name' => 'Ảnh giấy ủy quyền được công chứng tại cơ quan có thẩm quyền'
			],

			[
				'key' => 'anh_giay_khai_sinh',
				'name' => 'Ảnh giấy khai sinh'
			],

			[
				'key' => 'anh_giay_dang_ky_ket_hon',
				'name' => 'Ảnh giấy đăng ký kết hôn',
			],

			[
				'key' => 'anh_so_ho_khau',
				'name' => 'Ảnh tất cả các trang Sổ hộ khẩu giấy',
			],

			[
				'key' => 'anh_man_hinhg_app_vneid',
				'name' => 'Video quay màn hình ứng dụng VNeID từ khi đăng nhập đến các trang thể hiện mối quan hệ giữa người ủy quyền và người được ủy quyền',
			],

			[
				'key' => 'cccd_2mat_sau_cua_ca_nhan_duoc_uy_quyen',
				'name' => '*CCCD mặt trước và mặt sau của cá nhân được ủy quyền (bản gốc, rõ nét, có đầy đủ 4 góc, không dùng bản scan, PDF)'
			],

			[
				'key' => 'cccd_hai_mat_cua_nguoi_dai_dien_moi',
				'name' => '*CCCD 2 mặt của người đại diện mới (bản gốc, rõ nét, có đầy đủ 4 góc, không dùng bản scan, PDF)'
			]
		];
	}

	public function getRealIpAddr() {
		//check ip from share internet 
		if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
				$ip = $_SERVER['HTTP_CLIENT_IP'];
		}
		//to check ip is pass from proxy
		elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
				$ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
		} else {
				$ip = $_SERVER['REMOTE_ADDR'];
		}
		/////////////////////////////////
		$ip = str_replace(" ", "", $ip);
		$expIp = explode(",", $ip);
		if (count($expIp) > 1) {
				return $expIp[0];
		}
		/////////////////////////////////
		
		return $ip;
	}

	public static function chuanHoaOs(string $os='ANDROID') {
		if (strtoupper($os) == 'ANDROID') {
			return 'Android';
		}

		if (strtoupper($os) == 'IOS') {
			return 'iOS';
		}

		return 'Other';
	}

	public static function isUserNameEqualEmail(string $userName=''): bool {
		return filter_var($userName, FILTER_VALIDATE_EMAIL);
	}

	public static function removeVietnameseAccents($str) {
			$utf82abc = array(
				'à' => 'a',
				'á' => 'a',
				'ả' => 'a',
				'ã' => 'a',
				'ạ' => 'a',
				'ă' => 'a',
				'ằ' => 'a',
				'ắ' => 'a',
				'ẳ' => 'a',
				'ẵ' => 'a',
				'ặ' => 'a',
				'â' => 'a',
				'ầ' => 'a',
				'ấ' => 'a',
				'ẩ' => 'a',
				'ẫ' => 'a',
				'ậ' => 'a',
				'đ' => 'd',
				'è' => 'e',
				'é' => 'e',
				'ẻ' => 'e',
				'ẽ' => 'e',
				'ẹ' => 'e',
				'ê' => 'e',
				'ề' => 'e',
				'ế' => 'e',
				'ể' => 'e',
				'ễ' => 'e',
				'ệ' => 'e',
				'ì' => 'i',
				'í' => 'i',
				'ỉ' => 'i',
				'ĩ' => 'i',
				'ị' => 'i',
				'ò' => 'o',
				'ó' => 'o',
				'ỏ' => 'o',
				'õ' => 'o',
				'ọ' => 'o',
				'ô' => 'o',
				'ồ' => 'o',
				'ố' => 'o',
				'ổ' => 'o',
				'ỗ' => 'o',
				'ộ' => 'o',
				'ơ' => 'o',
				'ờ' => 'o',
				'ớ' => 'o',
				'ở' => 'o',
				'ỡ' => 'o',
				'ợ' => 'o',
				'ù' => 'u',
				'ú' => 'u',
				'ủ' => 'u',
				'ũ' => 'u',
				'ụ' => 'u',
				'ư' => 'u',
				'ừ' => 'u',
				'ứ' => 'u',
				'ử' => 'u',
				'ữ' => 'u',
				'ự' => 'u',
				'ỳ' => 'y',
				'ý' => 'y',
				'ỷ' => 'y',
				'ỹ' => 'y',
				'ỵ' => 'y',
				'À' => 'A',
				'Á' => 'A',
				'Ả' => 'A',
				'Ã' => 'A',
				'Ạ' => 'A',
				'Ă' => 'A',
				'Ằ' => 'A',
				'Ắ' => 'A',
				'Ẳ' => 'A',
				'Ẵ' => 'A',
				'Ặ' => 'A',
				'Â' => 'A',
				'Ầ' => 'A',
				'Ấ' => 'A',
				'Ẩ' => 'A',
				'Ẫ' => 'A',
				'Ậ' => 'A',
				'Đ' => 'D',
				'È' => 'E',
				'É' => 'E',
				'Ẻ' => 'E',
				'Ẽ' => 'E',
				'Ẹ' => 'E',
				'Ê' => 'E',
				'Ề' => 'E',
				'Ế' => 'E',
				'Ể' => 'E',
				'Ễ' => 'E',
				'Ệ' => 'E',
				'Ì' => 'I',
				'Í' => 'I',
				'Ỉ' => 'I',
				'Ĩ' => 'I',
				'Ị' => 'I',
				'Ò' => 'O',
				'Ó' => 'O',
				'Ỏ' => 'O',
				'Õ' => 'O',
				'Ọ' => 'O',
				'Ô' => 'O',
				'Ồ' => 'O',
				'Ố' => 'O',
				'Ổ' => 'O',
				'Ỗ' => 'O',
				'Ộ' => 'O',
				'Ơ' => 'O',
				'Ờ' => 'O',
				'Ớ' => 'O',
				'Ở' => 'O',
				'Ỡ' => 'O',
				'Ợ' => 'O',
				'Ù' => 'U',
				'Ú' => 'U',
				'Ủ' => 'U',
				'Ũ' => 'U',
				'Ụ' => 'U',
				'Ư' => 'U',
				'Ừ' => 'U',
				'Ứ' => 'U',
				'Ử' => 'U',
				'Ữ' => 'U',
				'Ự' => 'U',
				'Ỳ' => 'Y',
				'Ý' => 'Y',
				'Ỷ' => 'Y',
				'Ỹ' => 'Y',
				'Ỵ' => 'Y',
				'̀' => '',
				'́' => '',
				'̉' => '',
				'̃' => '',
				'̣' => ''
			);
	
			return str_replace(array_keys($utf82abc), array_values($utf82abc), $str);
	}

	public static function titleString($str) {
		return Str::of($str)->slug(' ')->title()->__toString();
	}

	public static function getBankCode($bankString) {
    $parts = explode(" - ", $bankString);
    return trim($parts[0]); // Lấy phần đầu tiên
	}

	public static function getPartnerCode($merchantId='') {
		try {
			$mcOnboard = MerchantOnboard::query()->where('merchantId', $merchantId)->first();
		
			if ($mcOnboard && !empty($mcOnboard->partnerCode)) {
				return $mcOnboard->partnerCode;
			}

			return 'NP';
		}catch(\Throwable $th) {
			//throw $th;
			return 'NP';
		}
	}
} // End class