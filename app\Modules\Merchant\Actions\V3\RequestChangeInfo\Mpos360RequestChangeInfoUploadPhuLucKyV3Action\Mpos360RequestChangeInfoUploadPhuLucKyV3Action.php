<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoUploadPhuLucKyV3Action;

use App\Exceptions\BusinessException;
use Exception;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoUploadPhuLucKyV3Request;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAttachSignatureAction\SubAction\GetCanByRequestChangeInfoSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\SubAction\GetPhuongThucQuetB3Ver2SubAction;

class Mpos360RequestChangeInfoUploadPhuLucKyV3Action
{
	public function run(Mpos360RequestChangeInfoUploadPhuLucKyV3Request $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		$id = $request->json('data.id');
		$merchantId = $deviceSession->getMerchantId();

		$mpos360McRequest = Mpos360MerchantRequest::query()->firstWhere([
			'id' => $id,
			'merchant_id' => $merchantId,
		]);

		if (!$mpos360McRequest) {
			throw new BusinessException('Lỗi: không tìm thấy thông tin yêu cầu');
		}

		mylog(['Yeu cau duoc xu ly la' => $mpos360McRequest->only(['id', 'mynextpay_id', 'order_code'])]);

		$dataRequest = json_decode($mpos360McRequest->data_request, true);
		$signatureUrl = trim($request->json('data.signature_url'));

		if ($mpos360McRequest->isKyGiay()) {
			if (empty($signatureUrl)) {
				throw new BusinessException('Bạn cần upload file chữ ký đính kèm lên');
			}
		}

		$dataRequest[0]['signProcess']['signature_url'] = $signatureUrl;
		$mpos360McRequest->data_request = json_encode($dataRequest);
		$mpos360McRequest->status_sign = Mpos360Enum::MPOS360_MC_SIGN_STT_CHUA_KY;
		$r = $mpos360McRequest->save();

		if (!$r) {
			throw new BusinessException('Lỗi lưu thông tin chữ ký');
		}

		$can = app(GetCanByRequestChangeInfoSubAction::class)->run($mpos360McRequest);

		return [
			'id' => $mpos360McRequest->id,
			'msg' => 'Xác nhận ký thành công',
			'status' => '1',
			'can' => $can,
			'scan_method' => app(GetPhuongThucQuetB3Ver2SubAction::class)->run($mpos360McRequest->merchant_id),
		];
	}
} // End class
