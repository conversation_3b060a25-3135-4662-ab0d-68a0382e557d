<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransactionCountSumByDateV2Action;

use Carbon\Carbon;
use App\Lib\Helper;
use App\Lib\partner\MPOS;
use App\Lib\partner\MposAWS;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionCountSumByDateRequest;

class Mpos360TransactionCountSumByDateV2Action
{
	public MposAWS $mposAws;
	public MPOS $mpos;

	public function __construct(MposAWS $mposAws, MPOS $mpos)
	{
		$this->mposAws = $mposAws;
		$this->mpos = $mpos;
	}

	public function returnEmpty($request) {
		$returnData[] = [
			'transactionCount' => '',
			'formatDate' => $request->json('data.rangeTime'),
			'totalAmount' => '',
			'wording' => 'Đang cập nhật',
		];

		return ['statistic' => $returnData];
	}

	public function run(Mpos360TransactionCountSumByDateRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		$merchantEmail = $deviceSession->getMerchantEmail();
		$merchantId = $deviceSession->getMerchantId();

		mylog([
			'merchantEmail' => $merchantEmail,
			'merchantId' => $merchantId
		]);

		if ($request->isGoiQuaMacq()) {
			return $this->getThongKeGiaoDichTheoNgayMacq($deviceSession, $request);
		}elseif($request->isGoiQuaMposCu()) {
			return $this->getThongKeChoGiaoDichCuMpos($deviceSession, $request);
		}else {
			return $this->returnEmpty($request);
		}
	}

	public function getThongKeGiaoDichTheoNgayMacq($deviceSession, Mpos360TransactionCountSumByDateRequest $request) {
		$merchantEmail = $deviceSession->getMerchantEmail();
		
		if (!$request->isDuocPhepGetSoLieuMacq($merchantEmail)) {
			return $this->returnEmpty($request);
		}

		try {
			$params = [
				'serviceName' => 'DAILY_COUNT_SUM_TRANSACTION',
				'merchantFk' => $deviceSession->getMerchantId(),
				'tokenLogin' => $deviceSession->getMposToken(),
			];
	
			$params = array_merge($params, $request->json('data'));
			$params['rangeTime'] = $request->getNgayTruyenLenAsDate()->format('d-m-Y');
			
	
			$result = $this->mposAws->countSumTransByDate($params);

			$countSum = [];
	
			$wording = sprintf('GD: 0');
	
			if ($request->isCountGiaoDichQr()) {
				$wording = sprintf('GD: 0   Tổng tiền: 0');
			}
	
			$countSum[] = [
				'transactionCount' => 0,
				'formatDate' => $request->json('data.rangeTime'),
				'totalAmount' => 0,
				'wording' => $wording
			];
	
			if (!empty($result['data']['data'])) {
				$countSum[0]['transactionCount'] = $result['data']['data'][0]['transactionCount'];
				$countSum[0]['totalAmount'] = $result['data']['data'][0]['totalAmount'];
	
				if ($request->isCountGiaoDichQr()) {
					$wording = sprintf(
						'GD: %s   Tổng tiền: %s', 
						$result['data']['data'][0]['transactionCount'],
						Helper::priceFormat($result['data']['data'][0]['totalAmount']),
					);
				}else {
					$wording = sprintf(
						'GD: %s', 
						$result['data']['data'][0]['transactionCount'],
					);
				}
	
				$countSum[0]['wording'] = $wording;
			}
	
			$returnData['statistic'] = $countSum;
			return $returnData;
		}catch(\Throwable $th) {
			mylog(['Err' => $th->getMessage()]);
			$returnData['statistic'] = $countSum;
			return $returnData;
		}
	}

	public function getThongKeChoGiaoDichCuMpos($deviceSession, Mpos360TransactionCountSumByDateRequest $request) {
		// // giao dich cu mpos k can check ngay
		// $merchantEmail = $deviceSession->getMerchantEmail();
		
		// if (!$request->isDuocPhepGetSoLieuMacq($merchantEmail)) {
		// 	return $this->returnEmpty($request);
		// }

		try {
			$params = [
				'serviceName' => 'DAILY_COUNT_SUM_TRANSACTION',
				'merchantFk' => $deviceSession->getMerchantId(),
				'tokenLogin' => $deviceSession->getMposToken(),
			];
	
			$params = array_merge($params, $request->json('data'));
			$params['rangeTime'] = $request->getNgayTruyenLenAsDate()->format('d-m-Y');
			
	
			$result = $this->mpos->countSumTransByDate($params);

			$countSum = [];
	
			$wording = sprintf('GD: 0');
	
			if ($request->isCountGiaoDichQr()) {
				$wording = sprintf('GD: 0   Tổng tiền: 0');
			}
	
			$countSum[] = [
				'transactionCount' => 0,
				'formatDate' => $request->json('data.rangeTime'),
				'totalAmount' => 0,
				'wording' => $wording
			];
	
			if (!empty($result['data']['data'])) {
				$countSum[0]['transactionCount'] = $result['data']['data'][0]['transactionCount'];
				$countSum[0]['totalAmount'] = $result['data']['data'][0]['totalAmount'];
	
				if ($request->isCountGiaoDichQr()) {
					$wording = sprintf(
						'GD: %s   Tổng tiền: %s', 
						$result['data']['data'][0]['transactionCount'],
						Helper::priceFormat($result['data']['data'][0]['totalAmount']),
					);
				}elseif ($request->isYcNtnVietQr()) {
					$wording = sprintf(
						'Số lượng YC: %s', 
						$result['data']['data'][0]['transactionCount'],
					);
				}else {
					$wording = sprintf(
						'GD: %s', 
						$result['data']['data'][0]['transactionCount'],
					);
				}
	
				$countSum[0]['wording'] = $wording;
			}
	
			$returnData['statistic'] = $countSum;
			return $returnData;
		}catch(\Throwable $th) {
			mylog(['Err' => $th->getMessage()]);
			$returnData['statistic'] = $countSum;
			return $returnData;
		}
	}
} // End class
