.login-wrap {
	padding-bottom: 90px;
	margin-top: -12px;
	height: 100%;
}
body{
background: #f1f1f1;
}
a{
text-decoration: none !important;
}
.line-height-16{
	line-height: 16px !important;
}
.line-height-18{
	line-height: 18px !important;
}
.line-height-20{
	line-height: 20px !important;
}
.mpos360-head{
text-align: center;
margin-bottom: 16px;
}
.mpos360-head label{
font-size: 18px;
font-weight: 600;
margin-bottom: 16px;
}
.mpos360-head h2{
font-weight: 600;
font-size: 20px;
color: #808890;
margin-bottom: 24px;
}
.mpos360-head h1{
font-size: 18px;
font-weight: 600;
}
.mpos360-head span{}
.mpos360-head p{}
.mpos360-scan{
margin: 0 auto;
	width: 250px;
}
.mpos360-scan > p{
text-align: center;
margin: 40px 0;
color: #808890;
font-size: 14px;
}
.mpos360-scan > a{
font-size: 20px;
color: #73AE4A;
text-align: center;
padding: 9px;
width: 100%;
background: #73AE4A1A;
border-radius: 8px;
text-decoration: none;
display: block;
}
.ms-qr{
display: block;
	border: 1px dashed #73ae4a;
	text-align: center;
	padding: 32px 9px !important;
	border-radius: 12px; 
	width: 100%;
}
.ms-qr span{}
.ms-qr span img{}
.ms-qr > p{
margin-bottom: 0;
}

.mf-title{
display: flex;
text-align: center;
justify-content: space-between;
color: #808890;
font-size: 14px;
margin-bottom: 18px;
}
.mf-title a{}
.mf-title a img{}


.mpos360-footer{
position: fixed;
left: 0;
right: 0;
bottom: 0;
margin: 0 auto;
max-width: 590px;
padding: 16px;
	margin: 0 auto;
	background: #fff;
	box-shadow: 0px -2px 12px 0px #00000029;
}
.mpos360-btn{

}
.mpos360-btn {
background: #73AE4A;
border-radius: 8px;
text-align: center;
color: #fff;
font-size: 20px;
padding: 9px;
width: 100%;
display: block;
}

{}
ul.mf-review{}
ul.mf-review  li{
list-style: none;
display: flex;
align-items: center;
justify-content: space-between;
border-bottom: 1px solid #EBEBEB ;
padding: 10px 0 ;
}
ul.mf-review  li:last-child{
border: 0;
}
ul.mf-review  li p{
margin: 0;
max-width: 130px;
}
ul.mf-review  li span{}

ul.mf-picture{
display: grid;
grid-template-columns: auto auto;
/* column-gap: 20px; */
row-gap: 20px;
margin-top: 20px;

}
ul.mf-picture li{
list-style: none;
}
ul.mf-picture li a{
position: relative;
display: block;
border-radius: 12px;
overflow: hidden;
}
ul.mf-picture li a > img{
width: 100%;
}
ul.mf-picture li a > span{
position: absolute;
bottom: 0;
	left: 0;
	right: 0;
	text-align: center;
	background: #00000099;
	padding: 6px;
	display: flex;
	align-items: center;
	color: #fff;
	justify-content: center;
}
ul.mf-picture li a > span > img{
margin-right: 5px;
}
ul.mf-picture li p{
text-align: center;
}

.mpos360-content{}
.mpos360-content p{
margin-bottom: 0;
}
.mpos360-content ul{
margin-top: 20px;
}
.mpos360-content ul li{
list-style: none;
display: flex;
align-items: center;
margin-bottom: 10px;
}
.mpos360-content ul li span{
width: 32px;
height: 32px;
background: #73AE4A;
font-size: 20px;
color: #fff;
display: flex;
align-items: center;
justify-content: center;
margin-right: 10px;
border-radius: 50%;
}
.mpos360-content a{
display: block;
background: #73AE4A;
color: #fff;
font-size: 20px;
text-align: center;
padding: 8px;
border-radius: 8px;
margin-top: 32px;
}
.mpos360-content .btn-link{
font-size: 14px;
color: #008BF4;
background: transparent;
}
.mpos360-form_reg{
background: #73AE4A1A;
padding: 14px 16px;
border-radius: 12px;
}

.mpos360-form_reg .btn-link{
font-size: 12px;
}
.mpos360-form_reg ul{}
.mpos360-form_reg ul li{
list-style: none;
display: flex;
align-items: center;
margin-bottom: 16px;
justify-content: space-between;
}
.mpos360-form_reg ul li:last-child{
margin-bottom: 0;
}
.mpos360-form_reg ul li span{
	width: 30px;
	height: 30px;
	background: #DADADA;
	font-size: 16px;
	color: #808890;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 10px;
	border-radius: 50%;
}
.mpos360-form_reg ul li span img{
width: 15px;
}
.mpos360-form_reg ul li span.mfr-check{
display: none;
}
.mpos360-form_reg ul li a{
background: #DADADA;
color: #808890;
display: flex;
align-items: center;
margin-right: 8px;
padding: 7px 12px;
border-radius: 40px;
	width: calc(100% - 56px);
}
.mpos360-form_reg ul li a img{
margin-right: 10px;
filter: grayscale(100%);
}
.mpos360-form_reg ul li.active span{
color: #fff;
background: #008BF4;
}
.mpos360-form_reg ul li.active a{
background: #fff;
box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.16);
color: #008BF4;
font-weight: 600;
}
.mpos360-form_reg ul li.active a img{
filter: grayscale(0);
}
.mpos360-form_reg ul li.step-success{
	display: flex;
	align-items: flex-start;
	justify-content: start;
	position: relative;
}
.mpos360-form_reg ul li.step-success:before{
position: absolute;
content: '';
width: 1px;
height: 100%;
border: 1px dashed #73AE4A;
	top: 20px;
	left: 14px;
}
.mpos360-form_reg ul li:last-child.step-success:before{
border: 0;
}
.mpos360-form_reg ul li.step-success span{
background: #73AE4A;
display: flex;
}
.mpos360-form_reg ul li.step-success span.mfr-numbe{
display: none;
}
.mpos360-form_reg ul li.step-success a{
background: transparent;
padding-right: 0;
font-family: "BeVietnamPro-Regular";
font-size: 12px;
}
.mpos360-form_reg ul li.step-success a img{}
.step-success .box-success{
padding: 12px 16px;
background: #fff;
border-radius: 16px;
}
.step-success .box-success label{
font-size: 16px;
color: #404041;
font-family: "BeVietnamPro-SemiBold";
}
.step-success .box-success p{
margin-bottom: 0;
color: #808890;
font-size: 14px;
font-weight: 400;
font-family: "BeVietnamPro-Regular";
}

.ss-content{
	width: calc(100% - 56px);
	margin-left: 8px;
}
.ss-content p{
display: flex;
align-items: center;
justify-content: space-between;
font-family: "BeVietnamPro-SemiBold";
margin-bottom: 0;
color: #808890;
}
.ss-content p a{
width: auto !important;
color: #008BF4 !important;
}
.box-acc{
	padding: 12px 16px;
	background: #fff;
	border-radius: 16px;
}
.box-acc label{
color: #404041;
font-size: 16px;
font-weight: 600;
line-height: 20px;
margin-bottom: 4px;
}
.box-acc p{
color: #808890;
font-family: "BeVietnamPro-Regular";
margin-bottom: 0;
line-height: 18px;
}
.box-success{
display: flex;
align-items: center;
justify-content: space-between;
}
.box-success article{}
.box-success article label{}
.box-success article p{
margin-bottom: 0;
}
.bs-warning{
position: relative;
color: #FDB62F !important;
padding-left: 16px;
}
.bs-warning:before{
position: absolute;
content: '';
width: 9px;
height: 9px;
border-radius: 50%;
background: #FDB62F;
top: 6px;
left: 0;
}
.bs-success{
position: relative;
color: #73AE4A !important;
padding-left: 16px;
}
.bs-success:before{
position: absolute;
content: '';
width: 9px;
height: 9px;
border-radius: 50%;
background: #73AE4A;
top: 6px;
left: 0;
}

.ss-acc{
width: calc(100% - 56px);
}
.ss-acc > p{
display: flex;
align-items: center;
justify-content: space-between;
font-family: "BeVietnamPro-SemiBold"; 
margin-bottom: 0;
color: #808890;
}
.ss-acc > p a{
width: auto !important;
color: #008BF4 !important;
}
.ss-acc .acc-show{
	padding: 12px 16px;
	background: #fff;
	border-radius: 16px;
}
.ss-acc .acc-show p{
margin: 0;
font-size: 16px;
}
.ss-acc .acc-show label{
margin-bottom: 0;
font-size: 16px;
font-weight: 700;
}
.hide-haft{
max-height: 150px;
overflow: hidden;
}
.hide-full{
max-height: auto;
}
.modal-search{
position: relative;
margin-bottom: 20px;
}
.modal-search span{
	display: inline-block;
	position: absolute;
	top: 20px;
	left: 15px;
}
.modal-search span img{}
.modal-search input{
border: 1px solid #22313F1A;
	border-radius: 8px;
	padding: 15px 10px 15px 40px;
	font-size: 16px;
	width: 100%;
}

.list-box{}
.list-box li{
list-style: none;
}
.list-box li a{
	font-size: 16px;
	font-weight: 600;
	color: #404041;
	padding: 10px 0px;
	border-bottom: 1px solid #dcdcdc;
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.list-box li:last-child a{
border-bottom: 0;
}
.list-box li span{}
.list-bank{}
.list-bank li{
list-style: none;
}
.list-bank li a{
display: flex;
align-items: center;
padding: 10px 0;
	border-bottom: 1px solid #dcdcdc;
}
.list-bank li:last-child a{
border-bottom: 0;
}
.list-bank li a figure{
width: 40px;
margin-right: 10px;
margin-bottom: 0;
}
.list-bank li a figure img{}
.list-bank li a article{}
.list-bank li a article label{
font-size: 16px;
font-weight: 600;
color: #404041;
}
.list-bank li a article p{
font-size: 14px;
color: #808890;
margin-bottom: 0;
}

/*#khaibao .modal-body{
max-height: 420px;
overflow: auto;
}*/
.head-wrap{
	max-width: 590px;
	padding: 12px 16px 20px;;
	margin: 0 auto;
	background: #73AE4A url(../img/bg-head.png) no-repeat;
	background-size: cover;
	background-position: center;
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.head-wrap label{
font-size: 20px;
font-family: "BeVietnamPro-SemiBold";
color: #fff;
}
.head-wrap span{
    width: 100px;
}
.head-wrap span img{
    width: 100%;
}
.lw-404{
text-align: center;
	margin-top: 60px;
}
.lw-404 span{}
.lw-404 h4{
color: #404041;
text-align: center;
font-family: "BeVietnamPro-SemiBold";
font-size: 20px;
}

.mpos360-scan-qr{}
.mpos360-scan-qr span{}
.mpos360-scan-qr span img{
width: 100%;
}

.mf-picture-default{
display: grid;
	grid-template-columns: auto auto;
	column-gap: 20px;
	row-gap: 20px;
	margin-top: 20px;
}
.mf-picture-default li{
list-style: none;
}
.mf-picture-default li a{
text-align: center;
display: block;
padding: 16px;
background: #E6F4FE;
border-radius: 12px;
border: 2px dashed #008BF4;
}
.mf-picture-default li a img{
margin-bottom: 12px;
display: inline-block;
}
.mf-picture-default li a span{
display: block;
}






















