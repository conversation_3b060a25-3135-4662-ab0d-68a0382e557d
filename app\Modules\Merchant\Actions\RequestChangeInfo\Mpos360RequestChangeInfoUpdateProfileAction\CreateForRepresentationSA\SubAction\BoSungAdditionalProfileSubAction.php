<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForRepresentationSA\SubAction;

class BoSungAdditionalProfileSubAction
{
	public function run($additionalProfiles = [], $choice = '')
	{
		/**
		 * ngày sinh: với case đổi thông tin liên hệ
		 * các case đổi ng đại diện mới hoặc đổi cccd, thì đọc thẳng vào qts lấy dữ liệu ngày sinh
		 */
		if ($choice == 'DOI_THONG_TIN_LIEN_HE') {
			$additionalProfiles[] = [
				'profileKey' => 'representBirthday',
				'value' => '',
				'label' => 'Ngày sinh',
				'other_data' => (object) [],
			];
		}

		return $additionalProfiles;
	}
}
