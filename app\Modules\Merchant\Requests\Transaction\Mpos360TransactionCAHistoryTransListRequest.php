<?php

namespace App\Modules\Merchant\Requests\Transaction;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360TransactionCAHistoryTransListRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.transaction_time' => ['present', 'string'],
			'data.transaction_status' => ['present', 'string'],
			'data.start' => ['required', 'numeric', 'integer', 'min:0'],
			'data.limit' => ['required', 'numeric', 'integer', 'min:5'],
		];
	}
}
