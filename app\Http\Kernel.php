<?php

namespace App\Http;

use App\Http\Middleware\LoggingMiddleware;
use App\Http\Middleware\ValidateHashMiddleware;
use Illuminate\Foundation\Http\Kernel as HttpKernel;
use App\Http\Middleware\AppendApiRequestIdMiddleware;
use App\Http\Middleware\ChecksumForCronjobMiddleware;
use App\Http\Middleware\ValidateHashCommonMiddleware;
use App\Http\Middleware\ValidateHashUploadMiddleware;
use App\Http\Middleware\ChecksumForAnyMobileMiddleware;
use App\Http\Middleware\ChecksumForAnyMobileUploadMiddleware;

class Kernel extends HttpKernel
{
	/**
	 * The application's global HTTP middleware stack.
	 *
	 * These middleware are run during every request to your application.
	 *
	 * @var array
	 */
	protected $middleware = [
		// \App\Http\Middleware\TrustHosts::class,
		\App\Http\Middleware\TrustProxies::class,
		\Fruitcake\Cors\HandleCors::class,
		\App\Http\Middleware\CheckForMaintenanceMode::class,
		\Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,

		AppendApiRequestIdMiddleware::class,
		LoggingMiddleware::class,
	];

	/**
	 * The application's route middleware groups.
	 *
	 * @var array
	 */
	protected $middlewareGroups = [
		'web' => [
			//            \App\Http\Middleware\EncryptCookies::class,
			           \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
			           \Illuminate\Session\Middleware\StartSession::class,
			// \Illuminate\Session\Middleware\AuthenticateSession::class,
			//            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
			//            \App\Http\Middleware\VerifyCsrfToken::class,
			//            \Illuminate\Routing\Middleware\SubstituteBindings::class,
		],
		'api' => [
			// 'throttle:60,1',
			\Illuminate\Routing\Middleware\SubstituteBindings::class,
		],
	];

	/**
	 * The application's route middleware.
	 *
	 * These middleware may be assigned to groups or used individually.
	 *
	 * @var array
	 */
	protected $routeMiddleware = [
		//        'auth' => \App\Http\Middleware\Authenticate::class,
		//        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
		//        'bindings' => \Illuminate\Routing\Middleware\SubstituteBindings::class,
		//        'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
		//        'can' => \Illuminate\Auth\Middleware\Authorize::class,
		//        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
		//        'password.confirm' => \Illuminate\Auth\Middleware\RequirePassword::class,
		//        'signed' => \Illuminate\Routing\Middleware\ValidateSignature::class,
		//        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
		'verified' => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,
		'checkip' => \App\Http\Middleware\CheckIPMiddleware::class,
		'validateHash' => ValidateHashMiddleware::class,
		'validateHashUpload' => ValidateHashUploadMiddleware::class,
		'validateHashCommon' => ValidateHashCommonMiddleware::class,
		'checkSumForAnyMobile' => ChecksumForAnyMobileMiddleware::class,
		'checkSumForCronjob' => ChecksumForCronjobMiddleware::class,
		'checkSumForAnyMobileUpload' => ChecksumForAnyMobileUploadMiddleware::class,
	];
}
