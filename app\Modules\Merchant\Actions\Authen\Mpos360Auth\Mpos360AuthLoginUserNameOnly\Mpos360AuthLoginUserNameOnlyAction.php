<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginUserNameOnly;

use App\Lib\Helper;
use App\Lib\Encryption;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use App\Lib\PasswordHandler;
use App\Lib\partner\SoundBox;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\DTOs\Authen\Mpos360Auth\LoginMposSuccessDto;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360AuthLoginUserNameRequest;
use App\Modules\Merchant\Actions\Device\GetDeviceIdByOsAndTokenAction\SaveDeviceWithMobileUserAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction\BuildBottomNavAppSubAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction\CreateMpos360UserSubAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction\CreateDeviceSessionSubAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction\PushDeviceToTingTingSubAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction\LoginMerchantViaMposUserNameSubAction;

class Mpos360AuthLoginUserNameOnlyAction
{
	public bool $isCoThietBi = false;

	public function run(Mpos360AuthLoginUserNameRequest $request): DeviceSession
	{
		// Đoạn này cần rẽ nhánh là login email hay sđt (hiện tại đang là email, cần rẽ nhánh)
		$loginMpos = app(LoginMerchantViaMposUserNameSubAction::class)->run(
			$request->json('data.value'),
			$request->json('data.password'),
			$request->json('data.os'),
			$request->json('data.fcmToken', '')
		);

		if (!empty($loginMpos['data']['countSoundBoxOfMc']) || !empty($loginMpos['data']['countReaderBoxOfMc'])) {
			$this->isCoThietBi = true;
		}

		$loginMposSuccessDto = new LoginMposSuccessDto(
			$loginMpos['data']['mobileUserToken'],
			$loginMpos['data']['merchantId'],
			$loginMpos['data']['username'],
			$loginMpos['data']['emailMerchant'],
			$loginMpos['data']['merchantName'] ?? $loginMpos['data']['username'],
		);


		// Thực hiện create user
		$mpos360User = app(CreateMpos360UserSubAction::class)->run($loginMposSuccessDto, $loginMpos);

		// Thực hiện lưu thông tin thiết bị
		$device = app(SaveDeviceWithMobileUserAction::class)->run(
			$request->json('data.os'),
			$request->json('data.deviceToken'),
			$mpos360User->id,
			$loginMpos['data']['mobileUserDTO']
		);

		// Đoạn này call sang MPOS để lấy thông tin
		$apiKey = (string) Str::uuid();

		$createDeviceSessionParams = [
			'device_id'          => $device->id,
			'user_id'						 => $mpos360User->id,
			'api_key'            => $apiKey,
			'api_secret'         => '',
			'time_expired'       => now()->addHours(12)->timestamp,
			'time_created'       => now()->timestamp,
			'partner_config_id'	 => 2
		];

		$deviceSession = app(CreateDeviceSessionSubAction::class)->run(
			$createDeviceSessionParams,
			$loginMposSuccessDto
		);

		$deviceSession->visibleAndDecryptSecret();

		$deviceSession->can = Mpos360Enum::CAN_GO_TO_HOME_SCREEN;


		// Không có thiết bị thì ra màn hình nhận mã OTP qua email
		if (!$this->isCoThietBi) {
			if (Helper::isUserNameEqualEmail($loginMposSuccessDto->username)) {
				$deviceSession->can = Mpos360Enum::CAN_GO_TO_OTP_LOGIN_FLOW_EMAIL;
			} else {
				if ($loginMpos['data']['merchantStatus'] == 'ACTIVE') {
					$deviceSession->can = Mpos360Enum::CAN_GO_TO_HOME_SCREEN;
				} else {
					$deviceSession->can = Mpos360Enum::CAN_GO_TO_TINGBOX_REGISTER_FLOW;
				}
			}
		}

		// Có thiết bị, thì kiểm tra mk
		if ($this->isCoThietBi) {
			$passwordHandler = new PasswordHandler([]);

			if ($passwordHandler->isSimplePassword($request->json('data.password'))) {
				$deviceSession->can = Mpos360Enum::CAN_GO_TO_VERIFY_MPOS_DEVICE_SCREEN;
			}
		}

		// Xử lý nếu là merchant mới bị pending -> thì đi vào thẳng màn Home
		if (!empty($loginMpos['data']['merchantStatus'])) {
			if ($loginMpos['data']['merchantStatus'] == 'PENDING_INFORMATION') {
				$deviceSession->can = Mpos360Enum::CAN_GO_TO_TINGBOX_REGISTER_FLOW;
			}
		}

		$listUserNameCanGoHome = $this->getListUserGoHome();

		if ($listUserNameCanGoHome) {
			if (in_array($request->json('data.value'), $listUserNameCanGoHome)) {
				$deviceSession->can = Mpos360Enum::CAN_GO_TO_HOME_SCREEN;
			}
		}

		@app(PushDeviceToTingTingSubAction::class)->run($device, [
			'merchantId' => $loginMposSuccessDto->merchantId,
			'merchantEmail' => $loginMposSuccessDto->merchantEmail,
			'username' => $loginMposSuccessDto->username
		]);

		$deviceSession->qrDefault = (object) [];


		if (true) {
			$deviceSession->load('mpos360User');
			$deviceSession->signed = Encryption::buildSign($deviceSession);
			$base64McData = encrypt($deviceSession->toJson());
			$deviceSession->api_key = $base64McData;

			if (@$deviceSession->relationLoaded('mpos360User')) {
				$deviceSession->unsetRelation('mpos360User');
			}
		}

		$deviceSession->makeHidden(['mpos_token']);

		// Add thêm menu bottom nav
		$deviceSession->bottom_nav_config = app(BuildBottomNavAppSubAction::class)->run($request);
		$deviceSession->type_receiver_tingting = $device->type_receiver_tingting ?? '';
		$deviceSession->merchant_info = $loginMposSuccessDto->toJson();
		$deviceSession->tingting = (object) [];

		$merchantInfo = json_decode($deviceSession->merchant_info, true);
		
		$merchantInfo['merchantName'] = Str::title($merchantInfo['merchantName'] ?? '');
		
		$merchantInfo['mobileNo'] = $merchantInfo['username'];
		if (Helper::isUserNameEqualEmail($merchantInfo['username'])) {
			$merchantInfo['mobileNo'] = $loginMpos['data']['mobileNo'] ?? '';
		}
		
		$deviceSession->merchant_info = json_encode($merchantInfo);

		return $deviceSession;
	}

	public function getListUserGoHome()
	{
		return ["<EMAIL>", "0365820650"];
	}
} // End class
