<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthGetOtpWithHinhThucV4Action;

use Exception;
use Carbon\Carbon;
use App\Exceptions\BusinessException;
use App\Lib\OtpHelper;
use App\Modules\Merchant\Model\Setting;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360User;
use App\Modules\Merchant\Model\Mpos360CodeOtp;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\SendSmsOtpMNPSubAction;
use App\Modules\Merchant\Requests\Authen\Mpos360\V4\Mpos360AuthGetOtpWithHinhThucV4Request;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\SendMailCustomizeViaMnpSubAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthGetOtpWithHinhThucV4Action\SubAction\TaoOtpTheoHinhThucSubAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthGetOtpWithHinhThucV4Action\SubAction\GetOtpMposForgotPasswordSubAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthGetOtpWithHinhThucV4Action\SubAction\TaoDeviceSessionWithMnpTokenSubAction;

class Mpos360AuthGetOtpWithHinhThucV4Action
{
	public function run(Mpos360AuthGetOtpWithHinhThucV4Request $request)
	{
		$value = trim($request->json('data.value'));
		$mposUserName = $request->json('data.username');

		$mpos360User = Mpos360User::query()->firstWhere([
			'username' => $mposUserName
		]);

		if (!$mpos360User) {
			throw new BusinessException('Không tìm được thông tin người dùng tương ứng');
		}

		$otpQmkChuaDung = Mpos360CodeOtp::query()->where('command_code', 'FORGOT_PASSWORD')
																						 ->where('status', Mpos360Enum::MPOS360_OTP_CHUA_SU_DUNG)
																						 ->where('time_out', '>', now()->timestamp)
																						 ->where('user_id', $mpos360User->id)
																						 ->orderBy('id', 'DESC')
																						 ->first();
																						 
		if ($otpQmkChuaDung) {
			$totalSeconds = $otpQmkChuaDung->time_out - now()->timestamp;
			$msg = sprintf('Bạn cần đợi %s nữa để lấy mã OTP mới', OtpHelper::getWordingSoPhutVaGiayConLai($totalSeconds));
			throw new BusinessException($msg);
		}
																		
																		

		// Thực hiện gọi api lấy otp của mpos (không dùng của mpos360)
		$getOtpMposResult = app(GetOtpMposForgotPasswordSubAction::class)->run([
			'serviceName' => 'MPOS_MANAGER_USER_PASSWORD_FORGOT',
			'username' => $mposUserName
		]);

		// Thực hiện tạo otp
		$mpos360CodeOtp = app(TaoOtpTheoHinhThucSubAction::class)->run([
			'channel' => $request->json('data.channel'),
			'user_id' => $mpos360User->id,
			'value' => $value,
			'merchant_id' => $mpos360User['merchant_id'],
			'command_code' => 'FORGOT_PASSWORD'
		], $getOtpMposResult, $request->json('data.otp_id'));

		
		$deviceSessionWithToken = app(TaoDeviceSessionWithMnpTokenSubAction::class)->run($request->json('data.merchant_id'));
		
		// Thực hiện gửi mail 
		if ($request->isEmailChannel()) {
			$settingHdsd = Setting::query()->firstWhere(['key' => 'HOST_ASSET_MAIL_TEMPLATE']);
			$settingHdsdValue = json_decode($settingHdsd->value, true);

			$bindParams = array_merge([
				'title' => 'Thiết lập lại mật khẩu ứng dụng ' . __('setting.appName'),
				'merchantName' => $mpos360CodeOtp->mpos360User->getUserMcName(),
				'username' => $mposUserName,
				'otp' => $mpos360CodeOtp->otp,
				'otpExpiredAt' => Carbon::createFromTimestamp($mpos360CodeOtp->time_out)->format('d/m/Y H:i:s')
			], $settingHdsdValue['common']);
			
			$mailMeta = [
				'subject' => sprintf('[%s] - Mã otp lấy lại mật khẩu mới từ ứng dụng %s', $mpos360CodeOtp->otp, __('setting.appName')),
				'to' => [$mpos360CodeOtp->obj_value],
				'cc' => [],
				'content' => view('EmailTemplate.QuenMatKhau', $bindParams)->render()
			];

			$sendOtp = app(SendMailCustomizeViaMnpSubAction::class)->run($deviceSessionWithToken->mnp_token, $mailMeta);
		}

		// Không phải gửi otp qua email
		if (!$request->isEmailChannel()) {
			// Gửi otp
			$sendOtp = app(SendSmsOtpMNPSubAction::class)->run(
				$mpos360CodeOtp,
				$deviceSessionWithToken,
				$request->isZnsChannel() ? true : false
			);
		}

		return [
			'username' => $mposUserName,
			'channel' => $request->json('data.channel'), 
			'value' => $value, 
			'merchant_id' => $request->json('data.merchant_id'), 
			'otp_id' => $mpos360CodeOtp->id,
			'countdown_time_get_new_otp' => $getOtpMposResult['countdown_time_get_new_otp'],
			'msg' => sprintf(
				'Hệ thống đã gửi otp tới %s qua kênh %s.%sBạn vui lòng kiểm tra trong hộp thư cá nhân',
				$value,
				$request->json('data.channel'),
				PHP_EOL
			)
		];
	}
} // End class
