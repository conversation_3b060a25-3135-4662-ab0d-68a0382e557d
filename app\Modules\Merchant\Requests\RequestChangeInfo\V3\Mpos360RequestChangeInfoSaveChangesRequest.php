<?php

namespace App\Modules\Merchant\Requests\RequestChangeInfo\V3;

use App\Modules\Merchant\Requests\MerchantRequest;
use Illuminate\Validation\Rule;

class Mpos360RequestChangeInfoSaveChangesRequest extends MerchantRequest
{
	public function authorize()
	{
		return true;
	}

	public function rules()
	{
		$rules = [
			'data.id' => ['required', 'numeric', 'integer', 'min:0'],
			'data.is_change_email_or_mobile' => ['required', 'string', Rule::in(['YES', 'NO'])],
			'data.replace_mpos_account_field' => ['present', 'array'],
			'data.replace_mpos_account_field.*' => ['nullable', 'string', Rule::in(['authoriserEmail', 'authoriserContactNumber'])],
			'data.profiles' => ['required', 'array'],
			'data.profiles.*.profileKey' => ['required', 'string'],
			'data.profiles.*.value' => ['required', 'string'],
		];

		return $rules;
	}

	public function withValidator($validator)
	{
		$validator->after(function ($validator) {
			$profiles = $this->json('data.profiles');
			$profilesCollect = collect($profiles);

			// qts_request_id
			$qtsRequestId = $profilesCollect->where('profileKey', 'qts_request_id')->first();
			if (empty($qtsRequestId) || empty($qtsRequestId['value'])) {
				$validator->errors()->add('qts_request_id', 'QtsRequestId là bắt buộc');
			}

			// email
			$authoriserEmail = $profilesCollect->where('profileKey', 'authoriserEmail')->first();
			if (empty($authoriserEmail) || empty($authoriserEmail['value'])) {
				$validator->errors()->add('authoriserEmail', 'Email là bắt buộc');
			}

			if (
				!empty($authoriserEmail['value'])
				&& !filter_var($authoriserEmail['value'], FILTER_VALIDATE_EMAIL)
			) {
				$validator->errors()->add('authoriserEmail', 'Email không đúng định dạng');
			}

			// mobile
			$authoriserContactNumber = $profilesCollect->where('profileKey', 'authoriserContactNumber')->first();
			if (empty($authoriserContactNumber) || empty($authoriserContactNumber['value'])) {
				$validator->errors()->add('authoriserContactNumber', 'SĐT là bắt buộc');
			}
		});
	}

	public function messages()
	{
		return [
			'data.id.required' => __('Id yêu cầu là bắt buộc'),
			'data.id.numeric' => __('Id yêu cầu phải là dạng số'),
			'data.id.integer' => __('Id yêu cầu phải là số nguyên'),
			'data.id.min' => __('Id yêu cầu phải có giá trị thấp nhất là: 0'),

			'data.is_change_email_or_mobile.required' => 'Có hay không thay thế tkmpos là bắt buộc',
			'data.is_change_email_or_mobile.string' => 'Có hay không thay thế tkmpos phải là kiểu chuỗi',
			'data.is_change_email_or_mobile.in' => 'Có hay không thay thế tkmpos chấp nhận các giá trị: YES, NO',

			'data.replace_mpos_account_field.present' => 'Danh sách trường thay thế tkmpos là bắt buộc',
			'data.replace_mpos_account_field.array' => 'Danh sách trường thay thế tkmpos phải là kiểu mảng',
			'data.replace_mpos_account_field.*.string' => 'Item trường thay thế phải là kiểu chuỗi',
			'data.replace_mpos_account_field.*.in' => 'Item trường thay thế phải thuộc 1 trong các giá trị: authoriserEmail, authoriserContactNumber',

			'data.profiles.required' => __('Dữ liệu hồ sơ là bắt buộc'),
			'data.profiles.array' => __('Dữ liệu hồ sơ phải là 1 mảng'),
			'data.profiles.*.profileKey.required' => __('ProfileKey là bắt buộc'),
			'data.profiles.*.profileKey.string' => __('ProfileKey phải là kiểu chuỗi'),
			'data.profiles.*.value.required' => __('Giá trị hồ sơ là bắt buộc'),
			'data.profiles.*.value.string' => __('Giá trị hồ sơ phải là kiểu chuỗi'),
		];
	}

	public function getProfileValueByKey($field = 'qts_request_id'): string
	{
		$profiles = $this->json('data.profiles');
		return collect($profiles)->where('profileKey', $field)->first()['value'];
	}
} // End class
