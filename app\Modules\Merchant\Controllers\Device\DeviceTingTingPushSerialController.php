<?php

namespace App\Modules\Merchant\Controllers\Device;

use App\Lib\Helper;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Actions\Device\Mpos360PushSerialToTingTingSystemAction\Mpos360PushSerialToTingTingSystemAction;

class DeviceTingTingPushSerialController extends Controller
{
	public function Mpos360PushSerialToTingTingSystem()
	{
		try {
			$result = app(Mpos360PushSerialToTingTingSystemAction::class)->run();
			return $this->successResponse(['result' => $result], request());
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
