<?php

namespace App\Modules\Merchant\Requests\InstantWithdrawal\V3;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360InstantWithdrawalListRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.email' => ['nullable', 'string', 'email'],
			'data.order_rtn_time' => ['nullable', 'string'],
			'data.order_rtn_status' => ['nullable', 'string'],
			'data.start' => ['numeric', 'integer'],
			'data.limit' => ['numeric', 'integer'],
		];
	}

	protected function prepareForValidation()
	{
		parent::prepareForValidation();
		
		$params = $this->all();
		if (empty($params['data']['start'])) {
			$params['data']['start'] = 0;
		}

		if (empty($params['data']['limit'])) {
			$params['data']['limit'] = 0;
		}
		$this->merge($params);
	}

	public function getStartDateEndDate() {
		$time = $this->json('data.order_rtn_time');
		switch ($time) {
			case 'LAST_MONTH': 
				return [
					'startDate' => now()->subMonth()->startOfMonth()->format('d/m/Y H:i:s'),
					'endDate' => now()->subMonth()->endOfMonth()->format('d/m/Y H:i:s'),
				];

			case 'THIS_MONTH': 
				return [
					'startDate' => now()->startOfMonth()->format('d/m/Y H:i:s'),
					'endDate' => now()->endOfMonth()->format('d/m/Y H:i:s'),
				];

			case 'TODAY': 
				return [
					'startDate' => now()->startOfDay()->format('d/m/Y H:i:s'),
					'endDate' => now()->endOfDay()->format('d/m/Y H:i:s'),
				];

			case 'YESTERDAY': 
				return [
					'startDate' => now()->subDay()->startOfDay()->format('d/m/Y H:i:s'),
					'endDate' => now()->subDay()->endOfDay()->format('d/m/Y H:i:s'),
				];

			default: 
				return [
					'startDate' => now()->subDays(10)->startOfDay()->format('d/m/Y H:i:s'),
					'endDate' => now()->endOfDay()->format('d/m/Y H:i:s'),
				];
		}

		return [];
	}

	public function getPageIndex() {
		$start = $this->json('data.start', 0);
		$limit = $this->json('data.limit', 10);

		if ($start == 0 || $start < $limit) {
			return 0;
		}

		return ceil($start / $this->json('data.limit'));
	}
} // End class
