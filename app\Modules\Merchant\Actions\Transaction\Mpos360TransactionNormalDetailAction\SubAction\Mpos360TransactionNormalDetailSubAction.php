<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransactionNormalDetailAction\SubAction;

use App\Lib\Helper;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionNormalDetailRequest;

use App\Modules\Merchant\Actions\Transaction\subAction\Mpos360TransactionDefineConfigSubAction;

class Mpos360TransactionNormalDetailSubAction
{
	public $_type = 'DETAIL_TRANSACTION';
	public function __convertDataReturn($listTransaction = [])
	{
		$data = $this->__defaultReturn();
		if ($listTransaction) {
			if (isset($listTransaction['data']['mc360DetailTransaction'])) {
				$dataDetail = $listTransaction['data']['mc360DetailTransaction'];
				$data['data'][] = $this->__commonInfo($dataDetail); //0
				$data['data'][] = $this->__orderInfo($dataDetail); //1

				$blockChuongTrinhKhuyenMai = $this->__promotionInfo($dataDetail); //3

				if (!empty($blockChuongTrinhKhuyenMai)) {
					$data['data'][] = $blockChuongTrinhKhuyenMai;
				}
				

				if (isset($dataDetail['cardholderName'])) {
					$data['data'][] = $this->__customerInfo($dataDetail); //4
				}
				$data['data'][] = $this->__paymentInfo($dataDetail); //2
				$data['data'][] = $this->__otherInfo($dataDetail); //5
			}
		}
		return $data;
	}
	private function __orderInfo($detail)
	{
		$discountValue =  (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'discountValue', 'amount');
		if ($discountValue) {
			$discountValue = '-' . $discountValue;
		}
		$detail['totalPaymentAmount2'] = (int)$detail['amount'] + (int)$detail['customerPaymentFee']  - (int)$detail['discountValue'];
		$data  = [
			'key' => 'order_info',
			'name' => trans_choice_fallback('trans.title.order_info', 'Thông tin đơn hàng'),
			'list' => [],
		];
		$data['list'] = [
			[
				'key' => 'amount_order',
				'label' =>trans_choice_fallback('trans.title.amount_order',  'Số tiền đơn hàng'),
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'amount', 'amount'),
				'other_data' => (object) [],
			],
			[
				'key' => 'discountValue',
				'label' => trans_choice_fallback('trans.title.discountValue',  'Số tiền KM'),
				'value' => $discountValue,
				'other_data' => (object) [
					// 'font_weight' => 'bold'
				],
			],
			[
				'key' => 'customerPaymentFee',
				'label' => trans_choice_fallback('trans.title.customerPaymentFee',  'Phí GD (thu KH)'),
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'customerPaymentFee', 'amount'),
				'other_data' => (object) [],
			],
			[
				'key' => 'totalPaymentAmount2',
				'label' => trans_choice_fallback('trans.title.totalPaymentAmount2', 'Tổng thanh toán'),
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'totalPaymentAmount2', 'amount'),
				'other_data' => (object) [
					'font_weight' => 'bold',
				],
			]

		];
		return $data;
	}
	

	
	private function __getTransMethodCode($value)
	{
		if ($value) {
			return (new Mpos360TransactionDefineConfigSubAction())->getPaymentMethodCode($value);
		}
		return '';
	}
	private function __getTransMethodName()
	{
		return (new Mpos360TransactionDefineConfigSubAction())->getPaymentMethod();
	}
	private function __commonInfo($detail)
	{
		$mapData = (new Mpos360TransactionDefineConfigSubAction())->getStatusTrans();
		$statusText = '';
		$statusOther = (object)[];
		if (isset($detail['status'])) {
			foreach ($mapData as $key => $value) {
				if ($value['value'] == $detail['status']) {
					$statusText = $value['label'];
					$statusOther = (object)$value;
					break;
				}
			}
		}
		$data  = [
			'key' => 'common_info',
			'name' => trans_choice_fallback('trans.title.common_info','Thông tin chung'),
			'list' => [],
		];
		$period = '';


		$paymentMethod = '';
		$mapData = (new Mpos360TransactionDefineConfigSubAction())->getPaymentMethod();
		foreach ($mapData as $key1 => $value1) {
			$transMethodNameArr[$value1['value']] = $value1['label'];
		}
		$transaction_method = $this->__getTransMethod($detail);
		$paymentMethod = isset($transMethodNameArr[$transaction_method]) ? $transMethodNameArr[$transaction_method] : $transaction_method;
		// var_dump($detail);die();
		$data['list'] = [
			[
				'key' => 'transType',
				'label' => trans_choice_fallback('trans.title.transType','Loại GD'),
				'value' => trans_choice_fallback('trans.title.transTypeNomarl','Thanh toán thường'),
				'other_data' => (object) [],
			],
			[
				'key' => 'paymentMethod',
				'label' => trans_choice_fallback('trans.title.paymentMethod','Hình thức'),
				'value' => $paymentMethod,
				'other_data' => (object) [],
			],
			[
				'key' => 'txid',
				'label' => trans_choice_fallback('trans.title.txid','Mã GD'),
				'value' => $detail['txid'],
				'other_data' => (object) [],
			],
			[
				'key' => 'authCode',
				'label' => trans_choice_fallback('trans.title.authCode','Mã chuẩn chi'),
				'value' => isset($detail['authCode'])?$detail['authCode']:'',
				'other_data' => (object) [],
			],
			[
				'key' => 'createdDate',
				'label' => trans_choice_fallback('trans.title.createdDate','Thời gian GD'),
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'createdDate', 'datetime'),
				'other_data' => (object) [],
			],
			[
				'key' => 'settleDate',
				'label' => trans_choice_fallback('trans.title.settleDate','Thời gian kết toán'),
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'settleDate', 'datetime'),
				'other_data' => (object) [],
			],
			[
				'key' => 'voildDate',
				'label' => trans_choice_fallback('trans.title.voildDate','Thời gian hủy GD'),
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'voildDate', 'datetime'),
				'other_data' => (object) [],
			],
			// [
			// 	'key' => 'status',
			// 	'label' =>trans_choice_fallback('trans.title.status', 'Trạng thái GD'),
			// 	'value' => $statusText,
			// 	'other_data' => $statusOther,
			// ],
			[
				'key' => 'status',
				'label' =>trans_choice_fallback('trans.title.status', 'Trạng thái GD'),
				'value' => 'Thành công',
				'other_data' => ['value' => '100', 'label' => trans_choice_fallback('trans.status.100', 'Thành công'), 'text_color' => '#ffffff', 'bg_color' => '#3BB54A', 'display_type' => 'pills'],
			]
		];
		$data['list'][] = [
			'label' => trans_choice_fallback('trans.title.description', 'Mô tả'),
			'value' => $detail['description'],
			'other_data' => ['text_color' => '#404041', 'bg_color' => '#ebebeb']
		];
		return $data;
	}
	private function __getTransMethod($value)
	{
		if ($value) {
			return (new Mpos360TransactionDefineConfigSubAction())->getPaymentMethodCode($value);
		}
		return '';
	}
	private function __paymentInfo($detail)
	{

		$data  = [
			'key' => 'payment_info',
			'name' => trans_choice_fallback('trans.title.payment_info','Thông tin thanh toán'),
			'list' => [],
		];
		$mapData = (new Mpos360TransactionDefineConfigSubAction())->getWithdrawStatusTrans2();
		$withdrawStatus = '';
		$withdrawStatusText = '';
		$withdrawStatusOther = (object)[];
		foreach ($mapData as $key => $value) {
			$withDrawstatusArr[$value['value']] = $value;
		}
		if (!isset($detail['withdrawStatus']) || $detail['withdrawStatus'] !== 'HAS_BALANCE') {
			$withdrawStatus =  'CHUA_THANH_TOAN';
			$detail['withdrawStatus'] = 'CHUA_THANH_TOAN';
		} else {
			$withdrawStatus =  $detail['withdrawStatus'];
		}
		$withdrawStatusText =  $withDrawstatusArr[$withdrawStatus]['label'];
		$withdrawStatusOther = (object)$withDrawstatusArr[$withdrawStatus];

		if (empty($detail['transactionFee'])) {
			$detail['transactionFee'] = 0;
		}
		
		$detail['totalAmount3'] = $detail['amount'] + $detail['transactionFee'];
		$data['list'] = [
			[
				'key' => 'totalAmount3',
				'label' => trans_choice_fallback('trans.title.amount_payment','Số tiền thanh toán'),
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'totalAmount3', 'amount'),
				'other_data' => (object) [],
			],
			[
				'key' => 'transactionFee',
				'label' =>trans_choice_fallback('trans.title.merchant_fee', 'Phí (thu Merchant)'),
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'transactionFee', 'amount'),
				'other_data' => (object) [],
			],
			[
				'key' => 'withdrawStatus',
				'label' => trans_choice_fallback('trans.title.statusInfo','Trạng thái'),
				'value' => $withdrawStatusText,
				'other_data' => $withdrawStatusOther,
			]
		];
		return $data;
	}
	private function __promotionInfo($detail)
	{
		if (!isset($detail['promotionCode']) || empty($detail['promotionCode'])) {
			return [];
		}

		$data  = [
			'key' => 'promotions',
			'name' => trans_choice_fallback('trans.title.promotions','CT khuyến mại'),
			'list' => [
				[
					'key' => 'campainName',
					'label' => trans_choice_fallback('trans.title.campainName','Chương trình'),
					'value' => $detail['campainName'],
					'other_data' => (object) [
						// 'display_type' => 'webview',
						// 'url' => 'https://mpos.vn'
						// update 06.11.2024: mpos không hỗ trợ đc website CTKM, off thành text đen
					],
				],
				[
					'key' => 'promotionCode',
					'label' => trans_choice_fallback('trans.title.promotionCode','Mã khuyến mại'),
					'value' => $detail['promotionCode'],
					'other_data' => (object) [],
				],
				[
					'key' => 'discountValue',
					'label' => trans_choice_fallback('trans.title.discountValue','Số tiền KM'),
					'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'discountValue', 'amount'),
					'other_data' => (object) [
						'font_weight' => 'bold'
					],
				]
			],
		];
		return $data;
	}
	private function __customerInfo($detail)
	{
		$cardType = '';
		if (isset($detail['issuerCode'])) {
			$cardType = (new Mpos360TransactionDefineConfigSubAction())->getTypeCard($detail['issuerCode']);
		}
		$pan = $cardType . ': ' . $detail['pan'];
		$data  = [
			'key' => 'customer',
			'name' => trans_choice_fallback('trans.title.customer','Khách hàng'),
			'list' => [
				[
					'key' => 'cardholderName',
					'label' => trans_choice_fallback('trans.title.cardholderName','Chủ thẻ'),
					'value' => $detail['cardholderName'],
					'other_data' => (object) [],
				],
				[
					'key' => 'pan',
					'label' => trans_choice_fallback('trans.title.pan','Thẻ/TKNH/Ví điện tử'),
					'value' =>  $pan,
					'other_data' => (object) [],
				]
			],
		];
		return $data;
	}
	private function __otherInfo($detail)
	{
		$data  = [
            'key' => 'other',
            'name' => trans_choice_fallback('trans.title.other','Khác'),
            'list' =>[
                [
                    'key' => 'batch',
                    'label' => trans_choice_fallback('trans.title.batch','Số lô'),
                    'value' => isset($detail['batch'])?$detail['batch']:'',
                    'other_data' => (object) [],
                ],
                [
                    'key' => 'tid',
                    'label' => trans_choice_fallback('trans.title.TID','TID'),
                    'value' => $detail['tid'],
                    'other_data' => (object) [],
                ],
                [
                    'key' => 'mid',
                    'label' => trans_choice_fallback('trans.title.MID','MID'),
                    'value' => $detail['mid'],
                    'other_data' => (object) [],
                ],
                [
                    'key' => 'rrn',
                    'label' => trans_choice_fallback('trans.title.rrn','Số tham chiếu'),
                    'value' => $detail['rrn'],
                    'other_data' => (object) [
                        'font_weight' => 'bold'
                    ],
                ],
                [
                    'key' => 'muid',
                    'label' => trans_choice_fallback('trans.title.muid','Mobile user'),
                    'value' => $detail['muid'],
                    'other_data' => (object) [],
                ]
            ],
        ];
        return $data;
	}



	private function __convertTime($time)
	{
		try {
			if ($time) {
				$strtotime = strtotime($time);
				return date('H:i d-m-Y', $strtotime);
			}
			return '';
		} catch (\Throwable $th) {
			return $time;
		}
	}
	private function __convertAmount($amount)
	{
		try {
			return Helper::numberFormat($amount) . ' VND';
		} catch (\Throwable $th) {
			return $amount;
		}
	}
	private function __defaultReturn()
	{
		return [
			'warning' => [],
			'data' => [],
			'other_data' => (object)[],
		];
		// 'warning' => [
		// 	// [
		// 	// 	'label' => '',
		// 	// 	'value' => 'Cần cung cấp chứng từ',
		// 	// 	'other_data' => [
		// 	// 		'text_color' => '#d329a0',
		// 	// 		'bg_color' => '#fbeaf6'
		// 	// 	]
		// 	// ]
		// ],
	}

	private function __getStatusDefault()
	{
		return (new Mpos360TransactionDefineConfigSubAction())->getStatusTrans();
	}
	private function __getCardType($value)
	{
		if (isset($value['issuerCode'])) {
			return (new Mpos360TransactionDefineConfigSubAction())->getTypeCard($value['issuerCode']);
		}
		return '';
	}
}
