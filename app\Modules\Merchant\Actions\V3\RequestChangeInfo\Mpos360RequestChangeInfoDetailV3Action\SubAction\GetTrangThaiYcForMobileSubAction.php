<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoDetailV3Action\SubAction;

use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;

class GetTrangThaiYcForMobileSubAction
{
	public function run(Mpos360MerchantRequest $mpos360McRequest)
	{
		$supplements = $mpos360McRequest->mpos360McSupplements;
		if ($supplements->isNotEmpty()) {
			$isTonTaiYcBoSungMoi = $supplements->where('status', Mpos360Enum::MPOS360_BO_SUNG_HO_SO_MOI_TAO)->first();
			// Tồn tại yc bổ sung thông tin phải thao tác
			if ($isTonTaiYcBoSungMoi) {
				$returnData['name'] = 'Cần bổ sung thông tin';
				$returnData['other_data'] = (object) [
					'text_color' => '#ffffff',
					'bg_color' => '#bb2deb',
					'display_type' => 'pills'
				];

				return $returnData;
			}
		}
		
		// Hết hạn
		if ($mpos360McRequest->isHetHanVaChuaTaoYc()) {
			$returnData['name'] = vmsg('Hết hạn');
			$returnData['other_data'] = (object) [
				'text_color' => '#ffffff',
				'bg_color' => '#808890',
				'display_type' => 'pills'
			];

			return $returnData;
		}

		// Tự hủy
		if ($mpos360McRequest->isMcTuHuyYc()) {
			$returnData['name'] = vmsg('MC hủy YC');
			$returnData['other_data'] = (object) [
				'text_color' => '#ffffff',
				'bg_color' => '#da2128',
				'display_type' => 'pills'
			];

			return $returnData;
		}

		// Nháp
		if ($mpos360McRequest->status == Mpos360Enum::MPOS360_MC_REQUEST_STT_NHAP) {
			$returnData['name'] = 'Cần hoàn thiện';
			$returnData['other_data'] = (object) [
				'text_color' => '#ffffff',
				'bg_color' => '#b8bed1',
				'display_type' => 'pills'
			];

			return $returnData;
		}

		// Chờ xử lý
		if (
			in_array($mpos360McRequest->status, [
				Mpos360Enum::MPOS360_MC_REQUEST_STT_CHUA_GUI,
				Mpos360Enum::MPOS360_MC_REQUEST_STT_DANG_GUI,
				Mpos360Enum::MPOS360_MC_REQUEST_STT_DA_GUI_SANG_MNP,
			])
		) {
			$returnData['name'] = 'Chờ xử lý';
			$returnData['other_data'] = (object) [
				'text_color' => '#ffffff',
				'bg_color' => '#e99323',
				'display_type' => 'pills'
			];

			return $returnData;
		}

		// Bị từ chối
		if ($mpos360McRequest->status == Mpos360Enum::MPOS360_MC_REQUEST_STT_DA_TU_CHOI) {
			$returnData['name'] = 'Bị từ chối';
			$returnData['other_data'] = (object) [
				'text_color' => '#ffffff',
				'bg_color' => '#da2128',
				'display_type' => 'pills'
			];

			return $returnData;
		}

		// Đã duyệt
		if ($mpos360McRequest->status == Mpos360Enum::MPOS360_MC_REQUEST_STT_DA_XU_LY) {
			$returnData['name'] = 'Đã duyệt';
			$returnData['other_data'] = (object) [
				'text_color' => '#ffffff',
				'bg_color' => '#73ae4a',
				'display_type' => 'pills'
			];

			return $returnData;
		}

		// Cập nhật lỗi
		if ($mpos360McRequest->status == Mpos360Enum::MPOS360_MC_REQUEST_STT_CAP_NHAT_LOI) {
			$returnData['name'] = 'Cập nhật lỗi';
			$returnData['other_data'] = (object) [
				'text_color' => '#ffffff',
				'bg_color' => '#da2128',
				'display_type' => 'pills'
			];

			return $returnData;
		}

		// Không xác định
		return [
			'name' => vmsg('Không xác định'),
			'other_data' => (object) [
				'text_color' => '#ffffff',
				'bg_color' => '#000000',
				'display_type' => 'pills'
			],
		];
	} // End method
} // End class