<?php

namespace App\Modules\Merchant\Controllers\Authen\Mpos360;

use App\Lib\Helper;
use App\Lib\MnpOnboardNewMcHelper;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360Register\Mpos360SendOtpRequest;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360Register\Mpos360ResendOtpRequest;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360Register\Mpos360RegisterUploadRequest;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360Register\Mpos360SubmitFormRegisterRequest;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360Register\Mpos360SubmitFormVerifyOtpRequest;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360Register\Mpos360GetFormAuthenAccountRequest;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360Register\Mpos360SubmitFormVerifyOtpV2Request;
use App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360ReSendOtpAction\Mpos360ReSendOtpAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360SendOtpAction\Mpos360RegisterSendOtpAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360RegisterUploadAction\Mpos360RegisterUploadAction;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360Register\Mpos360SubmitFormAuthenAccountStep1ActionRequest;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360Register\Mpos360SubmitFormAuthenAccountStep2ActionRequest;
use App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360SubmitFormRegisterAction\Mpos360SubmitFormRegisterAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360SubmitFormAuthenOtpAction\Mpos360SubmitFormVerifyOtpAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360GetFormAuthenAccountAction\Mpos360GetFormAuthenAccountAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360SubmitFormAuthenOtpAction\Mpos360SubmitFormVerifyOtpV2Action;
use App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360SubmitFormAuthenAccountAction\Mpos360SubmitFormAuthenAccountStep1Action;
use App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360SubmitFormAuthenAccountStep2Action\Mpos360SubmitFormAuthenAccountStep2Action;

class Mpos360AuthRegisterController extends Controller
{
	public function Mpos360SubmitFormRegister(Mpos360SubmitFormRegisterRequest $request)
	{
		try {
			$result = app(Mpos360SubmitFormRegisterAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360RegisterSendOtp(Mpos360SendOtpRequest $request)
	{
		try {
			$result = app(Mpos360RegisterSendOtpAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360SubmitFormVerifyOtp(Mpos360SubmitFormVerifyOtpRequest $request)
	{
		try {
			$result = app(Mpos360SubmitFormVerifyOtpAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360SubmitFormVerifyOtpV2(Mpos360SubmitFormVerifyOtpV2Request $request)
	{
		try {
			$result = app(Mpos360SubmitFormVerifyOtpV2Action::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360RegisterUpdateCccdInfo(Mpos360SubmitFormAuthenAccountStep2ActionRequest $request)
	{
		try {
			$result = app(Mpos360SubmitFormAuthenAccountStep2Action::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360RegisterUpload(Mpos360RegisterUploadRequest $request)
	{
		$merchantId = $request->input('data.merchantId');
		$mnpToken = app(MnpOnboardNewMcHelper::class)->getAccessTokenMcDefault();

		try {
			$result = app(Mpos360RegisterUploadAction::class)->run(
				$merchantId, 
				$mnpToken, 
				$request->file('data.attachments')
			);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
} // End class
