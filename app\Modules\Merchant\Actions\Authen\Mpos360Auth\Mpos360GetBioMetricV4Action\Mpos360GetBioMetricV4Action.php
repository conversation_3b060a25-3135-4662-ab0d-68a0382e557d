<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360GetBioMetricV4Action;

use Exception;
use App\Lib\Helper;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Device;
use App\Modules\Merchant\Model\Mpos360User;
use App\Modules\Merchant\Requests\Authen\Mpos360\V4\Mpos360GetBioMetricV4Request;

class Mpos360GetBioMetricV4Action
{

	public function run(Mpos360GetBioMetricV4Request $request)
	{
		$canUpdateSinhTrac = [
			'has_info' => 'NO',
			'msg' => 'Bạn chưa có thông tin sinh trắc'
		];

		$khongCanUpdateSinhTrac = [
			'has_info' => 'YES',
			'msg' => 'Bạn đã cập nhật thông tin sinh trắc rồi'
		];


		if (empty($request->json('data.deviceToken'))) {
			return $khongCanUpdateSinhTrac;
		}

		$mpos360User = Mpos360User::query()->where('username', $request->json('data.email'))->first();
		
		if (!$mpos360User) {
			return $canUpdateSinhTrac;
		};

		$device = Device::query()->whereRaw("UPPER(os) = ?", [trim($request->json('data.os'))])
														 ->where('token', $request->json('data.deviceToken'))
														 ->where('user_id', $mpos360User->id)
														 ->first();
						
		if (!$device) {
			$device = Device::query()->forceCreate([
				'os' => Helper::chuanHoaOs($request->json('data.os')),
				'token' => $request->json('data.deviceToken'),
				'user_id' => $mpos360User->id,
				'time_created' => now()->timestamp,
				'time_updated' => now()->timestamp,
				'other_key' => null
			]);

			if (!$device) {
				throw new BusinessException('Lỗi không có thông tin thiết bị của bạn');
			}
		}

		$otherData = json_decode($device->other_key, true);
		
		if (empty($otherData)) {
			return $canUpdateSinhTrac;
		}

		$hasBioMetric = collect($otherData)->contains(function ($item) {
			return $item['type'] == 'BIO_METRIC';
		});

		// Có thì k cần update
		if ($hasBioMetric) {
			return $khongCanUpdateSinhTrac;
		}

		return $canUpdateSinhTrac;
	}
} // End class
