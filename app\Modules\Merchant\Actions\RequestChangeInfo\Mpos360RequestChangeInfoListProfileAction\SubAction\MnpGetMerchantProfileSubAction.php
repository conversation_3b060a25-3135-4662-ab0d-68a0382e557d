<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction;

use App\Lib\Helper;
use App\Lib\partner\MNP;
use Illuminate\Support\Facades\Cache;

class MnpGetMerchantProfileSubAction
{
	public MNP $mnp;

	public int $cacheTime = 8*60*60;

	public function __construct(MNP $mnp)
	{
		$this->mnp = $mnp;
	}

	public function run(string $mposMerchantId, string $mnpToken)
	{
		$cacheName = sprintf(
			'mnp_profile_list_only_%s_%s', 
			request()->json('lang'),
			$mposMerchantId
		);

		// if (Cache::has($cacheName) && Helper::isLocalOrDevEnv()) {
		// 	return Cache::get($cacheName);
		// }

		$result = $this->mnp->getCustomerInfo($mposMerchantId, $mnpToken);

		// if (!empty($result['status']) && !empty($result['data'])) {
		// 	$r = Cache::put($cacheName, $result, $this->cacheTime);
		// }
		
		return $result;
	}
} // End class
