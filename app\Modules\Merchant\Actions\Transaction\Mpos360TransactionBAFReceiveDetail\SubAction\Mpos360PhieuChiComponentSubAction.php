<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransactionBAFReceiveDetail\SubAction;

use App\Lib\Helper;
use App\Modules\Merchant\Actions\Transaction\subAction\Mpos360TransactionDefineConfigSubAction;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalDetailAction\Mpos360DetailYcNtnAction;

class Mpos360PhieuChiComponentSubAction
{
	public $_type = 'DETAIL_WITHDRAW';

	public function __returnData($detailPhieuChi)
	{
		$data = $this->__defaultReturn(); // $data['data'] = [];
		
		if ($detailPhieuChi) {
			if (isset($detailPhieuChi['data']['mc360DetailWithdrawDTO'])) {
				$dataList = $detailPhieuChi['data']['mc360DetailWithdrawDTO'];
				$data['data'][] = $this->__commonInfo($dataList); //0
				$data['data'][] = $this->__customerPayment($dataList); //1
				$data['data'][] = $this->__customerInfo($dataList); //4
			}
		}

		return $data;
	}


	private function __commonInfo($detail)
	{
		$mapData = (new Mpos360TransactionDefineConfigSubAction())->getWithdrawStatusTrans();
		$statusText = '';
		$statusOther = (object)[];
		if (isset($detail['status'])) {
			foreach ($mapData as $key => $value) {
				if ($value['value'] == $detail['status']) {
					$statusText = $value['label'];
					$statusOther = (object)$value;
					break;
				}
			}
		}
		
		$data  = [
			'key' => 'common_info',
			'name' => 'Trả định kỳ',
			'list' => [],
		];

		$data['list'] = [
			[
				'key' => 'withdrawId',
				'label' => 'Mã phiếu chi',
				'value' => $detail['withdrawId'],
				'extra' => "",
				'other_data' => (object) [
					'font_weight' => 'bold'
				]
			],

			[
				'key' => 'createdDate',
				'label' => 'Ngày khởi tạo',
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'createdDate', 'datetime'),
				'extra' => "",
				'other_data' => (object) []
			],
			[
				'key' => 'status',
				'label' => 'Trạng thái',
				'value' => $statusText,
				'extra' => "",
				'other_data' => $statusOther
			],

		];
		return $data;
	}

	private function __customerPayment($detail)
	{
		$data  = [
			'key' => 'customer_payment',
			'name' => 'Giao dịch',
			'list' => [],
		];
		$receivedAmountStringOther = (object)[];
		if ($detail['receivedAmountString']) {
			$receivedAmountStringOther = (object)[
				"display_type" => "block"
			];
		}

		$data['list'] = [
			[
				'key' => 'totalSettlement',
				'label' => 'Tổng số tiền',
				'value' => Helper::priceFormat($detail['amount'], ' VND'),
				'extra' => "",
				'other_data' => (object) []
			],

			[
				'key' => 'transactionFee',
				'label' => 'Phí giao dịch',
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'transactionFee', 'amount'),
				'other_data' => (object) [],
			],


			[
				'key' => 'fee',
				'label' => 'Phí chuyển tiền',
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'fee', 'amount'),
				'extra' => "",
				'other_data' => (object) []
			],

			
			[
				'key' => 'totalCollection',
				'label' => 'Thu thêm',
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'totalCollection', 'amount'),
				'extra' => "",
				'other_data' => (object) []
			],

			[
				'key' => 'totalRefund',
				'label' => 'Trả lại',
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'totalRefund', 'amount'),
				'extra' => "",
				'other_data' => (object) []
			],

			[
				'key' => 'receivedAmount',
				'label' => 'Tổng nhận',
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'receivedAmount', 'amount'),
				'extra' => "",
				'other_data' => (object) [
					'font_weight' => 'bold',
					'text_color' => '#3BB54A'
				]
			],

			[
				'key' => 'receivedAmountString',
				'label' => trans_choice_fallback('trans.title.amount_string'),
				'value' => is_string($detail['receivedAmountString']) ? trim($detail['receivedAmountString']) : $detail['receivedAmountString'],
				'extra' => "",
				'other_data' => $receivedAmountStringOther
			]
		];
		return $data;
	}
	

	private function __customerInfo($detail)
	{
		$label = sprintf('%s:  %s - %s', $detail['bankName'], $detail['accountNo'], $detail['holderName']);

		$data  = [
			'key' => 'customer',
			'name' => trans_choice_fallback('trans.title.account_recieve'),
			'list' => [
				[
					'key' => 'NganHang',
					'label' => app(Mpos360DetailYcNtnAction::class)->__formatBankString($label),
					'value' => '',
					'extra' => '',
					'other_data' => (object) [
						'alignment' => 'center',
						'label_text_color' => '#404041'
					]
				],
			],
		];

		return $data;
	}

	private function __defaultReturn()
	{
		return [
			'warning' => [],
			'data' => [],
			'other_data' => (object)[],
		];
	}
}
