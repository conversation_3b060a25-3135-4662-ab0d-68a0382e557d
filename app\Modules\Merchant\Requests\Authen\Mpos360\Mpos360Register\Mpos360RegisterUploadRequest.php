<?php

namespace App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360Register;

use Illuminate\Foundation\Http\FormRequest;

class Mpos360RegisterUploadRequest extends FormRequest
{
    	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.merchantId' => ['required', 'string'],
			'data.attachments' => ['required', 'array', 'max:2'],
			'data.attachments.*' => [
				'required',
				'file',
				'mimetypes:image/jpeg,image/png,image/svg+xml,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,video/mp4,video/x-msvideo,video/x-ms-wmv,video/quicktime,video/mpeg,video/3gpp,video/ogg,video/webm,video/x-flv,video/x-matroska,video/x-m4v',
				'max:5200'
			]
		];
	}

	public function messages()
	{
		return [
			'data.merchantId.required' => 'Mã khách hàng là bắt buộc',
			'data.merchantId.string' => 'Mã khách hàng không đúng định dạng',

			'data.attachments.required' => 'File upload là bắt buộc',
			'data.attachments.array' => 'File upload phải là 1 danh sách',
			'data.attachments.max' => 'Số lượng file upload tối đa là 2 file',	

			'data.attachments.*.required' => 'Fie upload là bắt buộc',	
			'data.attachments.*.mimetypes' => 'File upload không đúng định dạng',	
			'data.attachments.*.max' => 'Dung lượng upload tối đa là dưới 5MB',	
		];
	}
}