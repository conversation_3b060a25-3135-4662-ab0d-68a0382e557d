<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360SendOtpAction\SubAction;

use Illuminate\Http\Request;
use App\Lib\MnpOnboardNewMcHelper;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Model\Mpos360CodeOtp;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\SendSmsOtpMNPSubAction;

class SendOtpSubAction
{
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run(Request $request, Mpos360CodeOtp $mpos360CodeOtp)
	{
		$deviceSession = new DeviceSession();
		$deviceSession->mnp_token = $this->mnpOnboardNewMcHelper->getAccessTokenMcDefault();
		$channel = strtolower($request->json('data.channel')) == 'zalo' ? true : false;
		$sendOtp = app(SendSmsOtpMNPSubAction::class)->run($mpos360CodeOtp, $deviceSession, $channel);
		return $sendOtp;
	}
}
