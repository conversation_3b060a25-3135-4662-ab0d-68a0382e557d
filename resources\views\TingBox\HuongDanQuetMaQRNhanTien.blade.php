
<html lang="zxx">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Tingbox - Hướng dẫn quét mã QR</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@300;400;500;600&display=swap" rel="stylesheet">

    <style type="text/css">
        .be-vietnam-pro-light {
          font-family: "Be Vietnam Pro", serif;
          font-weight: 300;
          font-style: normal;
        }

        .be-vietnam-pro-regular {
          font-family: "Be Vietnam Pro", serif;
          font-weight: 400;
          font-style: normal;
        }

        .be-vietnam-pro-medium {
          font-family: "Be Vietnam Pro", serif;
          font-weight: 500;
          font-style: normal;
        }

        .be-vietnam-pro-semibold {
          font-family: "Be Vietnam Pro", serif;
          font-weight: 600;
          font-style: normal;
        }
        .Tingbox-all{
            padding: 24px;
        }
        .Tingbox-head{display: block;text-align: center;margin-bottom: 16px}
        .Tingbox-head span{}
        .Tingbox-head span img{}
        .Tingbox-main{}
        .Tingbox-main h2{font-size: 18px;color: #808890;margin-bottom: 12px;}
        .Tingbox-main > p{font-size: 14px;color: #404040;margin-bottom: 16px;}
        .Tingbox-main > article{color: #DD8523;font-size: 14px;}
        .Tingbox-main > article > b{}
        .Tingbox-main > article > p{}
        .Tingbox-main > label{}
        .Tingbox-main > p.tm-step{margin-top: 12px;margin-bottom: 0}
        .Tingbox-main > p.tm-step span{color: #73AE4A;}
        .Tingbox-main > p.tm-step b{}
    </style>
</head>

<body class="be-vietnam-pro-regular">
    <div class="Tingbox-all">
        <div class="Tingbox-head">
            <span><img style="width: 200px;" src="https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/mpos360/app/67fccdc44465eb435f2eebaeHuongDanQuetMaQrNhanTien.png"></span>
        </div>
        <div class="Tingbox-main">
            <h2 class="be-vietnam-pro-semibold">Hướng dẫn quét mã QR nhận tiền trên loa Tingbox</h2>
            <p>Mã QR nhận tiền nằm ở mặt trước loa Tingbox. Khi khách hàng dùng ứng dụng mobile banking quét mã này, loa sẽ phát thông báo và tiền được ghi nhận ngay vào tài khoản của bạn.</p>
            <article>
                <b>⚠️ Lưu ý quan trọng: </b>
                <p>Mã QR nhận tiền dán ở mặt trước loa chỉ hoạt động khi hoàn tất bước liên kết tài khoản ngân hàng.</p>
            </article>
            <label class="be-vietnam-pro-semibold">Các bước thực hiện:</label>
            <p class="tm-step"><span class="be-vietnam-pro-medium">Bước 1:</span> <b>Tìm mã QR:</b> Mở hộp loa Tingbox, kiểm tra mã QR in sẵn (dạng "BIDV-[mã tham chiếu]").</p>
            <p class="tm-step"><span class="be-vietnam-pro-medium">Bước 2:</span> <b>Quét mã:</b> Hướng camera điện thoại đến mã QR nhận tiền và quét.</p>
            <p class="tm-step"><span class="be-vietnam-pro-medium">Bước 3:</span> <b>Hoàn tất:</b> Mã QR nhận tiền chỉ được kích hoạt khi hoàn tất bước liên kết tài khoản ngân hàng.</p>
        </div>
    </div>

        

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    
</body>

</html>