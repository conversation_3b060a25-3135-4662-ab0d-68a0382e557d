<?php

namespace App\Modules\TingBoxVAMC\Actions\Mpos360VAMCForVCBAction\SubAction;

use App\Exceptions\BusinessException;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use App\Modules\TingBoxVAMC\Enums\TingBoxVAMCEnum;
use Illuminate\Support\Facades\DB;

class ActiveNotifyAcbBankSubAction
{
	public function run($request)
	{
		// Lấy data từ vaRequest -> đóng cái này không có vaResponse của VA
		$vaRequest = $request->json('vaRequest');
		if (!isset($vaRequest['vaNextpayNumber'])) {
			throw new BusinessException('Thiếu thông tin vaNextpayNumber.');
		}

		// Tìm bản ghi dựa vào vaNextpayNumber
		$merchantShopBank = MerchantShopBank::query()
			->where('partner_request_id', $vaRequest['vaNextpayNumber'])
			->first();

		if (!$merchantShopBank) {
			throw new BusinessException('Không tìm thấy thông tin ngân hàng.');
		}

		
		try {
			if (!empty($request->json('notificationRequired'))) {
				$merchantShopBank->notificationRequired = 'YES'; 
			}else {
				$merchantShopBank->notificationRequired = 'NO'; 
			}
			
			$merchantShopBank->time_updated = now()->timestamp;

			if (!$merchantShopBank->save()) {
				throw new BusinessException('Không thể cập nhật trạng thái liên kết.');
			}


			return [
				'msg' => 'Hủy liên kết pending thành công.',
				'can' => TingBoxVAMCEnum::CAN_GO_TO_TINGBOX_VAMC_SUCCESS,
				'merchantShopBankId' => $merchantShopBank->id,
				'status' => $merchantShopBank->status_link
			];
		} catch (\Throwable $th) {
			throw new BusinessException($th->getMessage());
		}
	}
}
