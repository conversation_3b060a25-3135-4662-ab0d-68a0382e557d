<?php

return [
	'Hôm nay' => 'Hôm nay',
	'Hôm qua' => 'Hôm qua',
	'Thời gian' => 'Thời gian',
	'Tháng này' => 'Tháng này',
	'Tháng trước' => 'Tháng trước',
	'Trạng thái yc' => 'Trạng thái yc',
	'Tất cả' => 'Tất cả',

	'Bạn đã thực hiện quá số lần tạo yêu cầu rút tiền ngay hôm nay rồi. Vui lòng quay lại vào ngày mai' => 'Bạn đã thực hiện quá số lần tạo yêu cầu rút tiền ngay hôm nay rồi. Vui lòng quay lại vào ngày mai',
	'<PERSON><PERSON> tiền thực nhận của bạn đang nhỏ hơn mức min mà hệ thống cấu hình.' => '<PERSON><PERSON> tiền thực nhận của bạn đang nhỏ hơn mức min mà hệ thống cấu hình.',
	'<PERSON>ện chưa có giao dịch nào đủ điều kiện nhận tiền nhanh về tài khoản ngân hàng. Qúy khách vui lòng quay lại sau!' => 'Hiện chưa có giao dịch nào đủ điều kiện nhận tiền nhanh về tài khoản ngân hàng. Qúy khách vui lòng quay lại sau!',
	'Tính năng chỉ hỗ trợ trong giờ hành chính: Thứ 2 - Thứ 6: 08:30 - 18:00, riêng Thứ 7: 08:00 - 11:30' => 'Tính năng chỉ hỗ trợ trong giờ hành chính: Thứ 2 - Thứ 6: 08:30 - 18:00, riêng Thứ 7: 08:00 - 11:30',
	'Tài khoản nhận tiền' => 'Tài khoản nhận tiền',
	'Số GD VietQR' => 'Số GD VietQR',
	'Tổng tiền GD' => 'Tổng tiền GD',
	'Phí dịch vụ NTN' => 'Phí dịch vụ NTN',
	'Phí chuyển tiền' => 'Phí chuyển tiền',
	'Thực nhận tạm tính' => 'Thực nhận tạm tính',

	'Yêu cầu đã được ghi nhận' => 'Yêu cầu đã được ghi nhận',
	'Sau khi duyệt tiền sẽ được chuyển vào số {TKNH} đăng ký với mPOS.' => 'Sau khi duyệt tiền sẽ được chuyển vào số {TKNH} đăng ký với mPOS.',
	'Tài khoản ngân hàng' => 'Tài khoản ngân hàng',
	'Sau khi duyệt tiền sẽ được chuyển vào tài khoản ngân hàng:' => 'Sau khi duyệt tiền sẽ được chuyển vào tài khoản ngân hàng:',
	'Lưu ý: ' => 'Lưu ý: ',
	'Số lượng GD và số tiền thực nhận là tạm tính và có thể bị thay đổi sau khi yêu cầu được xét duyệt.' => 'Số lượng GD và số tiền thực nhận là tạm tính và có thể bị thay đổi sau khi yêu cầu được xét duyệt.',
	'Số tiền tối đa có thể rút' => 'Số tiền tối đa có thể rút',
	'Tổng phí rút tiền ngay' => 'Tổng phí rút tiền ngay',

	/**
	 * @param Mpos360InstantWithdrawalDetailAction
	 */
	'Thông tin chung' => 'Thông tin chung',
	'Mã yêu cầu' => 'Mã yêu cầu',
	'Mã phiếu chi' => 'Mã phiếu chi',
	'Chưa có thông tin' => 'Chưa có thông tin',
	'Loại giao dịch' => 'Loại giao dịch',
	'TG yêu cầu' => 'TG yêu cầu',
	'TG duyệt' => 'TG duyệt',
	'TG thanh toán' => 'TG thanh toán',
	'Trạng thái yêu cầu' => 'Trạng thái yêu cầu',
	'Chi tiết yêu cầu' => 'Chi tiết yêu cầu',
	'Số GD VietQR yêu cầu' => 'Số GD VietQR yêu cầu',
	'Yêu cầu NTN VietQR' => 'Yêu cầu NTN VietQR',
	'Số GD được duyệt' => 'Số GD được duyệt',
	'Yêu cầu NTN được duyệt' => 'Yêu cầu NTN được duyệt',
	'Tổng tiền GD' => 'Tổng tiền GD',
	'Tổng phí trả góp' => 'Tổng phí trả góp',
	'Tổng phí thanh toán ngay' => 'Tổng phí thanh toán ngay',
	'Phí rút tiền nhanh' => 'Phí rút tiền nhanh',
	'Thực nhận' => 'Thực nhận',
	'Tài khoản nhận tiền' => 'Tài khoản nhận tiền',
	'Thu thêm' => 'Thu thêm',
	'Giao dịch thu thêm' => 'Giao dịch thu thêm',
	'Trả lại' => 'Trả lại',
	'Giao dịch trả lại' => 'Giao dịch trả lại',

	/**
	 * @param MapTrangThaiYcRtnSubAction
	 */
	'Đã duyệt' => 'Đã duyệt',
	'Từ chối' => 'Từ chối',
	'Đã thanh toán' => 'Đã thanh toán',
	'Chờ duyệt' => 'Chờ duyệt',
	'Chờ duyệt chi' => 'Chờ duyệt chi',

	'Mã YC' => 'Mã YC',
	'Thời gian tạo' => 'Thời gian tạo',
	'Số GD VietQR' => 'Số GD VietQR',
	'Tổng tiền GD' => 'Tổng tiền GD',
	'Phí dịch vụ NTN' => 'Phí dịch vụ NTN',
	'Thực nhận' => 'Thực nhận',
	'Tổng phí giao dịch' => 'Tổng phí giao dịch',
	'Đã kết toán' => 'Đã kết toán',
	'Phí dịch vụ' => 'Phí dịch vụ',
	'Miễn phí' => 'Miễn phí',

	'Không có thông tin TKNH' => 'Không có thông tin TKNH',
	
	'status_rtn_detail' => [
		'Đã thanh toán' => 'Đã thanh toán',
		'Bị từ chối' => 'Bị từ chối',
		'Đã duyệt' => 'Đã duyệt',
	]
]; // end file