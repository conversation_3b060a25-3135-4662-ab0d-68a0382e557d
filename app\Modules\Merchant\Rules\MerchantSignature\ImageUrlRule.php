<?php

namespace App\Modules\Merchant\Rules\MerchantSignature;

use Illuminate\Contracts\Validation\Rule;

class ImageUrlRule implements Rule
{
	/**
	 * Validate đường link phía mobile gửi lên phải là link hình ảnh chứ ko phải link website
	 *
	 * @param  string  $attribute
	 * @param  mixed  $value: Link hình ảnh
	 * @return bool
	 */
	public function passes($attribute, $value): bool
	{
		return filter_var($value, FILTER_VALIDATE_URL) && preg_match('/\.(jpg|jpeg|png|gif|bmp|webp)$/i', $value);
	}

	/**
	 * Get the validation error message.
	 *
	 * @return string
	 */
	public function message()
	{
		return vmsg('ImageUrlRule_ChuKyPhaiLaMotHinhAnh');
	}
}
