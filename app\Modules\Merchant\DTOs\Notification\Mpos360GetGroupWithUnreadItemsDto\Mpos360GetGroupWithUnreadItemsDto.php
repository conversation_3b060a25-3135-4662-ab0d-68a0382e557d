<?php

namespace App\Modules\Merchant\DTOs\Notification\Mpos360GetGroupWithUnreadItemsDto;

use App\Modules\Merchant\DTOs\Notification\Mpos360GetGroupWithUnreadItemsDto\DataDto;

/**
 * {
    "status": "1",
    "data": {
      "code": "ALL",
      "count": "0",
      "groups": [
        {
          "code": "TRANSACTION",
          "count": "0"
        },
        {
          "code": "REMIND",
          "count": "0"
        },
        {
          "code": "NEWS",
          "count": "0"
        },
        {
          "code": "PROMOTION",
          "count": "0"
        }
      ]
    },
    "message": "Success"
  }
 */
class Mpos360GetGroupWithUnreadItemsDto
{
	public bool $status;
	public DataDto $data;
	public string $message;

	public function __construct(bool $status, DataDto $data, string $message)
	{
		$this->status = $status;
		$this->data = $data;
		$this->message = $message;
	}

	public function toArray(): array {
		return json_decode(json_encode($this), true);
	}
}
