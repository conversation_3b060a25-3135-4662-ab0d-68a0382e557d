<?php

use App\Lib\Mpos360UrlHelper;
use Symfony\Component\HttpFoundation\UrlHelper;

if (!function_exists('vmsg')) {
	function vmsg(string $key, $bindingKeys = []): string
	{
		if (!empty($bindingKeys)) {
			return __('mpos360.' . $key, $bindingKeys);
		}

		return __('mpos360.' . $key);
	}
}


if (!function_exists('cumtomAsset')) {
	function cumtomAsset(string $asset = ''): string
	{
		$baseUrl = config('app.url');

		$url = $baseUrl . '/' . $asset;
		return Mpos360UrlHelper::convertToHttps($url);
	}
}

if (!function_exists('assetTingBox')) {
	function assetTingBox(string $asset = ''): string
	{
		$baseUrl = config('app.url');
		return $baseUrl . '/images/tingbox/huongDanSuDung/' . $asset;
	}
}


if (!function_exists('mylog')) {
	function mylog(array $params = [])
	{
		return app('mylog')->cc($params);
	}
}

if (!function_exists('generateRandomNumber')) {
	function generateRandomNumber($length = 6)
	{
		$number = mt_rand(0, pow(10, $length) - 1);
		return str_pad($number, $length, '0', STR_PAD_LEFT);
	}
}


if (!function_exists('mask_string')) {
	function mask_string($string, $maskChar = '*', $unmaskAmount = 4, $maskFromEnd = false): string
	{
		$maskLength = strlen($string) - $unmaskAmount;
		return substr_replace($string, str_repeat($maskChar, $maskLength), $maskFromEnd ? -$maskLength : 0, $maskLength);
	}
}

if (!function_exists('filterKeys')) {
	function filterKeys($data, $keysToKeep) {
		return array_filter(
				$data,
				function($key) use ($keysToKeep) {
						return in_array($key, $keysToKeep);
				},
				ARRAY_FILTER_USE_KEY
		);
	}
}
if (!function_exists('trans_choice_fallback')) {
    function trans_choice_fallback($key, $fallback = null, $number = 1, array $replace = [], $locale = null)
    {
        $translation = trans_choice($key, $number, $replace, $locale);
        
        // Kiểm tra xem translation có giống với key không (nghĩa là không tìm thấy)
        if ($translation === $key && $fallback !== null) {
            return $fallback;
        }
        
        return $translation;
    }
}

if (!function_exists('convert_number_to_words')) {
	function convert_number_to_words($number) {
		$units = ['', 'mươi', 'trăm', 'nghìn', 'triệu', 'tỷ', 'nghìn tỷ', 'triệu tỷ', 'tỷ tỷ'];
		$numberWords = ['', 'một', 'hai', 'ba', 'bốn', 'năm', 'sáu', 'bảy', 'tám', 'chín'];
		
		if ($number == 0) {
				return 'không';
		}
	
		$isNegative = $number < 0;
		$number = abs($number);
		$result = '';
	
		$i = 0; // Vị trí hàng đơn vị
		while ($number > 0) {
				$subNumber = $number % 1000; // Lấy 3 chữ số cuối
				if ($subNumber > 0) {
						$result = readThreeDigits($subNumber, $numberWords, $units[$i]) . ' ' . $result;
				}
				$number = floor($number / 1000); // Bỏ 3 chữ số cuối
				$i++;
		}
	
		$result = trim($result);
		if ($isNegative) {
				$result = 'âm ' . $result;
		}
	
		return $result;
	}
}



