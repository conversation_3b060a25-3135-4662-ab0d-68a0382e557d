<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction;

use Exception;
use App\Exceptions\BusinessException;

/**
 * Class này nhằm đảm bảo khi MC tạo yêu cầu đổi thông tin thì không bị lỗi
 */
class KiemTraDaDuHoSoHayChuaSubAction
{
	public $groupKey = [
		'CHANGE_BANK_ACCOUN_INFO' => [
			'bank', // thông tin bank (id + name)
			'branch', // chi nhánh
			'accountNo', // stk
			'holderName', // tên ng thụ hưởng,
			'bankCity', // thành phố,
			'bankAccType' // loại
		],
		'CHANGE_REPRESENT_INFO' => [
			'passport', // cccd người đại diện
			'authoriserContactNumber', // sdt người đại diện
			'authoriserEmail' // email người đại diện
		],

		// key khac
	];


	public function run(string $groupCode, string $groupName='', $profiles = [])
	{
		if (isset($this->groupKey[$groupCode])) {
			// Tạo mảng chứa các profileKey đã có trong hồ sơ
			$existingKeys = array_column($profiles, 'profileKey');

			// Lọc các key còn thiếu
			$missingKeys = array_diff($this->groupKey[$groupCode], $existingKeys);

			// Nếu không thiếu key nào, trả về true
			if (empty($missingKeys)) {
				return true;
			}

			$msg = implode(',', $missingKeys);
			$msg = sprintf('Lỗi: Nhóm thông tin `%s` còn thiếu các trường `%s`', $groupName, $msg);
			throw new BusinessException($msg);
		}
	}
} // End class