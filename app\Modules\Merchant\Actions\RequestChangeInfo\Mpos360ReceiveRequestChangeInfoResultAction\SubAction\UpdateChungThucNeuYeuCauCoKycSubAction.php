<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360ReceiveRequestChangeInfoResultAction\SubAction;

use Illuminate\Support\Facades\DB;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360ChungThuc;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;

class UpdateChungThucNeuYeuCauCoKycSubAction
{
	/**
	 * Chỉ gọi vào đây khi MNP Duyệt yêu cầu
	 * Nếu là đổi CCCD hoặc người đại diện mới thì đánh dấu bản ghi chứng thực CCCD theo cái số mới đó
	 * Việc cần làm:
	 * 	1-Lấy QtsRequestId trong yêu cầu cập nhật ngược vào bảng chứng thực
	 * 	2-Đ<PERSON>a bản ghi chứng thực về trạng thái 2 (Chờ chứng thực)
	 */
	public function run(Mpos360MerchantRequest $mpos360McRequest)
	{
		$listChungThuc = Mpos360ChungThuc::query()->where([
			'merchant_id' => $mpos360McRequest->merchant_id
		])
		->get()
		->keyBy('key_code');

		$dataRequest = json_decode($mpos360McRequest->data_request, true);

		if ($mpos360McRequest->isDoiNguoiDaiDien() || $mpos360McRequest->isYeuCauDoiCccdMoi()) {

			$representPassport = collect($dataRequest[0]['request_vefify'])->first(function ($it) {
				return $it['field'] == 'representPassport' || $it['field'] == 'passport';
			});

			if (!empty($representPassport['value'])) {
				$qtsRequestId = $representPassport['value'];
				
				$recordChungThucCCCD = $listChungThuc->get('CCCD');
				$r = $recordChungThucCCCD
					->where(['merchant_id' => $mpos360McRequest->merchant_id, 'key_code' => 'CCCD'])
					->update([
						'status' => Mpos360Enum::MPOS360_CHUNG_THUC_STT_DANG_XAC_NHAN,
						'qts_request_id' => $qtsRequestId,
						'is_replace_new_represention' => 1,
					]);
					
				if (!$r) {
					mylog([
						'Lỗi' => 'Lỗi update chứng thực CCCD',
						'MerchantId' => $mpos360McRequest->merchant_id
					]);
				}
			}

			// Email nguoi dai dien moi
			$representEmail = $dataRequest[0]['profiles']['representEmail'] ?? '';
			if (!empty($representEmail)) {
				$recordChungThucEmail = $listChungThuc->get('EMAIL');

				$recordChungThucEmail->time_confirmed = now()->timestamp;
				$recordChungThucEmail->is_replace_new_represention = 1;
				$recordChungThucEmail->time_updated =  now()->timestamp;
				
				if ($recordChungThucEmail->value_confirmed != $representEmail) {
					$recordChungThucEmail->status = Mpos360Enum::MPOS360_CHUNG_THUC_STT_DANG_XAC_NHAN;
				}

				$recordChungThucEmail->value_confirmed = $representEmail;
				$r = $recordChungThucEmail->save();

				if (!$r) {
					mylog([
						'Lỗi' => 'Lỗi update chứng thực EMAIL',
						'MerchantId' => $mpos360McRequest->merchant_id
					]);
				}
			}

			// Mobile nguoi dai dien moi
			$representMobile = $dataRequest[0]['profiles']['representMobile'] ?? '';
			if (!empty($representMobile)) {
				$recordChungThucMobile = $listChungThuc->get('MOBILE');
				
				$recordChungThucMobile->time_confirmed = now()->timestamp;
				$recordChungThucMobile->is_replace_new_represention = 1;
				$recordChungThucMobile->time_updated =  now()->timestamp;
				
				if ($recordChungThucMobile->value_confirmed != $representMobile) {
					$recordChungThucMobile->status = Mpos360Enum::MPOS360_CHUNG_THUC_STT_DANG_XAC_NHAN;
				}

				$recordChungThucMobile->value_confirmed = $representMobile;
				$r = $recordChungThucMobile->save();

				if (!$r) {
					mylog([
						'Lỗi' => 'Lỗi update chứng thực MOBILE',
						'MerchantId' => $mpos360McRequest->merchant_id
					]);
				}
			}
		}

		if ($mpos360McRequest->isDoiThongTinLienHe()) {
			// For email
			$representEmail = collect($dataRequest[0]['request_vefify'])->first(function ($it) {
				return $it['field'] == 'representEmail';
			});
			
			if (!empty($representEmail['value'])) {
				$updateEmailChungThuc = Mpos360ChungThuc::query()->where([
					'merchant_id' => $mpos360McRequest->merchant_id,
					'key_code' => 'EMAIL'
				])
				->update([
					'status' => Mpos360Enum::MPOS360_CHUNG_THUC_STT_DANG_XAC_NHAN,
					'is_replace_new_represention' => 1,
					'value_confirmed' => $representEmail['value']
				]);

				if (!$updateEmailChungThuc) {
					mylog([
						'Lỗi' => 'Lỗi update chứng thực Email',
						'MerchantId' => $mpos360McRequest->merchant_id,
						'EmailValueChungThucMoi' => $representEmail['value']
					]);
				}
			}

			// For mobile
			$representMobile = collect($dataRequest[0]['request_vefify'])->first(function ($it) {
				return $it['field'] == 'representMobile';
			});

			if (!empty($representMobile['value'])) {
				$updateMobileChungThuc = Mpos360ChungThuc::query()->where([
					'merchant_id' => $mpos360McRequest->merchant_id,
					'key_code' => 'MOBILE'
				])
				->update([
					'status' => Mpos360Enum::MPOS360_CHUNG_THUC_STT_DANG_XAC_NHAN,
					'is_replace_new_represention' => 1,
					'value_confirmed' => $representMobile['value']
				]);

				if (!$updateMobileChungThuc) {
					mylog([
						'Lỗi' => 'Lỗi update chứng thực Mobile',
						'MerchantId' => $mpos360McRequest->merchant_id,
						'MobileValueChungThucMoi' => $representMobile['value']
					]);
				}
			} // End if check thong tin lien he
		}

		return;
	}
} // End class