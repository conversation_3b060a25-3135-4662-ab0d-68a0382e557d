<?php

namespace App\Modules\Merchant\Actions\Device\Mpos360DeviceUpdateTypeReceiverTingTingAction;

use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\TingTingEnum;
use App\Modules\Merchant\Model\Device;
use App\Modules\Merchant\Requests\Device\Mpos360DeviceUpdateTypeReceiverTingTingRequest;
use Exception;

class Mpos360DeviceUpdateTypeReceiverTingTingAction
{
	public function run(Mpos360DeviceUpdateTypeReceiverTingTingRequest $request) {
		$deviceSession = $request->getCurrentDeviceSession();
		$device = Device::query()->find($deviceSession->device_id);

		if (!$device) {
			throw new BusinessException('Lỗi không tìm thấy thông tin thiết bị');
		};

		$typeReceiverTingTing = $request->json('data.type_receiver_tingting');
		$typeReceiverTingTingAsArray = json_decode($typeReceiverTingTing, true);
		
		$typeNhanHopLe = [
			TingTingEnum::NHAN_TINGTING_QUA_TINGBOX,
			TingTingEnum::NHAN_TINGTING_QUA_MPOS360
		];

		foreach ($typeReceiverTingTingAsArray as $it) {
			if (!in_array($it, $typeNhanHopLe)) {
				throw new BusinessException('Lỗi sai phương thức nhận thông báo tingbox');
			}
		}

		$device->type_receiver_tingting = $request->json('data.type_receiver_tingting');
		$device->time_updated = now()->timestamp;
		$r = $device->save();

		if (!$r) {
			throw new BusinessException('Lỗi không lưu được tùy chọn nhận thông báo tingbox');
		}

		return [ 
			'status' => 'SUCCESS',
			'msg' => 'Đã cập nhật thành công phát thông báo giao dịch trên ứng dụng ' . __('setting.appName'),
			'deviceId' => $device->id
		];
	}
}
