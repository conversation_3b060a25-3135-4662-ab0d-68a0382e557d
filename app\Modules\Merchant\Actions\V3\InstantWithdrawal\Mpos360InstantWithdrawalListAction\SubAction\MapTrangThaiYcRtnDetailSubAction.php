<?php 
namespace App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalListAction\SubAction;

class MapTrangThaiYcRtnDetailSubAction {
	public function run($rc) {
		// Da thanh toan
		if ($rc['status'] == 'APPROVED' && $rc['withdrawStatus'] == 'HAS_BALANCE') {
			return [
				'text' => __('rtn.status_rtn_detail.Đã thanh toán'),
				'text_color' => '#ffffff',
				'bg_color' => '#73ae4a'
			];
		}

		if ($rc['status'] == 'APPROVED' && $rc['withdrawStatus'] == 'DENIED') {
			return [
				'text' => __('rtn.status_rtn_detail.Bị từ chối'),
				'text_color' => '#ffffff',
				'bg_color' => '#da2128'
			];
		}

		if ($rc['status'] == 'APPROVED') {
			return [
				'text' => __('rtn.status_rtn_detail.Đã duyệt'),
				'text_color' => '#ffffff',
				'bg_color' => '#018bf4'
			];
		}

		if ($rc['status'] == 'NEW') {
			return [
				'text' => 'Mới tạo',
				'text_color' => '#ffffff',
				'bg_color' => '#cccccc'
			];
		}

		$maptt = app(MapTrangThaiYcRtnSubAction::class)->run();

		return [
			'text' => $maptt[$rc['status']]['label'] ?? $rc['status'] ?? '',
			'text_color' => '#ffffff',
			'bg_color' => $maptt[$rc['status']]['bg_color'] ?? $rc['bg_color'] ?? '',
		];
	}
}