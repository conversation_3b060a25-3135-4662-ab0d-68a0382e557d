<?php

namespace App\Modules\Merchant\Requests\ChungThuc;

use App\Modules\Merchant\Requests\MerchantRequest;
use Illuminate\Validation\Rule;

class Mpos360ChungThucCheckRequest extends MerchantRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data.code' => ['required', 'string', 'max:50'],
      'data.choice' => [
				'required', 
				'string', 
				Rule::in([
					'DOI_NGUOI_DAI_DIEN_MOI',
					'DOI_THONG_TIN_LIEN_HE',
					'DOI_CCCD_MOI',
					''
				])
			],
    ];
  }
}
