<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoResendOtpAction;

use Exception;
use App\Lib\OtpHelper;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360CodeOtp;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\SendSmsOtpMNPSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\SendMailOtpMNPSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\GetMNPAccessTokenSubAction;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoResendOtpRequest;
use App\Modules\Merchant\Actions\ChungThuc\Mpos360ChungThucGetOtpAction\SubAction\SendEmailWithTemplateSubAction;

class Mpos360RequestChangeInfoResendOtpAction
{
	public function run(Mpos360RequestChangeInfoResendOtpRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();

		$otpId = $request->json('data.otp_id');
		$mpos360CodeOtp = Mpos360CodeOtp::query()->find($otpId);

		if (!$mpos360CodeOtp) {
			throw new BusinessException('Lỗi: không tìm thấy bản ghi otp');
		}

		if ($mpos360CodeOtp->isFinalStatus()) {
			throw new BusinessException('Otp hiện tại đã được sử dụng, từ chối cấp otp mới');
		}

		if ($deviceSession->user_id != $mpos360CodeOtp->user_id) {
			throw new BusinessException('Lỗi: không đúng thông tin user otp');
		}

		$otpGenerate = false;

		$otpGenerateIsExist = false;

		while (!$otpGenerate) {
			$otpGenerate = generateRandomNumber();

			$otpGenerateIsExist = Mpos360CodeOtp::query()
				->where('otp', $otpGenerate)
				->where('time_out', '>=', now()->timestamp)
				->first();

			if ($otpGenerateIsExist) {
				$otpGenerate = false;
			}
		}

		$mpos360CodeOtp->otp = $otpGenerate;
		$mpos360CodeOtp->time_out = now()->addSeconds(OtpHelper::getSoGiayCountdown())->timestamp;
		$mpos360CodeOtp->time_updated = now()->timestamp;
		$mpos360CodeOtp->status = Mpos360Enum::MPOS360_OTP_CHUA_SU_DUNG;
		$r = $mpos360CodeOtp->save();

		if (!$r) {
			throw new BusinessException('Lỗi: không gen được otp');
		}

		$deviceSessionWithToken = app(GetMNPAccessTokenSubAction::class)->run($deviceSession);

		if ($mpos360CodeOtp->service_code == 'SMS') {
			// gui otp sms
			$sendOtpResult = app(SendSmsOtpMNPSubAction::class)->run($mpos360CodeOtp, $deviceSessionWithToken);
			
			return [
				'otp_id' => $mpos360CodeOtp->id,
				'msg' => 'Đã gửi OTP đến SĐT của bạn. Vui lòng kiểm tra trong hộp thư đến..',
				'countdown_time_get_new_otp' => OtpHelper::getSoGiayCountdown()
			];
		}

		if ($mpos360CodeOtp->service_code == 'EMAIL') {
			$sendOtpResult = app(SendEmailWithTemplateSubAction::class)->run(
				$mpos360CodeOtp,
				$deviceSessionWithToken,
				sprintf('[%s] - Cấp lại OTP thay đổi email người đại diện từ ứng dụng %s', $mpos360CodeOtp->otp, __('setting.appName')),
				sprintf('Thay đổi email người đại diện của bạn'),
				'ThayDoiEmailNguoiDaiDien'
			);
			
			return [
				'otp_id' => $mpos360CodeOtp->id,
				'msg' => 'Đã gửi OTP đến Email của bạn. Vui lòng kiểm tra trong hộp thư đến, quảng cáo, spam',
				'countdown_time_get_new_otp' => OtpHelper::getSoGiayCountdown()
			];
		}

		throw new BusinessException('Unknow otp error');
	} // End method
} // End class
