<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestCronPushRecordAction\SubAction;

use Exception;
use App\Lib\partner\MNP;
use App\Exceptions\BusinessException;
use Illuminate\Support\Facades\Cache;

class GetMnpTokenByMerchantIdSubAction
{
	public MNP $mnp;

	public function __construct(MNP $mnp)
	{
		$this->mnp = $mnp;
	}

	public function run(string $merchantId)
	{
		$cacheKey = sprintf('mnp_merchant_token_%s', $merchantId);
		$cacheTime = 8*60*60;

		$cacheData = Cache::remember($cacheKey, $cacheTime, function () use ($merchantId) {
			$getToken = $this->mnp->getToken($merchantId);
			mylog(['getToken' => $getToken]);
			if (empty($getToken['status'])) {
				throw new BusinessException('Lỗi không lấy được thông tin MC MNP');
			}

			return $getToken['data'];
		});

		return $cacheData;
	}
} // End class