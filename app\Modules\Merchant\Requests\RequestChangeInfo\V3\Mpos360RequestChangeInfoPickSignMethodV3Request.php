<?php

namespace App\Modules\Merchant\Requests\RequestChangeInfo\V3;

use App\Modules\Merchant\Requests\MerchantRequest;
use Illuminate\Validation\Rule;

class Mpos360RequestChangeInfoPickSignMethodV3Request extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.id' => ['required', 'numeric', 'integer', 'min:1'],
			
			'data.sign_method' => ['required', 'array'],
			'data.sign_method.mpos360_sign_code' => [
				'required', 
				'string',
				Rule::in([
					'KY_DIEN_TU_MEGADOC', 
					'KY_VE_TAY',
					'KY_GIAY',
					'SALE_HO_TRO',
				])
			],
			'data.sign_method.code' => [
				'required', 
				Rule::in([
					'SALE_SUPPORT',
					'PAPER_CONTRACT',
					'E_CONTRACT'
				])
			],
			'data.sign_method.name' => ['required', 'string'],
			'data.sign_method.signature_url' => ['present', 'string'],
		];
	}
} // End class
