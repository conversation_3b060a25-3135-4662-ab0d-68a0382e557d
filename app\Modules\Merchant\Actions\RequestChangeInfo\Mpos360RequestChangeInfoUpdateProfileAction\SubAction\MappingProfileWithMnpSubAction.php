<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\SubAction;

use Exception;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoGetConfigAction\Mpos360RequestChangeInfoGetConfigAction;

class MappingProfileWithMnpSubAction
{
	public array $mnpCache;

	public function __construct()
	{
		$this->mnpCache = app(Mpos360RequestChangeInfoGetConfigAction::class)->run();
	}

	public function run(Request $request)
	{
		$mnpGroupCode = $request->json('data.request_change_info.code');

		$profileAsCollecton = collect($request->json('data.request_change_info.profiles'));

		$paramOfMnp = [];

		// Thong tin ngan hang
		if ($mnpGroupCode == 'CHANGE_BANK_ACCOUN_INFO') {
			// bankProfile
			$bankProfile = $profileAsCollecton->where('profileKey', 'bank')->first();
			if (empty($bankProfile['value'])) {
				throw new BusinessException('Lỗi thiếu thông tin bank');
			}
			$bank = $this->getValueFromComboBox($bankProfile);

			// bankCity
			$bankCityProfile = $profileAsCollecton->where('profileKey', 'bankCity')->first();
			if (empty($bankCityProfile['value'])) {
				throw new BusinessException('Lỗi: Thiếu thông tin bankCity');
			}
			$bankCity = $this->getValueFromComboBox($bankCityProfile);

			// holderName
			$holderNameProfile = $profileAsCollecton->where('profileKey', 'holderName')->first();
			if (empty($holderNameProfile['value'])) {
				throw new BusinessException('Lỗi thiếu thông tin người thụ hưởng');
			}

			// accountNo
			$accountNoProfile = $profileAsCollecton->where('profileKey', 'accountNo')->first();
			if (empty($accountNoProfile['value'])) {
				throw new BusinessException('Lỗi thiếu thông tin stk');
			}

			// branch
			$branchProfile = $profileAsCollecton->where('profileKey', 'branch')->first();
			if (empty($branchProfile['value'])) {
				throw new BusinessException('Lỗi thiếu thông tin chi nhánh');
			}

			// bankAccType
			$bankAccTypeProfile = $profileAsCollecton->where('profileKey', 'bankAccType')->first();
			if (empty($bankAccTypeProfile['value'])) {
				throw new BusinessException('Lỗi thiếu thông tin bank acc type');
			}

			$paramOfMnp = [
				'type' => $mnpGroupCode,
				'choice' => $request->json('data.choice', ''),
				'profiles' => [
					'bankId' => $bank['bankId'],
					'bankName' => $bank['bankName'],
					
					'holderName' => Str::of($holderNameProfile['value'])->slug(' ')->upper()->trim()->__toString(),
					'accountNo' => $accountNoProfile['value'],
					'branch' => $branchProfile['value'],

					'bankCityCode' => $bankCity['bankCityCode'],
					'bankCityName' => $bankCity['bankCityName'],

					'bankAccType' => $bankAccTypeProfile['value'],
					
					'positionAuthBank' => '', // optional - Chức vụ của người ủy quyền tại ngân hàng.
					'bankMutualRelation' => '' // optional - Mối quan hệ giữa chủ tài khoản và người có thẩm quyền tại ngân hàng.
				],
			];
		}

		/**
		 * Update 09.09.2024:
		 * -Nếu chỉ đổi thông tin của người cũ (thông tin liên hệ hoặc cập nhật cccd mới)
		 * thì chỉ cần require 1 trong 4 thông tin (tên ng đại diện, sdt, email, cccd)
		 * 
		 * -Nếu đổi hẳn luôn sang người đại diện mới
		 * thì đọc từ qts ra lấy full thông tin và tống sang bên mnp;
		 */
		if ($mnpGroupCode == 'CHANGE_REPRESENT_INFO') {
			$typeChangeRepresent = 'CHANGE_NEW_REPRESENT';

			if ($request->isDoiThongTinLienHe() || $request->isDoiCccdMoi()) {
				$typeChangeRepresent = 'CHANGE_CURRENT_REPRESENT_INFO';
			}
		
			$paramOfMnp = [
				'type' => $mnpGroupCode,
				'choice' => $request->json('data.choice', ''),
				'profiles' => [
					// Tên người đại diện mới.
					'representName' => $profileAsCollecton->where('profileKey', 'customerName')->first()['value'],

					// Danh sách các loại thay đổi thông tin đại diện.
					'typeChangeRepresent' => $typeChangeRepresent,

					// Hộ chiếu của người đại diện mới.
					'representPassport' => $profileAsCollecton->where('profileKey', 'passport')->first()['value'],
					
					// Nơi cấp hộ chiếu của người đại diện mới.
					'representIssuePlace' => $profileAsCollecton->where('profileKey', 'placeOfIssue')->first()['value'],
					
					// Email của người đại diện mới.
					'representEmail' => $profileAsCollecton->where('profileKey', 'authoriserEmail')->first()['value'],
					
					// Số điện thoại di động của người đại diện mới.
					'representMobile' => $profileAsCollecton->where('profileKey', 'authoriserContactNumber')->first()['value'],
					
					// Ngày sinh của người đại diện mới (dưới dạng timestamp).
					'representBirthday' => Carbon::parse('1990-01-01')->timestamp,

					// Địa chỉ hiện tại của người đại diện mới.
					'representCurrentAddress' => $profileAsCollecton->where('profileKey', 'authoriserAddress1')->first()['value'],

					// Địa chỉ thường trú của người đại diện mới.
					'representPermanentAddress' => $profileAsCollecton->where('profileKey', 'authoriserAddressPresent')->first()['value'],

					// Chức vụ của người đại diện mới.
					'representPosition' => '',

					// Mối quan hệ giữa người đại diện và người ủy quyền.
					'representMutualRelation' => '',

					// optional - Số giấy ủy quyền của người đại diện.
					// 'representAuthContractNumber' => '',

					// optional - URL của ảnh hộ chiếu mặt trước của người đại diện mới.
					'passportRepresentFrontUrl' => '',

					// optional - URL của ảnh hộ chiếu mặt sau của người đại diện mới.
					'passportRepresentBackUrl' => '',
				],
			];
		}

		return $paramOfMnp;
	}

	public function getValueFromComboBox(array $profile)
	{
		$returnData = [];

		if ($profile['profileKey'] == 'bank') {
			$bankValue = $profile['value'];
			$returnData['bankId'] = $bankValue;
			mylog(['BankId Value Code' => $bankValue]);

			if (empty($this->mnpCache['data']['banks'][$bankValue])) {
				throw new BusinessException('Giá trị bank bị sai định dạng');
			}

			$returnData['bankName'] = $this->mnpCache['data']['banks'][$bankValue];
		}

		if ($profile['profileKey'] == 'bankCity') {
			$bankCityCodeValue = $profile['value'];

			$returnData['bankCityCode'] = $bankCityCodeValue;
			mylog(['BankCity Value Code' => $bankCityCodeValue]);

			if (empty($this->mnpCache['data']['cities'][$bankCityCodeValue])) {
				throw new BusinessException('Giá trị bankCityCode bị sai định dạng');
			}

			$returnData['bankCityName'] = $this->mnpCache['data']['cities'][$bankCityCodeValue];
		}

		return $returnData;
	}
} // End class
