<?php

namespace App\Modules\Merchant\Requests\Notification;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360GetGroupWithUnreadItemsRequest extends MerchantRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data.email' => ['required', 'string', 'max:255'],
      'data.groupCode' => ['present', 'array'],
      'data.groupCode.*' => ['string'],
    ];
  }
}
