<?php

namespace App\Modules\Merchant\Actions\Device\Mpos360DeviceGetTypeReceiverTingTingAction;

use App\Modules\Merchant\Model\Device;
use App\Modules\Merchant\Enums\TingTingEnum;
use App\Modules\Merchant\Requests\Device\Mpos360DeviceGetTypeReceiverTingTingRequest;

class Mpos360DeviceGetTypeReceiverTingTingAction
{
	public function run(Mpos360DeviceGetTypeReceiverTingTingRequest $request) {
		$deviceSession = $request->getCurrentDeviceSession();
		$device = Device::query()->find($deviceSession->device_id);
		$countTingTingReceiveChannel = json_decode($device->type_receiver_tingting, true);

		$returnData = [
			'titleWording' => 'Tùy chỉnh thông báo giao dịch thành công bằng loa TingBox hoặc ứng dụng ' . __('setting.appName'),
			'optionsData' => [
				[
					'type_receiver_tingting' => json_encode([TingTingEnum::NHAN_TINGTING_QUA_TINGBOX]),
					'name' => 'Chỉ phát trên Loa TingBox',
					'desc' => '',
					'checked' => count($countTingTingReceiveChannel) == 1 ? 'YES' : 'NO'
				],

				[
					'type_receiver_tingting' => json_encode([TingTingEnum::NHAN_TINGTING_QUA_TINGBOX, TingTingEnum::NHAN_TINGTING_QUA_MPOS360]),
					'name' => 'Phát trên loa TingBox và ứng dụng ' . __('setting.appName'),
					'desc' => 'Để ứng dụng phát thông báo âm thanh giao dịch, quý khách vui lòng giữ kết nối Internet, tắt chế độ im lặng điện thoại và duy trì đăng nhập trên ' . __('setting.appName'),
					'checked' => count($countTingTingReceiveChannel) == 2 ? 'YES' : 'NO'
				]
			],

			'buyTingBoxWording' => sprintf('Quý Khách muốn mua thiết bị TingBox? %s Đặt mua ', PHP_EOL),
			'otherData' => [
				'buyTingBoxWording' => [
					'clickHere' => [
						'text_color' => '#73ae4a',
						'text' => 'tại đây',
						'webViewUrl' => 'https://tingbox.vn/'
					]
				]
			]
		];

		return $returnData;
	}
}
