<?php

namespace App\Modules\Merchant\Requests\RequestChangeInfo\V3\Traits;

use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoGetConfigAction\Mpos360RequestChangeInfoGetConfigAction;

trait UpdateProfileValidateTrait
{
	public function withValidator($validator)
	{
		$mnpConfig = app(Mpos360RequestChangeInfoGetConfigAction::class)->run();

		$validator->after(function ($validator) use ($mnpConfig) {
			$collectProfiles = collect($this->getMnpProfiles());
			$requestVerify = collect($this->json('data.request_vefify'));
/*--------------------------VALIDATE TKNH-------------------------*/
			if ($this->isChangeThongTinNganHang()) {
				// branch
				$branch = $collectProfiles->where('profileKey', 'branch')->first();
				if ( empty($branch['value']) ) {
					$validator->errors()->add('branch', 'Chi nhánh là bắt buộc');
				}

				// accountNo
				$accountNo = $collectProfiles->where('profileKey', 'accountNo')->first();
				if ( empty($accountNo['value']) ) {
					$validator->errors()->add('holderName', 'STK là bắt buộc');
				}

				// holderName
				$holderName = $collectProfiles->where('profileKey', 'holderName')->first();
				if ( empty($holderName['value']) ) {
					$validator->errors()->add('holderName', 'Người thụ hưởng là bắt buộc');
				}

				// Bo bat ky tu J, W
				// $pattern = "/^[ABCDEFGHIKLMNOPQRSTUVXY]+( [ABCDEFGHIKLMNOPQRSTUVXY]+)*$/";
				// if (!preg_match($pattern, $holderName['value'])) {
				// 	$validator->errors()->add('holderName', 'Người thụ hưởng phải viết in hoa không dấu, không có khoảng trắng thừa & không có ký tự W,F,J,Z');
				// }

				// holderNa
				$holderNameVerify = $requestVerify->where('field', 'holderName')->first();
				if (empty($holderNameVerify['value'])) {
					$validator->errors()->add('holderName', 'Người thụ hưởng verify là bắt buộc');
				}

				if (isset($holderNameVerify['value']) && $holderNameVerify['value'] != $holderName['value']) {
					$validator->errors()->add('holderName', 'Người thụ hưởng verify và người thụ hưởng trong hồ sơ đang không giống nhau');
				}
				

				// bankCity
				$bankCity = $collectProfiles->where('profileKey', 'bankCity')->first();
				if ( empty($bankCity['value']) ) {
					$validator->errors()->add('bankCity', 'Tỉnh/Thành là bắt buộc');
				}

				$listBankCityCode = array_keys($bankCity['other_data']['list']);
				if (!in_array($bankCity['value'], $listBankCityCode)) {
					$validator->errors()->add('bankCity', 'Giá trị Tỉnh/Thành không hợp lệ');
				}

			/*------------------bankAccType--------------*/
				$bankAccType = $collectProfiles->where('profileKey', 'bankAccType')->first();
				if ( empty($bankAccType['value']) ) {
					$validator->errors()->add('bankCity', 'Loại TKNH là bắt buộc');
				}

				$listBankAccountType = ['MC_COMPANY', 'MC_INDIVIDUAL', 'MC_HOUSEHOLD'];

				if (!in_array($bankAccType['value'], $listBankAccountType)) {
					$validator->errors()->add('bankAccType', 'Loại TKNH không hợp lệ');
				}

				// Nếu là đổi từ (HKD, Doanh nghiệp) -> Cá nhân thì VALUE phải là: MC_INDIVIDUAL
				if ($this->isDoiSTKCuaCaNhanDuocHKDUyQuyen() || $this->isDoiSTKCuaCaNhanDuocDoanhNghiepUyQuyen()) {
					if ($bankAccType['value'] != 'MC_INDIVIDUAL') {
						$validator->errors()->add('bankAccType', 'Loại TKNH phải là Cá nhân');
					}
				}

				// Nếu là đổi từ Doanh nghiệp => Doanh nghiệp. VALUE phải là MC_COMPANY
				if ($this->isDoiSTKKhacCuaDoanhNghiep()) {
					if ($bankAccType['value'] != 'MC_COMPANY') {
						$validator->errors()->add('bankAccType', 'Loại TKNH phải là Công ty');
					}
				}
			/*------------------End bankAccType--------------*/
				// validate bankId (bankId này là mã của mnp)
				$bankId = $requestVerify->where('field', 'bankId')->first();
				if ( empty($bankId['value']) ) {
					$validator->errors()->add('bankId', 'Verify ID NH là bắt buộc');
				}

				// Check trùng khớp bankId và bankName
				if ( !empty($bankId['value']) ) {
					// bankId ở request_vefify phải giống với "bank" ở profile
					$bankIdFromProfile = $collectProfiles->where('profileKey', 'bank')->first();
					if (empty($bankIdFromProfile['value']) || $bankIdFromProfile['value'] != $bankId['value']) {
						$validator->errors()->add('bankId', 'bank trong hồ sơ và bankId verify không trùng khớp');
					}
					
					$bankMnpNameString = data_get($mnpConfig, 'data.banks.' . $bankId['value']);

					if ( empty($bankMnpNameString) ) {
						$validator->errors()->add('bankName', 'bankId không hợp lệ');
					}

					// validate bankName
					$bankName = $requestVerify->where('field', 'bankName')->first();
					if ( empty($bankName['value']) ) {
						$validator->errors()->add('bankName', 'Verify Tên NH là bắt buộc');
					}

					if (md5($bankMnpNameString) != md5($bankName['value'])) {
						$validator->errors()->add('bankName', 'Tên NH không khớp với mã bank');
					}
				}
				

				// validate accountNo
				$accountNo = $requestVerify->where('field', 'accountNo')->first();
				if ( empty($accountNo['value']) ) {
					$validator->errors()->add('accountNo', 'Verify STK là bắt buộc');
				}

				if ( !empty($accountNo['value']) ) {
					$accountNoFromProfile = $collectProfiles->where('profileKey', 'accountNo')->first();
					if (empty($accountNoFromProfile['value']) || $accountNoFromProfile['value'] != $accountNo['value']) {
						$validator->errors()->add('bankId', 'STK trong profile đang khác với STK trong verify');
					}
				}
			} // End TKNH

/*--------------------------VALIDATE CCCD-------------------------*/
			if ($this->isDoiCccdMoi()) {
				$passport = $collectProfiles->where('profileKey', 'passport')->first();
				if ( empty($passport['value']) ) {
					$validator->errors()->add('passport', 'Số CMND/CCCD là bắt buộc');
				}
			}
/*--------------------------VALIDATE NGƯỜI ĐẠI DIỆN MỚI-------------------------*/
			if ($this->isDoiNguoiDaiDienMoi()) {
				$passport = $collectProfiles->where('profileKey', 'passport')->first();
				if ( empty($passport['value']) ) {
					$validator->errors()->add('passport', 'Số CMND/CCCD là bắt buộc');
				}

				$authoriserContactNumber = $collectProfiles->where('profileKey', 'authoriserContactNumber')->first();
				if ( empty($authoriserContactNumber['value']) ) {
					$validator->errors()->add('authoriserContactNumber', 'SĐT người đại diện mới là bắt buộc');
				}

				$authoriserEmail = $collectProfiles->where('profileKey', 'authoriserEmail')->first();
				if ( empty($authoriserEmail['value']) ) {
					$validator->errors()->add('authoriserEmail', 'Email người đại diện mới là bắt buộc');
				}
			}
/*-------------------------END VALIDATE TKNH-------------------------*/
		
		}); // End after validate
	} // End method
} // End class