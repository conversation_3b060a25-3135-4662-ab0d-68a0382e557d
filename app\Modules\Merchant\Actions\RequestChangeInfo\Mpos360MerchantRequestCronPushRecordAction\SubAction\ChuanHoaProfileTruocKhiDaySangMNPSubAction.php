<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestCronPushRecordAction\SubAction;

use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use Illuminate\Support\Arr;

class ChuanHoaProfileTruocKhiDaySangMNPSubAction
{
	public function run($profiles, Mpos360MerchantRequest $mpos360McRequest)
	{
		if (isset($profiles['identificationDocument'])) {
			unset($profiles['identificationDocument']);
		}

		if ($mpos360McRequest->isDoiThongTinLienHe() || $mpos360McRequest->isYeuCauDoiCccdMoi()) {
			$profiles['typeChangeRepresent'] = 'CHANGE_CURRENT_REPRESENT_INFO';
		}

		if ($mpos360McRequest->isYeuCauDoiNguoiDaiDienMoi()) {
			$profiles['typeChangeRepresent'] = 'CHANGE_NEW_REPRESENT';
		}
		
		$dataRequest = json_decode($mpos360McRequest->data_request, true);

		$rawAttachments = $dataRequest[0]['raw_attachments'];
		if (!empty($rawAttachments['id_documents'])) {
			if (!empty($rawAttachments['id_documents']['cccd_2mat_sau_cua_ca_nhan_duoc_uy_quyen'])) {
				$profiles['passportAuthoriserFrontUrl'] = Arr::first($rawAttachments['id_documents']['cccd_2mat_sau_cua_ca_nhan_duoc_uy_quyen']);
				$profiles['passportAuthoriserBackUrl'] = Arr::last($rawAttachments['id_documents']['cccd_2mat_sau_cua_ca_nhan_duoc_uy_quyen']);
			}
		}

		return $profiles;
	}
} // End class
