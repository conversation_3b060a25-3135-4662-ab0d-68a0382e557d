<?php

namespace App\Modules\Merchant\Actions\Home\Mpos360GetHomePageInfoAction\SubAction;

use App\Lib\partner\VA;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Actions\Home\Mpos360GetHomePageInfoAction\Task\GetTop5VATransactionTask;
use App\Modules\Merchant\Actions\Home\Mpos360GetHomePageInfoAction\Task\GetVaQrMposTransactionTask;
use App\Modules\Merchant\Actions\Home\Mpos360GetHomePageInfoAction\Task\MergeVietQrTransactionTask;

class GetThongKeVietQrSubAction
{
	/**
	 * 1-<PERSON><PERSON><PERSON> sang VA để lấy thông tin top 5 giao dịch trong ngày gần nhất từ mới -> cũ
	 * 2-G<PERSON><PERSON> sang mpos để lấy thông tin top 5 giao dịch trong ngày gần nhất từ mới -> cũ
	 * Merge 1 và 2 lại, sau đó sort theo gần nhất mới -> cũ và hiển thị ra
	 */
	public function run(DeviceSession $deviceSession)
	{
		$merchantId = $deviceSession->getMerchantId();
		// $listVaTransaction = app(GetTop5VATransactionTask::class)->run($merchantId);
		// $listVaQrMposTransaciton = app(GetVaQrMposTransactionTask::class)->run($deviceSession);

		// $mergeTransaction = app(MergeVietQrTransactionTask::class)->run($listVaTransaction, $listVaQrMposTransaciton);
		$mergeTransaction = [];

		$listMobileUser = [
			['mobileUser' => '', 'modileUserDisplay' => 'Tất cả tài khoản thanh toán'],
		];

		// if (!empty($deviceSession->getListMobileUser())) {
		// 	foreach ($deviceSession->getListMobileUser() as $mobileUserItem) {
		// 		$listMobileUser[] = [
		// 			'mobileUser' => $mobileUserItem['id'],
		// 			'modileUserDisplay' => $mobileUserItem['username']
		// 		];
		// 	}
		// }

		
		$returnData = [
			'listMobileUser' => $listMobileUser,

			'merchantName' => $deviceSession->getMerchantName(),

			'date' => __('home.Hôm nay'),

			'totalRevenue' => [
				'label' => __('home.Tổng doanh thu'),
				'value' => '200.000.000 đ'
			],

			'transactionStatistic' => [
				[
					'totalTrans' => 120,
					'transName' => __('home.Giao dịch QR'),
					'transRevenue' => '190.000.000 đ',
					'otherData' => [
						'bg_color' => '#73ae4a'
					]
				],

				[
					'totalTrans' => 10,
					'transName' => __('home.Giao dịch thẻ'),
					'transRevenue' => '10.000.000 đ',
					'otherData' => [
						'bg_color' => '#1860fe'
					]
				]
			],

			'transList' => [
				'wording' => [
					'title' => __('home.Giao dịch VietQR gần đây'),
					'description' => __('home.(Đang đồng bộ về Lịch sử giao dịch)')
				],

				'redirectTransBtn' => [
					'title' => __('home.Tất cả'),
					'screen' => 'RecentlyVietQrTransList'
				],

				'transData' => $mergeTransaction
			],

			'displayRules' => [
				'listMobileUser' => 'NO',
				'merchantName' => 'NO',
				'date' => 'NO',
				'totalRevenue' => 'NO',
				'transactionStatistic' => 'NO',
				'transList' => 'NO'
			] 
		];

		return $returnData;
	}
} // End class