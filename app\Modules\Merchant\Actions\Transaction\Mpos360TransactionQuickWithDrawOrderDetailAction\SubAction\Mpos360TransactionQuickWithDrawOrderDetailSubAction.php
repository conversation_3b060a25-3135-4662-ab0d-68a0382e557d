<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransactionQuickWithDrawOrderDetailAction\SubAction;

use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionQuickWithDrawOrderDetailRequest;
use App\Lib\Helper;
use App\Modules\Merchant\Actions\Transaction\subAction\Mpos360TransactionDefineConfigSubAction;

class Mpos360TransactionQuickWithDrawOrderDetailSubAction
{
	public $_type = 'DETAIL_QUICKWITHDRAW';
	public function __convertData($listTransaction)
	{
		$data = $this->__defaultReturn(); // $data['data'] = [];
		if ($listTransaction) {
			if (isset($listTransaction['data']['mc360DetailQuickWithdrawDTO'])) {
				$dataList = $listTransaction['data']['mc360DetailQuickWithdrawDTO'];
				$data['data'][] = $this->__commonInfo($dataList); //0
				$data['data'][] = $this->__customerPayment($dataList); //1
				$data['data'][] = $this->__customerInfo($dataList); //4
				$data['data'][] = $this->__paymentInfo($dataList); //2

			}
		}
		return $data;
	}
	public function run(Mpos360TransactionQuickWithDrawOrderDetailRequest $request)
	{

		$deviceSession = $request->getCurrentDeviceSession();
		$params = [
			'typeTransaction' => $this->_type,
			'merchantFk' => $deviceSession->getMerchantId(),
			'id' => $request->json('data.order_code', ''),
			'tokenLogin' => $deviceSession->getMposToken()
		];
		$listTransaction = app(Mpos360TransactionDefineConfigSubAction::class)->requestTransDetail($params);
		$data = $this->__defaultReturn(); // $data['data'] = [];
		if ($listTransaction) {
			if (isset($listTransaction['data']['mc360DetailQuickWithdrawDTO'])) {
				$dataList = $listTransaction['data']['mc360DetailQuickWithdrawDTO'];
				$data['data'][] = $this->__commonInfo($dataList); //0
				$data['data'][] = $this->__customerPayment($dataList); //1
				$data['data'][] = $this->__customerInfo($dataList); //4
				$data['data'][] = $this->__paymentInfo($dataList); //2

			}
		}
		return $data;
	}


	private function __commonInfo($detail)
	{
		$mapData = (new Mpos360TransactionDefineConfigSubAction())->getQuickWithdrawStatusTrans();
		$statusText = '';
		$statusOther = (object)[];
		if (isset($detail['status'])) {
			foreach ($mapData as $key => $value) {
				if ($value['value'] == $detail['status']) {
					$statusText = $value['label'];
					$statusOther = (object)$value;
					break;
				}
			}
		}
		$detail['transType'] = 'Thông tin chung';
		$data  = [
			'key' => 'common_info',
			'name' => 'Thông tin chung',
			'list' => [],
		];
		$data['list'] = [
			[
				'key' => 'quickWithdrawId',
				'label' => 'Mã yêu cầu',
				'value' => $detail['quickWithdrawId'],
				'extra' => "",
				'other_data' => (object) []
			],

			[
				'key' => 'createdDate',
				'label' => 'TG yêu cầu',
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'createdDate', 'datetime'),
				'extra' => "",
				'other_data' => (object) []
			],
			[
				'key' => 'approveDate',
				'label' => 'Thời gian duyệt',
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'approveDate', 'datetime'),
				'extra' => "",
				'other_data' => (object) []
			],
			[
				'key' => 'status',
				'label' => 'Trạng thái',
				'value' => $statusText,
				'extra' => "",
				'other_data' => $statusOther
			],

		];
		if (isset($detail['deniedReason'])) {
			$data['list'][] = [
				'key' => 'status',
				'label' => 'Lý do từ chối',
				'value' => $detail['deniedReason'],
				'extra' => "",
				'other_data' => (object) ['value' => $detail['deniedReason'], 'label' => '', 'text_color' => '#DA2128', 'bg_color' => '', 'display_type' => ''],
			];
		}
		return $data;
	}
	private function __customerPayment($detail)
	{
		$detail['totalPaymentAmount'] = $detail['amount'] + $detail['fee']  - (int)$detail['discountValue'];
		$data  = [
			'key' => 'baf_transaction',
			'name' => 'Giao dịch NTN',
			'list' => [],
		];
		if($detail['installmentFee']) {
			$data['list'] = [
				[
					'key' => 'amount',
					'label' => 'Số tiền đơn hàng ',
					'extra' => '',
					'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'amount', 'amount'),
					'other_data' => (object) []
				],
	
				[
					'key' => 'discountValue',
					'label' => 'Khuyến mại',
					'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'discountValue', 'amount'),
					'other_data' => (object) [],
				],
	
				[
					'key' => 'transactionFee',
					'label' => 'Phí GD',
					'extra' => '',
					'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'transactionFee', 'amount'),
					'other_data' => (object) []
				],
				[
					'key' => 'installmentFee',
					'label' => 'Phí chuyển đổi trả góp',
					'extra' => '',
					'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'installmentFee', 'amount'),
					'other_data' => (object) []
				],
				[
					'key' => 'quickFee',
					'label' => 'Phí NTN',
					'extra' => '',
					'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'quickFee', 'amount'),
					'other_data' => (object) []
				],
				[
					'key' => 'fee',
					'label' => 'Phí chuyển tiền',
					'extra' => '',
					'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'fee', 'amount'),
					'other_data' => (object) []
				],
				[
					'key' => 'refundAmount',
					'label' => 'Trả lại',
					'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'refundAmount', 'amount'),
					'extra' => "",
					'other_data' => (object) []
				],
	
				[
					'key' => 'receivedAmount',
					'label' => 'Thực nhận',
					'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'receivedAmount', 'amount'),
					'extra' => "",
					'other_data' => (object) []
				],
				[
					'key' => 'receivedAmountString',
					'label' => 'Bằng chữ',
					'value' => $detail['receivedAmountString'],
					'extra' => "",
					'other_data' => (object) [
						'display_type' => $detail['receivedAmountString'] ? 'block' : ''
					]
				]
			];
		}else {
			$data['list'] = [
				[
					'key' => 'amount',
					'label' => 'Số tiền đơn hàng ',
					'extra' => '',
					'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'amount', 'amount'),
					'other_data' => (object) []
				],
	
				[
					'key' => 'discountValue',
					'label' => 'Khuyến mại',
					'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'discountValue', 'amount'),
					'other_data' => (object) [],
				],
	
				[
					'key' => 'transactionFee',
					'label' => 'Phí GD',
					'extra' => '',
					'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'transactionFee', 'amount'),
					'other_data' => (object) []
				],
	
				[
					'key' => 'quickFee',
					'label' => 'Phí NTN',
					'extra' => '',
					'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'quickFee', 'amount'),
					'other_data' => (object) []
				],
				[
					'key' => 'fee',
					'label' => 'Phí chuyển tiền',
					'extra' => '',
					'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'fee', 'amount'),
					'other_data' => (object) []
				],
				[
					'key' => 'refundAmount',
					'label' => 'Trả lại',
					'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'refundAmount', 'amount'),
					'extra' => "",
					'other_data' => (object) []
				],
	
				[
					'key' => 'receivedAmount',
					'label' => 'Thực nhận',
					'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'receivedAmount', 'amount'),
					'extra' => "",
					'other_data' => (object) []
				],
				[
					'key' => 'receivedAmountString',
					'label' => 'Bằng chữ',
					'value' => $detail['receivedAmountString'],
					'extra' => "",
					'other_data' => (object) [
						'display_type' => $detail['receivedAmountString'] ? 'block' : ''
					]
				]
			];
		}
		
		return $data;
	}
	private function __getTransMethodNomarl($value)
	{
		if ($value) {
			return (new Mpos360TransactionDefineConfigSubAction())->getPaymentMethodCode($value);
		}
		return '';
	}
	private function __getTransMethodInstallment($value)
	{
		if ($value) {
			return (new Mpos360TransactionDefineConfigSubAction())->getTransInstallmentMethodCode($value);
		}
		return '';
	}
	private function __paymentInfo($detail)
	{
		// $detail['totalPaymentAmount'] = $detail['amount']  + $detail['quickFee']   - (int)$detail['discountValue'];
		$data  = [
			'key' => 'payment_info',
			'name' => '(*) Giao dịch thanh toán',
			'list' => [],
		];
		$statusText = '';
		$statusOther = (object)[];
		$paymentMethod = '';
		$mapData = (new Mpos360TransactionDefineConfigSubAction())->getStatusTrans();
		if (isset($detail['status'])) {
			foreach ($mapData as $key => $value) {
				if ($value['value'] == $detail['statusTransaction']) {
					$statusText = $value['label'];
					$statusOther = (object)$value;
					break;
				}
			}
		}
		if ($detail['installmentFee'] > 0) {

			$mapData = (new Mpos360TransactionDefineConfigSubAction())->getMethodInstallMent();
			foreach ($mapData as $key1 => $value1) {
				$transMethodNameArr[$value1['value']] = $value1['label'];
			}
			$transaction_method = $this->__getTransMethodInstallment($detail);
			$paymentMethod = isset($transMethodNameArr[$transaction_method]) ? $transMethodNameArr[$transaction_method] : $transaction_method;
		} else {
			// $mapData = (new Mpos360TransactionDefineConfigSubAction())->getStatusTrans();

			// if (isset($detail['status'])) {
			// 	foreach ($mapData as $key => $value) {
			// 		if ($value['value'] == $detail['status']) {
			// 			$statusText = $value['label'];
			// 			$statusOther = (object)$value;
			// 			break;
			// 		}
			// 	}
			// }
			$mapData = (new Mpos360TransactionDefineConfigSubAction())->getPaymentMethod();
			foreach ($mapData as $key1 => $value1) {
				$transMethodNameArr[$value1['value']] = $value1['label'];
			}
			$transaction_method = $this->__getTransMethodNomarl($detail);
			$paymentMethod = isset($transMethodNameArr[$transaction_method]) ? $transMethodNameArr[$transaction_method] : $transaction_method;
		}
		$data['list'] = [
			[
				'key' => 'createdDateTransaction',
				'label' => 'Thời gian',
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'createdDateTransaction', 'datetime'),
				'other_data' => (object) [],
			],
			[
				'key' => 'txid',
				'label' => 'Mã giao dịch',
				'value' => $detail['txid'],
				'other_data' => (object) [],
			],
			[
				'key' => 'trans',
				'label' => 'Loại giao dịch',
				'value' => $paymentMethod,
				'other_data' => (object) [],
			],
			[
				'key' => 'amount',
				'label' => 'Số tiền đơn hàng',
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'amount', 'datetime'),
				'other_data' => (object) [],
			],
			[
				'key' => 'totalAmount',
				'label' => 'Tổng tiền thanh toán',
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'totalAmount', 'amount'),
				'other_data' => (object) [],
			],
			[
				'key' => 'statusTransaction',
				'label' => 'Trạng thái giao dịch',
				'value' => $statusText,
				'other_data' => $statusOther,
			]
		];
		if ($detail['description']) {
			$data['list'][] = [
				'key' => 'status',
				'label' => 'Mô tả đơn hàng',
				'value' => $detail['description'],
				'extra' => "",
				'other_data' => (object) ['value' => $detail['description'], 'label' => '', 'text_color' => '#404041', 'bg_color' => '', 'display_type' => 'block'],
			];
		}
		return $data;
	}

	private function __customerInfo($detail)
	{
		$data  = [
			'key' => 'customer',
			'name' => 'Tài khoản nhận tiền',
			'list' => [],
		];
		$data['list'] = [
			[
				'key' => 'holderName',
				'label' => $detail['holderName'],
				'value' => "",
				'extra' => "",
				'other_data' => (object) []
			],

			[
				'key' => 'accountNo',
				'label' =>  $detail['accountNo'],
				'value' =>"",
				'extra' => "",
				'other_data' => (object) []
			],
			[
				'key' => 'bankName',
				'label' => $detail['bankName'] . '-'.$detail['branch'],
				'value' => "",
				'extra' => "",
				'other_data' => (object)[]
			],

		];

		return $data;
	}




	private function __convertTime($time)
	{
		try {
			if ($time) {
				$strtotime = strtotime($time);
				return date('H:i d-m-Y', $strtotime);
			}
			return '';
		} catch (\Throwable $th) {
			return $time;
		}
	}
	private function __convertAmount($amount)
	{
		try {
			return Helper::numberFormat($amount) . ' VND';
		} catch (\Throwable $th) {
			return $amount;
		}
	}
	private function __defaultReturn()
	{
		return [
			'warning' => [],
			'data' => [],
			'other_data' => (object)[],
		];
		// 'warning' => [
		// 	// [
		// 	// 	'label' => '',
		// 	// 	'value' => 'Cần cung cấp chứng từ',
		// 	// 	'other_data' => [
		// 	// 		'text_color' => '#d329a0',
		// 	// 		'bg_color' => '#fbeaf6'
		// 	// 	]
		// 	// ]
		// ],
	}

	private function __getStatusDefault()
	{
		return (new Mpos360TransactionDefineConfigSubAction())->getQuickWithdrawStatusTrans();
	}
	private function __getCardType($value)
	{
		if (isset($value['issuerCode'])) {
			return (new Mpos360TransactionDefineConfigSubAction())->getTypeCard($value['issuerCode']);
		}
		return '';
	}
}
