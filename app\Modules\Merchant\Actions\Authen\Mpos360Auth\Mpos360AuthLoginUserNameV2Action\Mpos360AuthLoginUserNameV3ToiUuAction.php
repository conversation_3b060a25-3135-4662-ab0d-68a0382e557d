<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginUserNameV2Action;

use App\Lib\Encryption;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\DTOs\Authen\Mpos360Auth\LoginMposSuccessDto;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360AuthLoginUserNameRequest;
use App\Modules\Merchant\Actions\Device\GetDeviceIdByOsAndTokenAction\SaveDeviceWithMobileUserAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction\BuildBottomNavAppSubAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction\CreateMpos360UserSubAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction\LoginMerchantViaMposUserNameSubAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginUserNameV2Action\SubAction\BuildDeviceSessionSimulatorSubAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginUserNameV2Action\SubAction\XuLyDieuHuongDangNhapSubAction;

class Mpos360AuthLoginUserNameV3ToiUuAction
{
	public bool $isCoThietBi = false;

	public function run(Mpos360AuthLoginUserNameRequest $request): DeviceSession
	{
		// Đoạn này cần rẽ nhánh là login email hay sđt (hiện tại đang là email, cần rẽ nhánh)
		$loginMpos = app(LoginMerchantViaMposUserNameSubAction::class)->run(
			$request->json('data.value'),
			$request->json('data.password'),
			$request->json('data.os'),
			$request->json('data.fcmToken', '')
		);


		$loginMposSuccessDto = new LoginMposSuccessDto(
			$loginMpos['data']['mobileUserToken'],
			$loginMpos['data']['merchantId'],
			$loginMpos['data']['username'],
			$loginMpos['data']['emailMerchant'],
			$loginMpos['data']['merchantName'] ?? $loginMpos['data']['username'],
		);

		// Thực hiện create user
		$mpos360User = app(CreateMpos360UserSubAction::class)->run($loginMposSuccessDto, $loginMpos);

		// Thực hiện lưu thông tin thiết bị
		$device = app(SaveDeviceWithMobileUserAction::class)->run(
			$request->json('data.os'),
			$request->json('data.deviceToken'),
			$mpos360User->id,
			$loginMpos['data']['mobileUserDTO']
		);

		// Đoạn này call sang MPOS để lấy thông tin
		$deviceSession = app(BuildDeviceSessionSimulatorSubAction::class)->run(
			$mpos360User, 
			$device, 
			$loginMpos
		);

		$deviceSession = app(XuLyDieuHuongDangNhapSubAction::class)->run(
			$deviceSession, 
			$loginMpos, 
			$request->json('data.password')
		);

		$deviceSession->signed = Encryption::buildSign($deviceSession);
		$base64McData = base64_encode($deviceSession->toJson());
		$deviceSession->api_key = $base64McData;

		if (@$deviceSession->relationLoaded('mpos360User')) {
			$deviceSession->unsetRelation('mpos360User');
		}

		$deviceSession->makeHidden(['mpos_token']);

		// Add thêm menu bottom nav
		$deviceSession->bottom_nav_config = app(BuildBottomNavAppSubAction::class)->run();
		$deviceSession->type_receiver_tingting = $device->type_receiver_tingting;
		$deviceSession->merchant_info = $loginMposSuccessDto->toJson();
		$deviceSession->tingting = (object) [];
		$deviceSession->qrDefault = (object) [];

		return $deviceSession;
	}
} // End class
