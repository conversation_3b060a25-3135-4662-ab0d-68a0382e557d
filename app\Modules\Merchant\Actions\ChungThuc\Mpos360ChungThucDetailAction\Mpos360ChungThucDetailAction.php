<?php

namespace App\Modules\Merchant\Actions\ChungThuc\Mpos360ChungThucDetailAction;

use Exception;
use Illuminate\Http\Request;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Mpos360ChungThuc;

class Mpos360ChungThucDetailAction
{

	public function run(Request $request): array
	{
		$id = $request->json('data.id');

		if (empty($id)) {
			throw new BusinessException('id bản ghi là bắt buộc');
		}

		$mpos360ChungThuc = Mpos360ChungThuc::query()->find($id);
		if (!$mpos360ChungThuc) {
			throw new BusinessException('Lỗi không tìm thấy bản ghi chứng thực');
		}

		return $mpos360ChungThuc->only(['id', 'merchant_id', 'qts_request_id', 'value_confirmed']);
	}
} // End class
