<?php

namespace App\Modules\TingBoxVAMC\Actions\MerchantBank\LinkBankAction\SubAction;

use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\LinkBankRequest;

class IsReopenVamcSubAction
{
	public function run(LinkBankRequest $request) {
		$mcShopBank = MerchantShopBank::query()
																	->where('merchant_id', $request->json('data.merchantId'))
																	->where('shop_id', $request->json('data.mobileUserId'))
																	->whereHas('merchantBank', function ($q) use ($request) {
																		return $q->where('bank_code', $request->json('data.bankCode'))
																						 ->where('account_number', $request->json('data.bankAccountNo'));
																	})
																	->first();
		
															
		if ($mcShopBank && $mcShopBank->isDaHuyLienKet()) {
			$mcShopBank->load('merchantBank');
			return $mcShopBank;
		}

		return false;
	}
} // End class
