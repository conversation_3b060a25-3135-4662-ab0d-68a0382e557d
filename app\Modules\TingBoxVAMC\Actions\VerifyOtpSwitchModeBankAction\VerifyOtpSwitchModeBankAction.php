<?php

namespace App\Modules\TingBoxVAMC\Actions\VerifyOtpSwitchModeBankAction;

use App\Lib\Logs;
use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Lib\partner\SoundBox;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Enums\TingTingEnum;
use App\Modules\TingBoxVAMC\Models\PlanEvent;
use App\Modules\Merchant\Enums\OtpResCodeEnum;
use App\Modules\Merchant\Model\Mpos360CodeOtp;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use App\Modules\Merchant\Model\Mpos360Logs\LogRequest;
use App\Modules\TingBoxVAMC\Actions\SetPaymentDefaultAction\SetPaymentDefaultAction;
use App\Modules\TingBoxVAMC\Models\ActionDelays;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\SetPaymentDefaultRequest;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\VerifyOtpSwitchModeBankRequest;

class VerifyOtpSwitchModeBankAction
{
	protected SoundBox $soundBox;
	public int $timeExpiredOtp = 5 * 60;

	public function __construct(SoundBox $soundBox)
	{
		$this->soundBox = $soundBox;
	}

	public function run(VerifyOtpSwitchModeBankRequest $request)
	{
		Logs::writeInfo("VerifyOtpSwitchModeBankAction", $request->json('data'));

		if (Helper::isLocalOrDevEnv() && !empty(env('APP_DEBUG'))) {
			goto CREATE_USER;
		}

		// B1: Xác thực OTP
		$otp = trim($request->json('data.otp'));
		$otpId = $request->json('data.otp_id');

		$mpos360CodeOtp = Mpos360CodeOtp::query()->where([
			'id' => $otpId,
			'otp' => $otp,
			'command_code' => $request->json('data.otpType')
		])->first();

		$msg = 'Mã OTP không chính xác hoặc đã hết thời gian hiệu lực. Xin vui lòng kiểm tra lại mã trong tin nhắn hoặc yêu cầu một mã mới';

		if (!$mpos360CodeOtp) {
			$msg = 'Mã OTP không chính xác';
			throw new BusinessException($msg, OtpResCodeEnum::OTP_ERR_KHONG_TIM_THAY);
		}

		if ($mpos360CodeOtp->isFinalStatus()) {
			throw new BusinessException($msg, OtpResCodeEnum::OTP_ERR_DA_DUOC_SU_DUNG);
		}

		if ($mpos360CodeOtp->isExpiredOtp()) {
			throw new BusinessException($msg, OtpResCodeEnum::OTP_ERR_HET_HAN);
		}

		$mpos360CodeOtp->status = Mpos360Enum::MPOS360_OTP_DA_SU_DUNG;
		$r = $mpos360CodeOtp->save();

		if (!$r) {
			throw new BusinessException('Lỗi không xử lý được bản ghi otp');
		}

		CREATE_USER:
		if ($request->json('data.otpType') == 'CHUYEN_MODE') {
			return $this->__chuyenMode($request);
		}

		if ($request->json('data.otpType') == 'SET_MAC_DINH') {
			return $this->__setMacDinh($request);
		}

		throw new BusinessException('Không xác định trường hợp verify otp');
	}

	private function __setMacDinh($request)
	{
		$merchantShopBank = MerchantShopBank::query()->where('id', $request->json('data.merchantShopBankId'))->first();
		
		if (!$merchantShopBank) {
			throw new BusinessException('Lỗi không tìm được liên kết muốn sét mặc định');
		}

		$inputs = [
			'data' => [
				'username' => $request->json('data.username'),
				'merchantShopBankId' => $request->json('data.merchantShopBankId'),
				'merchantId' => $request->json('data.merchantId'),
				'mobileUserId' => $request->json('data.userMobileId'),
			]
		];
		
		$rq = new SetPaymentDefaultRequest();
		$rq->setJson(new \Symfony\Component\HttpFoundation\ParameterBag((array) $inputs));

		$returnData = app(SetPaymentDefaultAction::class)->run($rq);
		
		return [
			'can' => TingTingEnum::CAN_GO_TO_CHI_TIET_CUA_HANG,
			'merchantId' => $request->json('data.merchantId'),
			'status' => 'SUCCESS'
		];
	}

	private function __chuyenMode($request)
	{
		$shopBankIdMuonChuyenMode = $request->json('data.merchantShopBankId');

		if ($shopBankIdMuonChuyenMode == '-1') {
			$params = [
				"mcId" => $request->json('data.merchantId'),
				"mobileUserName" => $request->json('data.userMobileId'),
				"integratedMethod" => 'VANP',
				"partnerCode" => Helper::getPartnerCode($request->json('data.merchantId')),
				"vaNextPayNumber" => ''
			];
		} else {
			$merchantShopBank = MerchantShopBank::query()->where('id', $request->json('data.merchantShopBankId'))->first();

			if (!$merchantShopBank) {
				throw new BusinessException('Không tìm thấy thông tin ngân hàng.');
			}

			$params = [
				"mcId" => $request->json('data.merchantId'),
				"mobileUserName" => $request->json('data.userMobileId'),
				"integratedMethod" => 'VAMC',
				"partnerCode" => Helper::getPartnerCode($request->json('data.merchantId')),
				"vaNextPayNumber" => $merchantShopBank->partner_request_id,
			];
		}


		$logRequest = LogRequest::query()->forceCreate([
			'merchant_id' => $request->json('data.merchantId'),
			'partner' => 'tingbox',
			'func' => 'changeModeBank',
			'request' => json_encode($params, JSON_UNESCAPED_UNICODE),
			'created_at' => now()->format('Y-m-d H:i:s'),
			'updated_at' => now()->format('Y-m-d H:i:s'),
		]);

		$sendSoundBox = $this->soundBox->changeIntegratedMethod($params);
		Logs::writeInfo('VerifyOtpSwitchModeBankAction-TingBoxResult', $sendSoundBox);

		$logRequest->update(['response' => json_encode($sendSoundBox, JSON_UNESCAPED_UNICODE), 'updated_at' => now()->format('Y-m-d H:i:s')]);

		if (empty($sendSoundBox['result'])) {
			// PlanEvent::query()->forceCreate([
			// 	'merchant_shop_bank_id' => $request->json('data.merchantShopBankId'),
			// 	'merchant_id' => $request->json('data.merchantId'),
			// 	'action' => 'SWITCHMODEBANK',
			// 	'data' => json_encode($params, JSON_UNESCAPED_UNICODE),
			// 	'time_created' => time(),
			// 	'time_updated' => time(),
			// 	'response' => json_encode($sendSoundBox, JSON_UNESCAPED_UNICODE)
			// ]);	
			
			$msg = 'Rất tiếc, không thể chuyển đổi hình thức nhận tiền. Vui lòng thử lại hoặc liên hệ bộ phận chăm sóc Khách hàng theo số Hotline: 1900.63.64.88 để được hỗ trợ';
			throw new BusinessException($msg);
		}else{
			ActionDelays::query()->forceCreate([
				'merchant_id' => $request->json('data.merchantId'),
				'shop_id' => $request->json('data.userMobileId'),
				'action_type' => 'send_otp_switch_mode',
				'scheduled_at' => time(),
				'executed_at' => now()->addSeconds($this->timeExpiredOtp)->timestamp,
			]);
		}

		return [
			'can' => TingTingEnum::CAN_GO_TO_CHI_TIET_CUA_HANG,
			'merchantId' => $request->json('data.merchantId'),
			'status' => 'SUCCESS'
		];
	}
} // End class
