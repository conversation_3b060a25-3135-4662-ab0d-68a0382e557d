<?php

namespace App\Modules\Merchant\Controllers\Transaction;

use App\Lib\Helper;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionHistoryRequest;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionListNormalRequest;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionNormalDetailRequest;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionCountSumByDateRequest;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransactionHistoryAction\Mpos360TransactionHistoryAction;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransactionListNormalAction\Mpos360TransactionListNormalAction;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransactionNormalDetailAction\Mpos360TransactionNormalDetailAction;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransactionCountSumByDateAction\Mpos360TransactionCountSumByDateAction;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransactionCountSumByDateV2Action\Mpos360TransactionCountSumByDateV2Action;

class Mpos360TransactionController extends Controller
{
	public function Mpos360TransactionHistory(Mpos360TransactionHistoryRequest $request)
	{
		try {
			$result = app(Mpos360TransactionHistoryAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360TransactionListNormal(Mpos360TransactionListNormalRequest $request)
	{
		try {
			$result = app(Mpos360TransactionListNormalAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}


	public function Mpos360TransactionNormalDetail(Mpos360TransactionNormalDetailRequest $request)
	{
		try {
			$result = app(Mpos360TransactionNormalDetailAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360TransactionCountSumByDate(Mpos360TransactionCountSumByDateRequest $request)
	{
		try {
			$result = app(Mpos360TransactionCountSumByDateAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360TransactionCountSumByDateV2(Mpos360TransactionCountSumByDateRequest $request)
	{
		try {
			$result = app(Mpos360TransactionCountSumByDateV2Action::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
} // End class
