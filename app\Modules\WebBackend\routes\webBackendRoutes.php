<?php

namespace App\Modules\WebBackend\routes;

use Illuminate\Support\Facades\Route;
use App\Http\Middleware\MakeSureThatRequestIsJsonMiddleware;
use App\Modules\WebBackend\Controllers\Setting\SettingController;
use App\Modules\WebBackend\Controllers\RequestChangeInfo\RequestChangeInfoController;

Route::group([
	'prefix' => 'WebBackend',
	'middleware' => [MakeSureThatRequestIsJsonMiddleware::class]
], function () {
	/**
	 * Setting
	 */
	Route::any('/GetAllSetting', [
		'uses' => SettingController::class . '@GetAllSetting',
		'as' => 'GetAllSetting_WB'
	])->middleware('checkSumForAnyMobile:settingId');

	Route::any('/GetDetailSetting', [
		'uses' => SettingController::class . '@GetDetailSetting',
		'as' => 'GetDetailSetting_WB'
	])->middleware('checkSumForAnyMobile:settingId');

	Route::any('/UpdateSetting', [
		'uses' => SettingController::class . '@UpdateSetting',
		'as' => 'UpdateSetting_WB'
	])->middleware('checkSumForAnyMobile:settingId');

	/**
	 * YC doi thong tin
	 */
	Route::any('/GetAllRequestChangeInfo', [
		'as' => 'GetAllRequestChangeInfo_WB',
		'uses' => RequestChangeInfoController::class . '@GetAllRequestChangeInfo'
	])->middleware('checkSumForAnyMobile:email|merchantId|limit|page');
});
