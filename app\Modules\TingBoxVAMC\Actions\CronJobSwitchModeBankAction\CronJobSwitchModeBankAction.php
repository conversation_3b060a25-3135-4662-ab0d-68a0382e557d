<?php

namespace App\Modules\TingBoxVAMC\Actions\CronJobSwitchModeBankAction;

use App\Lib\partner\SoundBox;
use App\Lib\TelegramAlertWarning;
use App\Modules\TingBoxVAMC\Models\PlanEvent;



class CronJobSwitchModeBankAction
{
	protected SoundBox $soundBox;

	public array $exceptIds = [];
	public int $tries = 1;

	public function __construct(SoundBox $soundBox)
	{
		$this->soundBox = $soundBox;
	}


	public function run()
	{
		for ($i=1; $i<=30; $i++) {
			$this->handle();
		}
	}

	public function handle() {
		$dataResponse = [];

		$event = PlanEvent::query()->where([
			'status' => 1,
			'action' => 'SWITCHMODEBANK'
		]);

		if (!empty($this->exceptIds)) {
			$event = $event->whereNotIn('id', $this->exceptIds);
		}
		
		$event = $event->first();

		if (!$event) {
			return $dataResponse;
		}

		$this->exceptIds[] = $event->id;

		$event->status = 3; // dang xu ly
		$event->save();

		$dataEvent = json_decode($event->data, true);
		if (empty($dataEvent) || !is_array($dataEvent)) {
			$event->status = 5; // that bai
			$event->time_updated = time();
			$event->save();
		}


		while ($this->tries < 3) {
			$sendSoundbox = $this->soundBox->changeIntegratedMethod($dataEvent);

			if (empty($sendSoundBox['result'])) {
				++$this->tries;

				if($this->tries == 3){
					$event->status = 5; // that bai
					$event->time_updated = time();
					$event->response = json_encode($sendSoundbox ?? []);
					$event->save();
					@TelegramAlertWarning::sendMessage("Chuyen mode bank sang Tingbox that bai (eventId:)" . $event->id);
				}
				
				sleep(2);
			}else {
				$event->response = json_encode($sendSoundbox ?? []);
				$event->status = 2;
				$event->save();
				break;
			}
		}

		return $dataResponse;
	}
}
