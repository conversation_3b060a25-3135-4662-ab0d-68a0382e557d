<?php

namespace App\Modules\TingBoxVAMC\Requests\Transaction;

use Illuminate\Foundation\Http\FormRequest;

class Mpos360GetDsDiemBanMuidRequest extends FormRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data' => ['required', 'array'],
			'data.merchantId' => ['required', 'string'],
			'data.username' => ['present', 'string']
		];
	}

	public function messages()
	{
		return [
			'data.merchantId.required' => 'MerchantId là bắt buộc',
			'data.merchantId.string' => 'MerchantId phải là kiểu chuỗi',
		];
	}
} // End class
