<?php

namespace App\Modules\Merchant\Actions\TingBox\Mpos360DetailThietBiTingBoxStep2Action;

use App\Modules\Merchant\Requests\TingBox\Mpos360DetailThietBiTingBoxStep2Request;

class Mpos360DetailThietBiTingBoxStep2Action
{
	public function run(Mpos360DetailThietBiTingBoxStep2Request $request)
	{
		if ($request->isTingBoxVer1()) {
			return [
				'tingBoxDetailUrl' => 'https://mpos.vn'
			];
		}

		if ($request->isTingBoxVer2()) {
			return [
				'tingBoxDetailUrl' => 'https://mpos.vn'
			];
		}
	}
}
