<?php

namespace App\Lib\partner;

use App\Lib\Helper;
use App\Lib\TelegramAlertWarning;
use Illuminate\Support\Facades\Http;

define('MPOS_AWS_URL', env('MPOS_AWS_URL', 'https://pushpayment.nextpay.vn/360/trxQR'));
define('MPOS_AWS_BASEURL', env('MPOS_AWS_BASEURL', 'https://pushpayment.nextpay.vn/'));
define('MPOS_AWS_ACCESSTOKEN', env('MPOS_AWS_ACCESSTOKEN'));

class MposAWS
{
	private array $__logData = [];
	
	public function getListGiaoDichQr($data = [], int $timeout = 10, $path='')
	{
		$startTime = microtime(true);
		try {
			$url = MPOS_AWS_URL . $path;
			$this->__logData['MposAwsUrl'] = $url;

			$params = ['data' => $data];
			$this->__logData['MposAwsParams'] = $params;
			$this->__logData['MposAwsStartTime'] = $startTime;
			
			$r = Http::retry(2, 300)->withoutVerifying()
															->withOptions(['connect_timeout' => 5])
															->timeout(5)
															->post($url, $params);

			$result = $r->json();
			$this->__logData['Result'] = $result;

			if (!empty($result['data']) && $result['error']['code'] == 1000) {
				$response = [
					'data' => $result,
					'status_code_partner' => $result['error']['code']
				];

				return $response;
			}

			return [];
		} catch (\Throwable $th) {
			TelegramAlertWarning::sendMessage(Helper::traceError($th));
			$this->__logData['MposAws_Err'] = Helper::traceError($th);
			return [];
		} finally {
			$endTime = microtime(true);
			$this->__logData['MposAwsTimeEnd'] = $endTime;
			$this->__logData['MposAwsDuration'] = $endTime - $startTime;
			mylog($this->__logData);
		}
	}

	public function chiTietGiaoDichQr($data = [], int $timeout = 5, $path='')
	{
		$startTime = microtime(true);
		try {
			$url = MPOS_AWS_URL . $path;
			$this->__logData['MposAwsUrl'] = $url;
			$this->__logData['MposAwsParams'] = $data;
			$this->__logData['MposAwsTimeStart'] = $startTime;

			$r = Http::retry(2, 300)->withoutVerifying()->withOptions(['connect_timeout' => 5])->timeout(5)->post($url, $data);

			$result = $r->json();

			$this->__logData['Result'] = $result;

			if (!empty($result['mc360DetailTransaction']) && $result['error']['code'] == 1000) {

				$response = [
					'data' => $result,
					'status_code_partner' => $result['error']['code']
				];

				return $response;
			}

			return [];
		} catch (\Throwable $th) {
			TelegramAlertWarning::sendMessage(Helper::traceError($th));
			$this->__logData['MposAws_Err'] = Helper::traceError($th);
		} finally {
			$endTime = microtime(true);
			$this->__logData['MposAwsTimeEnd'] = $endTime;
			$this->__logData['MposAwsDuration'] = $endTime - $startTime;
			mylog($this->__logData);
		}
	}

	public function countSumTransByDate($data = [], int $timeout = 10)
	{
		$startTime = microtime(true);
		try {
			$url = MPOS_AWS_BASEURL . '360/statistic';
			$this->__logData['MposAwsUrl'] = $url;
			$this->__logData['MposAwsParams'] = $data;
			$this->__logData['MposAwsTimeStart'] = $startTime;

			$r = Http::withoutVerifying()->timeout($timeout)->post($url, $data);

			$result = $r->json();

			$this->__logData['Result'] = $result;

			
			if (!empty($result['data']) && $result['error']['code'] == 1000) {
				$response = [
					'data' => $result,
					'status_code_partner' => $result['error']['code']
				];
				return $response;
			}

			$this->__logData['MposAws_Err'] = 'Khong co du lieu';
			return [];
		} catch (\Throwable $th) {
			TelegramAlertWarning::sendMessage(Helper::traceError($th));
			$this->__logData['MposAws_Err'] = Helper::traceError($th);
		} finally {
			$endTime = microtime(true);
			$this->__logData['MposAwsTimeEnd'] = $endTime;
			$this->__logData['MposAwsDuration'] = $endTime - $startTime;
			mylog($this->__logData);
		}
	}

	public function getThongKeLichSuGiaoDich($data = [], int $timeout = 5)
	{
		$startTime = microtime(true);
		try {
			$url = MPOS_AWS_BASEURL . '360/statistic';
			$this->__logData['MposAwsUrl'] = $url;
			$this->__logData['MposAwsParams'] = $data;
			$this->__logData['MposAwsTimeStart'] = $startTime;

			$r = Http::withoutVerifying()->timeout($timeout)->post($url, $data);

			$result = $r->json();

			$this->__logData['Result'] = $result;

			
			if (isset($result['totalAmountTransaction']) && $result['error']['code'] == 1000) {
				$response = [
					'data' => $result,
					'status_code_partner' => $result['error']['code']
				];
				return ['data' => $response];
			}

			$this->__logData['MposAws_Err'] = 'Khong co du lieu';
			return [];
		} catch (\Throwable $th) {
			TelegramAlertWarning::sendMessage(Helper::traceError($th));
			$this->__logData['MposAws_Err'] = Helper::traceError($th);
		} finally {
			$endTime = microtime(true);
			$this->__logData['MposAwsTimeEnd'] = $endTime;
			$this->__logData['MposAwsDuration'] = $endTime - $startTime;
			mylog($this->__logData);
		}
	}
} // End class
