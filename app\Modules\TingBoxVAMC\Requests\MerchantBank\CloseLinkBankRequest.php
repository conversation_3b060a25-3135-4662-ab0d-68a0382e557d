<?php

namespace App\Modules\TingBoxVAMC\Requests\MerchantBank;

use Illuminate\Foundation\Http\FormRequest;

class CloseLinkBankRequest extends FormRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data' => ['required', 'array'],
			'data.username' => ['present', 'string'],
			'data.merchantShopBankId' => ['required', 'numeric', 'min:1'],
			'data.userMobileId' => ['required', 'string'],
			'data.merchantId' => ['required', 'string'],
			'data.merchantShopBankIdAssign' => ['present', 'integer'],
		];
	}

	public function messages()
	{
		return [
			'data.merchantShopBankId.required' => 'Mã MC ShopBank là bắt buộc',
			'data.merchantShopBankId.numeric' => 'Mã MC ShopBank phải là kiểu số',
			'data.merchantShopBankId.min' => 'Mã MC ShopBank phải tối thiểu từ 1',

			'data.userMobileId.required' => 'Mobile User là bắt buộc',
			'data.userMobileId.string' => 'Mobile User phải là kiểu chuỗi',

			'data.merchantId.required' => 'Mã MC là bắt buộc',
			'data.merchantId.string' => 'Mã MC phải là kiểu chuỗi',

			'data.merchantShopBankIdAssign.required' => 'Mã ngân hàng chuyển giao là bắt buộc',
			'data.merchantShopBankIdAssign.integer' => 'Mã ngân hàng chuyển giao là kiểu số',
		];
	}
}
