<?php

namespace App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360GetHomePageInfoDiemBanAction;

use App\Lib\partner\SoundBox;
use App\Modules\Merchant\Model\Setting;
use App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360GetDsDiemBanMuidAction\Mpos360GetDsDiemBanMuidAction;
use App\Modules\TingBoxVAMC\Requests\DiemBan\Mpos360GetHomePageInfoDiemBanRequest;
use App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360GetHomePageInfoDiemBanAction\SubAction\GetBannerDiemBanSubAction;
use App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360GetHomePageInfoDiemBanAction\SubAction\GetDiemBanWithCurrentModeSubAction;
use App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360GetHomePageInfoDiemBanAction\SubAction\GetMenuHomeDiemBanSubAction;

class Mpos360GetHomePageInfoDiemBanAction
{
	public function run(Mpos360GetHomePageInfoDiemBanRequest $request)
	{
		$listSetting = Setting::query()->whereIn('key', [
			'LIST_USERNAME_CAN_GO_HOME_AFTER_LOGIN',
			'HUONG_DAN_SU_DUNG_MAN_HOME'
		])->get();

		$returnData = [
			'tutorial' => (object) [],
			'banners' => app(GetBannerDiemBanSubAction::class)->run($listSetting, $request),
			'menus' => app(GetMenuHomeDiemBanSubAction::class)->run(),
			// 'listDiemBan' => app(GetDiemBanWithCurrentModeSubAction::class)->run($request->json('data.merchantId'))
			'listDiemBan' => []
		];


		// Link hướng dẫn sử dụng
		$settingHuongDanSuDung = $listSetting->where('key', 'HUONG_DAN_SU_DUNG_MAN_HOME')->first();
		
		if ($settingHuongDanSuDung) {
			$settingHuongDanSuDungValue = json_decode($settingHuongDanSuDung->value, true);
			$returnData['tutorial'] = [
				'title' => $settingHuongDanSuDungValue['title_via_lang'][app()->getLocale()],
				'icon' => "https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/mpos360/images/67d015e14465eb435f2bb02cHuongDanSuDung.png",
				'tutorialUrl' => $settingHuongDanSuDungValue['tutorialUrl'],
			];
		}

		return $returnData;
	}
} // End class
