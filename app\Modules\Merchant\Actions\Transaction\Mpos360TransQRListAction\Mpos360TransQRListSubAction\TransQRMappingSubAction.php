<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransQRListAction\Mpos360TransQRListSubAction;

class TransQRMappingSubAction
{
	public function mappingStatusTransaction()
	{
		$returnData = [
			[
				'key' => 'CANCEL', // code bên mpos
				'label' => __('gdqr.Đã huỷ'),
				'text_color' => '#ffffff',
				'bg_color' => '#808890'
			],

			[
				'key' => 'SUCCESS', // code bên mpos
				'label' => __('gdqr.Thành công'),
				'text_color' => '#ffffff',
				'bg_color' => '#3BB54A'
			],

			[
				'key' => 'SETTLEMENT', // code bên mpos
				'label' => __('gdqr.Đ<PERSON> kết toán'),
				'text_color' => '#ffffff',
				'bg_color' => '#008BF4'
			],

			[
				'key' => 'PAY', // code bên mpos
				'label' => __('gdqr.Đ<PERSON> toán'),
				'text_color' => '#ffffff',
				'bg_color' => '#73ae4a'
			],
			[
				'key' => 'PENDING', // code bên mpos
				'label' => __('gdqr.Đang xử lý'),
				'text_color' => '#ffffff',
				'bg_color' => '#E99323'
			],
			[
				'key' => 'FAIL', // code bên mpos
				'label' => __('gdqr.Thất bại'),
				'text_color' => '#ffffff',
				'bg_color' => '#DA2128'
			],
			[
				'key' => 'REFUND', // code bên mpos
				'label' => __('gdqr.Hoàn trả'),
				'text_color' => '#ffffff',
				'bg_color' => '#E923DC'
			],
		];

		$map = collect($returnData)->keyBy('key')->toArray();
		return $map;
	}

	public function mappingTransactionType()
	{
		$returnData = [
			[
				'key' => 'VAQR', // code bên mpos
				'label' => 'VietQR',
				'icon' => cumtomAsset('images/transaction/transaction-qr/VietQR.png'),
			],

			[
				'key' => 'MVISA', // code bên mpos
				'label' => 'QR quốc tế',
				'icon' => cumtomAsset('images/transaction/transaction-qr/QR_THE_QT.png'),
			],

			[
				'key' => 'QR quốc tế', // code bên mpos
				'label' => 'QR quốc tế',
				'icon' => cumtomAsset('images/transaction/transaction-qr/QR_THE_QT.png'),
			],

			[
				'key' => 'QR Quốc tế', // code bên mpos
				'label' => 'QR Quốc tế',
				'icon' => cumtomAsset('images/transaction/transaction-qr/QR_THE_QT.png'),
			],
			//            [
			//                'key' => 'QRTTD', // code bên mpos
			//                'label' => 'QR thẻ tín dụng',
			//                'icon' => cumtomAsset('images/transaction/transaction-qr/VietQR.png'),
			//            ],
			//
			//            [
			//                'key' => 'QRVDT', // code bên mpos
			//                'label' => 'QR ví điện tử',
			//                'icon' => cumtomAsset('images/transaction/transaction-qr/QR_VI_DIEN_TU.png'),
			//            ],
		];
		$map = collect($returnData)->keyBy('key')->toArray();
		return $map;
	}

	public function mappingDate()
	{
		$returnData =
			[
				'key' => 'transaction_time',
				'name' => __('gdqr.Thời gian giao dịch'),
				'list' => [
					[
						'value' => 'ALL',
						'label' => __('gdqr.Tất cả'),
					],
					[
						'value' => 'YESTERDAY',
						'label' => __('gdqr.Hôm qua'),
					],
					[
						'value' => 'TODAY',
						'label' => __('gdqr.Hôm nay'),
					],
					[
						'value' => 'THIS_WEEK',
						'label' => __('gdqr.Tuần này'),
					],
					[
						'value' => 'THIS_MONTH',
						'label' => __('gdqr.Tháng này'),
					],
				]


			];
		return $returnData;
	}
}
