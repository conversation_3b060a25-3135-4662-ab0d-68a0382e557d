<?php

namespace App\Modules\TingBoxVAMC\Actions\MerchantBank\Mpos360ConfirmOtpBankFakeAction;

use App\Exceptions\BusinessException;
use App\Lib\partner\SoundBox;
use App\Lib\MnpOnboardNewMcHelper;
use App\Modules\TingBoxVAMC\Enums\TingBoxVAMCEnum;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\ConfirmOtpBankRequest;

class Mpos360ConfirmOtpBankFakeAction
{
	protected SoundBox $soundBox;
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public function __construct(SoundBox $soundBox, MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->soundBox = $soundBox;
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run(ConfirmOtpBankRequest $request)
	{
		$error = $request->get('error');
		
		if (!empty($error)) {
			throw new BusinessException('Lỗi nghiệp vụ: ' . $error);
		}

		return [
			'msg' => 'Huỷ liên kết tài khoản ngân hàng thành công.',
			'can' => TingBoxVAMCEnum::CAN_GO_TO_TINGBOX_VAMC_SUCCESS,
		];
	}
} // End class
