<?php

namespace App\Lib\partner;

use App\Exceptions\BusinessException;
use App\Lib\partner\MNP;

class MNPServiceProgram extends MNP
{
	public function sendRequest($endpoint = '', $method = 'GET', $data = null, $authToken = '')
	{
		$this->baseUrl = env('API_PARTNER_MNP_API_SCON', '');

		if (empty($this->baseUrl)) {
			throw new BusinessException('[MNP Err] - Lỗi sai thông tin url');
		}
		$this->authToken = $authToken;

		return parent::sendRequest($endpoint, $method, $data);
	}
} // End class
