<?php

namespace App\Modules\TingBoxVAMC\Actions\CronJobSyncDeviceSoundBoxAction;

use App\Lib\partner\SoundBox;
use App\Lib\TelegramAlertWarning;
use App\Exceptions\BusinessException;
use App\Modules\TingBoxVAMC\Models\PlanEvent;
use App\Modules\Merchant\Model\MerchantOnboard;



class CronJobSyncDeviceSoundBoxAction
{
	protected SoundBox $soundBox;

	public array $exceptIds = [];

	public function __construct(SoundBox $soundBox)
	{
		$this->soundBox = $soundBox;
	}


	public function run()
	{
		for ($i=1; $i<=30; $i++) {
			$this->handle();
		}
	}

	public function handle() {
		$dataResponse = [];

		$event = PlanEvent::query()->where([
			'status' => 1,
			'action' => 'ADD_THIET_BI'
		]);

		if (!empty($this->exceptIds)) {
			$event = $event->whereNotIn('id', $this->exceptIds);
		}
		
		$event = $event->first();

		if (!$event) {
			return $dataResponse;
		}

		$this->exceptIds[] = $event->id;

		$event->status = 3; // dang xu ly
		$event->save();

		$dataEvent = json_decode($event->data, true);
		if (empty($dataEvent) || !is_array($dataEvent)) {
			$event->status = 5; // that bai
			$event->time_updated = time();
			$event->save();
		}

		$sendSoundbox = $this->soundBox->addThietBi($dataEvent);
		// Add thành công
		if (empty($sendSoundbox['result'])) {
			$event->status = 5; // that bai
			$event->time_updated = time();
			$event->response = json_encode($sendSoundbox ?? []);
			$event->save();
			@TelegramAlertWarning::sendMessage("Add thiet bi sang tingbox that bai (eventId:)" . $event->id);
		} else {
			$event->response = json_encode($sendSoundbox ?? []);
			$event->status = 2;
			$event->save();
		}

		if (!empty($sendSoundBox['result'])) {
			$mcOnboard = MerchantOnboard::query()->where('merchantId', $dataEvent['mcId'])->first();
		
			if (!$mcOnboard) {
				$mcOnboard = MerchantOnboard::query()->forceCreate([
					'username' => '',
					'merchantId' => $dataEvent['mcId'],
					'partnerCode' => $dataEvent['partnerCode'],
					'createdAt' => now(),
					'updatedAt' => now(),
					'partnerFlow' => 'ADD_DEVICE'
				]);
			}

			// Chỉ update khi partnerCode của ông hiện tại là NP
			if ($mcOnboard && $mcOnboard->partnerCode == 'NP') {
				if ($dataEvent['partnerCode'] != 'NP') {
					$mcOnboard->partnerFlow = 'ADD_DEVICE';
				}
				$mcOnboard->partnerCode = $dataEvent['partnerCode'];
				$mcOnboard->updatedAt = now();
				$saveResult = $mcOnboard->save();
			}
		}
	} // End method
} // End class
