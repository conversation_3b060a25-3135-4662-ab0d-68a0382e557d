<?php

namespace App\Modules\Merchant\Model;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Traits\BoSungThongTinTrait;
use App\Modules\Merchant\Model\Traits\Mpos360McRequestSupportLoaiYeuCauV3Trait;

class Mpos360MerchantRequest extends Model
{
	use Mpos360McRequestSupportLoaiYeuCauV3Trait;
	use BoSungThongTinTrait;
	
	protected $connection = 'mpos360_data';

	protected $table      = 'merchant_request';

	protected $guarded    = [];

	public $timestamps    = false;

	protected $dates      = [];

	protected $hidden     = [];

	public function mpos360McSupplements() {
		return $this->hasMany(Mpos360MerchantRequestSupplement::class, 'merchant_request_id', 'id');
	}

	public function mpos360McSupplementNew() {
		return $this->hasOne(Mpos360MerchantRequestSupplement::class, 'merchant_request_id', 'id')->where('status', 1);
	}

	public function isRequestChuaDaySangMnp()
	{
		return $this->status == Mpos360Enum::MPOS360_MC_REQUEST_STT_NHAP
			|| $this->status == Mpos360Enum::MPOS360_MC_REQUEST_STT_CHUA_GUI;
	}

	public function isDoiThongTinNganHang(): bool {
		return $this->getMnpGroupCode() == 'CHANGE_BANK_ACCOUN_INFO';
	}

	public function isDoiNguoiDaiDien(): bool {
		return $this->getMnpGroupCode() == 'CHANGE_REPRESENT_INFO';
	}

	public function getMnpGroupCode(): string
	{
		$dataRequest = json_decode($this->data_request, true);
		return $dataRequest[0]['type'];
	}

	public function getProfilesByType(string $type = '')
	{
		$dataRequest = json_decode($this->data_request, true);
		return collect($dataRequest)->where('type', $type)->first();
	}


	public function isYeuCauDoiCccdMoi(): bool {
		$dataRequest = json_decode($this->data_request, true);
		$choice = $dataRequest[0]['choice'];
		return $choice == 'DOI_CCCD_MOI';
	}

	public function isYeuCauDoiNguoiDaiDienMoi(): bool {
		$dataRequest = json_decode($this->data_request, true);
		$choice = $dataRequest[0]['choice'];
		return $choice == 'DOI_NGUOI_DAI_DIEN_MOI';
	}

	public function isDoiThongTinLienHe(): bool {
		$dataRequest = json_decode($this->data_request, true);
		$choice = $dataRequest[0]['choice'];
		return $choice == 'DOI_THONG_TIN_LIEN_HE';
	}

	public function getChoiceName(): string {
		$dataRequest = json_decode($this->data_request, true);
		$choice = $dataRequest[0]['choice'] ?? '';
		return $choice;
	}

	public function getTypeName(): string {
		$dataRequest = json_decode($this->data_request, true);
		$type = $dataRequest[0]['type'];
		return $type;
	}

	public function getTimeCreateAsString(): string {
		$timeCreated = Carbon::createFromTimestamp($this->time_created);
		
		if (now()->isSameDay($timeCreated)) {
			return $timeCreated->format('H:i');
		}

		return $timeCreated->format('H:i d\T\h\gm');
	}

	public function getNoteAsString(): string {
		$note = '';
		$comments = json_decode($this->comment, true);
		foreach ($comments as $cm) {
			$note .= $cm . ' ';
		}

		return $note;
	}

	public function getStatusAsValue() {
		if (now()->timestamp > $this->time_expired) {
			return 9;
		}

		return $this->status;
	}

	public function isScanned3(): bool {
		$dataRequest = json_decode($this->data_request, true);
		
		return (
			($dataRequest[0]['scan_method']['QTS']['status'] ?? '') == 'DONE' 
			|| ($dataRequest[0]['scan_method']['SMS']['status'] ?? '') == 'DONE' 
			|| ($dataRequest[0]['scan_method']['EMAIL']['status'] ?? '') == 'DONE' 
		);
	}

	public function getStatusVerifyForMobile(): string {
		$statusVerify = Mpos360Enum::MPOS360_MC_VERIFY_STT_DA_XAC_THUC ? 'DA_XAC_THUC' : 'CHUA_XAC_THUC';
		
		if ($this->status > Mpos360Enum::MPOS360_MC_REQUEST_STT_DA_GUI_SANG_MNP) {
			return $statusVerify;
		}

		if (now()->timestamp > $this->time_expired) {
			$statusVerify = 'HET_HAN';
		}

		return $statusVerify;
	}

	public function isHetHanVaChuaTaoYc(): bool {
		return now()->timestamp > $this->time_expired && $this->status < Mpos360Enum::MPOS360_MC_REQUEST_STT_DA_GUI_SANG_MNP;
	}

	public function isMcTuHuyYc(): bool {
		return $this->status == Mpos360Enum::MPOS360_MC_REQUEST_STT_MC_TU_HUY;
	}

	public function isKhongXacThuc() {
		return $this->status_verify == Mpos360Enum::MPOS360_MC_VERIFY_STT_KHONG_CO_THONG_TIN_XAC_THUC;
	}

	public function isChuaXacThuc() {
		return $this->status_verify == Mpos360Enum::MPOS360_MC_VERIFY_STT_CHUA_XAC_THUC;
	}

	public function isDaXacThucNhungChuaLamBuoc3() {
		return $this->status_verify == Mpos360Enum::MPOS360_MC_VERIFY_STT_DA_XAC_THUC;
	}

	public function isDaLamBuoc3() {
		return $this->status_verify == Mpos360Enum::MPOS360_MC_VERIFY_STT_DA_THUC_HIEN_BUOC3;
	}

	public function isKyMegaDoc(): bool {
		$dataRequest = json_decode($this->data_request, true);
		return $dataRequest[0]['signProcess']['mpos360_sign_code'] == 'KY_DIEN_TU_MEGADOC';
	}

	public function isKyVeTay(): bool {
		$dataRequest = json_decode($this->data_request, true);
		return $dataRequest[0]['signProcess']['mpos360_sign_code'] == 'KY_VE_TAY';
	}

	public function isKyGiay(): bool {
		$dataRequest = json_decode($this->data_request, true);
		return $dataRequest[0]['signProcess']['mpos360_sign_code'] == 'KY_GIAY';
	}

	public function isKySaleHoTro(): bool {
		$dataRequest = json_decode($this->data_request, true);
		return $dataRequest[0]['signProcess']['mpos360_sign_code'] == 'SALE_HO_TRO';
	}
} // End class
