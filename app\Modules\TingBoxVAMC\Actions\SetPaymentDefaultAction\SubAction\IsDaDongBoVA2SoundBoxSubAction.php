<?php

namespace App\Modules\TingBoxVAMC\Actions\SetPaymentDefaultAction\SubAction;

use App\Modules\TingBoxVAMC\Models\MerchantShopBank;

class IsDaDongBoVA2SoundBoxSubAction
{
	public function run(MerchantShopBank $mcShopBank, $mcTingBox = []): bool {
		
		if (empty($mcTingBox)) {
			return false;
		}

		return collect($mcTingBox['data']['mcQr'])->contains(function ($item) use ($mcShopBank) {
			return $mcShopBank->partner_request_id == $item['vaNextPayNumber'];
		});
	}
}
