<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction;

use App\Lib\Helper;
use Illuminate\Support\Arr;

class SortHoSoSubAction
{
	public function run($mnpGroupProfiles = [])
	{
		$sortTknh = ['bankAccType', 'bank', 'accountNo', 'holderName', 'branch', 'bankCity'];
		$sortNgoiDaiDien = ['customerName', 'passport', 'placeOfIssue', 'authoriserContactNumber', 'authoriserEmail', 'authoriserAddressPresent', 'authoriserAddress1'];
		$sortTaiKhoanMpos = ['merchantName', 'businessShortName', 'email', 'mobile', 'address', 'district', 'city'];
		
		foreach ($mnpGroupProfiles['groups'] as &$group) {
			if ($group['code'] == 'CHANGE_BANK_ACCOUN_INFO') {
				$group['profiles'] = collect($group['profiles'])->sortBy(function ($profile) use ($sortTknh) {
					return array_search($profile['profileKey'], $sortTknh);
				})
				->map(function ($profile) {
					if ($profile['profileKey'] == 'bank') {
						$display = $profile['display'];
						$explode = explode('-', $display);
						$line2 = trim(Arr::last($explode));

						$strrpos = strrpos($display, $line2);
						$line1 = substr($display, 0, $strrpos);
						$line1 = trim(rtrim($line1, ' - '));
						$line = sprintf('%s
						%s', $line1, $line2);
						$profile['display'] = $line;
					}

					return $profile;
				})
				->values()
				->all();
			} // end if CHANGE_BANK_ACCOUN_INFO

			if ($group['code'] == 'CHANGE_REPRESENT_INFO') {
				$group['profiles'] = collect($group['profiles'])
				->filter(function ($item) {
					return !in_array($item['profileKey'], ['authoriserAddressPresent', 'authoriserAddress1']);
				})
				->sortBy(function ($profile) use ($sortNgoiDaiDien) {
					return array_search($profile['profileKey'], $sortNgoiDaiDien);
				})
				->map(function ($item) {
					if ($item['profileKey'] == 'authoriserContactNumber') {
						$item['label'] = 'Số điện thoại';
					}

					if ($item['profileKey'] == 'passport') {
						$item['value'] = mask_string($item['value'], '*', 3);
						$item['display'] = mask_string($item['display'], '*', 3);
					}

					return $item;
				})
				->values()->all();
			} // end if CHANGE_REPRESENT_INFO

			if ($group['code'] == 'CHANGE_MPOS_ACCOUNT_INFO') {
				
				$group['profiles'] = collect($group['profiles'])
				->filter(function ($item) {
					return $item['profileKey'] != 'businessShortName';
				})
				->sortBy(function ($profile) use ($sortTaiKhoanMpos) {
					return array_search($profile['profileKey'], $sortTaiKhoanMpos);
				})
				->filter(function ($it) {
					if ($it['profileKey'] != 'email') {
						return true;
					}

					return Helper::isUserNameEqualEmail($it['value']);
				})
				->map(function ($it) {
					if ($it['profileKey'] != 'email') {
						return $it;
					}

					if ($it['profileKey'] == 'email') {
						$it['label'] = 'Email';
						return $it;
					}

					return $it;
				})
				->values()->all();
			} // end if CHANGE_MPOS_ACCOUNT_INFO
		} // end for group

		return $mnpGroupProfiles;
	}
} // End class