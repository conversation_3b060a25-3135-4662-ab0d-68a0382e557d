<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360RegisterUploadAction;

use App\Lib\partner\MNPUpload;
use App\Exceptions\BusinessException;

class Mpos360RegisterUploadAction
{
	public MNPUpload $mnp;

	public array $uploadFileFormat = [];

	public function __construct(MNPUpload $mnp)
	{
		$this->mnp = $mnp;
	}

	public function run($merchantId, $mnpToken, array $uploadedFiles)
	{
		$returnData = [
			'success' => [],
			'error' => []
		];

		$uploadResult = $this->mnp->uploadFileLaravel(
			$uploadedFiles, 
			$mnpToken,
			sprintf('user/mpos360/%s/%s', $merchantId, 'IMAGE'),
			true
		);

		if (!empty($uploadResult['status'])) {
			$returnData['success'] = $uploadResult['data'];
			return $returnData;
		}

		$msg = sprintf('Lỗi upload hình ảnh mặt trước & mặt sau CCCD', $uploadResult['code'] ?? '00');
		throw new BusinessException($msg);
	} // End method
} // End clasds
