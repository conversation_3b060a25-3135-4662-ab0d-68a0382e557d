<?php

namespace App\Modules\Merchant\Actions\ServiceProgramRegister\Mpos360ServiceProgramRegisterViewPdfAction;

use App\Exceptions\BusinessException;
use Exception;
use App\Lib\partner\MNP;
use App\Lib\partner\MNPServiceProgram;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\GetMNPAccessTokenSubAction;
use App\Modules\Merchant\Requests\ServiceProgramRegister\Mpos360ServiceProgramRegisterViewPdfRequest;

class Mpos360ServiceProgramRegisterViewPdfAction
{
	public MNPServiceProgram $mnpServiceProgram;

	public function __construct(MNPServiceProgram $mnpServiceProgram)
	{
		$this->mnpServiceProgram = $mnpServiceProgram;
	}

	public function run(Mpos360ServiceProgramRegisterViewPdfRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		$deviceSessionWithToken = app(GetMNPAccessTokenSubAction::class)->run($deviceSession);

		$viewPdf = $this->mnpServiceProgram->sendRequest(
			'/api/scon/partner/preview-pdf', 
			'POST', 
			[ 'mposId' => $deviceSessionWithToken->getMerchantId() ],
			$deviceSessionWithToken->mnp_token
		);

		if (empty($viewPdf['result'])) {
			$msg = sprintf('[MNP Error] - %s (Code: %s)', $viewPdf['message'] ?? 'Unknow', $viewPdf['code'] ?? '00');
			throw new BusinessException($msg);
		}
		
		$params = $request->json('data');
		$params['pdf_url'] = $viewPdf['message'];
		return $params;
	}
} // End class