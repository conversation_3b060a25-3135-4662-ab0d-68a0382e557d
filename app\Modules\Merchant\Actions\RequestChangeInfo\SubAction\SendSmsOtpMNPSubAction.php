<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\SubAction;

use Exception;
use App\Lib\partner\MNP;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Model\Mpos360CodeOtp;

class SendSmsOtpMNPSubAction
{
	public MNP $mnp;


	public function __construct(MNP $mnp)
	{
		$this->mnp = $mnp;
	}

	public function run(Mpos360CodeOtp $mpos360CodeOtp, DeviceSession $deviceSessionWithToken, bool $isZns=false)
	{
		$sendOtpResult = $this->mnp->sentOtp(
			$mpos360CodeOtp->obj_value,
			$mpos360CodeOtp->otp,
			$deviceSessionWithToken->mnp_token,
			$isZns
		);

		mylog([
			'Param Send Otp' => [
				'mobile' => $mpos360CodeOtp->obj_value,
				'otp' => $mpos360CodeOtp->otp,
				'token' => $deviceSessionWithToken->mnp_token,
				'isZns' => $isZns
			],
			'Resunt Send OTP' => $sendOtpResult
		]);

		if (empty($sendOtpResult['status'])) {
			$msg = $sendOtpResult['message'] ?? 'không xác định';
			throw new BusinessException(vmsg('LoiGuiOtpQuaSMS') . $msg);
		}

		return true;
	}
} // End clasds
