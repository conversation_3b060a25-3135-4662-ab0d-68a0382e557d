<?php

namespace App\Modules\Merchant\Requests\Notification;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360GetMyNotificationsRequest extends MerchantRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data.groupCode' => ['present', 'string', 'max:50'],
      'data.cateCode' => ['present', 'string', 'max:50'],
      'data.subCateCode' => ['present', 'string', 'max:50'],
			'data.pageNum' => ['required', 'numeric', 'integer', 'min:1'],
			'data.pageSize' => ['required', 'numeric', 'integer', 'min:5'],
    ];
  }

	protected function prepareForValidation()
	{
		parent::prepareForValidation();
		
		$params = $this->all();
		$params['data']['groupCode'] = trim($params['data']['groupCode']);
		$params['data']['cateCode'] = trim($params['data']['cateCode']);
		$params['data']['subCateCode'] = trim($params['data']['subCateCode']);

		$this->merge($params);
	}
}
