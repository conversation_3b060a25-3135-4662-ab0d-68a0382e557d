<?php

use Illuminate\Support\Facades\Route;
use App\Http\Middleware\MakeSureThatRequestIsJsonMiddleware;
use App\Modules\Merchant\Controllers\Authen\TBV5\TingBoxAuthController;

Route::group([
	'middleware' => [MakeSureThatRequestIsJsonMiddleware::class]
], function () {

	Route::post('/Mpos360LoginTingBoxV5', [
		'uses' => TingBoxAuthController::class . '@Mpos360LoginTingBoxV5',
		'as' => 'Mpos360LoginTingBoxV5Action'
	])->middleware('checkSumForAnyMobile:value|password|os|fcmToken|deviceToken');
});
