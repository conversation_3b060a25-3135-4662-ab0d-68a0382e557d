<?php

namespace App\Modules\Merchant\Actions\ChungThuc\Mpos360ChungThucGetOtpAction\SubAction;

use Carbon\Carbon;
use App\Modules\Merchant\Model\Setting;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Model\Mpos360CodeOtp;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\SendMailCustomizeViaMnpSubAction;

class SendEmailWithTemplateSubAction
{
	public function run(
		Mpos360CodeOtp $mpos360CodeOtp, 
		DeviceSession $deviceSessionWithToken, 
		string $subject,
		string $title, 
		string $bladeMailContent
	) {
		$settingHdsd = Setting::query()->firstWhere(['key' => 'HOST_ASSET_MAIL_TEMPLATE']);
		$settingHdsdValue = json_decode($settingHdsd->value, true);

		$mposUserName = $mpos360CodeOtp->obj_value;

		$bindParams = array_merge([
			'title' => $title,
			'merchantName' => $mpos360CodeOtp->mpos360User->getUserMcName(),
			'username' => $mposUserName,
			'otp' => $mpos360CodeOtp->otp,
			'otpExpiredAt' => Carbon::createFromTimestamp($mpos360CodeOtp->time_out)->format('d/m/Y H:i:s')
		], $settingHdsdValue['common']);

		$mailMeta = [
			'subject' => $subject,
			'to' => [$mpos360CodeOtp->obj_value],
			'cc' => [],
			'content' => view('EmailTemplate.' . $bladeMailContent, $bindParams)->render()
		];

		$sendOtp = app(SendMailCustomizeViaMnpSubAction::class)->run($deviceSessionWithToken->mnp_token, $mailMeta);
		return $sendOtp;
	}
}
