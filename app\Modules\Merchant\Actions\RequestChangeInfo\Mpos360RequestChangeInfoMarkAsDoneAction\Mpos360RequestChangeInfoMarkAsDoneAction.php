<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoMarkAsDoneAction;

use App\Exceptions\BusinessException;
use Exception;
use App\Lib\partner\MNP;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoMarkAsDoneRequest;

class Mpos360RequestChangeInfoMarkAsDoneAction
{
	public MNP $mnp;

	public function __construct(MNP $mnp)
	{
		$this->mnp = $mnp;
	}

	public function run(Mpos360RequestChangeInfoMarkAsDoneRequest $request)
	{
		$mpos360MerchantRequest = Mpos360MerchantRequest::query()->find($request->json('data.id'));

		if (!$mpos360MerchantRequest) {
			throw new BusinessException('Lỗi: không tìm thấy yêu cầu thay đổi');
		}

		if (!$mpos360MerchantRequest->isRequestChuaDaySangMnp()) {
			throw new BusinessException('Lỗi: yêu cầu của bạn có trạng thái không hợp lệ');
		}
		

		$dataRequest = json_decode($mpos360MerchantRequest->data_request, true);

		foreach ($dataRequest as &$it) {
			$it['scan_method']['QTS']['status'] = 'DONE';
			$it['scan_method']['QTS']['other_data'] = $request->json('data.other_data');
		}

		$mpos360MerchantRequest->data_request = json_encode($dataRequest);
		$mpos360MerchantRequest->method_code = 'QTS';

		$mpos360MerchantRequest->status = Mpos360Enum::MPOS360_MC_REQUEST_STT_CHUA_GUI;
		$mpos360MerchantRequest->status_verify = Mpos360Enum::MPOS360_MC_VERIFY_STT_DA_THUC_HIEN_BUOC3;
		$mpos360MerchantRequest->time_updated = now()->timestamp;
		$mpos360MerchantRequest->time_expired = now()->addDays(2)->timestamp;
		$r = $mpos360MerchantRequest->save();

		if (!$r) {
			throw new BusinessException('Lỗi: không đánh dấu được yc là mới tạo');
		}

		return [
			'id' => $mpos360MerchantRequest->id,
			'msg' => 'Đã hoàn thành quy trình'
		];
	}
} // End class
