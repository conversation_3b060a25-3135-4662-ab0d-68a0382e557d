<?php

namespace App\Modules\Merchant\Actions\ChungThuc\Mpos360ChungThucGetFieldInfoAction;

use App\Lib\partner\MNP;
use Illuminate\Support\Str;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360ChungThuc;
use App\Modules\Merchant\Requests\ChungThuc\Mpos360ChungThucGetFieldInfoRequest;
use App\Modules\Merchant\Actions\ChungThuc\Mpos360ChungThucGetFieldInfoAction\SubAction\GetHoSoNguoiDaiDienCanChungThucSubAction;

class Mpos360ChungThucGetFieldInfoImproveAction
{
	public MNP $mnp;

	public function __construct(MNP $mnp)
	{
		$this->mnp = $mnp;
	}
	
	public function run(Mpos360ChungThucGetFieldInfoRequest $request)
	{
		$returnData = [
			'warning' =>  [
				'msg' => vmsg('DeDamBaoAnToanChoTaiKhoanQuyKhachVuiLongXacThucCacThongTinDuoiDay'),
			],

			'data' => []
		];


		// Đoạn này, cần gọi api sang bên anh Hiếu để lấy được dữ liệu chứng thực realTime
		$deviceSession = $request->getCurrentDeviceSession();
		
		if (!$deviceSession->isMposActiveMerchant()) {
			$returnData['warning']['msg'] = '';
			return $returnData;
		}

		$listMpos360ChungThuc = app(GetHoSoNguoiDaiDienCanChungThucSubAction::class)->run($deviceSession);

		if ($listMpos360ChungThuc->isEmpty()) {
			return [
				'warning' => [
					'msg' => vmsg('BanCanPhaiChungThucCccdLanDauTienDeCoTheTaoYeuCauThayDoi'),
				],
				'data' => []
			];
		}

		$order = ['CCCD', 'MOBILE', 'EMAIL'];
		$listMpos360ChungThuc = $listMpos360ChungThuc->sortBy(function ($record) use ($order) {
			return array_search($record['key_code'], $order);
		});
		

		$isCoThongTinChoChungThuc = $listMpos360ChungThuc->contains(function (Mpos360ChungThuc $it) {
			return $it->status == Mpos360Enum::MPOS360_CHUNG_THUC_STT_DANG_XAC_NHAN;
		});

		if ($isCoThongTinChoChungThuc) {
			$returnData['warning']['msg'] = vmsg('QuyKhachVuiLongChoTrongGiayLatDeHeThongXuLyChungThucThongTin');
		}

		$isDaChungThuc3ThongTin = $listMpos360ChungThuc->every(function (Mpos360ChungThuc $it) {
			return $it->status == Mpos360Enum::MPOS360_CHUNG_THUC_STT_DA_XAC_NHAN;
		});

		if ($isDaChungThuc3ThongTin) {
			$returnData['warning']['msg'] = '';
		}

		foreach ($listMpos360ChungThuc as $mpos360ChungThuc) {
			if ($mpos360ChungThuc->method_code == 'EMAIL') {
				$isDomainNextPay = Str::contains($mpos360ChungThuc->value_confirmed, '@nextpay.vn');
				if ($isDomainNextPay) {
					continue;
				}
			}

			$returnData['data'][] = $mpos360ChungThuc->forMobile();
		}
	
		return $returnData;
	}
}
