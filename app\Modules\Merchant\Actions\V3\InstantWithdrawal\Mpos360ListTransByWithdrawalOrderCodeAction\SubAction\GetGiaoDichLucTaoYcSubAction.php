<?php

namespace App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360ListTransByWithdrawalOrderCodeAction\SubAction;

use Carbon\Carbon;
use App\Lib\Helper;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransQRListAction\Mpos360TransQRListSubAction\TransQRMappingSubAction;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalListAction\SubAction\MapTrangThaiGiaoDichVietQrCuaYeuCauSubAction;

class GetGiaoDichLucTaoYcSubAction
{
	public function run($rc = [])
	{
		if (empty($rc['transactionQRList'])) {
			return [];
		}

		$resultByDate = collect($rc['transactionQRList'])->groupBy(function ($item) {
			return Carbon::createFromFormat('d/m/Y H:i:s', $item['createdDate'])->format('d-m-Y');
		})
		->all();

		$data = [];

		$mapTrangThaiGiaoDichTrongYeuCau = app(TransQRMappingSubAction::class)->mappingStatusTransaction();
		
		foreach ($resultByDate as $date => $listTrans) {
			$item = [
				'date' => $date,
				'total_trans' => sprintf('%sGD', count($listTrans)),
				'data' => []
			];

			$records = [];
			foreach ($listTrans as $tr) {
				$records[] = [
					'code' => $tr['transactionType'],
					'type' => $tr['transactionType'],
					'icon' => cumtomAsset('images/rtn/VietQr.png'),
					'amount' => Helper::priceFormat($tr['amount']),
					'pan' => $tr['pan'],
					'time_created' => Carbon::createFromFormat('d/m/Y H:i:s', $tr['createdDate'])->format('H:i'),
					'note' => '',
					'status' => $tr['status'],
					'other_data' => [
						'status' => [
							'text' => $mapTrangThaiGiaoDichTrongYeuCau[$tr['status']]['label'] ?? $tr['status'] ?? 'Unknow',
							'text_color' => $mapTrangThaiGiaoDichTrongYeuCau[$tr['status']]['text_color'] ?? '',
							'bg_color' => $mapTrangThaiGiaoDichTrongYeuCau[$tr['status']]['bg_color'] ?? '',
						],

						'amount' => [
							'text_color' => '#dd8523',
							'font-weight' => 'bold'
						],

						'pan' => [
							'text_color' => '#808890'
						],

						'time_created' => [
							'text_color' => '#808890'
						]
					]
				];
			}

			$item['data'] = $records;
			$data[] = $item;
		}

		return $data;
	}
} // End class