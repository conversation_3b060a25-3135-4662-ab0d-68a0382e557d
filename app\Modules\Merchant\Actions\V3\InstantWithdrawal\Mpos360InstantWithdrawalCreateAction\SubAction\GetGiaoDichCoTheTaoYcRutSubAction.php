<?php

namespace App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalCreateAction\SubAction;

use Exception;
use App\Lib\Helper;
use Illuminate\Support\Arr;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Setting;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalFetchEstimateAction\Mpos360InstantWithdrawalFetchEstimateAction;

class GetGiaoDichCoTheTaoYcRutSubAction
{
	public array $listTranIds = [];

	public function run($estimateResult) {
		// lấy toàn bộ giao dịch trong danh sách
		$tranIds = Arr::pluck($estimateResult['data']['transactionVAQRList'], 'id');
		return $tranIds;

		// giới hạn số giao dịch với số tiền max khi tạo (tạm thời bỏ chưa dùng đến)
		$soTienThucNhanMposTraRa = $estimateResult['data']['sumAmountAfterFee'];
		$settingCreateIW = Setting::query()->firstWhere(['key' => 'CONFIG_RUT_TIEN_NGAY']);
		
		$getSoTienCoTheRut = app(Mpos360InstantWithdrawalFetchEstimateAction::class)->getSoTienMaxCoTheRut(
			$settingCreateIW,
			$soTienThucNhanMposTraRa
		);

		$soTienMax = $getSoTienCoTheRut['max_amount'];
		$tongSoTienCacGiaoDichCongDon = 0;

		$estimateResult['data']['transactionVAQRList'] = collect($estimateResult['data']['transactionVAQRList'])->sortBy('amount')->values()->all();
		foreach ($estimateResult['data']['transactionVAQRList'] as $tran) {
			$tongSoTienCacGiaoDichCongDon += $tran['amount'];
			if ($tongSoTienCacGiaoDichCongDon < $soTienMax) {
				$this->listTranIds[] = $tran['id'];
			}
		}

		mylog(['settingCauHinhMinMax' => $settingCreateIW]);

		if (empty($this->listTranIds)) {
			throw new BusinessException(
				'Không có giao dịch nào đủ điều kiện tạo yc. Bạn được rút tối đa: ' . Helper::priceFormat($soTienMax)
			);
		}

		return $this->listTranIds;
	}
}
