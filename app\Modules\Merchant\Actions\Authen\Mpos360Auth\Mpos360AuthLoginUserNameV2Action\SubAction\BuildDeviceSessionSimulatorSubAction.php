<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginUserNameV2Action\SubAction;

use Illuminate\Support\Str;
use App\Modules\Merchant\Model\Device;
use App\Modules\Merchant\Model\Mpos360User;
use App\Modules\Merchant\Model\DeviceSession;

class BuildDeviceSessionSimulatorSubAction
{
	const MOBILE_PARTNER_CONFIG_ID = 2;

	public function run(Mpos360User $mpos360User, Device $device, $loginMpos): DeviceSession
	{
		$apiKey = (string) Str::uuid();

		$deviceSession = new DeviceSession([
			'id' => time() . $loginMpos['data']['merchantId'],
			'device_id' => $device->id,
			'user_id' => $mpos360User->id,
			'api_key' => $apiKey,
			'api_secret' => '',
			'time_expired' => now()->addHours(12)->timestamp,
			'time_created' => time(),
			'time_updated' => time(),
			'mpos_token' => $loginMpos['data']['mobileUserToken'],
			'mnp_token' => '',
			'partner_config_id' => self::MOBILE_PARTNER_CONFIG_ID,
			'signed' => ''
		]);

		$deviceSession = $deviceSession->setRelation('mpos360User', $mpos360User);

		$deviceSession->visibleAndDecryptSecret();
		
		return $deviceSession;
	}
}
