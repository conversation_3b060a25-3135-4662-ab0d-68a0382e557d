<?php

namespace App\Modules\TingBoxVAMC\Actions\VAMC;

use App\Lib\Logs;
use App\Lib\partner\VA;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use App\Exceptions\BusinessException;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use App\Modules\TingBoxVAMC\Actions\VAMC\BuildLinkParams\OCBVAMC;
use App\Modules\TingBoxVAMC\Actions\VAMC\BuildLinkParams\VABVAMC;
use App\Modules\TingBoxVAMC\Actions\VAMC\BuildLinkParams\BIDVVAMC;
use App\Modules\TingBoxVAMC\Actions\VAMC\BuildLinkParams\VAMCConnector;

class RequestLinkVAMCAction
{
	public VA $va;

	public function __construct(VA $va)
	{
		$this->va = $va;
	}

	/**
	 * {
			"error_code": "475",
			"error_message": "Yêu cầu xác thực <PERSON>.",
			"checksum": "984fde7f40354ad3a3074aaafc510fc4",
			"data": {
				"mcRequestId": "MPOS-**********",
				"vaNextpayNumber": "NPQQYIWRBHEE4PD",
				"vaBankNumber": "962VMS9476428177088",
				"qrCode": "",
				"qrImage": "",
				"status": "ACTIVED",
				"channelCode": "",
				"vaReference": "MC8IKCMLJFO5GNP",
				"transferDesc": "MC8IKCMLJFO5GNP Mua hang",
				"urlConffirm": "",
				"deepLinkConfirm": "",
				"methodConfirm": "OTP",
				"detail1": "",
				"detail2": "",
				"detail3": "",
				"detail4": "",
				"detail5": ""
			}
		}
	 */
	public function run($params = [])
	{
		$p = app(VAMCConnector::class)->run($params);

		Logs::writeInfo("RequestLinkVAMCAction", $p);
		mylog(['Param-VA-Raw' => $p]);

		$dataAsJson = json_encode($p);
		$encrypt = $this->va->encrypt($dataAsJson);

		$checksum = $this->va->createChecksum($encrypt);

		$dataPost = [
			'app_id' => VAG_APP_ID,
			'data' => $encrypt,
			'checksum' => $checksum,
		];

		$url = VAG_BASE_URL . '/v1/merchant/va-mc/request-link';
		try {
			$r = $this->va->call($url, $dataPost, 5);
		} catch (\Throwable $th) {
			$r = [];
		}

		mylog(['Response-VA-Raw' => $r]);
		Logs::writeInfo("RequestLinkVAMCAction-Response", $r);
		return $r;
	}
} // End class