<?php

namespace App\Modules\Merchant\Controllers\Transaction;

use App\Lib\Helper;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\Transaction\Mpos360RecentlyVietQrTransactionRequest;
use App\Modules\Merchant\Actions\Transaction\Mpos360RecentlyVietQrTransactionAction\Mpos360RecentlyVietQrTransactionAction;

// Xử lý các giao dịch lịch sử tạm ứng, gồm: Mã tạm ứng & Giao dịch
class Mpos360RecentlyVietQrTransactionController extends Controller
{
	public function Mpos360RecentlyVietQrTransaction(Mpos360RecentlyVietQrTransactionRequest $request)
	{
		try {
			$result = app(Mpos360RecentlyVietQrTransactionAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
} // End class
