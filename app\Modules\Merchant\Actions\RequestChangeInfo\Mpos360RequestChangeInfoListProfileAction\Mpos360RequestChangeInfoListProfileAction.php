<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction;

use App\Exceptions\BusinessException;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\GetMNPAccessTokenSubAction;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoListProfileRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction\DamBaoCoCccdSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction\BuildChoiceOptionSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoGetConfigAction\Mpos360RequestChangeInfoGetConfigAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction\BuildBankAccTypeSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction\MnpGetMerchantProfileSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction\KiemTraDaDuHoSoHayChuaSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction\SortHoSoSubAction;

class Mpos360RequestChangeInfoListProfileAction
{
	public string $cacheName = 'mnp_profile_list_only';

	public function run(Mpos360RequestChangeInfoListProfileRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		
		$merchantId = $deviceSession->getMerchantId();

		$deviceSessionWithToken = app(GetMNPAccessTokenSubAction::class)->run($deviceSession);
		
		$mnpGetConfig = app(Mpos360RequestChangeInfoGetConfigAction::class)->run($request);

		if (empty($mnpGetConfig['data'])) {
			throw new BusinessException('Lỗi không get được config của mnp');
		};

		$merchantId = $deviceSessionWithToken->getMerchantId();

		$mnpMerchantDetail = app(MnpGetMerchantProfileSubAction::class)->run(
			$merchantId, 
			$deviceSessionWithToken->mnp_token
		);
		
		$groupProfiles = [];

		foreach ($mnpGetConfig['data']['typeChangeInfo'] as $groupType) {
			$code = $groupType['code'];
			$groupProfiles['groups'][$code] = $groupType;
			
			// rút gọn tên nhóm
			if ($code == 'CHANGE_BANK_ACCOUN_INFO') {
				$groupProfiles['groups'][$code]['name'] = vmsg('TKNH nhận tiền');
				$groupProfiles['groups'][$code]['choice'] = [];
			}

			if ($code == 'CHANGE_REPRESENT_INFO') {
				$groupProfiles['groups'][$code]['name'] = vmsg('Người đại diện ký HĐ');
				$groupProfiles['groups'][$code]['choice'] = app(BuildChoiceOptionSubAction::class)->run($mnpMerchantDetail);
			}

			if ($code == 'CHANGE_MPOS_ACCOUNT_INFO') {
				$groupProfiles['groups'][$code]['name'] = 'Thông tin tài khoản ' . __('setting.appName');
				$groupProfiles['groups'][$code]['choice'] = app(BuildChoiceOptionSubAction::class)->run($mnpMerchantDetail);
			}

			// Add hồ sơ vào nhóm
			foreach ($mnpMerchantDetail['data'] as $profileKey => $profileItem) {
				$profileItem['label'] = vmsg($profileItem['label']);
				$profileItem['isChanged'] = '0';
				$profileItem['other_data'] = (object) [];

				// ngân hàng
				if ($profileKey == 'bank') {
					$profileItem['other_data'] = [
						'list' => $mnpGetConfig['data']['banks']
					];
				}

				// loại tài khoản
				if ($profileKey == 'bankAccType') {
					/**{
							"code": "MC_COMPANY",
							"name": "Công ty"
					}, */

					$mcBankAccTypeGenForApp = [];

					foreach ($mnpGetConfig['data']['mcBankAccType'] as $item) {
						$mcBankAccTypeGenForApp[$item['code']] = $item['name'];
					}

					$profileItem['other_data'] = [
						'list' => $mcBankAccTypeGenForApp
					];
				}

				// tỉnh/thành phố
				if ($profileKey == 'bankCity') {
					$profileItem['other_data'] = [
						'list' => $mnpGetConfig['data']['cities']
					];
				}

				// Gom nhóm
				if ($profileItem['group'] == $code) {
					$profileItem['profileKey'] = $profileKey;
					$groupProfiles['groups'][$code]['profiles'][] = $profileItem;
				}
			}

			// Đổi thông tin ngân hàng
			if ($code == 'CHANGE_BANK_ACCOUN_INFO') {
				$groupProfiles['groups'][$code]['other_data'] = [
					'banks' => $mnpGetConfig['data']['banks'],
					'cities' => $mnpGetConfig['data']['cities']
				];
			}
		}

		$bankAccountTypeSinhMoi = app(BuildBankAccTypeSubAction::class)->run(
			$groupProfiles, 
			$mnpMerchantDetail['data']['customerType'],
			$mnpGetConfig['data']['mcBankAccType']
		);

		if (!empty($bankAccountTypeSinhMoi)) {
			$groupProfiles['groups']['CHANGE_BANK_ACCOUN_INFO']['profiles'][] = $bankAccountTypeSinhMoi;
		}

		// Làm đổi thông CCCD hoặc ng đại diện mới thì phải có cái này
		app(DamBaoCoCccdSubAction::class)->run($groupProfiles['groups']['CHANGE_REPRESENT_INFO']);

		$groupProfiles['groups'] = array_values($groupProfiles['groups']);

		foreach ($groupProfiles['groups'] as $group) {
			app(KiemTraDaDuHoSoHayChuaSubAction::class)->run($group['code'], $group['name'], $group['profiles']);
		}


		$returnData = app(SortHoSoSubAction::class)->run($groupProfiles);

		return $returnData;
	}
} // End clas
