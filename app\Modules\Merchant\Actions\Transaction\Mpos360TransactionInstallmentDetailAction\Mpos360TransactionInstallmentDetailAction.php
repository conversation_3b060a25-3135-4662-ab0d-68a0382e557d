<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransactionInstallmentDetailAction;

use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionInstallmentDetailRequest;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionInstallmentListRequest;
use App\Lib\partner\MPOS;
use App\Lib\Helper;
use App\Modules\Merchant\Actions\Transaction\subAction\Mpos360TransactionDefineConfigSubAction;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransactionInstallmentDetailAction\SubAction\Mpos360TransactionInstallmentDetailSubAction;
class Mpos360TransactionInstallmentDetailAction
{
	public $_type = 'DETAIL_INSTALLMENT_TRANSACTION';
	public function run(Mpos360TransactionInstallmentDetailRequest $request)
	{

		$deviceSession = $request->getCurrentDeviceSession();
		$params = [
			'typeTransaction' => $this->_type,
			'merchantFk' => $deviceSession->getMerchantId(),
			'id' => $request->json('data.transaction_id', ''),
			'tokenLogin' => $deviceSession->getMposToken()
		];
		$listTransaction = app(Mpos360TransactionDefineConfigSubAction::class)->requestTransDetail($params);
		return (new Mpos360TransactionInstallmentDetailSubAction)->returnData($listTransaction);
	}
}
