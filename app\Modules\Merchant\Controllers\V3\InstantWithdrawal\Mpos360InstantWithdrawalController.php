<?php

namespace App\Modules\Merchant\Controllers\V3\InstantWithdrawal;

use App\Lib\Helper;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\InstantWithdrawal\V3\Mpos360InstantWithdrawalListRequest;
use App\Modules\Merchant\Requests\InstantWithdrawal\V3\Mpos360InstantWithdrawalCreateRequest;
use App\Modules\Merchant\Requests\InstantWithdrawal\V3\Mpos360InstantWithdrawalDetailRequest;
use App\Modules\Merchant\Requests\InstantWithdrawal\V3\Mpos360InstantWithdrawalFetchEstimateRequest;
use App\Modules\Merchant\Requests\InstantWithdrawal\V3\Mpos360ListTransByWithdrawalOrderCodeRequest;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalDetailAction\Mpos360DetailYcNtnAction;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalListAction\Mpos360InstantWithdrawalListAction;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalCreateAction\Mpos360InstantWithdrawalCreateAction;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalDetailAction\Mpos360InstantWithdrawalDetailAction;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalListV2Action\Mpos360InstantWithdrawalListV2Action;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalFetchEstimateAction\Mpos360InstantWithdrawalFetchEstimateAction;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360ListTransByWithdrawalOrderCodeAction\Mpos360ListTransByWithdrawalOrderCodeAction;
use App\Modules\Merchant\Requests\InstantWithdrawal\V3\Mpos360InstantWithdrawalListV2Request;

class Mpos360InstantWithdrawalController extends Controller
{
	public function Mpos360InstantWithdrawalFetchEstimate(Mpos360InstantWithdrawalFetchEstimateRequest $request)
	{
		try {
			$result = app(Mpos360InstantWithdrawalFetchEstimateAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360InstantWithdrawalCreate(Mpos360InstantWithdrawalCreateRequest $request)
	{
		try {
			$result = app(Mpos360InstantWithdrawalCreateAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360InstantWithdrawalList(Mpos360InstantWithdrawalListRequest $request)
	{
		try {
			$result = app(Mpos360InstantWithdrawalListAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360InstantWithdrawalDetail(Mpos360InstantWithdrawalDetailRequest $request)
	{
		try {
			$result = app(Mpos360DetailYcNtnAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360ListTransByWithdrawalOrderCode(Mpos360ListTransByWithdrawalOrderCodeRequest $request)
	{
		try {
			$result = app(Mpos360ListTransByWithdrawalOrderCodeAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360InstantWithdrawalListV2(Mpos360InstantWithdrawalListV2Request $request)
	{
		try {
			$result = app(Mpos360InstantWithdrawalListV2Action::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
} // End class
