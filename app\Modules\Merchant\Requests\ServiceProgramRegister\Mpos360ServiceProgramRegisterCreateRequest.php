<?php

namespace App\Modules\Merchant\Requests\ServiceProgramRegister;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360ServiceProgramRegisterCreateRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.email' => ['required', 'email'],
			'data.merchant_id' => ['required', 'numeric'],
			'data.merchant_type' => ['required', 'string', 'in:INDIVIDUAL,HOUSEHOLD,COMPANY'],
			'data.service_program_id' => ['required', 'string']
		];
	}

	public function messages()
	{
		return [
			'data.email.required' => 'Email là bắt buộc',
			'data.email.email' => 'Email không đúng định dạng',
			'data.merchant_id.required' => 'MerchantId là bắt buộc',
			'data.merchant_id.numeric' => 'MerchantId phải là kiểu số',
			'data.merchant_type.required' => 'Loại merchant là bắt buộc',
			'data.merchant_type.string' => 'Loại merchant phải là kiểu chuỗi ký tự',
			'data.merchant_type.in' => 'Loại merchant phải thuộc trong các loại: Cá nhân, Hộ kinh doanh hoặc Công ty',
			'data.service_program_id.required' => 'Mã dịch vụ là bắt buộc',
			'data.service_program_id.string' => 'Mã dịch vụ phải là kiểu chuỗi ký tự',
		];
	}
} // End class
