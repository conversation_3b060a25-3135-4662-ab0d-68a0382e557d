<?php

namespace App\Modules\Merchant\Enums;

use App\Lib\partner\MNPNOTIFY;
use App\Lib\partner\MPOS;

class Mpos360Enum
{
	const CAN_GO_TO_TINGBOX_REGISTER_FLOW = 'CAN_GO_TO_TINGBOX_REGISTER_FLOW'; // tiếp tục luồng đăng ký tingbox
	const CAN_GO_TO_HOME_SCREEN = 'CAN_GO_TO_HOME_SCREEN'; // có thể vào màn trang chủ
	const CAN_GO_TO_VERIFY_MPOS_DEVICE_SCREEN = 'CAN_GO_TO_VERIFY_MPOS_DEVICE_SCREEN'; // phải qua màn xác thực thiết bị
	
	// chuyển qua màn hình nhập otp do: MC không có thiết bị hoặc Đang ở màn hình nhập mã thiết bị mà lại đổi ý
	// muốn dùng otp qua email
	const CAN_GO_TO_OTP_LOGIN_FLOW_EMAIL = 'CAN_GO_TO_OTP_LOGIN_FLOW_EMAIL'; 
	
	// xác thực thiết bị
	const CAN_STAY_VERIFY_MPOS_DEVICE_SCREEN = 'CAN_STAY_VERIFY_MPOS_DEVICE_SCREEN';

	// có thể vào màn trang chủ nhưng cần đổi mật khẩu ngay
	const CAN_GO_TO_HOME_SCREEN_AND_CHANGE_PASSWORD = 'CAN_GO_TO_HOME_SCREEN_AND_CHANGE_PASSWORD'; 

	// ngữ cảnh: lúc đổi mật khẩu
	const CAN_GO_TO_LOGIN_SCREEN = 'CAN_GO_TO_LOGIN_SCREEN'; // quay về màn hình đăng nhập
	const CAN_STAY_CHANGE_PASSWORD_SCREEN = 'CAN_STAY_CHANGE_PASSWORD_SCREEN';
	
	// Làm việc với api mpos
	const API_SUCCESS_CODE = 1000;
	const API_OVER_HAN_MUC_RUT_TIEN_NGAY = 9430;
	const API_WRONG_PASSWORD_CODE = 3301;
	const API_MERCHANT_NOT_FOUND = 3000;

	// Mpos360User
	const MPOS360_USER_STT_ACTIVE = 1;
	const MPOS360_USER_STT_LOCKED = 2;

	// Mpos360MerchantRequest
	const MPOS360_MC_REQUEST_THAY_DOI_THONG_TIN = 1;
	const MPOS360_MC_REQUEST_YC_XOA_TAI_KHOAN = 2;

	// ngữ cảnh: Form xác tài khoan
	const CAN_GO_TO_FORM_XAC_THUC_TAI_KHOAN = 'CAN_GO_TO_FORM_XAC_THUC_TAI_KHOAN'; // đi đến form nhập CCCD
	const CAN_GO_TO_FORM_NHAN_THANH_TOAN = 'CAN_GO_TO_FORM_NHAN_THANH_TOAN'; // đi đến form nhập CCCD

/* --------------------- OTP --------------------- */
	const MPOS360_OTP_SO_PHUT_TIMEOUT = 5;
	const MPOS360_OTP_CHUA_SU_DUNG = 1;
	const MPOS360_OTP_DA_SU_DUNG = 2;
/* --------------------- /End OTP --------------------- */

/* --------------------- Chứng thực --------------------- */
	const MPOS360_CHUNG_THUC_STT_CHUA_XAC_NHAN = 1;
	const MPOS360_CHUNG_THUC_STT_DANG_XAC_NHAN = 2;
	const MPOS360_CHUNG_THUC_STT_DA_XAC_NHAN = 3;
	const MPOS360_CHUNG_THUC_STT_TU_CHOI_XAC_NHAN = 4;
/* --------------------- /End chứng thực --------------------- */

/* --------------------- YC thay đổi --------------------- */
	/**
	 * 18.09.2024 thứ tự hiển thị ra app theo logic như sau
	 * 	-tự hủy
	 *  -hết hạn và chưa tạo yc 
	 *  -chưa xác thực 
	 *  -isDaXacThucNhungChuaLamBuoc3
	 *  -isDaLamBuoc3
	 */

	// Status Sign
	const MPOS360_MC_SIGN_STT_KHONG_CO_CHU_KY = 0;
	const MPOS360_MC_SIGN_STT_CHUA_KY = 1;
	const MPOS360_MC_SIGN_STT_DANG_KY = 2;
	const MPOS360_MC_SIGN_STT_DA_KY = 3;
	const MPOS360_MC_SIGN_STT_KY_LOI = 4;
	
	// Status Verify
	const MPOS360_MC_VERIFY_STT_DA_THUC_HIEN_BUOC3 = 3;
	const MPOS360_MC_VERIFY_STT_DA_XAC_THUC = 2;
	const MPOS360_MC_VERIFY_STT_CHUA_XAC_THUC = 1;
	const MPOS360_MC_VERIFY_STT_KHONG_CO_THONG_TIN_XAC_THUC = 0;
	
	// Status
	const MPOS360_MC_REQUEST_STT_NHAP = 1;
	const MPOS360_MC_REQUEST_STT_CHUA_GUI = 2;
	const MPOS360_MC_REQUEST_STT_DANG_GUI = 3;
	const MPOS360_MC_REQUEST_STT_DA_GUI_SANG_MNP = 4;
	const MPOS360_MC_REQUEST_STT_DA_XU_LY = 5;
	const MPOS360_MC_REQUEST_STT_DA_TU_CHOI = 6;
	const MPOS360_MC_REQUEST_STT_CAP_NHAT_LOI = 7;
	const MPOS360_MC_REQUEST_STT_MC_TU_HUY = 8;

	// lỗi gì đó nên phải ở lại form
	const MPOS360_CAN_STAY_AT_FORM = 'MPOS360_CAN_STAY_AT_FORM';

	// khi tạo yc xong - thì điều hướng đi đâu
	const MPOS360_CAN_NEED_VERIFY_OTP = 'MPOS360_CAN_NEED_VERIFY_OTP';
	const MPOS360_CAN_GOTO_SDK_FLOW = 'MPOS360_CAN_GOTO_SDK_FLOW';

	const MPOS360_CAN_EKYC_CA_NHAN_DUOC_UY_QUYEN = 'MPOS360_CAN_EKYC_CA_NHAN_DUOC_UY_QUYEN';
	
	// sau khi bước 2 đã mc làm xong, mc có thể là gì tiếp theo
	const MPOS360_CAN_GOTO_STEP3 = 'MPOS360_CAN_GOTO_STEP3'; // đi sang màn hình chọn pt scan ở bước 3
	const MPOS360_CAN_MARK_DONE_REQUEST = 'MPOS360_CAN_MARK_DONE_REQUEST'; // có thể gọi api đánh done do qts khớp thông tin (nếu đổi cccd hoặc ng đại diện mới)
	const MPOS360_CAN_GOTO_STEP3_SELFIE = 'MPOS360_CAN_GOTO_STEP3_SELFIE'; // đi đến màn hình selfie
	const MPOS360_CAN_GOTO_STEP3_SDK = 'MPOS360_CAN_GOTO_STEP3_SDK'; // đi đến màn hình selfie
	
	const MPOS360_CAN_CHOOSE_SIGNATURE = 'MPOS360_CAN_CHOOSE_SIGNATURE'; // điều hướng sang màn hình danh sách chữ ký
	const MPOS360_CAN_CREATE_SIGNATURE = 'MPOS360_CAN_CREATE_SIGNATURE'; // điều hướng sang màn hình tạo chữ ký
	const MPOS360_CAN_PICK_SIGN_METHOD = 'MPOS360_CAN_PICK_SIGN_METHOD'; // điều hướng sang màn chọn phương thức ký

	// Chọn 1 phương thức ký
	const MPOS360_CAN_GOTO_UPLOAD_PHU_LUC_GIAY = 'MPOS360_CAN_GOTO_UPLOAD_PHU_LUC_GIAY'; 
	const MPOS360_CAN_GOTO_KY_DIEN_TU_MEGADOC = 'MPOS360_CAN_GOTO_KY_DIEN_TU_MEGADOC'; 
	const MPOS360_CAN_GOTO_SALE_HO_TRO = 'MPOS360_CAN_GOTO_SALE_HO_TRO'; 

	// Step 3 Handler: Đánh dấu phương thức làm B3
	const MPOS360_CAN_STEP3_QTS = 'MPOS360_CAN_STEP3_QTS'; // đi vào luồng qts
	const MPOS360_CAN_STEP3_SMS_OTP = 'MPOS360_CAN_STEP3_SMS_OTP'; // otp sms
	const MPOS360_CAN_STEP3_EMAIL_OTP = 'MPOS360_CAN_STEP3_EMAIL_OTP';
	const MPOS360_CAN_STEP3_ZALO_OTP = 'MPOS360_CAN_STEP3_ZALO_OTP';
	const MPOS360_CAN_STEP3_SDK_HANDLER = 'MPOS360_CAN_STEP3_SDK_HANDLER';


	// cần bổ sung tài liệu đính kèm
	const MPOS360_CAN_SUBMIT_ADDITIONAL_ATTACHMENT = 'MPOS360_CAN_SUBMIT_ADDITIONAL_ATTACHMENT';
/* --------------------- /End YC thay đổi --------------------- */

/* --------------------- Bổ sung hồ sơ  --------------------- */
	const MPOS360_BO_SUNG_HO_SO_MOI_TAO = 1;
	const MPOS360_BO_SUNG_HO_SO_DA_BO_SUNG_THONG_TIN = 2;
	const MPOS360_BO_SUNG_HO_SO_DANG_GUI_MNP = 3;
	const MPOS360_BO_SUNG_HO_SO_DA_GUI_SANG_MNP = 4;
	const MPOS360_BO_SUNG_HO_SO_GUI_LOI = 5;
	const MPOS360_BO_SUNG_HO_SO_KHONG_CAN_XU_LY = 6;
/* --------------------- /.End bổ sung hồ sơ  --------------------- */

/* --------------------- Rút tiền nhanh  --------------------- */
	const MPOS360_RTN_CAN_STAY_THERE = 'MPOS360_RTN_CAN_STAY_THERE';
	const MPOS360_RTN_CAN_CREATE_YC = 'MPOS360_RTN_CAN_CREATE_YC';
	const MPOS360_RTN_CAN_GOTO_LIST_YC = 'MPOS360_RTN_CAN_GOTO_LIST_YC';

	const MPOS360_RTN_TYPE_GD_VIETQR_TAO_YC = 'ListTransWhenCreateRequest';
	const MPOS360_RTN_TYPE_GD_VIETQR_DUOC_DUYET = 'ListTransApproved';
	const MPOS360_RTN_TYPE_GD_VIETQR_TU_CHOI = 'ListTransDenied'; // chưa dùng
	const MPOS360_RTN_TYPE_GD_TRA_LAI = 'ListTransReturn';
	const MPOS360_RTN_TYPE_GD_THU_THEM = 'ListTransAdditionalCharge';
/* --------------------- /End rút tiền nhanh --------------------- */

	//TUAN
	const ENUM_MNPNOTIFY_GROUP_CODE = [
		'TRANSACTION'=> 'TRANSACTION',
		'REMIND'=> 'REMIND',
		'NEWS'=> 'NEWS',
		'PROMOTION'=> 'PROMOTION'
	];
	const ENUM_MNPNOTIFY_GROUP_CODE_NAME = [
		'TRANSACTION'=> 'Giao dịch',
		'REMIND'=> 'Nhắc nhở',
		'NEWS'=> 'Thông báo',
		'PROMOTION'=> 'Ưu đãi'
	];
	const ENUM_MNPNOTIFY_CATEGORY_CODE = [
		'PAYMENT'=> 'PAYMENT',
		'NOTIFICATION'=> 'NOTIFICATION',
		'REMIND'=> 'REMIND',
		'PROMOTION'=> 'PROMOTION'
	];
	const ENUM_MNPNOTIFY_CATEGORY_CODE_NAME = [
		'PAYMENT'=> 'Giao dịch',
		'REMIND'=> 'Nhắc nhở',
		'NOTIFICATION'=> 'Thông báo',
		'PROMOTION'=> 'Ưu đãi'
	];

	const ENUM_MNPNOTIFY_SUB_CATEGORY_CODE = [
		'UPDATE_QR'=> 'UPDATE_QR',
		'STATIC_QR'=> 'STATIC_QR',
		'DYNAMIC_QR'=> 'DYNAMIC_QR',
		'INSTALLMENT_PAYMENT_LINK'=> 'INSTALLMENT_PAYMENT_LINK',
		'DIRECT_PAYMENT_LINK'=> 'DIRECT_PAYMENT_LINK',
		'REFUND'=> 'REFUND',
		'QUICK_WITHDRAW'=> 'QUICK_WITHDRAW',
		'INSTALLMENT_SWIPE_CARD'=> 'INSTALLMENT_SWIPE_CARD',
		'CANCEL_TRANSACTION'=> 'CANCEL_TRANSACTION',
		'PROVIDE_DOCUMENTS'=> 'PROVIDE_DOCUMENTS',
		'NEED_SETTLEMENT'=> 'NEED_SETTLEMENT',
		'PAYMENT_PENDING_SIGNATURE'=> 'PAYMENT_PENDING_SIGNATURE',
		'LENDING_EVALUATION'=> 'LENDING_EVALUATION',
		'SERVICE_ERROR_REPORT'=> 'SERVICE_ERROR_REPORT',
		'SERVICE_RESTORE'=> 'SERVICE_RESTORE',
		'LOCK_SERVICE'=> 'LOCK_SERVICE',
		'UNLOCK_SERVICE'=> 'UNLOCK_SERVICE',
		'SERVICE_UPDATE'=> 'SERVICE_UPDATE',
		'APP_UPDATE'=> 'APP_UPDATE',
		'LUCKY_DRAW'=> 'LUCKY_DRAW',
		'PROMOTION'=> 'PROMOTION',
	];
	const ENUM_MNPNOTIFY_SUB_CATEGORY_CODE_NAME = [
		'UPDATE_QR'=> 'Cập nhật QR',
		'STATIC_QR'=> 'GD QR Tĩnh',
		'DYNAMIC_QR'=> 'QR đơn hàng',
		'INSTALLMENT_PAYMENT_LINK'=> 'Link trả góp',
		'DIRECT_PAYMENT_LINK'=> 'Link trả thẳng',
		'REFUND'=> 'Hoàn tiền',
		'QUICK_WITHDRAW'=> 'Rút tiền nhanh',
		'INSTALLMENT_SWIPE_CARD'=> 'Quẹt thẻ trả góp',
		'CANCEL_TRANSACTION'=> 'Hủy giao dịch',
		'PROVIDE_DOCUMENTS'=> 'Yêu cầu cung cấp chứng từ',
		'NEED_SETTLEMENT'=> 'Cần kết toán',
		'PAYMENT_PENDING_SIGNATURE'=> 'Giao dịch chờ chữ ký',
		'LENDING_EVALUATION'=> 'Ứng vốn',
		'SERVICE_ERROR_REPORT'=> 'Báo lỗi dịch vụ',
		'SERVICE_RESTORE'=> 'Khôi phục dịch vụ',
		'LOCK_SERVICE'=> 'Khóa dịch vụ',
		'UNLOCK_SERVICE'=> 'Mở dịch vụ',
		'SERVICE_UPDATE'=> 'Cập nhật hệ thống',
		'APP_UPDATE'=> 'Cập nhật ứng dụng',
		'LUCKY_DRAW'=> 'Quay số trúng thưởng',
		'PROMOTION'=> 'Ưu đãi',
	];
	const ENUM_MPOS_LANG_CODE = [
		'vi'=> 'vi'
	];
	const ENUM_MPOS_LANG_CODE_NAME = [
		'vi'=> 'Việt Nam'
	];
	
}
