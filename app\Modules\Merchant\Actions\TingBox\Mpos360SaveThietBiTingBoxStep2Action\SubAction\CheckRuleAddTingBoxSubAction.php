<?php

namespace App\Modules\Merchant\Actions\TingBox\Mpos360SaveThietBiTingBoxStep2Action\SubAction;

use App\Exceptions\BusinessException;
use App\Lib\partner\SoundBox;
use App\Lib\Logs;

class CheckRuleAddTingBoxSubAction
{
	protected SoundBox $soundBox;

	public function __construct(SoundBox $soundBox)
	{
		$this->soundBox = $soundBox;
	}

	public function run(): bool
	{
		$request = request();

		$params = [
			'serial' => $request->json('data.tingboxSerial'),
			'mcId' => $request->json('data.merchantId'),
			'mobileUserName' => $request->json('data.mobileUserId'),
			'partnerCode' => 'BIDV'
		];

		$response = $this->soundBox->checkDevice($params);
		// Kiểm tra response rỗng hoặc không có result
		if (empty($response)) {
			throw new BusinessException('Không thể kết nối tới hệ thống kiểm tra thiết bị. Vui lòng thử lại sau.', 5001);
		}

		// Kiểm tra kết quả trả về
		if (empty($response['result']) || empty($response['success'])) {
			$errorMessage = $response['message'] ?? 'Không thể thêm thiết bị TingBox. Vui lòng thử lại sau.';
			$errorCode = $response['code'] ?? 5002;
			throw new BusinessException($errorMessage, $errorCode);
		}

		return true;
	}
}
