<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestUpdateFileAction;

use App\Exceptions\BusinessException;
use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Http\Request;
use App\Lib\partner\MNPExtend;
use Illuminate\Support\Facades\DB;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequestSupplement;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestCronPushRecordAction\SubAction\GetMnpTokenByMerchantIdSubAction;

class Mpos360MerchantRequestUpdateFileAction
{
	public MNPExtend $mnpExtend;

	public $totalPushCount = 3; 

	private array $__exceptIds = [];

	public function __construct(MNPExtend $mnpExtend)
	{
		$this->mnpExtend = $mnpExtend;
	}

	public function run(Request $request)
	{
		$returnData = [];

		for ($i = 1; $i < 20; $i++) {
			try {
				$result = $this->handle();

				if ($result == 'EMPTY') {
					$returnData[] = 'EMPTY';
					break;
				}

				if (optional($result)->id) {
					$returnData[] = $result->id;
				}
			} catch (\Throwable $th) {
				mylog(['Loi xu ly ban ghi' => Helper::traceError($th)]);
				TelegramAlert::sendMessage(Helper::traceError($th));
				throw $th;
			}
		}

		return $returnData;
	}

	public function handle()
	{
		$mpos360McSupplement = Mpos360MerchantRequestSupplement::query()
			->with('mpos360McRequest:id,merchant_id,mynextpay_id')
			->where('status', Mpos360Enum::MPOS360_BO_SUNG_HO_SO_DA_BO_SUNG_THONG_TIN)
			->where('total_push_count', '<=', $this->totalPushCount);

		if (!empty($this->__exceptIds)) {
			$mpos360McSupplement = $mpos360McSupplement->whereNotIn('id', $this->__exceptIds);
		}

		$mpos360McSupplement = $mpos360McSupplement->first();

		if (!$mpos360McSupplement) {
			return 'EMPTY';
		}

		$this->__exceptIds[] = $mpos360McSupplement->id;

		// Upldate lên thành đang gửi
		$wasUpdateDangGui = Mpos360MerchantRequestSupplement::query()
			->where('id', $mpos360McSupplement->id)
			->where('status', Mpos360Enum::MPOS360_BO_SUNG_HO_SO_DA_BO_SUNG_THONG_TIN)
			->update([
				'status' => Mpos360Enum::MPOS360_BO_SUNG_HO_SO_DANG_GUI_MNP,
				'total_push_count' => DB::raw('total_push_count+1'),
				'time_updated' => now()->timestamp
			]);

		if (!$wasUpdateDangGui) {
			throw new BusinessException('Lỗi: không thể update lên đang gửi bổ sung hồ sơ');
		}

		$mpos360McSupplement->refresh();

		if ($mpos360McSupplement->status != Mpos360Enum::MPOS360_BO_SUNG_HO_SO_DANG_GUI_MNP) {
			throw new BusinessException('Lỗi: yêu cầu bổ sung đang không ở trạng thái đang gửi');
		}

		DB::beginTransaction();

		$listYcBoSungTruocDo = Mpos360MerchantRequestSupplement::query()
			->where('merchant_id', $mpos360McSupplement->merchant_id)
			->where('merchant_request_id', $mpos360McSupplement->merchant_request_id)
			->where('id', '!=', $mpos360McSupplement->id)
			->get();

		try {
			$mnpToken = app(GetMnpTokenByMerchantIdSubAction::class)->run($mpos360McSupplement->merchant_id);

			if (empty($mnpToken)) {
				throw new BusinessException('Lỗi không có thông tin mnp token');
			}

			$result = $this->mnpExtend->updateBoSungThongTin($mpos360McSupplement, $mnpToken, $listYcBoSungTruocDo);
			
			// Bổ sung thành công
			if (!empty($result['result'])) {
				$paramUpdate = [
					'status' => Mpos360Enum::MPOS360_BO_SUNG_HO_SO_DA_GUI_SANG_MNP,
					'supplement_msg' => $result['message']
				];

				// Update hết những cái trước đó về cuối cùng
				if ($listYcBoSungTruocDo->isNotEmpty()) {
					$updateListTruocDoLaDaGui = Mpos360MerchantRequestSupplement::query()
						->whereIn('id', $listYcBoSungTruocDo->pluck('id')->toArray())
						->where('merchant_id', $mpos360McSupplement->merchant_id)
						->update([
							'status' => Mpos360Enum::MPOS360_BO_SUNG_HO_SO_DA_GUI_SANG_MNP, 
							'time_updated' => now()->timestamp
						]);
				}
			}

			// Bổ sung thất bại
			if (empty($result['result'])) {
				$paramUpdate = [
					'status' => Mpos360Enum::MPOS360_BO_SUNG_HO_SO_DA_BO_SUNG_THONG_TIN,
					'supplement_msg' => $result['message']
				];

				if ($mpos360McSupplement->total_push_count >= $this->totalPushCount) {
					$paramUpdate['status'] = Mpos360Enum::MPOS360_BO_SUNG_HO_SO_GUI_LOI;
				}
			}

			$r = Mpos360MerchantRequestSupplement::query()
																					 ->where('id', $mpos360McSupplement->id)
																					 ->where('status', Mpos360Enum::MPOS360_BO_SUNG_HO_SO_DANG_GUI_MNP)
																					 ->update($paramUpdate);
			if (!$r) {
				throw new BusinessException('Lỗi không update được bản ghi cập nhật bổ sung hồ sơ');
			}

			DB::commit();
			return $mpos360McSupplement;
		} catch (\Throwable $th) {
			mylog(['Error' => Helper::traceError($th)]);
			DB::rollBack();

			$r = Mpos360MerchantRequestSupplement::query()
																					 ->where('id', $mpos360McSupplement->id)
																					 ->where('status', Mpos360Enum::MPOS360_BO_SUNG_HO_SO_DANG_GUI_MNP)
																					 ->update([
																						'status' => Mpos360Enum::MPOS360_BO_SUNG_HO_SO_DA_BO_SUNG_THONG_TIN,
																						'supplement_msg' => $th->getMessage()
																					 ]);

			throw $th;
		}
	} // End method
} // End class
