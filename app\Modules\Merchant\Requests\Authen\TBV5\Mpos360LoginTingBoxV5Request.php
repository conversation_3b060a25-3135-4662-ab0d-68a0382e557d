<?php

namespace App\Modules\Merchant\Requests\Authen\TBV5;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class Mpos360LoginTingBoxV5Request extends FormRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.value' => [ 'required', 'string', ],
			'data.password' => ['required', 'string', 'max:36'],
			'data.os' => ['required', 'string', Rule::in(['ANDROID', 'IOS'])],
			
			'data.fcmToken' => ['present', 'string', 'max:400'],
			'data.deviceToken' => ['required', 'string', 'max:255'],
		];
	}

	protected function passedValidation()
	{
		$params  = $this->all();
		$os = $params['data']['os'];
		$params['data']['os'] = ($os == 'IOS' ? 'iOS' : 'Android');

		$this->merge($params);
	}

	public function messages()
	{
		return [
			'data.password.required' => __('authen.Mật khẩu đăng nhập là bắt buộc'),
			'data.password.string' => __('authen.Mật khẩu đăng nhập phải là kiểu chuỗi'),
			'data.password.max' => __('authen.Mật khẩu đăng nhập không được dài quá 36 ký tự'),


			'data.os.required' => __('authen.Hệ điều hành thiết bị là bắt buộc'),
			'data.os.string' => __('authen.Hệ điều hành thiết bị phải là kiểu chuỗi'),
			'data.os.in' => __('authen.Hệ điều hành thiết bị phải là: ANDROID hoặc IOS'),
			
			'data.fcmToken.present' => __('authen.fcmToken là bắt buộc'),
			'data.fcmToken.string' => __('authen.fcmToken phải là kiểu chuỗi'),
			'data.fcmToken.max' => __('authen.fcmToken không được vượt quá 400 ký tự'),

			'data.deviceToken.present' => __('authen.deviceToken là bắt buộc'),
			'data.deviceToken.string' => __('authen.deviceToken phải là kiểu chuỗi'),
			'data.deviceToken.max' => __('authen.deviceToken không được vượt quá 255 ký tự'),

			'data.deviceSerial.required' => __('authen.Serial thiết bị là bắt buộc'),
			'data.deviceSerial.string' => __('authen.Serial thiết bị phải là kiểu chuỗi'),
			'data.deviceSerial.max' => __('authen.Serial thiết phải có độ dài tối đa là 255 ký tự'),
		];
	}
} // End class
