<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransactionQuickWithDrawOrderListAction;

use Carbon\Carbon;
use App\Lib\Helper;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionQuickWithDrawOrderListRequest;
use App\Modules\Merchant\Actions\Transaction\subAction\Mpos360TransactionDefineConfigSubAction;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransactionListNormalAction\SubAction\GetTransactionAsListMposSubAction;

class Mpos360TransactionQuickWithDrawOrderListAction
{
	public function run(Mpos360TransactionQuickWithDrawOrderListRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();

		$params = [
			'typeTransaction' => 'STATISTIC_QUICKWITHDRAW',
			'merchantFk' => $deviceSession->getMerchantId(),
			'startDate' => $request->getStartDate(),
			'endDate' => $request->getEndDate(),
			'tokenLogin' => $deviceSession->getMposToken(),
			'pageIndex' =>  $request->json('data.start', '1'),
			'pageSize' =>  $request->json('data.limit', '100'),
			'quickWithdrawStatus' => $request->getStatus(),
		];
		$listTransaction = app(Mpos360TransactionDefineConfigSubAction::class)->requestTransList($params);
		
		$data = [];
		if ($listTransaction) {
			if (isset($listTransaction['data']['quickWithdrawStatisticList'])) {
				$data = $this->__convertDataReturn($listTransaction['data']['quickWithdrawStatisticList']);
				$params['rangeTime'] = collect($listTransaction['data']['quickWithdrawStatisticList'])->map(function ($item) {
					return Carbon::createFromFormat('d-m-Y H:i:s', $item['createdDate'])->format('d-m-Y');
				})->unique()->implode(',');
			}
		}

		$params['email'] = $deviceSession->getMerchantEmail();
		return [
			'rows' => 0,
			'data' => $data,
			'other_data' => [
				'status' => $this->__getStatusDefault(),
				'filter' => $this->__getFilterDefault(),
				'countSumFilter' => Arr::only($params, [
					'typeTransaction',
					'merchantFk',
					'quickWithdrawStatus',
					'rangeTime',
					'email'
				])
			]
		];
	}

	private function __getStatusDefault()
	{
		return (new Mpos360TransactionDefineConfigSubAction())->getQuickWithdrawStatusTrans();
	}
	private function __getFilterDefault()
	{

		$data = $this->__getStatusDefault();
		$statusArr[] = ['value' => 'ALL', 'label' => trans_choice_fallback('trans.all')];
		foreach ($data as $key => $value) {
			$statusArr[] = [
				'value' => $value['value'],
				'label' => $value['label']
			];
		}
		return  [
			[
				'key' => 'transaction_time',
				'name' => trans_choice_fallback('trans.title.createdDate'),
				'list' => [
					['value' => 'TODAY', 'label' => trans_choice_fallback('trans.title.TODAY')],
					// ['value' => 'YESTERDAY', 'label' => 'Hôm qua'],
					['value' => 'THIS_MONTH', 'label' => trans_choice_fallback('trans.title.THIS_MONTH')],
					['value' => 'LAST_MONTH', 'label' => trans_choice_fallback('trans.title.LAST_MONTH')],
				]
			],
			[
				'key' => 'transaction_status',
				'name' => trans_choice_fallback('trans.title.statusInfo'),
				'list' => $statusArr
			]
		];
	}
	protected function __getStatusInstallMent()
	{
		return (new Mpos360TransactionDefineConfigSubAction())->getStatusInstallment();
	}
	private function __convertDataReturn($data)
	{
		if ($data) {
			$dataReturn = [];
			$mapData = $this->__getTransMethodName();
			foreach ($mapData as $key1 => $value1) {
				$transMethodNameArr[$value1['value']] = $value1['label'];
			}
			foreach ($data as $key => $value) {
				// var_dump($value);die();
				$timestamp = strtotime($value['createdDate']);
				$timeD = date('d-m-Y', $timestamp);
				$timeKey = date('Ymd', $timestamp);
				$transaction_method = $this->__getTransMethodCode($value);
				$transaction_method_name = isset($transMethodNameArr[$transaction_method]) ? $transMethodNameArr[$transaction_method] : $transaction_method;
				// $cardType = $this->__getCardType($value);
				$dataArr[$timeD][] = [
					'order_code' => $value['quickWithdrawId'],
					'transaction_id' => $value['quickWithdrawId'],
					'transaction_method' => $transaction_method,
					'transaction_method_name' => $transaction_method_name,
					'time_created' => date('H:i', $timestamp),
					'amount' => Helper::numberFormat($value['amountTransaction']) . ' VND',
					'fee' => '0 vnd',
					'status' => $value['statusQuickWithdraw'],
					'note' => '',
					'other_data' => (object) [],

					// 'icon' => cumtomAsset('images/transaction/transaction-list-normal/CARDS_WIPE.png'),
					// 'transaction_id' => $value['quickWithdrawId'],
					// 'order_code' =>  $value['quickWithdrawId'],
					// // 'transaction_method' => $transaction_method, // thẻ
					// 'transaction_method_name' => 'Quẹt thẻ thường',
					// // 'card_number' => ucfirst(strtolower($cardType)) . ': **** ' . substr($value['pan'], -4),
					// 'time_created' => now()->format('H:i'),
					// 'amount' => Helper::numberFormat($value['amountTransaction']) . ' VND',
					// 'status' => $value['statusQuickWithdraw'],
					// 'note' => '',
					// 'other_data' => [
					// 	'note' => ['text_color' => '', 'bg_color' => '']
					// ],
				];
			}
			foreach ($dataArr as $key => $value) {
				$dataReturn[] = [
					'date' => $key,
					'total_transaction' => 0,
					'list_transaction' => $value,
				];
			}
			return $dataReturn;
		}
		return [];
	}


	private function __getCardType($value)
	{
		if (isset($value['issuerCode'])) {
			return (new Mpos360TransactionDefineConfigSubAction())->getTypeCard($value['issuerCode']);
		}
		return '';
	}

	
	private function __getTransMethodCode($value)
	{
		if ($value) {
			return (new Mpos360TransactionDefineConfigSubAction())->getPaymentMethodCode($value); 
		}
		return '';
	}
	private function __getTransMethodName()
	{
		return (new Mpos360TransactionDefineConfigSubAction())->getPaymentMethod();
	}
}
