<?php

namespace App\Modules\Merchant\Controllers\MerchantSignature;

use App\Lib\Helper;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\MerchantSignature\Mpos360MerchantSignatureListRequest;
use App\Modules\Merchant\Requests\MerchantSignature\Mpos360MerchantSignatureCreateRequest;
use App\Modules\Merchant\Actions\MerchantSignature\Mpos360MerchantSignatureListAction\Mpos360MerchantSignatureListAction;
use App\Modules\Merchant\Actions\MerchantSignature\Mpos360MerchantSignatureCreateAction\Mpos360MerchantSignatureCreateAction;

class Mpos360MerchantSignatureController extends Controller
{
	public function Mpos360MerchantSignatureList(Mpos360MerchantSignatureListRequest $request)
	{
		try {
			$listSignature = app(Mpos360MerchantSignatureListAction::class)->run($request);
			return $this->successResponse([
				'rows' => $listSignature->count(),
				'data' => $listSignature->toArray(),
			], $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360MerchantSignatureCreate(Mpos360MerchantSignatureCreateRequest $request)
	{
		try {
			$signature = app(Mpos360MerchantSignatureCreateAction::class)->run($request);
			return $this->successResponse([
				'signature_id' => $signature->id
			], $request, 200, __('Lưu chữ ký thành công'));
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
} // End class
