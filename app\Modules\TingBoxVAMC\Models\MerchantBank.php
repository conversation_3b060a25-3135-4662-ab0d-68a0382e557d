<?php

namespace App\Modules\TingBoxVAMC\Models;

use Illuminate\Database\Eloquent\Model;

class MerchantBank extends Model
{
	const LOAI_TK_TRUC_TIEP = 1;
	const LOAI_TK_TRUNG_GIAN = 2;

	protected $connection = 'mpos360_data';

	protected $table      = 'merchant_bank';
	
	protected $guarded    = [];
	
	public $timestamps    = false;

	protected $dates      = [];

	protected $hidden     = [];

	public function getBankIconUrl() {
		if ($this->bank_code == 'BIDV') {
			return "https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/test/67c6c2cf4465eb435f2af70eBIDV_Icon.png";
		}

		if ($this->bank_code == 'MB') {
			return 'https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/mpos360/app/67e2aa004465eb435f2d25a2MBBank_Ico.png';
		}

		if ($this->bank_code == 'OCB') {
			return 'https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/test/67c279514465eb435f2aa427Icon_OCB.png';
		}

		if ($this->bank_code == 'VAB') {
			return 'https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/test/67d165c94465eb435f2bce55VAB.jpg';
		}

		if ($this->bank_code == 'VCB') {
			return 'https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/mpos360/app/67d8d2b04465eb435f2c4becVCB_Ico.png';
		}

		if ($this->bank_code == 'ACB') {
			return 'https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/mpos360/app/680aed624465eb435f2fef7eACB_Icon.png';
		}

		return $this->getDefaultBankIconUrl();
	}

	public function getDefaultBankIconUrl() {
		return 'https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/test/67b5971c4465eb435f2991c5IconBank.png';
	}
} // End class
