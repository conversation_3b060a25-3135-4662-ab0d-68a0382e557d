<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360RecentlyVietQrTransactionAction;

use App\Modules\Merchant\Requests\Transaction\Mpos360RecentlyVietQrTransactionRequest;

class Mpos360RecentlyVietQrTransactionAction
{
	public function run(Mpos360RecentlyVietQrTransactionRequest $request)
	{
		$returnData = [
			'wording' => '(Đang đồng bộ về Lịch sử giao dịch)',
			'rows' => 100,
			'data' => [
				[
					'date' => 'Hôm nay',
					'total_transaction' => 5,
					'list_transaction' => [
						[
							'icon' => cumtomAsset('images/rtn/VietQr.png'),
							'transaction_id' => '',
							'time_created' => '10:10',
							'amount' => '3.000.000 đ',
							'customer' => 'Nguyen Van A',
							'status' => 'SYNCING',
							'note' => '',
							'other_data' => (object) [
								'status' => [
									'text' => 'Đang đồng bộ',
									'text_color' => '#f3a200'
								]
							],
						],

						[
							'icon' => cumtomAsset('images/rtn/VietQr.png'),
							'transaction_id' => '',
							'time_created' => '20:10',
							'amount' => '3.000.000 đ',
							'customer' => 'Nguyen Van B',
							'status' => 'SYNCING',
							'note' => '',
							'other_data' => (object) [
								'status' => [
									'text' => 'Đang đồng bộ',
									'text_color' => '#f3a200'
								]
							],
						],
					]
				], // end item

				[
					'date' => '18-12-2024',
					'total_transaction' => 3,
					'list_transaction' => [
						[
							'icon' => cumtomAsset('images/rtn/VietQr.png'),
							'transaction_id' => '',
							'time_created' => '10:10',
							'amount' => '3.000.000 đ',
							'customer' => 'Nguyen Van C',
							'status' => 'SYNCED',
							'note' => '',
							'other_data' => (object) [
								'status' => [
									'text' => 'Đang đồng bộ',
									'text_color' => '#f3a200'
								]
							],
						],

						[
							'icon' => cumtomAsset('images/rtn/VietQr.png'),
							'transaction_id' => '',
							'time_created' => '20:10',
							'amount' => '3.000.000 đ',
							'customer' => 'Nguyen Van D',
							'status' => 'SYNCED',
							'note' => '',
							'other_data' => (object) [
								'status' => [
									'text' => 'Đang đồng bộ',
									'text_color' => '#f3a200'
								]
							],
						],
					]
				], // end item
			],
			'other_data' => (object) [
				// 'filter' => [
				// 	// thoi gian giao dich
				// 	'transaction_time' => [
				// 		['label' => 'Hôm nay', 'value' => 'TODAY'],
				// 		['label' => 'Tháng này', 'value' => 'THIS_MONTH'],
				// 		['label' => '01 tháng trước', 'value' => 'LAST_MONTH'],
				// 	],


				// 	// trang thai xu ly
				// 	'transaction_status' => [
				// 		['label' => 'Tất cả', 'value' => 'ALL'],
				// 		['label' => 'Đang diễn ra', 'value' => 'SYNCING'],
				// 		['label' => 'Đã kết thúc', 'value' => 'SYNCED'],
		
				// 	]
				// ]
			]
		];

		return $returnData;
	}
}
