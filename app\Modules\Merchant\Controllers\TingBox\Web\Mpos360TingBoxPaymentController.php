<?php

namespace App\Modules\Merchant\Controllers\TingBox\Web;

use App\Lib\MnpOnboardNewMcHelper;
use App\Modules\Merchant\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class Mpos360TingBoxPaymentController extends Controller
{
    public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

    public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
    {
        $this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
    }

    public function TBMpos360NhanThanhToan(Request $request)
    {
        $data = $request->all();
        $dataNganhNgheNganHangThanhPho = $this->mnpOnboardNewMcHelper->getNganhNgheNganHangThanhPho();
        $dataQuanHuyen = $this->mnpOnboardNewMcHelper->getQuanHuyenTuMaTinhThanh();
        $inputs = [
            'mposMcId' => 50446633, // mpos merchantId
        ];
        $dataList = $this->mnpOnboardNewMcHelper->getTingBoxList($inputs);

//        dd($dataNganhNgheNganHangThanhPho);
        return view('TingBoxWeb.nhanthanhtoan', [
                'dataNganhNgheNganHangThanhPho' => $dataNganhNgheNganHangThanhPho,
                'dataQuanHuyen' => $dataQuanHuyen
            ]
        );
    }

    public function getQuanHuyenTuMaTinhThanh(Request $request)
    {
        $id_city = $request->input('id_city', 'vn-kha');
        if (empty($id_city)) {
            $id_city = 'vn-kha';
        }
        $dataQuanHuyen = $this->mnpOnboardNewMcHelper->getQuanHuyenTuMaTinhThanh($id_city);
        if ($dataQuanHuyen && isset($dataQuanHuyen['result'])) {
            if ($dataQuanHuyen['result'] == true && $dataQuanHuyen['code'] == 1000) {
                if ($dataQuanHuyen['data']) {
                    $html = "";
                    foreach ($dataQuanHuyen['data'] as $data) {
                        $html .= '<option value="' . $data['code'] . '">' . $data['name'] . '</option>';
                    }
                    return response()->json([
                        'code' => 200,
                        'data' => $html,
                    ]);
                }
                return false;
            }
            return false;
        }
        return false;

    }

    public function TBMpos360SubmitThongTinCuaHang(Request $request)
    {
			$inputs = [
					"mposMcId" => $request->json('data.merchant_id'),
					'areaName' => $request->json('data.ten_cua_hang'),
					'mcc' => $request->json('data.nganh_nghe_kinh_doanh'),
					'areaCityCode' => $request->json('data.tinh_thanh_pho'),
					'areaDistrictCode' => $request->json('data.quan_huyen'),
					'areaAddress' => $request->json('data.dia_chi'),
					'businessShortName' => $request->json('data.ten_ma_qr_cua_hang', ''),
			];

			$result = $this->mnpOnboardNewMcHelper->taoCuaHangMoi($inputs);

			if (!empty($result['result']) && $result['code'] == 1000) {
					return response()->json([
							'code' => $result['code'],
							'data' => $result['data'],
							'message' => 'Tạo thành công'
					]);
			}
			
			return response()->json([
					'code' => $result['code'],
					'data' => $result['data'],
					'message' => $result['message']
			]);
    }

    public function TBMpos360SubmitLoaTingBox(Request $request)
    {

        $inputs = [
            'serial' => $request->json('data.serialId'),
            'mposMcId' => ********,
            'areaId' => $request->json('data.id_cua_hang'),
        ];
//        dd($inputs);
        $result = $this->mnpOnboardNewMcHelper->ganThietBiTingBox($inputs);
        if ($result['result'] == 'success' && $result['code'] == 1000) {
            return response()->json([
                'code' => $result['code'],
                'data' => $result['data'],
                'message' => 'Tạo thành công'
            ]);
        }
        return response()->json([
            'code' => $result['code'],
            'data' => $result['data'],
            'message' => $result['message']
        ]);

    }

    public function TBMpos360SubmitTaiKhoanNhanTien(Request $request)
    {

        $inputs = [
            'mposMcId' => ********,
            'holderName' => $request->json('data.ten_tai_khoan'),
            'accountNo' => $request->json('data.so_tai_khoan'),
            'bankId' => $request->json('data.id_ngan_hang'),
            'bankVerify' => $request->json('data.verify') == 1 ? true : false,
        ];
        $result = $this->mnpOnboardNewMcHelper->updateThongTinBanking($inputs);
        if ($result['result'] == 'success' && $result['code'] == 1000) {
            return response()->json([
                'code' => $result['code'],
                'data' => $result['data'],
                'message' => 'Tạo thành công'
            ]);
        }
        return response()->json([
            'code' => $result['code'],
            'data' => $result['data'],
            'message' => $result['message']
        ]);

    }

    public function TBMpos360CheckThongTinTaiKhoanNhanTien(Request $request)
    {
        $ten_ngan_hang = $request->json('data.ten_ngan_hang');
        $ten_ngan_hang = explode('-', $ten_ngan_hang);

        $inputs = [
            'bank_account_holder' => $request->json('data.ten_tai_khoan'),
            'bank_account' => $request->json('data.so_tai_khoan'),
            'bank_id' => $ten_ngan_hang[1],
        ];
        $result = $this->mnpOnboardNewMcHelper->checkThongTinBanking($inputs);
        return response()->json(
            $result
        );

    }

    public function TBMpos360DetailLoaTingBox(Request $request)
    {
        $data = $request->all();
        $inputs = [
            'id' => $data['id'],
            'color' => $data['color'],
        ];
        $detail = [];

        return view('TingBox.detail');

    }

    public function getAllBank(Request $request)
    {
        $dataNganhNgheNganHangThanhPho = $this->mnpOnboardNewMcHelper->getNganhNgheNganHangThanhPho();
        $banks = $dataNganhNgheNganHangThanhPho['banks'];
        if ($banks) {
            $html = '';
            foreach ($banks as $bank) {
                $bankName = [];
                if ($bank['bankName']) {
                    $bankName = explode('-', $bank['bankName']);
                    $html .= '<li><a class="click_bank" href="#" data-bs-toggle="modal" data-bankvimo="'. $bank['bankVimoId'] .'" data-label="' . $bank['bankName'] . '" data-id="' . $bank['bankId'] . '" data-bs-target="#acc-receiving1">
                                <figure><img src=""></figure>
                                <article>
                                    <label>' . $bankName[0] . '</label>
                                    <p>' . $bank['bankName'] . '</p>
                                    <p class="bankId" hidden> ' . $bank['bankId'] . '</p>
                                </article>
                            </a></li>';
                }
            }
            return response()->json([
                'data' => $html,
                'code' => 200,
            ]);
        }
        return false;

    }


}