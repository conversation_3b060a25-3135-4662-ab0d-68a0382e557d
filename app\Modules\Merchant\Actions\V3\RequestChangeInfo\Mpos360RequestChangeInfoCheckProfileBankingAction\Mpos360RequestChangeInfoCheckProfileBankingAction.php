<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoCheckProfileBankingAction;

use Exception;
use App\Lib\partner\VMMC;
use Illuminate\Support\Str;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Setting;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoCheckProfileBankingRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoGetConfigAction\Mpos360RequestChangeInfoGetConfigAction;

class Mpos360RequestChangeInfoCheckProfileBankingAction
{
	const STK_KHONG_DUNG = 0;
	const STK_KHONG_XAC_DINH = 1;
	const STK_HOP_LE = 2;

	public VMMC $vmmc;

	public function __construct(VMMC $vmmc)
	{
		$this->vmmc = $vmmc;
	}

	public function run(Mpos360RequestChangeInfoCheckProfileBankingRequest $request)
	{
		$requestVerify = [];

		foreach ($request->json('data') as $profileKey => $profileValue) {
			$requestVerify[] = [
				'field' => $profileKey,
				'value' => $profileValue,
				'status_verify' => '1',
				'date_verify' => now()->timestamp
			];
		}

		$params = $request->getParamCheckBankVmmc();
		$mnpConfigCache = app(Mpos360RequestChangeInfoGetConfigAction::class)->run();
		$params['bank_id'] = $mnpConfigCache['data']['bankMnpVimo'][$request->json('data.bankId')];

		mylog(['Thong tin TKNH can check' => $params]);

		$checkBankInfoResult = $this->vmmc->checkBankAccountMerchant($params);
		mylog(['Ket qua check' => $checkBankInfoResult]);

		if (empty($checkBankInfoResult['status_code_partner'])) {
			throw new BusinessException(vmsg('Lỗi không kiểm tra được thông tin ngân hàng'));
		}

		$code = $checkBankInfoResult['status_code_partner'];
		
		// Thành công
		if ($code == '00') {
			if (
				!empty($checkBankInfoResult['data'])
				&& Str::contains($checkBankInfoResult['data']['bank_account_holder'], $params['bank_account_holder'])
			) {
				$returnData = [
					'status' => self::STK_HOP_LE,
					'msg'    => vmsg('Số tài khoản hợp lệ'),
					'desc' => vmsg('Để tránh sai sót khi nhận tiền. Vui lòng kiểm tra lại thông tin trước khi tiếp tục'),
					'can'    => 'CAN_TIEP_TUC_TAO_YC',
					'request_vefify' => $requestVerify,
				];
			} else {
				$returnData = $this->__handleWrongInfo($checkBankInfoResult, $requestVerify);
			}
		} elseif ($code == '99') {
			$returnData = $this->__handleWrongInfo($checkBankInfoResult, $requestVerify);
			// $returnData = [
			// 	'status' => self::STK_KHONG_XAC_DINH,
			// 	'msg'    => vmsg('Số tài khoản chưc được xác minh bởi ngân hàng. Vui lòng kiểm tra lại thông tin trước khi tiếp tục!'),
			// 	'desc'   => '',
			// 	'can'    => 'CAN_TIEP_TUC_TAO_YC',
			// 	'request_vefify' => collect($requestVerify)->transform(function ($item) {
			// 		$item['status_verify'] = '0';
			// 		return $item;
			// 	}),
			// ];
		} else {
			$returnData = $this->__handleWrongInfo($checkBankInfoResult, $requestVerify);
		}


		$setting = Setting::query()->firstWhere(['key' => 'FAKE_CHECK_BANKING_INFO']);

		switch (optional($setting)->value) {
			case 'HOP_LE';
				$returnData['status'] = self::STK_HOP_LE;
				$returnData['msg'] = vmsg('Số tài khoản hợp lệ');
				$returnData['can'] = 'CAN_TIEP_TUC_TAO_YC';
				break;

			case 'TAI_KHOAN_KHONG_DUNG';
				$returnData['status'] = self::STK_KHONG_DUNG;
				$returnData['msg'] = vmsg('Số tài khoản không chính xác. Vui lòng kiểm tra lại');
				$returnData['can'] = 'CAN_DUNG_LAI_MAN_HINH_CHECK_NH';
				break;

			case 'KHONG_XAC_DINH';
				$returnData['status'] = self::STK_KHONG_XAC_DINH;
				$returnData['msg'] = vmsg('Số tài khoản chưc được xác minh bởi ngân hàng. Vui lòng kiểm tra lại thông tin trước khi tiếp tục!');
				$returnData['can'] = 'CAN_TIEP_TUC_TAO_YC';
				break;

			default;
				// code: dùng thật
				break;
		}

		return $returnData;
	}

	private function __handleWrongInfo($checkBankInfoResult, $requestVerify)
	{
		return [
			'status' => self::STK_KHONG_DUNG,
			'msg'    => vmsg('Số tài khoản không hợp lệ'),
			'desc' => $checkBankInfoResult['message'] ?? vmsg('Sai STK hoặc người thụ hưởng'),
			'can'    => 'CAN_DUNG_LAI_MAN_HINH_CHECK_NH',
			'request_vefify' => collect($requestVerify)->transform(function ($item) {
				$item['status_verify'] = '0';
				return $item;
			}),
		];
	}
} // End clas
