<?php

namespace App\Modules\Merchant\Actions\Notification\Mpos360GetGroupWithUnreadItemsAction;

use App\Lib\partner\MPOS;
use App\Modules\Merchant\Requests\Notification\Mpos360GetGroupWithUnreadItemsRequest;
use App\Modules\Merchant\Actions\Notification\Mpos360GetGroupWithUnreadItemsAction\SubAction\MapUnreadNotiIntoMposGroupCodeResultSubAction;

class Mpos360GetGroupWithUnreadMposAction
{
	public MPOS $mpos;

	public function __construct(MPOS $mpos)
	{
		$this->mpos = $mpos;
	}

	public function run(Mpos360GetGroupWithUnreadItemsRequest $request)
	{
		$getNotifyCategoryMposResult = $this->mpos->getNotifyCategory([]);
		$mpos360GetGroupWithUnreadItemsDto = app(Mpos360GetGroupWithUnreadItemsAction::class)->run($request);
		
		$getNotifyCategoryMposAfterMap = app(MapUnreadNotiIntoMposGroupCodeResultSubAction::class)->run(
			$mpos360GetGroupWithUnreadItemsDto,
			$getNotifyCategoryMposResult,
			$request->json('lang')
		);

		return $getNotifyCategoryMposAfterMap;
	}
} // End class