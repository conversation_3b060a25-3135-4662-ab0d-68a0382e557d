<html lang="zxx">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title><PERSON><PERSON><PERSON> k<PERSON> t<PERSON></title>
  <link rel="icon" type="image/png" href="{{ cumtomAsset('assets/img/favicon-mpos.svg') }}">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
    integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
		<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
  

  <script src="https://code.jquery.com/jquery-3.7.1.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>  
  <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
  <link rel="stylesheet" href="{{ cumtomAsset('assets/css/style.css') }}">
  <link rel="stylesheet" href="{{ cumtomAsset('assets/css/style-other.css') }}">
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<style>
    .se-pre-con {
        position: fixed;
        left: 0px;
        top: 0px;
        width: 100%;
        height: 100%;
        z-index: 9999;
        opacity: 0.6;
        /* background: url(/mpos360-app-api/public/images/web/loader-64x/Preloader_3.gif) center no-repeat #fff; */
        background: url('https://app-360-api.mpos.vn/images/web/loader-64x/Preloader_3.gif') center no-repeat #fff;
    }

    .ui-datepicker {
        z-index: 99 !important;
    }
</style>
<body>
  <div id="content-step-1">
    <section class="ftco-section login-wap">

      <div class="wrapper-page">
        <div class="head-wrap">
            <label>Đăng ký tài khoản</label>
            <span><img src="{{ cumtomAsset('assets/img/logo-mpos360.svg') }}"></span>
        </div>
        <div class="login-wrap">


          <div class="mpos360-head">
            <label>Xác thực tài khoản</label>
          </div>
          <div class="mpos360-form">
            <label class="mf-title">Căn cước/CCCD chủ cửa hàng
              <!-- <a href=""><img src="assets/img/icon-qr.svg"></a> -->
            </label>
            <div class="notifi-item">
              <form id="form-submit">
                <div class="form-floating mb-3">
                  <input type="text" class="form-control" id="name" name="name" placeholder="Họ và tên" autocomplete="off" value="{{ $dataStepTru2['name'] ?? '' }}">
                  <label for="name">Họ và tên</label>
                </div>
                <div class="form-floating mb-3">
                  <input type="tel" class="form-control" id="cccd" name="cccd" placeholder="Số CCCD" autocomplete="off" value="{{ $dataStepTru2['cccd'] ?? '' }}">
                  <label for="cccd">Số CCCD</label>
                </div>

								<div class="form-floating mb-3">
                  <input type="text" class="form-control" id="birthDay" name="birthDay" autocomplete="off" value="{{ $dataStepTru2['birthDay'] ?? '' }}">
                  <label for="birthDay">Ngày sinh</label>
                </div>

                <div class="form-floating mb-3">
                  <select class="form-select" id="gender" name="gender">
                    <option value="">-- Chọn Giới tính --</option>
										@if (empty($dataStepTru2['gender']))
											<option value="male" selected>Nam</option>
											<option value="female">Nữ</option>
										@else 
											<option value="male" @if (strtolower($dataStepTru2['gender'] == 'male' || $dataStepTru2['gender'] == 'Nam')) selected @endif>Nam</option>
											<option value="female" @if (strtolower($dataStepTru2['gender'] == 'female' || $dataStepTru2['gender'] == 'Nữ')) selected @endif>Nữ</option>
										@endif
                  </select>
                  <label for="gender">Giới tính</label>
                </div>
                <div class="form-floating mb-3">
                  <input type="text" class="form-control" id="address" name="address" placeholder="Địa chỉ thường trú" autocomplete="off" value="{{ $dataStepTru2['address'] ?? '' }}">
                  <label for="address">Địa chỉ thường trú</label>
                </div>
                
              </form>


            </div>

          </div>

          <div class="mpos360-footer">
            <a href="#" class="text-center btn-blue w-100 d-block submit-form">Tiếp tục</a>
          </div>

        </div>
      </div>

    </section>
  </div>

  <div id="content-step-2">

  </div>
  <div id="loadingPage"></div>




  <script>
    $(document).ready(function() {
      const APP_URL = "";
      $("#birthDay").flatpickr({
				dateFormat: 'd/m/Y',
				enableTime: false,
				disableMobile: "true"
			});


      $(document).on('click', '.submit-form', function(e) {
        e.preventDefault(); // Ngăn form reload

        // Xóa lỗi cũ
        $('.is-invalid').removeClass('is-invalid');
        $('.invalid-feedback').remove();

        // Lấy dữ liệu từ form
        const name = $('#name').val().trim();
        const cccd = $('#cccd').val().trim();
        const gender = $('#gender').val();
        const address = $('#address').val().trim();
        const issueDate = $('#birthDay').val();

        let isValid = true;

        // Validate Họ và tên
        if (name === '') {
          $('#name').addClass('is-invalid').after('<div class="invalid-feedback">Họ và tên không được để trống.</div>');
          isValid = false;
        } else {
          $('#name').removeClass('is-invalid');
          $('#name').next('.invalid-feedback').remove();
        }

        // Validate Số CCCD
        if (!/^\d{12}$/.test(cccd)) {
          $('#cccd').addClass('is-invalid').after('<div class="invalid-feedback">Số CCCD phải có 12 chữ số.</div>');
          isValid = false;
        } else {
          $('#cccd').removeClass('is-invalid');
          $('#cccd').next('.invalid-feedback').remove();
        }

        // Validate Giới tính
        if (gender === '') {
          $('#gender').addClass('is-invalid').after('<div class="invalid-feedback">Vui lòng chọn giới tính.</div>');
          isValid = false;
        } else {
          $('#gender').removeClass('is-invalid');
          $('#gender').next('.invalid-feedback').remove();
        }

        // Validate Địa chỉ
				const regexAddress = /^[a-zA-Z0-9\s\/,\u00C0-\u1EF9]*$/u;
				
        if (address === '') {
          $('#address').addClass('is-invalid').after('<div class="invalid-feedback">Địa chỉ không được để trống.</div>');
          isValid = false;
        }else if (address.length > 0 && !regexAddress.test(address)) {
					$('#address').addClass('is-invalid').after('<div class="invalid-feedback">Vui lòng Không sử dụng các ký tự đặc biệt như:  (-), @, #, $, %, ! *… trong trường địa chỉ.</div>');
					isValid = false;
				}
				else {
          $('#address').removeClass('is-invalid');
          $('#address').next('.invalid-feedback').remove();
        }

        // Validate Ngày cấp
        if (issueDate === '') {
          $('#birthDay').addClass('is-invalid').after('<div class="invalid-feedback">Vui lòng chọn ngày cấp.</div>');
          isValid = false;
        } else {
          $('#birthDay').removeClass('is-invalid');
          $('#birthDay').next('.invalid-feedback').remove();
        }

        // Nếu hợp lệ, gửi AJAX
        if (isValid) {
          const additionalData = {
            sessionId: "{{ $sessionId }}",
            merchantId: "{{ $merchantId }}",
            signed: "{{ $signed }}",
          };

          $('#loadingPage').addClass('se-pre-con');

          $.ajax({
            url: APP_URL + "/TBWebMpos360SubmitAuthenAccountStep1",
            type: 'POST',
            data: {
              ...$('#form-submit').serializeArray().reduce((obj, item) => ({
                ...obj,
                [item.name]: item.value
              }), {}),
              ...additionalData
            },
            headers: {
              "X-CSRF-TOKEN": "{{ csrf_token() }}",
            },
            success: function(data) {
              if (data.success) {
                $('#content-step-1').html('');
                $('#content-step-2').html(data.html);
              } else {
                alert(data.message || "Có lỗi xảy ra, Hãy thông báo kỹ thuật hỗ trợ xử lý.");
              }
              $('#loadingPage').removeClass('se-pre-con');
            },
            error: function(xhr) {
              $('#loadingPage').removeClass('se-pre-con');
              alert("Có lỗi xảy ra, Hãy thông báo kỹ thuật hỗ trợ xử lý.");
            },
          });
        }
      });
      $(document).on('input change', '#form-submit input, #form-submit select', function() {
        $(this).removeClass('is-invalid');
        $(this).next('.invalid-feedback').remove();
      });
    });
  </script>

	<script>
		$(document).ready(function () {
			var url = window.location.href;
			var urlParams = new URLSearchParams(window.location.search);
			var message = urlParams.get('message');
			if (message) {
				alert(message);
			}
		});
	</script>

</body>

</html>