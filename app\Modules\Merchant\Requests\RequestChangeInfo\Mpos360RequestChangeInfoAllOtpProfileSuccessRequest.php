<?php

namespace App\Modules\Merchant\Requests\RequestChangeInfo;

use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360RequestChangeInfoAllOtpProfileSuccessRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.id' => ['required', 'numeric', 'integer', 'min:1'],
			'data.request_vefify' => ['required', 'array'],
			'data.request_vefify.*.profileKey' => ['required', 'string'],
			'data.request_vefify.*.value' => ['required', 'string'],
			'data.request_vefify.*.status_verify' => ['required', 'string', Rule::in(['1'])],
			'data.request_vefify.*.date_verify' => ['required', 'numeric', 'integer'],

			'data.attachments' => ['present', 'array']
		];
	}

	public function getRequestVefifyAsArray()
	{
		$verifyResult = $this->json('data.request_vefify', []);
		$verifyInsertDataRequest = [];

		foreach ($verifyResult as $item) {
			$verifyInsertDataRequest[] = [
				'field'         => config('profilemnp.profile.' . $item['profileKey']),
				'value'         => $item['value'],
				'status_verify' => $item['status_verify'],
				'date_verify'   => $item['date_verify']
			];
		}

		return $verifyInsertDataRequest;
	}

	protected function passedValidation()
	{
		$params = $this->all();
		foreach ($params['data']['request_vefify'] as &$verifiedInfo) {
			if (is_string($verifiedInfo['value'])) {
				$verifiedInfo['value'] = trim(strip_tags($verifiedInfo['value']));
			}
		}

		$this->merge($params);
	}
} // End class
