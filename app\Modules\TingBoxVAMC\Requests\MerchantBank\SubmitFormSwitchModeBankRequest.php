<?php
namespace App\Modules\TingBoxVAMC\Requests\MerchantBank;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class SubmitFormSwitchModeBankRequest extends FormRequest
{
      /**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data' => ['required', 'array'],
			'data.username' => ['required', 'string'],
			'data.merchantId' => ['required', 'string'],
		];
	}

	public function messages()
	{
		return [
			
			'data.merchantId.required' => 'Id merchant là bắt buộc',
			'data.merchantId.string' => 'Id merchant phải là kiểu chuỗi',
			'data.username.required' => 'username là bắt buộc',
			'data.username.string' => 'username phải là kiểu chuỗi',

		];
	}
}