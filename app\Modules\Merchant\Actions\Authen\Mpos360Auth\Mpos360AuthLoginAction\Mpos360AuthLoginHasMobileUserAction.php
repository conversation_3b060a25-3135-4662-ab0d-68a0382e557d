<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction;

use Illuminate\Support\Str;
use App\Lib\PasswordHandler;
use App\Modules\Merchant\Model\Setting;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Model\PartnerConfig;
use App\Modules\Merchant\DTOs\Authen\Mpos360Auth\LoginMposSuccessDto;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360AuthLoginRequest;
use App\Modules\Merchant\Actions\Device\GetDeviceIdByOsAndTokenAction\SaveDeviceWithMobileUserAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction\BuildBottomNavAppSubAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction\CreateMpos360UserSubAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction\CreateDeviceSessionSubAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction\LoginMerchantViaMposUserNameSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestCronPushRecordAction\SubAction\GetMnpTokenByMerchantIdSubAction;

/**
 * [19.12.2024] - Version login này phía mpos đổi rất nhiều kết quả trả về
 * Yêu cầu phía mobile đổi lại version
 */
class Mpos360AuthLoginHasMobileUserAction
{
	public bool $isCoThietBi = false;

	public function run(Mpos360AuthLoginRequest $request)
	{
		return;
	}
} // End class
