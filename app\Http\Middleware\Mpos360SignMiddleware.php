<?php

namespace App\Http\Middleware;

use App\Lib\Helper;
use Closure;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class Mpos360SignMiddleware
{
	public $timeStart;

	protected $excepts = [
		'healthcheck/liveness',
		'healthcheck/readiness',
		
		// 'Mpos360AuthLogin', 
		'Mpos360QtsGetConfig',
		'Mpos360TingBoxDeviceHuongDanSuDung'
	];

	/**
	 * Handle an incoming request.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  \Closure  $next
	 * @return mixed
	 */
	public function handle($request, Closure $next)
	{
		if (empty($request->get('sign'))) {
			dd("Empty sign");
		}

		if (empty($request->get('sessionId'))) {
			dd("Empty session");
		}
	}
}
