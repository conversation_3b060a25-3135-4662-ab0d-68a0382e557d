<?php

namespace App\Modules\Merchant\Requests\Authen\Mpos360\V4;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360AuthGetOtpWithHinhThucV4Request extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.username' => ['required', 'string'],
			'data.channel' => ['required', 'string', 'in:EMAIL,SMS,ZALO'],
			'data.value' => ['required', 'string'],
			'data.merchant_id' => ['required', 'string'],
			'data.otp_id' => ['present', 'string'],
		];
	}

	public function messages() {
		return [
			'data.channel.required' => 'Kênh nhận otp là bắt buộc',
			'data.channel.string' => 'Kênh nhận otp phải là kiểu chuỗi',
			'data.channel.in' => 'Kênh nhận otp phải thuộc một trong các giá trị: EMAIL, SMS hoặc ZALO',
			'data.value.required' => 'đối tượng nhận otp là bắt buộc',
			'data.value.string' => 'đối tượng nhận otp phải là kiểu chuỗi',
		];
	}

	public function isTaoMoiOtp(): bool {
		return empty($this->json('data.otp_id'));
	}

	public function isGuiLaiOtp(): bool {
		return !$this->isTaoMoiOtp();
	}

	public function isEmailChannel(): bool {
		return $this->json('data.channel') == 'EMAIL';
	}

	public function isZnsChannel(): bool {
		return $this->json('data.channel') == 'ZALO';
	}
} // End class
