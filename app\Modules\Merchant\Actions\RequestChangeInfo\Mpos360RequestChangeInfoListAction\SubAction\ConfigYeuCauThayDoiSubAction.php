<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListAction\SubAction;

use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoDetailV3Action\SubAction\GetTrangThaiYcForMobileSubAction;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;

class ConfigYeuCauThayDoiSubAction
{
	public static function getCog(string $loaiDuLieuMuonLay = 'status', string $valuePair, string $fieldReturn)
	{
		$returnData = [
			'status' => [
				['value' => '1', 'label' => vmsg('ConfigYeuCauThayDoiSubAction_Nhap'), 'text_color' => '#ffffff', 'bg_color' => '#808890'],
				['value' => '2', 'label' => vmsg('ConfigYeuCauThayDoiSubAction_ChuaGui'), 'text_color' => '#ffffff', 'bg_color' => '#86baef'],
				['value' => '3', 'label' => vmsg('ConfigYeuCauThayDoiSubAction_DangGui'), 'text_color' => '#ffffff', 'bg_color' => '#f09b24'],
				['value' => '4', 'label' => vmsg('ConfigYeuCauThayDoiSubAction_DaGuiSangMnp'), 'text_color' => '#ffffff', 'bg_color' => '#0366ad'],
				['value' => '5', 'label' => vmsg('ConfigYeuCauThayDoiSubAction_MnpDaXuLy'), 'text_color' => '#ffffff', 'bg_color' => '#3bb54a'],
				['value' => '6', 'label' => vmsg('ConfigYeuCauThayDoiSubAction_MnpTuChoi'), 'text_color' => '#ffffff', 'bg_color' => '#d22e2a'],
				['value' => '7', 'label' => vmsg('ConfigYeuCauThayDoiSubAction_CapNhatLoi'), 'text_color' => '#ffffff', 'bg_color' => '#d22e2a'],
				['value' => '8', 'label' => vmsg('ConfigYeuCauThayDoiSubAction_MCTuHuyYeuCau'), 'text_color' => '#ffffff', 'bg_color' => '#404041'],
				['value' => '9', 'label' => vmsg('ConfigYeuCauThayDoiSubAction_HetHan'), 'text_color' => '#ffffff', 'bg_color' => '#808890'],
			],
		];

		$r = collect($returnData[$loaiDuLieuMuonLay])->where('value', $valuePair)->first();

		return $r[$fieldReturn];
	} // end method

	public static function getStyleAtList(Mpos360MerchantRequest $mpos360MerchantRequest)
	{
		$getTrangThaiForMobile = app(GetTrangThaiYcForMobileSubAction::class)->run($mpos360MerchantRequest);
		return [
			'text' => $getTrangThaiForMobile['name'],
			'text_color' => $getTrangThaiForMobile['other_data']->text_color,
			'bg_color' => $getTrangThaiForMobile['other_data']->bg_color,
		];
	} // end method
} // end class