<?php

namespace App\Modules\Merchant\Requests\Authen\Mpos360;

use App\Modules\Merchant\Requests\MerchantRequest;
use Illuminate\Foundation\Http\FormRequest;

class Mpos360AuthRemoveAccountRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.email' => ['required', 'string', 'email', 'max:255'],
			'data.reason_remove_account' => ['present', 'string', 'max:300'],
			'data.password' => ['required', 'string'],
		];
	}

	public function messages()
	{
		return [
			'data.email.required' => vmsg('Mpos360AuthRemoveAccountRequest_EmailLaBatBuoc'),
			'data.email.string' => vmsg('Mpos360AuthRemoveAccountRequest_EmailPhaiLaKieuChuoi'),
			'data.email.email' => vmsg('Mpos360AuthRemoveAccountRequest_EmailPhaiDungDinhDang'),
			'data.email.max' => vmsg('Mpos360AuthRemoveAccountRequest_EmailKhongDuocVuotQua255KyTu'),

			'data.reason_remove_account.present' => vmsg('Mpos360AuthRemoveAccountRequest_LyDoXoaTaiKhoanLaBatBuoc'),
			'data.reason_remove_account.string' => vmsg('Mpos360AuthRemoveAccountRequest_LyDoXoaTaiKhoanPhaiLaKieuChuoi'),
			'data.reason_remove_account.max' => vmsg('Mpos360AuthRemoveAccountRequest_LyDoXoaTaiKhoanPhaiPhaiCoDoDaiDuoi300KyTu'),

			'data.password.required' => vmsg('Mpos360AuthRemoveAccountRequest_MatKhauLaBatBuoc'),
			'data.password.string' => vmsg('Mpos360AuthRemoveAccountRequest_MatKhauPhaiLaKieuChuoi'),
		];
	}
} // End class
