<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction;

use App\Exceptions\BusinessException;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\SA\BuildBankAccTypeSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\GetMNPAccessTokenSubAction;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoListProfileRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction\SortHoSoSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction\DamBaoCoCccdSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoGetConfigAction\Mpos360RequestChangeInfoGetConfigAction;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction\BuildChoiceOptionSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction\MnpGetMerchantProfileSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction\KiemTraDaDuHoSoHayChuaSubAction;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction\BuildListNganHangCoLogoSubAction;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction\XuLyChoPhepQuyenTaoYcNguoiDaiDienSubAction;
use App\Modules\Merchant\Model\Setting;

class Mpos360RequestChangeInfoListProfileAction
{
	private array $__listEmailDuocTaoYc = [];

	public function run(Mpos360RequestChangeInfoListProfileRequest $request)
	{
		$this->__listEmailDuocTaoYc = $this->__getMerchantEmailTestCoTheDuocTaoYc();

		$deviceSession = $request->getCurrentDeviceSession();
		// if (!$deviceSession->isMposActiveMerchant()) {
		// 	throw new BusinessException('Tài khoản của bạn đang ở trạng thái CHỜ THIẾT LẬP THÔNG TIN. Vui lòng liên hệ chuyên viên hỗ trợ để được xử lý');
		// }
		
		$merchantId = $deviceSession->getMerchantId();

		$deviceSessionWithToken = app(GetMNPAccessTokenSubAction::class)->run($deviceSession);
		
		$mnpGetConfig = app(Mpos360RequestChangeInfoGetConfigAction::class)->run($request);

		if (empty($mnpGetConfig['data'])) {
			throw new BusinessException('Lỗi không get được config của mnp');
		};

		$merchantId = $deviceSessionWithToken->getMerchantId();

		$mnpMerchantDetail = app(MnpGetMerchantProfileSubAction::class)->run(
			$merchantId, 
			$deviceSessionWithToken->mnp_token
		);

		$groupProfiles = [];

		foreach ($mnpGetConfig['data']['typeChangeInfo'] as $groupType) {
			$code = $groupType['code'];
			$groupProfiles['groups'][$code] = $groupType;
			
			
			// Add hồ sơ vào nhóm
			foreach ($mnpMerchantDetail['data'] as $profileKey => $profileItem) {
				$profileItem['label'] = vmsg($profileItem['label']);
				$profileItem['isChanged'] = '0';
				$profileItem['other_data'] = (object) [];

				// ngân hàng
				if ($profileKey == 'bank') {
					
					$profileItem['other_data'] = [
						'list' => $mnpGetConfig['data']['banks'],
						'list_with_logo' => app(BuildListNganHangCoLogoSubAction::class)->run($mnpGetConfig['data']['banks'])
					];
				}

				// loại tài khoản
				if ($profileKey == 'bankAccType') {
					/**{
							"code": "MC_COMPANY",
							"name": "Công ty"
					}, */

					$mcBankAccTypeGenForApp = [];

					foreach ($mnpGetConfig['data']['mcBankAccType'] as $item) {
						$mcBankAccTypeGenForApp[$item['code']] = $item['name'];
					}

					$profileItem['other_data'] = [
						'list' => $mcBankAccTypeGenForApp
					];
				}

				// tỉnh/thành phố
				if ($profileKey == 'bankCity') {
					$profileItem['other_data'] = [
						'list' => $mnpGetConfig['data']['cities']
					];
				}

				// Gom nhóm
				if ($profileItem['group'] == $code) {
					$profileItem['profileKey'] = $profileKey;
					$groupProfiles['groups'][$code]['profiles'][] = $profileItem;
				}
			}

			// Đổi thông tin ngân hàng
			if ($code == 'CHANGE_BANK_ACCOUN_INFO') {
				$groupProfiles['groups'][$code]['other_data'] = [
					'banks' => $mnpGetConfig['data']['banks'],
					'cities' => $mnpGetConfig['data']['cities']
				];
			}
		}

		$bankAccountTypeSinhMoi = app(BuildBankAccTypeSubAction::class)->run(
			$groupProfiles, 
			$mnpMerchantDetail['data']['customerType'],
			$mnpGetConfig['data']['mcBankAccType']
		);

		if ($bankAccountTypeSinhMoi['is_need_push_profile']) {
			$groupProfiles['groups']['CHANGE_BANK_ACCOUN_INFO']['profiles'][] = $bankAccountTypeSinhMoi['bankAccTypeItem'];
		}

		// Làm đổi thông CCCD hoặc ng đại diện mới thì phải có cái này
		app(DamBaoCoCccdSubAction::class)->run($groupProfiles['groups']['CHANGE_REPRESENT_INFO']);

		$groupProfiles['groups'] = array_values($groupProfiles['groups']);

		foreach ($groupProfiles['groups'] as $group) {
			app(KiemTraDaDuHoSoHayChuaSubAction::class)->run($group['code'], $group['name'], $group['profiles']);
		}

		$returnData = app(SortHoSoSubAction::class)->run($groupProfiles);
		
		foreach ($returnData['groups'] as &$group) {
			$code = $group['code'];
			$group['is_can_make_request'] = 0;
			
			if ($code == 'CHANGE_BANK_ACCOUN_INFO') {
				$group['name'] = vmsg('TKNH nhận tiền');
				$group['choice'] = app(BuildChoiceOptionSubAction::class)->run(
					$code,
					$mnpMerchantDetail
				);
				$group['is_can_make_request'] = 1;
			}

			if ($code == 'CHANGE_REPRESENT_INFO') {
				$group['name'] = vmsg('Người đại diện ký HĐ');
				$group['choice'] = app(BuildChoiceOptionSubAction::class)->run(
					$code,
					$mnpMerchantDetail
				);
				$group['is_can_make_request'] = app(XuLyChoPhepQuyenTaoYcNguoiDaiDienSubAction::class)->run(
					$mnpMerchantDetail
				);
			}

			if ($code == 'CHANGE_MPOS_ACCOUNT_INFO') {
				$group['name'] = 'Thông tin tài khoản ' . __('setting.appName');
				$group['choice'] = app(BuildChoiceOptionSubAction::class)->run(
					$code,
					$mnpMerchantDetail
				);
			}

			$group['is_can_make_request'] = 0; // off het

			$merchantEmail = $deviceSession->getMerchantEmail();
			if (in_array($merchantEmail, $this->__listEmailDuocTaoYc)) {
				$group['is_can_make_request'] = 1; // off het
			}
		}

		return $returnData;
	}

	private function __getMerchantEmailTestCoTheDuocTaoYc() {
		$setting = Setting::query()->firstWhere(['key' => 'LIST_MC_EMAIL_DUOC_TAO_YC_DOI_THONG_TIN']);
		if (!$setting) {
			return [];
		}
		$listEmail = json_decode($setting->value, true);
		return $listEmail;
	}
} // End clas
// rút gọn tên nhóm
			