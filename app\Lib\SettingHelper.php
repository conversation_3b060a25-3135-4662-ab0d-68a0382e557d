<?php

namespace App\Lib;

use App\Modules\Merchant\Model\Setting;

class SettingHelper
{
	public static $soGiayCountDown = null;

	public static function getSoGiayCountdown(): int
	{
		if (self::$soGiayCountDown) {
			return self::$soGiayCountDown;
		}

		$countDownGetNewOtpSetting = Setting::query()->firstWhere(['key' => 'COUNTDOWN_GET_NEW_OTP']);
		$seconds = optional($countDownGetNewOtpSetting)->value ?? 90;
		self::$soGiayCountDown = intval($seconds);
		return self::$soGiayCountDown;
	}

	public static function getSettingVideo(): array {
		$setting = Setting::query()->firstWhere(['key' => 'CAU_HINH_QUAY_VIDEO_UPLOAD']);
		$value = optional($setting)->value;
		
		if (empty($value)) {
			return [
				'total_second' => 60,
				'total_filesize' => 25,
				'filesize_unit' => 'MB'
			];
		}

		$valueAsArray = json_decode($value, true);
		return [
			'total_second' => $valueAsArray['total_second'],
			'total_filesize' => $valueAsArray['total_filesize'],
			'filesize_unit' =>  $valueAsArray['filesize_unit'],
		];
	}

	public static function hasBetaTest(Setting $settingBetaTest, string $functionCode='DangKyDichVu', $email): bool {
		$settingValue = json_decode($settingBetaTest->value, true);
		$func = collect($settingValue['funcs'])->where('code', $functionCode)->first();
		
		if (!empty($func['is_golive'])) {
			return true;
		}

		array_map(function ($item) {
			return strtolower($item);
		}, $settingValue['emails']);
		
		return in_array(strtolower($email), $settingValue['emails']);
	}

	public static function isForcePushTingTing(string $username=''): bool {
		$settingForcePushTingTing = Setting::query()->firstWhere(['key' => 'FORCE_SYNC_DEVICE_TO_TINGTING_SYSTEM']);
		if (!$settingForcePushTingTing) {
			return false;
		}

		$settingValue = json_decode($settingForcePushTingTing->value, true);
		return $settingValue['all'] == 'YES' || in_array($username, $settingValue['usernames']);
	}
} // End class