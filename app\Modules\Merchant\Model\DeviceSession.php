<?php

namespace App\Modules\Merchant\Model;

use App\User;
use Carbon\Carbon;
use Illuminate\Notifications\Notifiable;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Foundation\Auth\User as Authenticatable;

class DeviceSession extends Authenticatable
{
  protected $table = 'device_session';
  protected $guarded = [];
  public $timestamps = false;

  protected $dates = [];

  protected $hidden = [
		'mpos_token',
		'mnp_token',
		'api_secret'
	];

	public function mpos360User() {
		return $this->hasOne(Mpos360User::class, 'id', 'user_id');
	}

  public function partnerConfig() {
    return $this->belongsTo(PartnerConfig::class, 'partner_config_id', 'id');
  }

  public function isExprired(): bool {
    $expireTime = Carbon::createFromTimestamp($this->time_expired);
    return empty($this->time_expired) || now()->gt($expireTime);
  }

  public static function getCurrentMerchantUserSession(string $apiKey='') {
    return self::where('api_key', $apiKey)->first();
  }

  public function getProfileUserInfo(string $key='') {
    $profileUserInfo = json_decode($this->profile_users_info, true);
    return $profileUserInfo[$key] ?? '';
  }

  public function setExpired() {
    $this->time_expired = now()->timestamp;
    $this->save();
    return $this;
  }


	public static function getBlankDeviceSession() {
		return new DeviceSession([
			'id' => '',
			'device_id' => '',
			'user_id' => '',
			'api_key' => '',
			'api_secret' => '',
			'time_expired' => '0',
			'time_created' => '0',
			'time_updated' => '0',
			'email' => ''
		]);
	}

	public function visibleAndDecryptSecret(): self {
		$this->makeVisible(['api_secret']);
		return $this;
	}
	

	public function getMerchantEmail() {
		$merchantData = json_decode($this->mpos360User->data_users, true);
		
		if (!empty($merchantData['merchantEmail'])) {
			return $merchantData['merchantEmail'];
		}
		
		return $merchantData['username'];
	}

	public function getMerchantUserName() {
		$merchantData = json_decode($this->mpos360User->data_users, true);
		return $merchantData['username'];
	}

	public function getMerchantName() {
		$merchantData = json_decode($this->mpos360User->data_users, true);
		return $merchantData['merchantName'];
	}

	public function getMerchantId() {
		$merchantId = $this->mpos360User->merchant_id;
		return $merchantId;
	}

	public function getMposToken(): string {
		$this->makeVisible(['mpos_token']);
		return $this->mpos_token;
	}

	public function isEmptyMnpToken(): bool {
		$this->makeVisible(['mnp_token']);
		return empty($this->mnp_token);
	}

	public function getListMobileUser(): array {
		$dataMerchant = json_decode($this->mpos360User->data_merchant, true);
		if (empty($dataMerchant['data']['mobileUserDTO'])) {
			return [];
		}

		return $dataMerchant['data']['mobileUserDTO'];
	}

	public function isChoThietLapThongTin(): bool {
		$dataMerchant = json_decode($this->mpos360User->data_merchant, true);
		if (!empty($dataMerchant['data']['merchantStatus'])) {
			return $dataMerchant['data']['merchantStatus'] == 'PENDING_INFORMATION' || 
			(
				$dataMerchant['data']['merchantStatus'] == 'ACTIVE' 
				&& empty($dataMerchant['data']['countSoundBoxOfMc']) 
				&& empty($dataMerchant['data']['countReaderBoxOfMc']) 
			);
		}
		return false;
	}

	public function isMposActiveMerchant(): bool {
		$dataMerchant = json_decode($this->mpos360User->data_merchant, true);

		if (!empty($dataMerchant['data']['merchantStatus'])) {
			if ($dataMerchant['data']['merchantStatus'] == 'ACTIVE') {
				return true;
			}

			return false;
		}

		return false;
	}

	public function isStatusChoThietLapThongTin(): bool {
		$dataMerchant = json_decode($this->mpos360User->data_merchant, true);
		
		if (!empty($dataMerchant['data']['merchantStatus'])) {
			return $dataMerchant['data']['merchantStatus'] == 'PENDING_INFORMATION';
		}
		return false;;
	}
} // End class
