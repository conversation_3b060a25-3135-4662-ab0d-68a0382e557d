<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestCronPushRecordAction;

use App\Exceptions\BusinessException;
use App\Lib\Helper;
use App\Lib\partner\MNP;
use App\Lib\TelegramAlert;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestCronPushRecordAction\SubAction\GetMnpTokenByMerchantIdSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestCronPushRecordAction\SubAction\GetCccdInfoByQtsRequestIdSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestCronPushRecordAction\SubAction\ChuanHoaProfileTruocKhiDaySangMNPSubAction;

class Mpos360MerchantRequestCronPushRecordAction
{
	public MNP $mnp;

	public GetCccdInfoByQtsRequestIdSubAction $action;
	private array $__ids = [];

	public function __construct(MNP $mnp, GetCccdInfoByQtsRequestIdSubAction $action)
	{
		$this->mnp = $mnp;
		$this->action = $action;
	}

	public function run(Request $request)
	{
		$returnData = [];

		for ($i = 1; $i < 10; $i++) {
			try {
				$result = $this->handle();

				if ($result == 'EMPTY') {
					$returnData[] = 'EMPTY';
					break;
				}

				$returnData[] = optional($result)->id;
			} catch (\Throwable $th) {
				mylog(['Loi xu ly ban ghi' => Helper::traceError($th)]);
				TelegramAlert::sendMessage(Helper::traceError($th));
				// throw $th;
			}
		}

		return $returnData;
	}

	public function handle()
	{
		$mpos360McRequest = Mpos360MerchantRequest::query()
																							->where('status', Mpos360Enum::MPOS360_MC_REQUEST_STT_CHUA_GUI)
																							->where('time_expired', '>', now()->timestamp)
																							->where('version', 1);

		if (!empty($this->__ids)) {
			$mpos360McRequest = $mpos360McRequest->whereNotIn('id', $this->__ids);
		}

		$mpos360McRequest = $mpos360McRequest->first();

		if (!$mpos360McRequest) {
			mylog(['EMPTY' => 'khong co thong tin']);
			return 'EMPTY';
		}

		$this->__ids[] = $mpos360McRequest->id;

		mylog(['YeuCauDoiThongTin' => $mpos360McRequest]);

		// update len dang gui
		$updateLenDangGui = Mpos360MerchantRequest::query()
			->where('id', $mpos360McRequest->id)
			->where('status', Mpos360Enum::MPOS360_MC_REQUEST_STT_CHUA_GUI)
			->update(['status' => Mpos360Enum::MPOS360_MC_REQUEST_STT_DANG_GUI]);

		if (!$updateLenDangGui) {
			mylog(['Loi' => 'cap nhat thanh dang gui khong thanh cong']);
			throw new BusinessException('Loi cap nhat ban ghi thanh `DANG GUI` khong thanh cong');
		}

		$mpos360McRequest = Mpos360MerchantRequest::query()->find($mpos360McRequest->id);

		if ($mpos360McRequest->status != Mpos360Enum::MPOS360_MC_REQUEST_STT_DANG_GUI) {
			throw new BusinessException('Loi ban ghi khong o trang thai dang gui');
		}

		DB::beginTransaction();
		try {
			$dataRequest = json_decode($mpos360McRequest->data_request, true);
			$isScanBuoc3KhopChungThuc = $dataRequest[0]['scan_method']['QTS']['other_data']['is_matching_facescan'];

			// Kết quả quét mặt là pass
			if ( !empty($isScanBuoc3KhopChungThuc) ) {
				$dataRequest = json_decode($mpos360McRequest->data_request, true);

				$mposId = $mpos360McRequest->merchant_id;
				$typeChanges = $dataRequest[0]['type'];
				$token = app(GetMnpTokenByMerchantIdSubAction::class)->run($mposId);
				$profiles = $dataRequest[0]['profiles'];

				$profiles = app(ChuanHoaProfileTruocKhiDaySangMNPSubAction::class)->run($profiles, $mpos360McRequest);

				$updateResult = $this->mnp->requestUpdateData(
					$mposId,
					$typeChanges,
					$token,
					$profiles
				);

				$dataRequest[0]['profiles'] = $profiles;

				// cập nhật thành công
				if (
					!empty($updateResult['status']) && !empty($updateResult['data'])
				) {
					$mpos360McRequest->status = Mpos360Enum::MPOS360_MC_REQUEST_STT_DA_GUI_SANG_MNP;
					$mpos360McRequest->mynextpay_id = $updateResult['data'];
					$mpos360McRequest->data_request = json_encode($dataRequest);
					$mpos360McRequest->order_code = $updateResult['meta']['dataCode'] ?? '';
					$mpos360McRequest->comment = '{}';
					$r = $mpos360McRequest->save();

					if (!$r) {
						throw new BusinessException('Lỗi không cập nhật được bản ghi là `ĐÃ GỬI`');
					}

					DB::commit();
					return $mpos360McRequest;
				}

				// cập nhật bị lỗi
				$danhDauLaLoi = $this->danhDauLaCapNhatLoi(
					$mpos360McRequest, 
					$updateResult['message'] ?? 'Lỗi mnp không xác định',
					$dataRequest
				);
				DB::commit();
				return $danhDauLaLoi;
			}

			// Không khớp chứng thực -> đánh dấu là cập nhật lỗi
			$danhDauLaLoi = $this->danhDauLaCapNhatLoi(
				$mpos360McRequest, 
				'Lỗi không trùng khớp nhận dạng cccd',
				$dataRequest
			);

			DB::commit();
			return $danhDauLaLoi;
		} catch (\Throwable $th) {
			mylog(['Error' => Helper::traceError($th)]);
			DB::rollBack();

			$capNhatVeChuaXuLy = Mpos360MerchantRequest::query()
																								 ->where('id', $mpos360McRequest->id)
																								 ->where('status', Mpos360Enum::MPOS360_MC_REQUEST_STT_DANG_GUI)
																								 ->update(['status' => Mpos360Enum::MPOS360_MC_REQUEST_STT_CHUA_GUI]);

			if (!$capNhatVeChuaXuLy) {
				mylog(['Loi cap nhat ve chua xu ly' => $capNhatVeChuaXuLy]);
			}

			throw $th;																								
		}
	} // End method

	public function danhDauLaCapNhatLoi(Mpos360MerchantRequest $mpos360McRequest, string $message = '', $dataRequest=[]): Mpos360MerchantRequest
	{
		$mpos360McRequest->status = Mpos360Enum::MPOS360_MC_REQUEST_STT_CAP_NHAT_LOI;
		$mpos360McRequest->comment = json_encode(['error' => $message]);
		
		if (!empty($dataRequest)) {
			$mpos360McRequest->data_request = json_encode($dataRequest);
		}
		
		$r = $mpos360McRequest->save();

		if (!$r) {
			throw new BusinessException('Lỗi không cập nhật được về trạng thái cuối');
		}

		return $mpos360McRequest;
	}
} // End class
