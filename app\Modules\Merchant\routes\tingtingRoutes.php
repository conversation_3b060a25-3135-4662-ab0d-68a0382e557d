<?php

use Illuminate\Support\Facades\Route;
use App\Http\Middleware\MakeSureThatRequestIsJsonMiddleware;
use App\Modules\Merchant\Controllers\Home\Mpos360HomeController;
use App\Modules\Merchant\Controllers\Device\DeviceTingTingController;
use App\Modules\Merchant\Controllers\TingBox\Mpos360TingBoxController;
use App\Modules\Merchant\Controllers\Device\DeviceTingTingPushSerialController;
use App\Modules\Merchant\Controllers\Authen\Mpos360\Mpos360AuthRegisterController;
use App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360SendOtpAction\Mpos360RegisterSendOtpAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360SubmitFormAuthenOtpAction\Mpos360SubmitFormVerifyOtpAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360SubmitFormAuthenAccountStep2Action\Mpos360SubmitFormAuthenAccountStep2Action;


Route::group([
	'middleware' => [MakeSureThatRequestIsJsonMiddleware::class]
], function () {

	Route::post('/Mpos360DeviceGetTypeReceiverTingTing', [
		'uses' => DeviceTingTingController::class . '@Mpos360DeviceGetTypeReceiverTingTing',
		'as' => 'Mpos360DeviceGetTypeReceiverTingTingAction'
	])->middleware('validateHash:email');

	Route::post('/Mpos360DeviceUpdateTypeReceiverTingTing', [
		'uses' => DeviceTingTingController::class . '@Mpos360DeviceUpdateTypeReceiverTingTing',
		'as' => 'Mpos360DeviceUpdateTypeReceiverTingTingAction'
	])->middleware('validateHash:email|type_receiver_tingting');


	Route::post('/Mpos360SyncPartnerToken', [
		'uses' => Mpos360HomeController::class . '@Mpos360SyncPartnerToken',
		'as' => 'Mpos360SyncPartnerTokenAction'
	])->middleware('validateHash:email');
/*---------------------------Đăng ký tài khoản tingbox---------------------*/
	// submit form đăng ký tài khoản
	Route::post('/Mpos360SubmitFormRegister', [
		'uses' => Mpos360AuthRegisterController::class . '@Mpos360SubmitFormRegister',
		'as' => 'Mpos360SubmitFormRegisterAction'
	])->middleware('checkSumForAnyMobile:username|password|password_confirmation');

	Route::post('/Mpos360SubmitFormRegisterV2', [
		'uses' => Mpos360AuthRegisterController::class . '@Mpos360SubmitFormRegister',
		'as' => 'Mpos360SubmitFormRegisterAction'
	]);

	// gửi mã otp
	Route::post('/Mpos360RegisterSendOtp', [
		'uses' => Mpos360AuthRegisterController::class . '@Mpos360RegisterSendOtp',
		'as' => 'Mpos360RegisterSendOtpAction'
	])->middleware('checkSumForAnyMobile:username|channel|otp_id');

	// Submit form verify otp
	Route::post('/Mpos360SubmitFormVerifyOtp', [
		'uses' => Mpos360AuthRegisterController::class . '@Mpos360SubmitFormVerifyOtp',
		'as' => 'Mpos360SubmitFormVerifyOtpAction'
	])->middleware('checkSumForAnyMobile:username|password|otp|otp_id');

	Route::post('/Mpos360SubmitFormVerifyOtpV2', [
		'uses' => Mpos360AuthRegisterController::class . '@Mpos360SubmitFormVerifyOtpV2',
		'as' => 'Mpos360SubmitFormVerifyOtpV2Action'
	])->middleware('checkSumForAnyMobile:username');

	// Submit form nhập CCCD có thêm yếu tố ảnh CCCD mặt trước & CCCD mặt sau
	Route::any('/Mpos360RegisterUpdateCccdInfo', [
		'uses' => Mpos360AuthRegisterController::class . '@Mpos360RegisterUpdateCccdInfo',
		'as' => 'Mpos360SubmitFormAuthenAccountStep2Action'
	])->middleware('checkSumForAnyMobile:username|merchant_id|fullname|cccd|gender|address|birthday|cccd_image_before|cccd_image_after');

	Route::post('/Mpos360CheckProgressKhaiBaoTingBox', [
		'uses' => Mpos360TingBoxController::class . '@Mpos360CheckProgressKhaiBaoTingBox',
		'as' => 'Mpos360CheckProgressKhaiBaoTingBoxAction'
	])->middleware('checkSumForAnyMobile:username|merchantId');

	Route::post('/Mpos360GetStepKhaiBaoTingBox', [
		'uses' => Mpos360TingBoxController::class . '@Mpos360GetStepKhaiBaoTingBox',
		'as' => 'Mpos360GetStepKhaiBaoTingBoxAction'
	])->middleware('checkSumForAnyMobile:username|merchantId');

	Route::post('/Mpos360GetStepKhaiBaoTingBoxVamc', [
		'uses' => Mpos360TingBoxController::class . '@Mpos360GetStepKhaiBaoTingBoxVamc',
		'as' => 'Mpos360GetStepKhaiBaoTingBoxVamcAction'
	]);
	// ->middleware('checkSumForAnyMobile:username|merchantId');

	Route::post('/Mpos360GetFormTingBoxStep1', [
		'uses' => Mpos360TingBoxController::class . '@Mpos360GetFormTingBoxStep1',
		'as' => 'Mpos360GetFormTingBoxStep1Action'
	])->middleware('checkSumForAnyMobile:username|merchantId');

	Route::post('/Mpos360GetQuanHuyenTuMaTinhThanhStep1', [
		'uses' => Mpos360TingBoxController::class . '@Mpos360GetQuanHuyenTuMaTinhThanhStep1',
		'as' => 'Mpos360GetQuanHuyenTuMaTinhThanhStep1'
	])->middleware('checkSumForAnyMobile:username|merchantId|cityId');
		
	// Lưu thông tin cửa hàng
	Route::post('/Mpos360SaveCuaHangStep1', [
		'uses' => Mpos360TingBoxController::class . '@Mpos360SaveCuaHangStep1',
		'as' => 'Mpos360SaveCuaHangStep1Action'
	])->middleware('checkSumForAnyMobile:username|merchantId|shopId|shopName|industryId|cityId|districtId|address|qrDisplayName');

	Route::post('/Mpos360SaveCuaHangStep1V2', [
		'uses' => Mpos360TingBoxController::class . '@Mpos360SaveCuaHangStep1V2',
		'as' => 'Mpos360SaveCuaHangStep1V2Action'
	])->middleware('checkSumForAnyMobile:merchantId');

	Route::post('/Mpos360SaveThietBiTingBoxStep2', [
		'uses' => Mpos360TingBoxController::class . '@Mpos360SaveThietBiTingBoxStep2',
		'as' => 'Mpos360SaveThietBiTingBoxStep2Action'
	])->middleware('checkSumForAnyMobile:username|merchantId|tingboxSerial|shopId');

	Route::post('/Mpos360SaveThietBiTingBoxStep2V2', [
		'uses' => Mpos360TingBoxController::class . '@Mpos360SaveThietBiTingBoxStep2V2',
		'as' => 'Mpos360SaveThietBiTingBoxStep2V2Action'
	])->middleware('checkSumForAnyMobile:merchantId');

	Route::post('/Mpos360DetailThietBiTingBoxStep2', [
		'uses' => Mpos360TingBoxController::class . '@Mpos360DetailThietBiTingBoxStep2',
		'as' => 'Mpos360DetailThietBiTingBoxStep2Action'
	])->middleware('checkSumForAnyMobile:username|tingboxSerial');

	Route::post('/Mpos360GetFormKhaiBaoTknhStep3', [
		'uses' => Mpos360TingBoxController::class . '@Mpos360GetFormKhaiBaoTknhStep3',
		'as' => 'Mpos360GetFormKhaiBaoTknhStep3Action'
	])->middleware('checkSumForAnyMobile:username|merchantId');

	Route::post('/Mpos360CheckTknhKhaiBaoStep3', [
		'uses' => Mpos360TingBoxController::class . '@Mpos360CheckTknhKhaiBaoStep3',
		'as' => 'Mpos360CheckTknhKhaiBaoStep3Action'
	])->middleware('checkSumForAnyMobile:username|bankAccountHolder|bankAccountNumber|bankId');


	Route::post('/Mpos360SaveTknhStep3', [
		'uses' => Mpos360TingBoxController::class . '@Mpos360SaveTknhStep3',
		'as' => 'Mpos360SaveTknhStep3Action'
	])->middleware('checkSumForAnyMobile:username|merchantId|bankAccountHolder|bankAccountNumber|bankId');

	Route::post('/Mpos360GetCauHinhByMerchant', [
		'uses' => Mpos360TingBoxController::class . '@Mpos360GetCauHinhByMerchant',
		'as' => 'Mpos360GetCauHinhByMerchantAction'
	])->middleware('checkSumForAnyMobile:merchantId');
});

Route::any('/Mpos360PushSerialToTingTingSystem', [
	'uses' => DeviceTingTingPushSerialController::class . '@Mpos360PushSerialToTingTingSystem',
	'as' => 'Mpos360PushSerialToTingTingSystemAction'
])->middleware('validateHash:abcdef'); 

Route::get('/Mpos360TingBoxDeviceHuongDanSuDung', [
	'uses' =>  Mpos360TingBoxController::class . '@Mpos360TingBoxDeviceHuongDanSuDung',
	'as' => 'Mpos360TingBoxDeviceHuongDanSuDungAction'
]);

Route::get('/Mpos360TingBoxDeviceHuongDanLayMaTb', function () {
	return view('TingBox.HuongDanLayMaThietBi');
})->name('Mpos360TingBoxDeviceHuongDanLayMaTb');

Route::get('/Mpos360HuongDanQuetMaQRNhanTien', function () {
	return view('TingBox.HuongDanQuetMaQRNhanTien');
})->name('Mpos360HuongDanQuetMaQRNhanTien');

Route::any('/Mpos360RegisterUpload', [
	'uses' => Mpos360AuthRegisterController::class . '@Mpos360RegisterUpload',
	'as' => 'Mpos360RegisterUploadAction'
])->middleware('checkSumForAnyMobileUpload:merchantId');

