<!-- <html lang="zxx">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title><PERSON><PERSON><PERSON> k<PERSON></title>
  <link rel="icon" type="image/png" href="{{ cumtomAsset('assets/img/favicon-mpos.svg') }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous">
    </script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.0/jquery.min.js"></script>
    <link rel="stylesheet" href="{{ cumtomAsset('assets/css/style.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

</head> -->

<!-- <body> -->

<section class="ftco-section login-wap">

  <div class="wrapper-page">
    <div class="head-wrap">
      <label>Đăng ký tài khoản</label>
      <span><img src="assets/img/logo-mpos360.svg"></span>
    </div>
    <div class="login-wrap">
      <p class="BE-bold f18 text-center">Xác thực số điện thoại</p>
      <p class="f16 text-center">Mã xác thực được gửi đến tài khoản {{ $channel }}<span class="BE-bold"> {{ $phone }},</span>
        <span class="time-end" style="display: none;">mã đã hết hiệu lực</span>
        <span class="time-start">mã có hiệu lực trong vòng <span class="BE-bold">{{ $newTimeContext }}</span></span>
      </p>
      <div class="notifi-item">
        <div class="form-floating mb-3">
          <input type="tel" class="form-control" id="otp-input" placeholder="" autocomplete="off">
          <div class="error-message text-danger mt-1" id="error-otp"></div>
          <label for="floatingtk">Mã OTP</label>
          <div class="d-flex align-items-center justify-content-between mt-2">
            <div class="times">Gửi lại sau <span id="countdown">{{ $count_time }}</span>s</div>
            <div class="clearfix">
              <a href="#" class="cl890" data-bs-toggle="modal" data-bs-target="#no-received">Không nhận được ? </a>
            </div>
          </div>
        </div>

        <div class="d-flex mt-5">
          <button type="button" id="verify-otp" class="text-center btn-blue w-100" style="border: none;" onclick="onVerifyOtp(this)">Tiếp tục</button>
        </div>
      </div>

    </div>
  </div>
  <footer>
    <ul>
      <li>
        <a href="tel:1900636488">
          <img src="assets/img/ic-phone.svg">
          1900636488
        </a>
      </li>
      <li>
        <a href="https://zalo.me/2910216453995933825">
          <img src="assets/img/ic-zalo.svg">
          mPOS Việt Nam
        </a>
      </li>
      <li>
        <a href="mailto:">
          <img src="assets/img/ic-mail.svg">
          <EMAIL>
        </a>
      </li>
    </ul>
  </footer>
</section>


<script>
  $(document).ready(function() {

    const APP_URL = "";
    let countdown = "{{ $count_time }}";

    const interval = setInterval(function() {
      countdown--;
      $("#countdown").text(countdown);

      if (countdown <= 0) {
        clearInterval(interval);
        $(".times").html(`<a href="#" id="resend-otp">Lấy mã khác</a>`);
        $(".time-start").removeAttr("style").hide();
        $(".time-end").show();
      }
    }, 1000); // 1 giây = 1000ms

    $(document).on('click', '#no-received-close', function(e) {
      e.preventDefault();
      $("#no-received").modal('hide');
    });

    const $otpInput = $("#otp-input");
    const $errorOtp = $("#error-otp");

    function validateOTP() {
      const otp = $otpInput.val().trim();
      if (!/^[0-9]{6}$/.test(otp)) {
        $errorOtp.text("Mã xác thực không đúng định dạng");
        $otpInput.addClass("is-invalid");
      } else {
        $errorOtp.text("");
        $otpInput.removeClass("is-invalid");
      }
    }

    $otpInput.on("input", validateOTP);

    
		

    // resend otp
    $(document).on("click", '#resend-otp', function(e) {
      e.preventDefault();

      const formData = {
        signed: "{{ $data }}",
        otp_id: "{{ $otp_id }}",
      };
      $('#loadingPage').addClass('se-pre-con');

      $.ajax({
        url: APP_URL + "/TBWebMpos360SubmitDangKy",
        type: "POST",
        headers: {
          "X-CSRF-TOKEN": "{{ csrf_token() }}",
        },
        contentType: "application/json",
        data: JSON.stringify(formData),
        success: function(data) {
          if (data.success) {
						$('#incorrect-code').modal('hide');
            $("#sendOtpModal").modal('show')
              .find("#sendOtpModalContent")
              .html(data.html);
							clearInterval(interval);
          } else {
            alert(data.message);
           
          }
          $('#loadingPage').removeClass('se-pre-con');
        },
        error: function(error) {
          $('#loadingPage').removeClass('se-pre-con');
          alert("Có lỗi xảy ra, Hãy thông báo kỹ thuật hỗ trợ xử lý.");
        },
      });
    });

    $(document).on('click', '.close-incorrect-code', function(e) {
      e.preventDefault();
      $('#incorrect-code').modal('hide')
    });
  });
</script>

<script>
	function onVerifyOtp(element) {
		if ($("#otp-input").val().length == 0) {
			return alert("Bạn cần nhập mã OTP");
		}

		const formData = {
			otp: $("#otp-input").val().trim(),
			signed: "{{ $data }}",
			otp_id: "{{ $otp_id }}",
			phone: "{{ $phone }}",
		};

		$('#loadingPage').addClass('se-pre-con');
		
		$.ajax({
			url: "/TBWebMpos360VerifyOtp",
			type: "POST",
			headers: {
				"X-CSRF-TOKEN": "{{ csrf_token() }}",
			},
			contentType: "application/json",
			data: JSON.stringify(formData),
			success: function(data) {
				if (data.success) {
					window.location.href = data.link;
				} else {
					alert(data.message);
				}
				$('#loadingPage').removeClass('se-pre-con');
			},
			error: function(error) {
				$('#loadingPage').removeClass('se-pre-con');
				alert("Có lỗi xảy ra, Hãy thông báo kỹ thuật hỗ trợ xử lý.");
			},
		});		
	}
</script>
