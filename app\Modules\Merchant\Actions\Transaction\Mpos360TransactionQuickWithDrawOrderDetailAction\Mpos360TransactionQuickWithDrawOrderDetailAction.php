<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransactionQuickWithDrawOrderDetailAction;

use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionQuickWithDrawOrderDetailRequest;
use App\Lib\Helper;
use App\Modules\Merchant\Actions\Transaction\subAction\Mpos360TransactionDefineConfigSubAction;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransactionQuickWithDrawOrderDetailAction\SubAction\Mpos360TransactionQuickWithDrawOrderDetailSubAction;
class Mpos360TransactionQuickWithDrawOrderDetailAction
{
	public $_type = 'DETAIL_QUICKWITHDRAW';
	public function run(Mpos360TransactionQuickWithDrawOrderDetailRequest $request)
	{

		$deviceSession = $request->getCurrentDeviceSession();
		$params = [
			'typeTransaction' => $this->_type,
			'merchantFk' => $deviceSession->getMerchantId(),
			'id' => $request->json('data.order_code', ''),
			'tokenLogin' => $deviceSession->getMposToken()
		];
		$listTransaction = app(Mpos360TransactionDefineConfigSubAction::class)->requestTransDetail($params);
		return (new Mpos360TransactionQuickWithDrawOrderDetailSubAction)->__convertData($listTransaction);
	}
}
