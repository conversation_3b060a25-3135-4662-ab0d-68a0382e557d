<?php

namespace App\Modules\Merchant\Controllers\Transaction;

use App\Lib\Helper;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionQuickWithDrawOrderListRequest;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionQuickWithDrawOrderDetailRequest;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransactionQuickWithDrawOrderListAction\Mpos360TransactionQuickWithDrawOrderListAction;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransactionQuickWithDrawOrderDetailAction\Mpos360TransactionQuickWithDrawOrderDetailAction;

// xử lý các yêu cầu rút tiền nhanh
class Mpos360TransactionQuickWithDrawOrderController extends Controller
{
	public function Mpos360TransactionQuickWithDrawOrderList(Mpos360TransactionQuickWithDrawOrderListRequest $request)
	{
		try {
			$result = app(Mpos360TransactionQuickWithDrawOrderListAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360TransactionQuickWithDrawOrderDetail(Mpos360TransactionQuickWithDrawOrderDetailRequest $request)
	{
		try {
			$result = app(Mpos360TransactionQuickWithDrawOrderDetailAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
} // End class
