<?php

namespace App\Modules\Merchant\Actions\TingBox\Mpos360GetCauHinhByMerchantAction;

use App\Lib\Logs;
use App\Lib\Helper;
use Illuminate\Support\Arr;
use App\Lib\partner\MposAWS;
use App\Lib\partner\SoundBox;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\CacheAction;
use App\Modules\Merchant\Enums\TingTingEnum;
use App\Modules\Merchant\Requests\TingBox\Mpos360GetCauHinhByMerchantRequest;
use App\Modules\Merchant\Actions\TingBox\Mpos360GetCauHinhByMerchantAction\SubAction\EmptyCauHinhMcSubAction;
use App\Modules\Merchant\Actions\TingBox\Mpos360GetCauHinhByMerchantAction\Mpos360GetCauHinhByMerchantV2Action;

class Mpos360GetCauHinhByMerchantAction
{
	public MposAWS $mposAws;

	public function __construct(MposAWS $mposAWS)
	{
		$this->mposAws = $mposAWS;
	}


	public function run(Mpos360GetCauHinhByMerchantRequest $request)
	{
		if ($request->json('data.versionChange')) {
			return app(Mpos360GetCauHinhByMerchantV2Action::class)->run($request);
		}
		
		Logs::writeInfo("Mpos360GetCauHinhByMerchantAction", $request->json('data'));

		$returnData = [
			'tingbox' => [
				'currentMode' => 'VANP',
				'qrDefault' => (object)[],
				'tingting' => [
					'listMobileUser' => []
				]
			]
		];

		$merchantId = $request->json('data.merchantId');
		$clientId = sprintf('%s_%s', $merchantId, $request->json('data.deviceToken'));

		$tingting = [
			'merchantId' => $merchantId,
			'listMobileUser' => [],
			'clientId' => $clientId,
			'topic' => sprintf('/APP360/%s/data', $clientId),
			'mqAuthen' => [
				'userName' => env('SOUNDBOX_MQ_USERNAME'),
				'password' => env('SOUNDBOX_MQ_PASSWORD'),
				'mqtt' => env('SOUNDBOX_MQ_BROKERS')
			]
		];


		$listLocation = (new SoundBox())->getLocationByMcId([
			'mcId' => $merchantId,
			'partnerCode' => Helper::getPartnerCode($merchantId)
		]);

		if (!empty($listLocation['result']) && isset($listLocation['code']) && $listLocation['code'] == 1002) {
			$deviceSession = $request->getCurrentDeviceSession();
			return app(EmptyCauHinhMcSubAction::class)->run($tingting, $deviceSession);
		}

		$tingting['totalAmountCurrent'] = '--';
		$tingting['transactionCount'] = '--';

		if (!empty($listLocation['data'])) {
			$tingting['listMobileUser'] = [];

			if (!empty($listLocation['data']['currentMode'])) {
				$returnData['tingbox']['currentMode'] = $listLocation['data']['currentMode'];
			}

			if (!empty($listLocation['data']['qrDefault4App']['deviceMobileUsers'])) {

				$qrDefault = Arr::last($listLocation['data']['qrDefault4App']['deviceMobileUsers']);

				if ($qrDefault && isset($qrDefault['mobileUserName']) && !empty($qrDefault['mobileUserName'])) {
					$mobileUserDefault = Arr::first($listLocation['data']['mcStores'], function ($item) use ($qrDefault) {
						return isset($item['mobileUser']) && isset($item['mobileUser']['muName']) && $item['mobileUser']['muName'] == $qrDefault['mobileUserName'];
					});

					$qrDefault['address'] = $mobileUserDefault && isset($mobileUserDefault['address']) ? $mobileUserDefault['address'] : '';
					$qrDefault['muId'] = $mobileUserDefault && isset($mobileUserDefault['mobileUser']) && isset($mobileUserDefault['mobileUser']['muName'])  ? $mobileUserDefault['mobileUser']['muName'] : '';
					$qrDefault['bank'] =  $mobileUserDefault && isset($mobileUserDefault['bank']) && !empty($mobileUserDefault['bank']) ? $mobileUserDefault['bank'] : (object)[];
					$qrDefault['step_action'] =  [
						[
							'step' => 1,
							'status' => $mobileUserDefault && isset($mobileUserDefault['bank']) && isset($mobileUserDefault['bank']['accountNo']) && !empty($mobileUserDefault['bank']['accountNo']) ? 2 : 1,
							'step_name' => 'Khai báo tài khoản ngân hàng',
						],
						[
							'step' => 2,
							'status' => $mobileUserDefault && isset($mobileUserDefault['name']) && !empty($mobileUserDefault['name']) ? 2 : 1,
							'step_name' => 'Khai báo cửa hàng kinh doanh',
						],
						[
							'step' => 3,
							'status' => $mobileUserDefault && isset($mobileUserDefault['mobileUser']) && isset($mobileUserDefault['mobileUser']['devices']) && !empty($mobileUserDefault['mobileUser']['devices']) ? 2 : 1,
							'step_name' => 'Gán loa Tingbox',
						],
					];
				}
				$returnData['tingbox']['qrDefault'] = (object) $qrDefault;

				$tingting['listMobileUser'] = $listLocation['data']['qrDefault4App']['deviceMobileUsers'];

				$countStore = count($tingting['listMobileUser']);

				foreach ($tingting['listMobileUser'] as $key => $s) {
					$mobileUser = Arr::last($listLocation['data']['mcStores'], function ($item) use ($s) {
						return isset($item['mobileUser']) && isset($item['mobileUser']['muName']) && $item['mobileUser']['muName'] == $s['mobileUserName'];
					});

					$tingting['listMobileUser'][$key]['address'] = $mobileUser && isset($mobileUser['address']) ? $mobileUser['address'] : '';
					$tingting['listMobileUser'][$key]['can'] = Mpos360Enum::MPOS360_CAN_STAY_AT_FORM;
					$tingting['listMobileUser'][$key]['muId'] = $mobileUser && isset($mobileUser['mobileUser']) && isset($mobileUser['mobileUser']['muName'])  ? $mobileUser['mobileUser']['muName'] : '';
					$tingting['listMobileUser'][$key]['bank'] = $mobileUser && isset($mobileUser['bank']) && !empty($mobileUser['bank']) ? $mobileUser['bank'] : (object)[];
					$tingting['listMobileUser'][$key]['step_action'] =  [
						[
							'step' => 1,
							'status' => $mobileUser && isset($mobileUser['bank']) && isset($mobileUser['bank']['accountNo']) && !empty($mobileUser['bank']['accountNo']) ? 2 : 1,
							'step_name' => 'Khai báo tài khoản ngân hàng',
						],
						[
							'step' => 2,
							'status' => $mobileUser && isset($mobileUser['name']) && !empty($mobileUser['name']) ? 2 : 1,
							'step_name' => 'Khai báo cửa hàng kinh doanh',
						],
						[
							'step' => 3,
							'status' => $mobileUser && isset($mobileUser['mobileUser']) && isset($mobileUser['mobileUser']['devices']) && !empty($mobileUser['mobileUser']['devices']) ? 2 : 1,
							'step_name' => 'Gán loa Tingbox',
						],
					];

					foreach ($tingting['listMobileUser'][$key]['step_action'] as $action) {
						if ($action['status'] != 2) {
							if ($countStore == 1) {
								$tingting['listMobileUser'][$key]['can'] = TingTingEnum::CAN_GO_TO_KHAI_BAO_MAN_HINH_3STEP;
							} else {
								$tingting['listMobileUser'][$key]['can'] = TingTingEnum::CAN_GO_TO_CHI_TIET_CUA_HANG;
							}
							break;
						}
					}
				}
			} else {
				$returnData['tingbox']['qrDefault'] = (object) [
					"deviceId" => '',
					"storeName" => '',
					"mobileUserName" => $listLocation['data'] && isset($listLocation['data']['mcUserMobile']) ? $listLocation['data']['mcUserMobile'] : '',
					"qrDefault" => '',
					"qrTypeDefault" => '',
					"qrIdDefault" => '',
					"accountNumberDefault" => '',
					"accountNameDefault" => '',
					"address" => "",
					"muId" => $listLocation['data'] && isset($listLocation['data']['mcUserMobile']) ? $listLocation['data']['mcUserMobile'] : '',
					"bank" => (object) [],
					"step_action" => [
						[
							'step' => 1,
							'status' => 1,
							'step_name' => 'Khai báo tài khoản ngân hàng',
						],
						[
							'step' => 2,
							'status' => 1,
							'step_name' => 'Khai báo cửa hàng kinh doanh',
						],
						[
							'step' => 3,
							'status' => 1,
							'step_name' => 'Gán loa Tingbox',
						],
					]
				];

				$tingting['listMobileUser'][] = [
					"deviceId" => '',
					"storeName" => '',
					"mobileUserName" => $listLocation['data'] && isset($listLocation['data']['mcUserMobile']) ? $listLocation['data']['mcUserMobile'] : '',
					"qrDefault" => '',
					"qrTypeDefault" => '',
					"qrIdDefault" => '',
					"accountNumberDefault" => '',
					"accountNameDefault" => '',
					"address" => "",
					"muId" => $listLocation['data'] && isset($listLocation['data']['mcUserMobile']) ? $listLocation['data']['mcUserMobile'] : '',
					"can" => "",
					"bank" => (object) [],
					"step_action" => [
						[
							'step' => 1,
							'status' => 1,
							'step_name' => 'Khai báo tài khoản ngân hàng',
						],
						[
							'step' => 2,
							'status' => 1,
							'step_name' => 'Khai báo cửa hàng kinh doanh',
						],
						[
							'step' => 3,
							'status' => 1,
							'step_name' => 'Gán loa Tingbox',
						],
					]
				];
			}

			if (!empty($listLocation['data']['qrDefault4App']['mqttInfo'])) {
				$tingting['mqAuthen'] = [
					'userName' => $listLocation['data']['qrDefault4App']['mqttInfo']['username'],
					'password' => $listLocation['data']['qrDefault4App']['mqttInfo']['password'],
					'mqtt' => $listLocation['data']['qrDefault4App']['mqttInfo']['endpoint'],
				];
			}

			$returnData['tingbox']['tingting'] = $tingting;

			// For kiểm tra cửa hàng
			$listMobileUserQr = collect($listLocation['data']['qrDefault4App']['deviceMobileUsers'])->pluck('mobileUserName')->values()->toArray();

			if (!empty($listMobileUserQr)) {
				foreach ($listLocation['data']['mcStores'] as $store) {
					if (!in_array($store['mobileUser']['muName'], $listMobileUserQr)) {
						$item = [
							"deviceId" => '',
							"storeName" => sprintf('%s (%s)', $store['name'] ?? '', $store['mobileUser']['muName']),
							"mobileUserName" => $store['mobileUser']['muName'],
							"qrDefault" => '',
							"qrTypeDefault" => '',
							"qrIdDefault" => '',
							"accountNumberDefault" => '',
							"accountNameDefault" => '',
							"address" =>  $store['address'],
							"muId" => $store['mobileUser']['muName'],
							"can" => TingTingEnum::CAN_GO_TO_CHI_TIET_CUA_HANG,
							"bank" => (object) $store['bank'],
							"step_action" => [
								[
									'step' => 1,
									'status' => 1,
									'step_name' => 'Khai báo tài khoản ngân hàng',
								],
								[
									'step' => 2,
									'status' => 1,
									'step_name' => 'Khai báo cửa hàng kinh doanh',
								],
								[
									'step' => 3,
									'status' => 1,
									'step_name' => 'Gán loa Tingbox',
								],
							]
						];

						if (!empty($store['locationId'])) {
							$item['step_action'][1]['status'] = 2;
						}

						if (!empty($store['bank'])) {
							$item['step_action'][0]['status'] = 2;
						}

						if (!empty($store['mobileUser']['devices'])) {
							$item['step_action'][2]['status'] = 2;
						}

						$returnData['tingbox']['tingting']['listMobileUser'][] = $item;
					}
				}
			}
		} // End if !empty data

		// Kiểm tra xem có mobile user nào hoàn thành đủ 3 steps không
		$hasCompletedAllSteps = false;
		foreach ($returnData['tingbox']['tingting']['listMobileUser'] as &$mu) {
			$allStepsCompleted = collect($mu['step_action'])->every(function ($step) {
				return $step['status'] == 2;
			});

			if ($allStepsCompleted) {
				$hasCompletedAllSteps = true;
				break;
			}

			foreach ($listLocation['data']['qrDefault4App']['deviceMobileUsers'] as $qrMu) {
				if ($qrMu['mobileUserName'] == $mu['mobileUserName']) {
					if ($request->json('data.username') != '**********') {
						if (!empty($qrMu['qrBankDefault'])) {
							$mu['step_action'][0]['status'] = 2;
						}
					}
				}
			}

			foreach ($listLocation['data']['mcStores'] as $s) {
				if (@$s['mobileUser']['muName'] == $mu['mobileUserName']) {
					if ($request->json('data.username') != '**********') { 
						$mu['step_action'][0]['status'] = 2;
					}
				}

				if (!empty($s['locationId'])) {
					$mu['step_action'][1]['status'] = 2;
				}

				if (!empty($s['mobileUser']['devices'])) {
					$mu['step_action'][2]['status'] = 2;
				}
			}
		}


		// if (true) {
		// 	$macqThongKeHomNay = $this->canCallMacq($merchantId);
		// 	if (!empty($macqThongKeHomNay)) {
		// 		$returnData['tingbox']['tingting']['totalAmountCurrent'] = $macqThongKeHomNay['totalAmountCurrent'];
		// 		$returnData['tingbox']['tingting']['transactionCount'] = $macqThongKeHomNay['transactionCount'];
		// 	}
		// }

		// Xu ly luu dem
		$cacheAction = CacheAction::query()->firstWhere([
			'reference_id' => $request->json('data.merchantId'),
			'step_code' => 'KHAI_BAO_TKNH',
		]);

		foreach ($returnData['tingbox']['tingting']['listMobileUser'] as &$mu) {
			if ($cacheAction && $mu['step_action'][0]['status'] != 2) {
				$mu['step_action'][0]['status'] = 2;
			}
		}

		Logs::writeInfo("Mpos360GetCauHinhByMerchantAction-Response", $returnData);
		return $returnData;
	}

	public function canCallMacq($merchantId)
	{
		$returnData = [];

		$p = [
			'serviceName' => 'DAILY_COUNT_SUM_TRANSACTION',
			'merchantFk' => $merchantId,
			'tokenLogin' => 'abcxyz',
			'rangeTime' => now()->format('d-m-Y'),
			'typeTransaction' => 'STATISTIC_QR'
		];

		$thongKeCountSumQrHomNay = $this->mposAws->countSumTransByDate($p);

		if (!empty($thongKeCountSumQrHomNay['data']['data'])) {
			$transactionCurrent = Arr::first($thongKeCountSumQrHomNay['data']['data'], function ($item) use ($p) {
				return isset($item['formatDate']) && $item['formatDate'] == $p['rangeTime'];
			});

			$returnData['totalAmountCurrent'] = $transactionCurrent && isset($transactionCurrent['totalAmount'])  ? Helper::numberFormat($transactionCurrent['totalAmount']) : '--';
			$returnData['transactionCount'] = $transactionCurrent && isset($transactionCurrent['transactionCount'])  ? $transactionCurrent['transactionCount'] : '--';
		}

		return $returnData;
	}

	public function isAllVANPMode($data): bool
	{
		return collect($data)->every(function ($item) {
			return $item['currentMode'] == 'VANP';
		});
	}
} // End class
