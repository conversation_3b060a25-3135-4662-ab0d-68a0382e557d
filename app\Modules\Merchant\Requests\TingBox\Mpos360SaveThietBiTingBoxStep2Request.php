<?php

namespace App\Modules\Merchant\Requests\TingBox;

use Illuminate\Foundation\Http\FormRequest;

class Mpos360SaveThietBiTingBoxStep2Request extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data.username' => ['required', 'string'],
			'data.merchantId' => ['required', 'string'],
      'data.tingboxSerial' => ['required', 'string'],
      'data.shopId' => ['required', 'string'],
    ];
  }

	public function messages() {
		return [
			'data.username.required' => 'Username là bắt buộc',
			'data.username.string' => 'Username phải là kiểu chuỗi',
			'data.merchantId.required' => 'MerchantID là bắt buộc',
			'data.merchantId.string' => 'MerchantID phải là kiểu chuỗi',
			'data.tingboxSerial.required' => 'Mã serial tingbox là bắt buộc',
			'data.tingboxSerial.string' => 'Mã serial tingbox phải là kiểu chuỗi',
			'data.shopId.required' => 'Mã cửa hàng là bắt buộc',
			'data.shopId.string' => 'Mã cửa hàng phải là kiểu chuỗi',
		];
	}
}
