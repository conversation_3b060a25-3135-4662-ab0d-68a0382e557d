<?php

use Illuminate\Support\Facades\Route;
use App\Http\Middleware\MakeSureThatRequestIsJsonMiddleware;
use App\Modules\Merchant\Controllers\Qts\Mpos360QtsController;
use App\Modules\Merchant\Controllers\Home\Mpos360HomeController;
use App\Modules\Merchant\Controllers\Banner\Mpos360BannerController;
use App\Modules\Merchant\Controllers\Authen\Mpos360\Mpos360AuthController;
use App\Modules\Merchant\Controllers\ChungThuc\Mpos360ChungThucController;
use App\Modules\Merchant\Controllers\Transaction\Mpos360TransactionController;
use App\Modules\Merchant\Controllers\Notification\Mpos360NotificationController;
use App\Modules\Merchant\Controllers\Transaction\Mpos360TransactionQRController;
use App\Modules\Merchant\Controllers\Authen\Mpos360\Mpos360AuthOtpLoginController;
use App\Modules\Merchant\Controllers\Transaction\Mpos360TransactionCAHistoryController;
use App\Modules\Merchant\Controllers\Transaction\Mpos360TransactionBAFReceiveController;
use App\Modules\Merchant\Controllers\Transaction\Mpos360TransactionInstallmentController;
use App\Modules\Merchant\Controllers\MerchantSignature\Mpos360MerchantSignatureController;
use App\Modules\Merchant\Controllers\RequestChangeInfo\Mpos360RequestChangeInfoController;
use App\Modules\Merchant\Controllers\Transaction\Mpos360RecentlyVietQrTransactionController;
use App\Modules\Merchant\Controllers\Transaction\Mpos360TransactionQuickWithDrawOrderController;

Route::middleware(MakeSureThatRequestIsJsonMiddleware::class)->group(function () {
	/******************* QTS *******************/
	Route::any('/Mpos360QtsGetConfig', [
		'uses' => Mpos360QtsController::class . '@Mpos360QtsGetConfig',
		'as' => 'Mpos360QtsGetConfigAction'
	])->middleware('validateHash:email');
	
	/******************* Authen *******************/
	Route::any('/Mpos360AuthGetDeviceTutorial', [
		'uses' => Mpos360AuthController::class . '@Mpos360AuthGetDeviceTutorial',
		'as' => 'Mpos360AuthGetDeviceTutorialAction'
	])->middleware('validateHash:email');

	Route::any('/Mpos360AuthVerifyMposDevice', [
		'uses' => Mpos360AuthController::class . '@Mpos360AuthVerifyMposDevice',
		'as' => 'Mpos360AuthVerifyMposDeviceAction'
	])->middleware('validateHash:mpos_device_code');

	Route::any('/Mpos360AuthGetOtpLogin', [
		'uses' => Mpos360AuthOtpLoginController::class . '@Mpos360AuthGetOtpLogin',
		'as' => 'Mpos360AuthGetOtpLoginAction'
	])->middleware('validateHash:email|otp_id');

	Route::any('/Mpos360AuthVerifyOtpLogin', [
		'uses' => Mpos360AuthOtpLoginController::class . '@Mpos360AuthVerifyOtpLogin',
		'as' => 'Mpos360AuthVerifyOtpLogin'
	])->middleware('validateHash:email|otp|otp_id');

	Route::any('/Mpos360ChangePassword', [
		'uses' => Mpos360AuthController::class . '@Mpos360ChangePassword',
		'as' => 'Mpos360ChangePasswordAction'
	])->middleware('validateHash:email|current_password|password|password_confirmation');

	Route::any('/Mpos360AuthRemoveAccount', [
		'uses' => Mpos360AuthController::class . '@Mpos360AuthRemoveAccount',
		'as' => 'Mpos360AuthRemoveAccountAction'
	])->middleware('validateHash:email|password|reason_remove_account');

	Route::any('/Mpos360AuthLogout', [
		'uses' => Mpos360AuthController::class . '@Mpos360AuthLogout',
		'as' => 'Mpos360AuthLogoutAction'
	])->middleware('validateHash:email');


	/******************* Home *******************/
	Route::any('/Mpos360GetHomePageInfo', [
		'uses' => Mpos360HomeController::class . '@Mpos360GetHomePageInfo',
		'as' => 'Mpos360GetHomePageInfoAction'
	])->middleware('validateHash:date');

	/******************* Home *******************/
	Route::any('/Mpos360GetMyBanners', [
		'uses' => Mpos360BannerController::class . '@Mpos360GetMyBanners',
		'as' => 'Mpos360GetMyBannersAction'
	])->middleware('validateHash:email');

	/******************* Notification *******************/
	Route::any('/Mpos360GetMyNotifications', [
		'uses' => Mpos360NotificationController::class . '@Mpos360GetMyNotifications',
		'as' => 'Mpos360GetMyNotificationsAction'
	])->middleware('validateHash:groupCode|cateCode|subCateCode|pageNum|pageSize');

	Route::any('/Mpos360GetNotificationDetail', [
		'uses' => Mpos360NotificationController::class . '@Mpos360GetNotificationDetail',
		'as' => 'Mpos360GetNotificationDetailAction'
	])->middleware('validateHash:notificationId');

	Route::any('/Mpos360GetGroupWithUnreadItems', [
		'uses' => Mpos360NotificationController::class . '@Mpos360GetGroupWithUnreadItems',
		'as' => 'Mpos360GetGroupWithUnreadItemsAction'
	])->middleware('validateHash:email');

	Route::any('/Mpos360MaskNotificationAsRead', [
		'uses' => Mpos360NotificationController::class . '@Mpos360MaskNotificationAsRead',
		'as' => 'Mpos360MaskNotificationAsReadAction'
	])->middleware('validateHash:id_list|groupCode|cateCode|subCateCode');

	Route::any('/Mpos360MaskAsReadByIds', [
		'uses' => Mpos360NotificationController::class . '@Mpos360MaskAsReadByIds',
		'as' => 'Mpos360MaskAsReadByIdsAction'
	])->middleware('validateHash:email');

	Route::any('/Mpos360GetTotalUnread', [
		'uses' => Mpos360NotificationController::class . '@Mpos360GetTotalUnread',
		'as' => 'Mpos360GetTotalUnreadAction'
	])->middleware('validateHash:email');

	/******************* Transaction *******************/
	Route::any('/Mpos360TransactionHistory', [
		'uses' => Mpos360TransactionController::class . '@Mpos360TransactionHistory',
		'as' => 'Mpos360TransactionHistoryAction'
	])->middleware('validateHash:date');

	Route::any('/Mpos360TransactionListNormal', [
		'uses' => Mpos360TransactionController::class . '@Mpos360TransactionListNormal',
		'as' => 'Mpos360TransactionListNormalAction'
	])->middleware('validateHash:transaction_time|transaction_method|transaction_status|start|limit');

	Route::any('/Mpos360TransactionCountSumByDate', [
		'uses' => Mpos360TransactionController::class . '@Mpos360TransactionCountSumByDate',
		'as' => 'Mpos360TransactionCountSumByDateAction'
	]);

	Route::any('/Mpos360TransactionCountSumByDateV2', [
		'uses' => Mpos360TransactionController::class . '@Mpos360TransactionCountSumByDateV2',
		'as' => 'Mpos360TransactionCountSumByDateV2Action'
	])->middleware('validateHash:email|merchantFk|typeTransaction|rangeTime');
	

	Route::any('/Mpos360TransactionNormalDetail', [
		'uses' => Mpos360TransactionController::class . '@Mpos360TransactionNormalDetail',
		'as' => 'Mpos360TransactionNormalDetailAction'
	])->middleware('validateHash:transaction_id');

	// trả góp
	Route::any('/Mpos360TransactionInstallmentList', [
		'uses' => Mpos360TransactionInstallmentController::class . '@Mpos360TransactionInstallmentList',
		'as' => 'Mpos360TransactionInstallmentListAction'
	])->middleware('validateHash:transaction_time|transaction_method|transaction_status|transaction_status_installment|start|limit');

	Route::any('/Mpos360TransactionInstallmentDetail', [
		'uses' => Mpos360TransactionInstallmentController::class . '@Mpos360TransactionInstallmentDetail',
		'as' => 'Mpos360TransactionInstallmentDetailAction'
	])->middleware('validateHash:transaction_id');

	// rút tiền nhanh
	Route::any('/Mpos360TransactionQuickWithDrawOrderList', [
		'uses' => Mpos360TransactionQuickWithDrawOrderController::class . '@Mpos360TransactionQuickWithDrawOrderList',
		'as' => 'Mpos360TransactionQuickWithDrawOrderListAction'
	])->middleware('validateHash:transaction_time|transaction_status|start|limit');

	Route::any('/Mpos360TransactionQuickWithDrawOrderDetail', [
		'uses' => Mpos360TransactionQuickWithDrawOrderController::class . '@Mpos360TransactionQuickWithDrawOrderDetail',
		'as' => 'Mpos360TransactionQuickWithDrawOrderDetailAction'
	])->middleware('validateHash:order_code');

	// nhận tiền về tknh
	Route::any('/Mpos360TransactionBAFReceiveList', [
		'uses' => Mpos360TransactionBAFReceiveController::class . '@Mpos360TransactionBAFReceiveList',
		'as' => 'Mpos360TransactionBAFReceiveListAction'
	])->middleware('validateHash:transaction_time|transaction_status|start|limit');

	Route::any('/Mpos360TransactionBAFReceiveDetail', [
		'uses' => Mpos360TransactionBAFReceiveController::class . '@Mpos360TransactionBAFReceiveDetail',
		'as' => 'Mpos360DetailPhieuChiAction'
	])->middleware('validateHash:order_code');

	// Lịch sử tạm ứng - mã tạm ứng
	Route::any('/Mpos360TransactionCAHistoryCodeList', [
		'uses' => Mpos360TransactionCAHistoryController::class . '@Mpos360TransactionCAHistoryCodeList',
		'as' => 'Mpos360TransactionCAHistoryCodeListAction'
	])->middleware('validateHash:transaction_time|transaction_status|start|limit');

	Route::any('/Mpos360TransactionCAHistoryCodeDetail', [
		'uses' => Mpos360TransactionCAHistoryController::class . '@Mpos360TransactionCAHistoryCodeDetail',
		'as' => 'Mpos360TransactionCAHistoryCodeDetailAction'
	])->middleware('validateHash:order_code');

	// Lịch sử tạm ứng - giao dịch tạm ứng
	Route::any('/Mpos360TransactionCAHistoryTransList', [
		'uses' => Mpos360TransactionCAHistoryController::class . '@Mpos360TransactionCAHistoryTransList',
		'as' => 'Mpos360TransactionCAHistoryTransListAction'
	])->middleware('validateHash:transaction_time|transaction_status|start|limit');

	Route::any('/Mpos360TransactionCAHistoryTransDetail', [
		'uses' => Mpos360TransactionCAHistoryController::class . '@Mpos360TransactionCAHistoryTransDetail',
		'as' => 'Mpos360TransactionCAHistoryTransDetailAction'
	])->middleware('validateHash:transaction_id');
	
	Route::any('/Mpos360RecentlyVietQrTransaction', [
		'uses' => Mpos360RecentlyVietQrTransactionController::class . '@Mpos360RecentlyVietQrTransaction',
		'as' => 'Mpos360RecentlyVietQrTransactionAction'
	])->middleware('validateHash:email|page|limit');

	/******************* Yêu cầu thay đổi thông tin *******************/
	Route::any('/Mpos360RequestChangeInfoGetConfig', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoGetConfig',
		'as' => 'Mpos360RequestChangeInfoGetConfigAction'
	])->middleware('validateHash:email');

	// các hồ sơ của tôi
	Route::any('/Mpos360RequestChangeInfoListProfile', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoListProfile',
		'as' => 'Mpos360RequestChangeInfoListProfileAction'
	])->middleware('validateHash:email');;

	// kiểm tra thông tin banking
	Route::any('/Mpos360RequestChangeInfoCheckProfileBanking', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoCheckProfileBanking',
		'as' => 'Mpos360RequestChangeInfoCheckProfileBankingAction'
	])->middleware('validateHash:bankId|bankName|accountNo|holderName');

	// tạo yêu cầu
	Route::any('/Mpos360RequestChangeInfoUpdateProfile', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoUpdateProfile',
		'as' => 'Mpos360RequestChangeInfoUpdateProfileAction'
	])->middleware('validateHash:email|choice');

	// lấy otp
	Route::any('/Mpos360RequestChangeInfoGetOtp', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoGetOtp',
		'as' => 'Mpos360RequestChangeInfoGetOtpAction'
	])->middleware('validateHash:request_id|service_code|object_value|command_code');

	// gửi lại otp
	Route::any('/Mpos360RequestChangeInfoResendOtp', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoResendOtp',
		'as' => 'Mpos360RequestChangeInfoGetOtpAction'
	])->middleware('validateHash:otp_id');

	// verify otp
	Route::any('/Mpos360RequestChangeInfoVerifyProfile', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoVerifyProfile',
		'as' => 'Mpos360RequestChangeInfoVerifyProfileAction'
	])->middleware('validateHash:request_id|otp|otp_id');
	
	// với case đổi thông tin liên hệ: bắn vào đây để đánh dấu là toàn bộ otp đã đc verify
	Route::any('/Mpos360RequestChangeInfoAllOtpProfileSuccess', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoAllOtpProfileSuccess',
		'as' => 'Mpos360RequestChangeInfoAllOtpProfileSuccessAction'
	])->middleware('validateHash:id');

	// kiểm tra sđt+email mới có thay đổi so với giá trị cũ hay không?
	Route::any('/Mpos360RequestChangeInfoCheckEmailAndMobile', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoCheckEmailAndMobile',
		'as' => 'Mpos360RequestChangeInfoCheckEmailAndMobileAction'
	])->middleware('validateHash:new_email|new_mobile');
	
	// add thêm tài liệu đính kèm
	Route::any('/Mpos360RequestChangeInfoAdditionalAttachment', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoAdditionalAttachment',
		'as' => 'Mpos360RequestChangeInfoAdditionalAttachmentAction'
	])->middleware('validateHash:email|id');

	// điều hướng phương thức quét bước 3
	Route::any('/Mpos360RequestChangeInfoStep3Handler', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoStep3Handler',
		'as' => 'Mpos360RequestChangeInfoStep3HandlerAction'
	])->middleware('validateHash:id|scan_method');

	// đánh dấu hoàn thiện yc để sẵn sàng đẩy sang MNP
	Route::any('/Mpos360RequestChangeInfoMarkAsDone', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoMarkAsDone',
		'as' => 'Mpos360RequestChangeInfoMarkAsDoneAction'
	])->middleware('validateHash:id');

	// danh sách yc đổi thông tin
	Route::any('/Mpos360RequestChangeInfoList', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoList',
		'as' => 'Mpos360RequestChangeInfoListAction'
	])->middleware('validateHash:time_created|status|start|limit');

	// chi tiết yc đổi thông tin
	Route::any('/Mpos360RequestChangeInfoDetail', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoDetail',
		'as' => 'Mpos360RequestChangeInfoDetailAction'
	])->middleware('validateHash:id');

	// hủy yc chưa gửi
	Route::any('/Mpos360RequestChangeInfoCancel', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoCancel',
		'as' => 'Mpos360RequestChangeInfoCancelAction'
	])->middleware('validateHash:id');

/******************* Chứng thực *******************/
	Route::any('/Mpos360ChungThucGetFieldInfo', [
		'uses' => Mpos360ChungThucController::class . '@Mpos360ChungThucGetFieldInfo',
		'as' => 'Mpos360ChungThucGetFieldInfoAction'
	])->middleware('validateHash:email');

	Route::any('/Mpos360ChungThucGetOtp', [
		'uses' => Mpos360ChungThucController::class . '@Mpos360ChungThucGetOtp',
		'as' => 'Mpos360ChungThucGetOtpAction'
	])->middleware('validateHash:service_code|obj_value|reference_id');

	Route::any('/Mpos360ChungThucResendOtp', [
		'uses' => Mpos360ChungThucController::class . '@Mpos360ChungThucResendOtp',
		'as' => 'Mpos360ChungThucResendOtpAction'
	])->middleware('validateHash:otp_id');

	Route::any('/Mpos360ChungThucVerifyOtp', [
		'uses' => Mpos360ChungThucController::class . '@Mpos360ChungThucVerifyOtp',
		'as' => 'Mpos360ChungThucVerifyOtpAction'
	])->middleware('validateHash:otp_id|otp|reference_id');

	Route::any('/Mpos360ChungThucCreate', [
		'uses' => Mpos360ChungThucController::class . '@Mpos360ChungThucCreate',
		'as' => 'Mpos360ChungThucCreateAction'
	])->middleware('validateHash:id|qts_request_id');

	Route::any('/Mpos360ChungThucCheck', [
		'uses' => Mpos360ChungThucController::class . '@Mpos360ChungThucCheck',
		'as' => 'Mpos360ChungThucCheckAction'
	])->middleware('validateHash:code|choice');

	Route::any('/Mpos360ChungThucDetail', [
		'uses' => Mpos360ChungThucController::class . '@Mpos360ChungThucDetail',
		'as' => 'Mpos360ChungThucDetailAction'
	])->middleware('validateHash:id');
/******************* KÝ MNP *******************/
	Route::any('/Mpos360MerchantSignatureList', [
		'uses' => Mpos360MerchantSignatureController::class . '@Mpos360MerchantSignatureList',
		'as' => 'Mpos360MerchantSignatureListAction'
	])->middleware('validateHash:email|status');

	Route::any('/Mpos360MerchantSignatureCreate', [
		'uses' => Mpos360MerchantSignatureController::class . '@Mpos360MerchantSignatureCreate',
		'as' => 'Mpos360MerchantSignatureCreateAction'
	])->middleware('validateHash:email|signature_url|status');
}); // End group middleware json

// Upload file sử dụng form-data nên sẽ cần có cơ chế riêng
Route::post('/Mpos360RequestChangeInfoUploadProfile', [
	'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoUploadProfile',
	'as' => 'Mpos360RequestChangeInfoUploadProfileAction'
])->middleware('validateHashUpload:email');

Route::any('/Mpos360TransactionQRList', [
	'uses' => Mpos360TransactionQRController::class . '@Mpos360TransactionQRList',
	'as' => 'Mpos360TransQRListAction'
])->middleware('validateHash:email|transaction_time|transaction_method|transaction_status');