<?php

namespace App\Modules\Merchant\Actions\Device\Mpos360PushSerialToTingTingSystemAction;

use App\Lib\partner\SoundBox;
use App\Modules\Merchant\Model\Device;

class Mpos360PushSerialToTingTingSystemAction
{
	const CHUA_DONG_BO_TINGTING = 0;
	const DA_DONG_BO_TINGTING = 1;

	public SoundBox $soundBox;

	public function __construct(SoundBox $soundBox)
	{
		$this->soundBox = $soundBox;
	}

	public function run()
	{
		Device::query()
			->where('status_sync_tingting', self::CHUA_DONG_BO_TINGTING)
			->where('has_mobile_user', 1)
			->with('user')
			->chunkById(200, function ($devices) {

				$mangDongBo = [];
				foreach ($devices as $dv) {
					$merchantBasicInfo = json_decode($dv->user->data_users, true);
					$merchantData = json_decode($dv->user->data_merchant, true);
					$listMobileUser = $merchantData['data']['mobileUserDTO'];

					$mangDongBo[] = [
						'mcId' => (string) $merchantBasicInfo['merchantId'],
						'mcEmail' => $merchantBasicInfo['merchantEmail'],
						// 'mobileUser' => [
						// 	'mobileUserId' => (string) $listMobileUser[0]['id'],
						// 	'mobileUserName' => $listMobileUser[0]['username'],
						// ],
						'serial' => $dv->token,
						'cateCode' => 'APP360',
						'status' => 'ACTIVE'
					];
				}
				
				$r = $this->soundBox->sendRequestAsJson('/create-device-4-app', $mangDongBo);
			
				if (!empty($r['result']) && !empty($r['success'])) {
					$deviceIds = $devices->pluck('id')->toArray();
					$r = Device::query()->whereIn('id', $deviceIds)
						->where('status_sync_tingting', self::CHUA_DONG_BO_TINGTING)
						->update([
							'status_sync_tingting' => self::DA_DONG_BO_TINGTING,
							'time_updated' => now()->timestamp
						]);
				}
			});

		return 'ok';
	}
}
