<?php

namespace App\Modules\Merchant\Actions\TingBox\Mpos360CheckTknhKhaiBaoStep3Action;

use App\Lib\MnpOnboardNewMcHelper;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Actions\TingBox\Mpos360CheckTknhKhaiBaoStep3Action\SubAction\KiemTraStkLaThatSubAction;
use App\Modules\Merchant\Requests\TingBox\Mpos360CheckTknhKhaiBaoStep3Request;

class Mpos360CheckTknhKhaiBaoStep3Action
{

	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run(Mpos360CheckTknhKhaiBaoStep3Request $request)
	{
		$listBank = $this->mnpOnboardNewMcHelper->getNganhNgheNganHangThanhPho(true);
		$bankItem = collect($listBank['banks'])->where('bankId', $request->json('data.bankId'))->first();
		
		if (!$bankItem) {
			throw new BusinessException('Không tìm được thông tin ngân hàng mà bạn đã cung cấp');
		}

		$paramCheckStk = [
			'bank_account_holder' => $request->json('data.bankAccountHolder'),
			'bank_account' => $request->json('data.bankAccountNumber'),
			'bank_id' => $bankItem['bankVimoId']
		];

		$check = app(KiemTraStkLaThatSubAction::class)->run($paramCheckStk);
		return $check;
	}
} // End class
