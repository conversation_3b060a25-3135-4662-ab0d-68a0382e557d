<?php

namespace App\Modules\Merchant\Requests\MerchantSignature;

use App\Modules\Merchant\Requests\MerchantRequest;
use Illuminate\Validation\Rule;

class Mpos360MerchantSignatureListRequest extends MerchantRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data.email' => ['required', 'string', 'email'],
      'data.status' => ['required', 'numeric', Rule::in([1])],
    ];
  }

	public function messages()
	{
		return [
			'data.email.required' => vmsg('ChungThucEmailMerchantLaBatBuoc'),
			'data.email.string' => vmsg('ChungThucEmailMerchantPhaiLaKieuChuoi'),
			'data.email.email' => vmsg('ChungThucEmailMerchantPhaiLaEmailDungDinhDang'),
		];
	}
}
