<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoDetailAction\SubAction;

use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoGetConfigAction\Mpos360RequestChangeInfoGetConfigAction;
use Carbon\Carbon;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;

class GenGroupMnpDetailForMobileSubAction
{
	public array $ignoreProfiles = [
		'passportRepresentFrontUrl',
		'passportRepresentBackUrl',
		'passportAuthoriserFrontUrl',
		'passportAuthoriserBackUrl',
		'substituteCertUrls',
		'attachedCertUrls',
		'lostEmailUrls',
		'lostPassportUrls',
		'identificationDocument'
	];

	public function run(Mpos360MerchantRequest $mpos360McRequest)
	{
		$returnData = [];

		$dataRequest = json_decode($mpos360McRequest->data_request, true);

		foreach ($dataRequest as $item) {
			$list = [];

			foreach ($item['profiles'] as $profileUpdateKey => $value) {
				// Loại bỏ các hồ sơ là url
				if (in_array($profileUpdateKey, $this->ignoreProfiles)) {
					continue;
				}

				if ($profileUpdateKey == 'typeChangeRepresent') {
					$list[] = [
						'label' => config('profilemnp.profile_wording.' . $profileUpdateKey),
						'value' => config('profilemnp.typeChangeRepresent.' . $value),
						'other_data' => (object) []
					];
				}

				if ($profileUpdateKey != 'typeChangeRepresent') {
					if ($profileUpdateKey == 'representBirthday') {
						$list[] = [
							'label' => config('profilemnp.profile_wording.' . $profileUpdateKey),
							'value' => Carbon::createFromTimestampMs($value)->format('d/m/Y'),
							'other_data' => (object) []
						];
					} elseif ($profileUpdateKey == 'bankAccType') {
						$mnpConfig = app(Mpos360RequestChangeInfoGetConfigAction::class)->run();
						$bankAccountGroupByType = collect($mnpConfig['data']['mcBankAccType'])->keyBy('code')->toArray();
						
						$list[] = [
							'label' => config('profilemnp.profile_wording.' . $profileUpdateKey),
							'value' => $bankAccountGroupByType[$value]['name'] ?? 'Không xác định',
							'other_data' => (object) []
						];
					} else {
						$list[] = [
							'label' => config('profilemnp.profile_wording.' . $profileUpdateKey),
							'value' => $value,
							'other_data' => (object) []
						];
					}
				}
			}

			if ($mpos360McRequest->isYeuCauDoiCccdMoi()) {
				$list[] = [
					'label' => 'Kiểu thay đổi',
					'value' => 'Đổi CCCD mới',
					'other_data' => (object) []
				];
			}

			if ($mpos360McRequest->isYeuCauDoiNguoiDaiDienMoi()) {
				$list[] = [
					'label' => 'Kiểu thay đổi',
					'value' => 'Đổi hẳn người đại diện mới',
					'other_data' => (object) []
				];
			}

			if ($mpos360McRequest->isDoiThongTinLienHe()) {
				$list[] = [
					'label' => 'Kiểu thay đổi',
					'value' => 'Đổi thông tin liên hệ',
					'other_data' => (object) []
				];
			}

			$returnData[] = [
				'key' => $item['type'],
				'name' => config('profilemnp.type.' . $item['type']),
				'list' => $list
			];
		}

		return $returnData;
	}
} // End class
