<div class="modal fade action-sheet" id="khaiBaoCuaHang" tabindex="-1" role="dialog" style="display: none;">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                <h5>Thông tin cửa hàng</h5>
            </div>
            <div class="modal-body">

                <div class="mpos360-form">
                    <div class="notifi-item">
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control name_cua_hang" id="floatingInputValue" placeholder=""
                                   autocomplete=""
                                   value="">
                            <label for="floatingInputValue">Tên cửa hàng</label>
                            <div class="error text-danger mt-1" id="error-name_cua_hang"></div>
                        </div>
                        

                        <div class="form-floating mb-3">
                            <select class="form-select job" id="floatingSelect"
                                    aria-label="Floating label select example">
                                <option selected="" value="">-- Chọn ngành nghề --</option>
                                @foreach($dataNganhNgheNganHangThanhPho['industries'] as $dataNganhNghe)
                                    <option value="{{$dataNganhNghe['industryId']}}">{{$dataNganhNghe['industryName']}}</option>
                                @endforeach
                            </select>
                            <label for="floatingSelect">Ngành nghề kinh doanh</label>
                            <div class="error-message text-danger mt-1" id="error-job"></div>
                        </div>
                        <div class="form-floating mb-3">
                            <select class="form-select cities" id="floatingSelect "
                                    aria-label="Floating label select example ">
                                <option selected="" value="">-- Chọn tỉnh/Thành phố --</option>
                                @foreach($dataNganhNgheNganHangThanhPho['cities'] as $dataThanhPho)
                                    <option value="{{$dataThanhPho['cityId']}}">{{$dataThanhPho['cityName']}}</option>
                                @endforeach
                            </select>
                            <label for="floatingSelect">Tỉnh/Thành phố</label>
                            <div class="error-message text-danger mt-1" id="error-cities"></div>
                        </div>
                        <div class="form-floating mb-3">
                            <select class="form-select district" id="floatingSelect"
                                    aria-label="Floating label select example">
                                <option selected="" value="">-- Chọn quận/Huyện --</option>
                            </select>
                            <label for="floatingSelect">Quận/Huyện</label>
                            <div class="error-message text-danger mt-1" id="error-district"></div>
                        </div>

                        <div class="form-floating mb-3">
                            <input type="text" class="form-control address" id="floatingInputValue" placeholder=""
                                   autocomplete=""
                                   value="">
                            <label for="floatingInputValue">Địa chỉ</label>
                            <div class="error-message text-danger mt-1" id="error-address"></div>
                        </div>

                            <input type="text" class="form-control merchant_id" id="floatingInputValue" placeholder="" hidden
                                   autocomplete=""
                                   value="{{$_GET['merchantId']}}">


                        <p class="mt-4 mb-0">
                            <a href="#" class="text-center btn-blue w-100 d-block submit_cua_hang">
                                Tiếp tục
                                <span class="spinner-border spinner-border-sm d-none" role="status"
                                      aria-hidden="true"></span>
                            </a>
                        </p>

                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
<script>
    $(document).ready(function () {

        $('.cities').on('change', function () {
            let optionSelect2 = $('.district');
            $('#loadingPage').addClass('se-pre-con');
            $.post('/Mpos360TingBoxOnChangeTinhThanh', {cityId: $(this).val()}).then(function (response) {
                optionSelect2.html(response.message); // Gán dữ liệu trả về vào input
                $('#loadingPage').removeClass('se-pre-con');
            });
        });

        var $name_cua_hang = $(".name_cua_hang");
        var $error_name_cua_hang = $("#error-name_cua_hang");

        function validateName() {
            var name_cua_hang = $name_cua_hang.val().trim();
            if (name_cua_hang === "") {
                $error_name_cua_hang.text("Vui lòng nhập tên cửa hàng");
                $name_cua_hang.addClass("is-invalid");
            } else {
                $error_name_cua_hang.text("");
                $name_cua_hang.removeClass("is-invalid");
            }
        }

        var $qr_cua_hang = $(".qr_cua_hang");
        var $error_qr_cua_hang = $("#error-qr_cua_hang");

        function validateQrCuaHang() {
            var qr_cua_hang = $qr_cua_hang.val().trim();
			const regexQrCuaHang = /^[a-zA-Z0-9_\s]{1,17}$/;

            if (qr_cua_hang === "") {
                $error_qr_cua_hang.text("Vui lòng nhập mã QR cửa hàng");
                $qr_cua_hang.addClass("is-invalid");
            } else if (qr_cua_hang.length > 0 && !regexQrCuaHang.test(qr_cua_hang)) {
                $error_qr_cua_hang.text("Nhập từ 1-17 ký tự, chỉ gồm chữ cái, số, khoảng trắng và dấu gạch dưới (_).");
                $qr_cua_hang.addClass("is-invalid");
            } else {
                $error_qr_cua_hang.text("");
                $qr_cua_hang.removeClass("is-invalid");
            }
        }

        var $job = $(".job");
        var $errorJob = $("#error-job");

        function validateJob() {
            var job = $job.val().trim();
            if (job === "") {
                $errorJob.text("Vui lòng chọn ngành nghề");
                $job.addClass("is-invalid");
            } else {
                $errorJob.text("");
                $job.removeClass("is-invalid");
            }
        }

        var $cities = $(".cities");
        var $errorCities = $("#error-cities");

        function validateCities() {
            var cities = $cities.val().trim();
            if (cities === "") {
                $errorCities.text("Vui lòng chọn tỉnh/ thành phố");
                $cities.addClass("is-invalid");
            } else {
                $errorCities.text("");
                $cities.removeClass("is-invalid");
            }
        }

        var $district = $(".district");
        var $errorDistrict = $("#error-district");

        function validateDistrict() {
            var district = $district.val().trim();
            if (district === "") {
                $errorDistrict.text("Vui lòng chọn quận/huyện");
                $district.addClass("is-invalid");
            } else {
                $errorDistrict.text("");
                $district.removeClass("is-invalid");
            }
        }

        var $address = $(".address");
        var $errorAddress = $("#error-address");

        function validateAddress() {
            var address = $address.val().trim();
						const regexAddress = /^[a-zA-Z0-9\s\/,\u00C0-\u1EF9]*$/u;

            if (address === "") {
                $errorAddress.text("Vui lòng nhập tên cửa hàng");
                $address.addClass("is-invalid");
            } else if (address.length > 0 && !regexAddress.test(address)) {
							$errorAddress.text("Vui lòng Không sử dụng các ký tự đặc biệt như:  (-), @, #, $, %, ! *… trong trường địa chỉ.");
							$address.addClass("is-invalid");
						}
						else {
                $errorAddress.text("");
                $address.removeClass("is-invalid");
            }
        }
        $name_cua_hang.on("input", validateName);
        $qr_cua_hang.on("input", validateQrCuaHang);
        $address.on("input", validateAddress);
        $job.on("change", validateJob);
        $district.on("change", validateDistrict);
        $cities.on("change", validateCities);

        $('.submit_cua_hang').on('click', function (e) {
            e.preventDefault();
            validateName();
            validateJob();
            validateCities();
            validateDistrict();
            validateAddress();

            if (
                $error_name_cua_hang.text() === "" &&
                $errorJob.text() === "" &&
                $errorCities.text() === "" &&
                $errorDistrict.text() === "" &&
                $errorAddress.text() === ""
            ) {

                $('#loadingPage').addClass('se-pre-con');
                let nameCuaHang = $('.name_cua_hang').val();
                let nganhNghe = $('.job').val();
                let city = $('.cities').val();
                let district = $('.district').val();
                let address = $('.address').val();
                let merchant_id = $('.merchant_id').val();

                $.ajax({
                    url: '/TBMpos360SubmitThongTinCuaHang',
                    method: 'POST',
                    data: JSON.stringify({
                        data: {
                            ten_cua_hang: nameCuaHang,
                            nganh_nghe_kinh_doanh: nganhNghe,
                            tinh_thanh_pho: city,
                            quan_huyen: district,
                            dia_chi: address,
                            merchant_id: merchant_id
                        }
                    }),
                    success: function (response) {
                        if (response.code == 1000) {
                            alert("Tạo cửa hàng thành công");
                            window.location.reload();
                        } else {
                            alert(response.message);
                        }
                        $('#loadingPage').removeClass('se-pre-con');
                    },
                    error: function (error) {
                        $('#loadingPage').removeClass('se-pre-con');
                        alert("Có lỗi xảy ra, Hãy thông báo kỹ thuật");
                    }
                    // Handle error response
                });
            }
        });
    });

</script>