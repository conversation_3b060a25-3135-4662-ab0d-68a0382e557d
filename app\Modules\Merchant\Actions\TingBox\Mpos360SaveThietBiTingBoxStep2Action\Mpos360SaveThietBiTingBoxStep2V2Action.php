<?php

namespace App\Modules\Merchant\Actions\TingBox\Mpos360SaveThietBiTingBoxStep2Action;

use App\Lib\Logs;
use App\Lib\partner\SoundBox;
use App\Lib\MnpOnboardNewMcHelper;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\MerchantOnboard;
use App\Modules\TingBoxVAMC\Models\MerchantDeviceTingBox;
use App\Modules\Merchant\Requests\TingBox\Mpos360SaveThietBiTingBoxStep2V2Request;
use App\Modules\Merchant\Actions\TingBox\Mpos360SaveThietBiTingBoxStep2Action\SubAction\DongBoThietBiSauKhiAddSangTingBoxSubAction;

class Mpos360SaveThietBiTingBoxStep2V2Action
{
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public SoundBox $soundBox;

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper, SoundBox $soundBox)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
		$this->soundBox = $soundBox;
	}

	public function run(Mpos360SaveThietBiTingBoxStep2V2Request $request)
	{
		Logs::writeInfo('Mpos360SaveThietBiTingBoxStep2V2Action', $request->json('data'));

		$merchantId = $request->json('data.merchantId');
		
		/**
		 * Gọi api này sẽ lấy được từ 2-3 loại partnerCode của: 
		 * 1. partnerCode của MC 
		 * 2. partnerCode của thiết bị
		 * 3. partnerCode của mã giới thiệu
		 *  
		 *  "numDevice" => 0
			  "partnerCodeMc" => "BIDV"
			  "partnerCodeDv" => "NP"
			  "partnerCodeInvite" => "NP"
			  "partnerCode" => "BIDV"
			  "hasCheckInviteCode" => true
		 */
		$paramGetPartner = [
			'mcId' => $request->json('data.merchantId'),
			'muName' => $request->json('data.mobileUserId'),
			'deviceId' => $request->json('data.tingboxSerial'),
			'inviteCode' => $request->json('data.partnerInviteCode', '')
		];

		$checkPartners = $this->soundBox->checkDeviceMpos($paramGetPartner);

		if (!empty($checkPartners['code']) && $checkPartners['code'] == 5001) {
			$msg = 'Mã giới thiệu không hợp lệ (Mã lỗi: 5001)';
			throw new BusinessException($msg, 5001);
		}

		if (!empty($checkPartners['code']) && $checkPartners['code'] == 5000) {
			$msg = 'Thiết bị không thuộc đúng đối tác (Mã lỗi: 5000)';
			throw new BusinessException($msg, 5000);
		}

		if (!empty($checkPartners['code']) && $checkPartners['code'] == 5002) {
			$msg = 'Thiết bị không hợp lệ (Mã lỗi: 5002)';
			throw new BusinessException($msg, 5002);
		}

		if (empty($checkPartners['result']) || empty($checkPartners['data']['partnerCode'])) {
			$msg = sprintf('Lỗi không tìm được thông tin partnerCode đối tác (Mã lỗi: %s)', 4000);
			throw new BusinessException($msg, 4000);
		}

		$partnerCode = $checkPartners['data']['partnerCode'];

		$inputs = [
			'serial' => $request->json('data.tingboxSerial'),
			'mposMcId' => $merchantId,
			'areaId' => $request->json('data.shopId'),
			'refDeviceCode' => $request->json('data.partnerInviteCode', ''),
			'partnerCode' => $partnerCode
		];

		$assignTingBox = $this->mnpOnboardNewMcHelper->ganThietBiTingBox($inputs);

		if (isset($assignTingBox['result']) && $assignTingBox['code'] == Mpos360Enum::API_SUCCESS_CODE) {
			
			if (!empty($request->json('data.mobileUserId'))) {
				$addThietBiResult = app(DongBoThietBiSauKhiAddSangTingBoxSubAction::class)->run(
					$request, 
					$request->json('data.mobileUserId'),
					$partnerCode
				);

				// Nếu lưu sang mnp thành công thì mới lưu vào DB
				if (!empty($partnerCode) && !empty($addThietBiResult['isAddThietBiThanhCong'])) {
					$mcOnboard = MerchantOnboard::query()->where('merchantId', $merchantId)->first();
					
					try {
						if (!$mcOnboard) {
							$mcOnboard = MerchantOnboard::query()->forceCreate([
								'username' => $request->json('data.username'),
								'merchantId' => $merchantId,
								'partnerCode' => $partnerCode,
								'createdAt' => now(),
								'updatedAt' => now(),
								'partnerFlow' => 'ADD_DEVICE'
							]);
			
							if (!$mcOnboard) {
								throw new BusinessException('Lỗi không lưu được thông tin partner merchant');
							}
						}
			
						// Chỉ update khi partnerCode của ông hiện tại là NP
						if ($mcOnboard && $mcOnboard->partnerCode == 'NP') {
							// Nếu partner check được khác NP -> tức là có cập nhật partner rồi ghi vài luồng add device
							if ($partnerCode != 'NP') {
								$mcOnboard->partnerFlow = 'ADD_DEVICE';
							}
							$mcOnboard->partnerCode = $partnerCode;
							$mcOnboard->updatedAt = now();
							$saveResult = $mcOnboard->save();
			
							if (!$saveResult) {
								throw new BusinessException('Lỗi không cập nhật được partner merchant');
							}
						}
					}catch(\Throwable $th) {
						mylog(['Lỗi lưu merchant_onboard' => $th->getMessage()]);
					}
				}
			}
		
			$returnData = [ 'status' => 'SUCCESS', 'msg' => 'Thành công', ];

			// try {
			// 	$mcDeviceTingBox = MerchantDeviceTingBox::query()->forceCreate([
			// 		'merchantId' => $merchantId,
			// 		'mobileUserId' => $request->json('data.mobileUserId', ''),
			// 		'displayName' => '',
			// 		'serial' => $request->json('data.tingboxSerial'),
			// 		'status' => 1,
			// 		'vaBankNumber' => $request->json('data.vaBankNumber', ''),
			// 		'partnerInviteCode' => $request->json('data.partnerInviteCode', ''),
			// 		'locationId' => $request->json('data.shopId'),
			// 		'createdAt' => now(),
			// 		'updatedAt' => now(),
			// 		'rulesAddDevice' => json_encode([
			// 			'paramGetParner' => $paramGetPartner,
			// 			'partnerInfoResult' => $checkPartners
			// 		])
			// 	]);
			// }catch(\Throwable $th) {
			// 	mylog(['Lỗi lưu merchant_device_tingbox' => $th->getMessage()]);
			// }
			
			return $returnData;
		}

		$mnpErr = $assignTingBox['message'] ?? 'Lỗi không xác định';
		$errMsg = sprintf('%s (Code: %s)', $mnpErr, -1);

		throw new BusinessException($errMsg, -1);
	}
} // End class
