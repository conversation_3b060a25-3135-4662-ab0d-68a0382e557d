<?php

namespace App\Modules\Merchant\Controllers\Authen\Mpos360\V4;

use App\Modules\Merchant\Controllers\Controller;
use Illuminate\Http\Request;

class Mpos360LogV4Controller extends Controller
{
	public function Mpos360LogInfo(Request $request)
	{
		if (empty($request->json('data.app_version'))) {
			return $this->errorResponse(422, 'app version is required', []);
		}

		if (empty($request->json('data.log_level'))) {
			return $this->errorResponse(422, 'level is required', []);
		}

		if (empty($request->json('data.log_data'))) {
			return $this->errorResponse(422, 'data log is required', []);
		}

		return $this->successResponse(['msg' => 'MOBILE_LOG'], $request);
	}
} // End class
