<?php

namespace App\Modules\Merchant\Actions\ServiceProgramRegister\Mpos360ServiceProgramRegisterCreateAction;

use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Mpos360MerchantRequestServiceProgramRegister;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\GetMNPAccessTokenSubAction;
use App\Modules\Merchant\Requests\ServiceProgramRegister\Mpos360ServiceProgramRegisterCreateRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction\MnpGetMerchantProfileSubAction;

class Mpos360ServiceProgramRegisterCreateAction
{
	public function run(Mpos360ServiceProgramRegisterCreateRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		$merchantId = $deviceSession->getMerchantId();

		if ($request->json('data.merchant_id') != $merchantId) {
			throw new BusinessException('Lỗi không đúng thông tin merchant');
		}

		$deviceSessionWithToken = app(GetMNPAccessTokenSubAction::class)->run($deviceSession);

		$mnpMerchantDetail = app(MnpGetMerchantProfileSubAction::class)->run(
			$merchantId,
			$deviceSessionWithToken->mnp_token
		);
		
		$passport = $mnpMerchantDetail['data']['passport']['value'] ?? '';

		if (empty($passport)) {
			throw new BusinessException('MNP Err: Hệ thống không xác minh được CCCD của Merchant');
		}

		$r = Mpos360MerchantRequestServiceProgramRegister::query()->forceCreate([
			'merchant_id' => $merchantId,
			'service_program_id' => $request->json('data.service_program_id'),
			'username' => $deviceSession->getMerchantEmail(),
			'data_request' => json_encode([
				'merchantType' => $request->json('data.merchant_type'),
				'merchantId' => $merchantId,
				'merchantPassport' => $passport
			]),
			'time_created' => now()->timestamp
		]);

		if (!$r) {
			throw new BusinessException('Lỗi không tạo được yêu cầu đăng ký dịch vụ');
		}

		return [
			'request_id' => $r->id,
			'msg' => 'Tạo yc thành công'
		];
	}
} // End class