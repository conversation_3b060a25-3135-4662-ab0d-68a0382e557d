<?php

namespace App\Modules\Merchant\Actions\ChungThuc\Mpos360PushChungThucAction\SubAction;

use App\Lib\partner\MNP;
use App\Modules\Merchant\Model\Mpos360ChungThuc;

class PushChungThucSangMnpSubAction
{
	public MNP $mnp;

	public function __construct(MNP $mnp)
	{
		$this->mnp = $mnp;
	}

	public function run(Mpos360ChungThuc $mpos360ChungThuc)
	{
		$mnpTokenResult = $this->mnp->getToken($mpos360ChungThuc->merchant_id);

		if (empty($mnpTokenResult['status']) || empty($mnpTokenResult['data'])) {
			mylog([
				'Lỗi không lấy được thông tin AccessToken của MNP' => [
					'MerchantId' => $mpos360ChungThuc->merchant_id,
					'ChungThucRecordId' => $mpos360ChungThuc->id,
				]
			]);
		}

		$mnpAccessToken = $mnpTokenResult['data'];
		$merchantId = $mpos360ChungThuc->merchant_id;
		$setChungThuc = [];

		switch ($mpos360ChungThuc->key_code) {
			case 'CCCD':
				$setChungThuc = $this->mnp->customerVerifyInfo(
					$merchantId,
					$mnpAccessToken,
					[
						['representPassport' => $mpos360ChungThuc->getOriginalCCCD()]
					]
				);
				break;

			case 'EMAIL':
				$setChungThuc = $this->mnp->customerVerifyInfo(
					$merchantId,
					$mnpAccessToken,
					[
						['representEmail' => $mpos360ChungThuc->value_confirmed]
					]
				);
				break;

			case 'MOBILE':
				$setChungThuc = $this->mnp->customerVerifyInfo(
					$merchantId,
					$mnpAccessToken,
					[
						['representMobile' => $mpos360ChungThuc->value_confirmed]
					]
				);
				break;

			default:
				// code
				break;
		}

		if (!empty($setChungThuc['status'])) {
			$mpos360ChungThuc->is_push_mnp = 1;
			$mpos360ChungThuc->save();
		}
	} // End pushMnp
}
