{
  "name": "<PERSON><PERSON><PERSON>esigner Default",
  "type": "light",
  "colors": {
    // Editor colors
    "editor.background": "#FFFFFF",
    "editor.foreground": "#000000",
    "editorLineNumber.foreground": "#999999",
    "editorLineNumber.activeForeground": "#000000",
    "editor.selectionBackground": "#316AC5",
    "editor.selectionForeground": "#FFFFFF",
    "editor.inactiveSelectionBackground": "#E5E5E5",
    "editor.lineHighlightBackground": "#F5F5F5",
    "editor.lineHighlightBorder": "#E0E0E0",
    "editorCursor.foreground": "#000000",
    "editorWhitespace.foreground": "#CCCCCC",
    "editorIndentGuide.background": "#E0E0E0",
    "editorIndentGuide.activeBackground": "#CCCCCC",
    
    // Sidebar colors (mimicking <PERSON><PERSON>Designer's project panel)
    "sideBar.background": "#F0F0F0",
    "sideBar.foreground": "#000000",
    "sideBar.border": "#CCCCCC",
    "sideBarTitle.foreground": "#000000",
    "sideBarSectionHeader.background": "#E8E8E8",
    "sideBarSectionHeader.foreground": "#000000",
    "sideBarSectionHeader.border": "#CCCCCC",
    
    // Activity bar (left panel)
    "activityBar.background": "#E8E8E8",
    "activityBar.foreground": "#000000",
    "activityBar.border": "#CCCCCC",
    "activityBar.activeBorder": "#316AC5",
    "activityBarBadge.background": "#316AC5",
    "activityBarBadge.foreground": "#FFFFFF",
    
    // Status bar
    "statusBar.background": "#E8E8E8",
    "statusBar.foreground": "#000000",
    "statusBar.border": "#CCCCCC",
    "statusBar.noFolderBackground": "#E8E8E8",
    "statusBar.debuggingBackground": "#316AC5",
    "statusBar.debuggingForeground": "#FFFFFF",
    
    // Title bar
    "titleBar.activeBackground": "#E8E8E8",
    "titleBar.activeForeground": "#000000",
    "titleBar.inactiveBackground": "#F0F0F0",
    "titleBar.inactiveForeground": "#666666",
    "titleBar.border": "#CCCCCC",
    
    // Tabs
    "tab.activeBackground": "#FFFFFF",
    "tab.activeForeground": "#000000",
    "tab.activeBorder": "#316AC5",
    "tab.inactiveBackground": "#F0F0F0",
    "tab.inactiveForeground": "#666666",
    "tab.border": "#CCCCCC",
    "editorGroupHeader.tabsBackground": "#E8E8E8",
    "editorGroupHeader.tabsBorder": "#CCCCCC",
    
    // Panel (terminal, output, etc.)
    "panel.background": "#F8F8F8",
    "panel.border": "#CCCCCC",
    "panelTitle.activeForeground": "#000000",
    "panelTitle.inactiveForeground": "#666666",
    "panelTitle.activeBorder": "#316AC5",
    
    // Lists (file explorer, etc.)
    "list.activeSelectionBackground": "#316AC5",
    "list.activeSelectionForeground": "#FFFFFF",
    "list.inactiveSelectionBackground": "#E5E5E5",
    "list.inactiveSelectionForeground": "#000000",
    "list.hoverBackground": "#F0F0F0",
    "list.focusBackground": "#E5E5E5",
    
    // Input controls
    "input.background": "#FFFFFF",
    "input.foreground": "#000000",
    "input.border": "#CCCCCC",
    "input.placeholderForeground": "#999999",
    "inputOption.activeBorder": "#316AC5",
    
    // Buttons
    "button.background": "#316AC5",
    "button.foreground": "#FFFFFF",
    "button.hoverBackground": "#2859A3",
    
    // Dropdown
    "dropdown.background": "#FFFFFF",
    "dropdown.foreground": "#000000",
    "dropdown.border": "#CCCCCC",
    
    // Scrollbar
    "scrollbar.shadow": "#DDDDDD",
    "scrollbarSlider.background": "#CCCCCC",
    "scrollbarSlider.hoverBackground": "#999999",
    "scrollbarSlider.activeBackground": "#666666",
    
    // Breadcrumb
    "breadcrumb.background": "#F8F8F8",
    "breadcrumb.foreground": "#666666",
    "breadcrumb.focusForeground": "#000000",
    "breadcrumb.activeSelectionForeground": "#316AC5",
    
    // Menu
    "menu.background": "#F0F0F0",
    "menu.foreground": "#000000",
    "menu.selectionBackground": "#316AC5",
    "menu.selectionForeground": "#FFFFFF",
    "menu.separatorBackground": "#CCCCCC",
    
    // Notifications
    "notificationCenter.border": "#CCCCCC",
    "notificationCenterHeader.background": "#E8E8E8",
    "notificationToast.border": "#CCCCCC",
    "notifications.background": "#F8F8F8",
    "notifications.foreground": "#000000",
    "notifications.border": "#CCCCCC",
    
    // Extensions
    "extensionButton.prominentBackground": "#316AC5",
    "extensionButton.prominentForeground": "#FFFFFF",
    "extensionButton.prominentHoverBackground": "#2859A3"
  },
  "tokenColors": [
    // Comments
    {
      "name": "Comment",
      "scope": [
        "comment",
        "punctuation.definition.comment"
      ],
      "settings": {
        "fontStyle": "italic",
        "foreground": "#008000"
      }
    },
    // Strings
    {
      "name": "String",
      "scope": [
        "string.quoted.double",
        "string.quoted.single",
        "string.template"
      ],
      "settings": {
        "foreground": "#800080"
      }
    },
    // Numbers
    {
      "name": "Number",
      "scope": [
        "constant.numeric"
      ],
      "settings": {
        "foreground": "#FF0000"
      }
    },
    // Keywords
    {
      "name": "Keyword",
      "scope": [
        "keyword",
        "storage.type",
        "storage.modifier"
      ],
      "settings": {
        "fontStyle": "bold",
        "foreground": "#0000FF"
      }
    },
    // PHP Keywords
    {
      "name": "PHP Keyword",
      "scope": [
        "keyword.control.php",
        "keyword.operator.php",
        "storage.type.php",
        "storage.modifier.php"
      ],
      "settings": {
        "fontStyle": "bold",
        "foreground": "#0000FF"
      }
    },
    // PHP Variables
    {
      "name": "PHP Variable",
      "scope": [
        "variable.other.php",
        "punctuation.definition.variable.php"
      ],
      "settings": {
        "foreground": "#800000"
      }
    },
    // Functions
    {
      "name": "Function name",
      "scope": [
        "entity.name.function",
        "support.function"
      ],
      "settings": {
        "foreground": "#000080"
      }
    },
    // Classes
    {
      "name": "Class name",
      "scope": [
        "entity.name.class",
        "entity.name.type.class"
      ],
      "settings": {
        "fontStyle": "bold",
        "foreground": "#008080"
      }
    },
    // HTML Tags
    {
      "name": "HTML Tag",
      "scope": [
        "entity.name.tag",
        "punctuation.definition.tag"
      ],
      "settings": {
        "foreground": "#800000"
      }
    },
    // HTML Attributes
    {
      "name": "HTML Attribute",
      "scope": [
        "entity.other.attribute-name"
      ],
      "settings": {
        "foreground": "#FF0000"
      }
    },
    // CSS Properties
    {
      "name": "CSS Property",
      "scope": [
        "support.type.property-name.css"
      ],
      "settings": {
        "foreground": "#FF0000"
      }
    },
    // CSS Values
    {
      "name": "CSS Value",
      "scope": [
        "support.constant.property-value.css",
        "constant.numeric.css"
      ],
      "settings": {
        "foreground": "#0000FF"
      }
    },
    // JavaScript
    {
      "name": "JavaScript Keyword",
      "scope": [
        "keyword.control.js",
        "storage.type.js"
      ],
      "settings": {
        "fontStyle": "bold",
        "foreground": "#0000FF"
      }
    },
    // Operators
    {
      "name": "Operator",
      "scope": [
        "keyword.operator"
      ],
      "settings": {
        "foreground": "#000000"
      }
    },
    // Punctuation
    {
      "name": "Punctuation",
      "scope": [
        "punctuation"
      ],
      "settings": {
        "foreground": "#000000"
      }
    },
    // Constants
    {
      "name": "Constant",
      "scope": [
        "constant.language",
        "support.constant",
        "constant.character",
        "constant.escape"
      ],
      "settings": {
        "foreground": "#800080"
      }
    }
  ]
}
