<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthGetOtpWithHinhThucV4Action\SubAction;

use Illuminate\Support\Str;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestCronPushRecordAction\SubAction\GetMnpTokenByMerchantIdSubAction;

class TaoDeviceSessionWithMnpTokenSubAction
{
	public function run($merchantId): DeviceSession
	{
		$mnpToken = app(GetMnpTokenByMerchantIdSubAction::class)->run($merchantId);
		$deviceSessionWithToken = new DeviceSession([
			'device_id' => 1,
			'user_id' => 1,
			'api_key' => (string) Str::uuid(),
			'mnp_token' => $mnpToken
		]);

		return $deviceSessionWithToken;
	}
} // End class