<?php

namespace App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360ListDiemBanAction;

use App\Lib\MnpOnboardNewMcHelper;
use App\Exceptions\BusinessException;
use App\Lib\Helper;
use App\Lib\Logs;
use App\Lib\partner\SoundBox;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use App\Modules\TingBoxVAMC\Requests\DiemBan\Mpos360ListDiemBanRequest;
use App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360ListDiemBanAction\SubAction\GetThongTinVATrenTungMUSubAction;
use App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360ListDiemBanAction\SubAction\LoaiBoMobileUserNullSubAction;

class Mpos360ListDiemBanCaiThienAction
{
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;
	public array $listMuIds = [];

	public array $fieldMcBankSelect = [
		'account_main',
		'account_number',
		'account_holder',
		'account_qr',
		'account_qr_display',
		'status',
		'bank_branch',
		'bank_code'
	];

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run(Mpos360ListDiemBanRequest $request)
	{
		Logs::writeInfo("list diem ban param", $request->json('data'));
		
		$listStore = (new SoundBox())->getLocationByMcId([
			"mcId" => $request->json('data.merchantId'),
			"partnerCode" => Helper::getPartnerCode($request->json('data.merchantId'))
		]);

		if (empty($listStore['data'])) {
			throw new BusinessException('Chưa có thông tin điểm bán');
		}

		$listDiemBan = [];

		$isAllVAMC = collect($listStore['data']['mcStores'])->every(function ($item) {
			return !empty($item['bank']['integratedMethod']) && $item['bank']['integratedMethod'] == 'VAMC';
		});

		if (!$isAllVAMC) {
			$detailMcMnp = $this->mnpOnboardNewMcHelper->detailMcV2(['mposMcId' => $request->json('data.merchantId')]);
			
			if (empty($detailMcMnp['data'])) {
				throw new BusinessException('Không tìm thấy merchant MNP');
			}
		}

		foreach ($listStore['data']['mcStores'] as $s) {
			if (empty($s['mobileUser']) || empty($s['cityCode'])) {
				continue;
			}

			$itemDiemBan = [
				'shopInfo' => [
					'shopId' => $s['mobileUser']['muName'],
					'muId' => $s['mobileUser']['muName'],
					'shopName' => $s['name'],
					'address' => $s['address'],
					'cityId' => $s['cityCode'],
					'districtId' => $s['districtCode'] ?? '',
					'industryId' => $s['mcc'],
					'locationId' => $s['mobileUser']['muName'],
				],

				'listTingBoxDevice' => []
			];

			if (!empty($s['mobileUser']['devices'])) {
				foreach ($s['mobileUser']['devices'] as $serial) {
					$itemDiemBan['listTingBoxDevice'][] = [
						'serialNumber' => $serial,
						'status' => 'ACTIVE',
						'mobileUserId' => $s['mobileUser']['muName']
					];
				}
			} 

			$itemDiemBan['listBanking']['direct'] = [];
			$itemDiemBan['listBanking']['inter'] = [];
			$itemDiemBan['receiveType'] = 'QUA_VI_MO';
			$itemDiemBan['receiveAuto'] = [];
			$itemDiemBan['receiveVimo'] = [];

			if (!empty($s['bank']['integratedMethod']) && $s['bank']['integratedMethod'] == 'VAMC') {
				$itemDiemBan['receiveType'] = 'NHAN_TU_DONG';
				$itemDiemBan['receiveAuto'] = $this->__getReceiveAuto($s);;
			}

			if (!empty($s['bank']['integratedMethod']) && $s['bank']['integratedMethod'] == 'VANP') {
				$itemDiemBan['receiveType'] = 'QUA_VI_MO';
				$itemDiemBan['receiveVimo'] = [
					[
						'bankCode' => $s['bank']['bankCode'] ?? '',
						'bankAccountNumber' => $s['bank']['accountNo'] ?? '',
						'mcBankAccountHolderName' => $s['bank']['holderName'] ?? '',
						'mcBankAccountNumber' => $s['bank']['accountNo'] ?? '',
					]
				];
			}

			if ( empty($s['bank']) ) {
				$itemDiemBan['receiveType'] = 'QUA_VI_MO';

				if (!empty($detailMcMnp['data']['accountNo'])) {
					$itemDiemBan['receiveVimo'] = [
						[
							'bankCode' => Helper::getBankCode($detailMcMnp['data']['bankName']),
							'bankAccountNumber' =>  $detailMcMnp['data']['accountNo'],
							'mcBankAccountHolderName' => $detailMcMnp['data']['holderName'],
							'mcBankAccountNumber' => $detailMcMnp['data']['accountNo'],
						]
					];
				}
			}

			$listDiemBan[] = $itemDiemBan;
		} // End foreach store

		return [
			'merchantId' => $request->json('data.merchantId'),
			'username' => $request->json('data.username'),
			'listPos' => $listDiemBan
		];
	}

	private function __getReceiveAuto($s=[]) {
		if (	empty($s['bank']['bankCode']) 
			|| (!empty($s['bank']['bankCode']) && $s['bank']['bankCode'] != 'VCB')
		) {
			return [
				[
					'bankCode' => $s['bank']['bankCode'] ?? '',
					'bankAccountNumber' => $s['bank']['accountNo'] ?? '',
					'mcBankAccountHolderName' => $s['bank']['holderName'] ?? '',
					'mcBankAccountNumber' => $s['bank']['accountNo'] ?? '',
				]
			];
		}

		return [
			[
				'bankCode' => $s['bank']['bankCode'] ?? '',
				'bankAccountNumber' => $s['bank']['accountNo'] ?? '',
				'mcBankAccountHolderName' => '',
				'mcBankAccountNumber' => '',
			]
		];	
	}
} // End class
