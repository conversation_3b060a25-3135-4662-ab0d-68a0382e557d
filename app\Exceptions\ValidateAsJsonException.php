<?php

namespace App\Exceptions;

use Exception;
use App\Traits\ApiResponser;

class ValidateAsJsonException extends Exception
{
	use ApiResponser;

	public $errors;
	public $errorCode;
	public $message;

	public function __construct($message = '', $errorCode = 422, $errors=[])
	{
		parent::__construct($message, $errorCode);
		$this->errors = $errors;
		$this->errorCode = $errorCode;
		$this->message = $message;
	}

	public function render()
	{
		return $this->errorResponse($this->errorCode, $this->message, $this->errors);
	}
}
