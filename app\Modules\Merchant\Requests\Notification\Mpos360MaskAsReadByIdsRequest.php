<?php

namespace App\Modules\Merchant\Requests\Notification;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360MaskAsReadByIdsRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.email' => ['required', 'string', 'email', 'max:255'], 
			'data.notificationIds' => ['required', 'array'],
			'data.notificationIds.*' => ['required', 'string'],
		];
	}
}
