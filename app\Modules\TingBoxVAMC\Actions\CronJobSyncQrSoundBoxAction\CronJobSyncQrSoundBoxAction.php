<?php

namespace App\Modules\TingBoxVAMC\Actions\CronJobSyncQrSoundBoxAction;

use App\Lib\partner\SoundBox;
use App\Lib\TelegramAlertWarning;
use App\Lib\MnpOnboardNewMcHelper;
use App\Exceptions\BusinessException;
use App\Lib\Helper;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use App\Modules\Merchant\Model\Mpos360Logs\LogRequest;
use App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360DetailDiemBanAction\SubAction\HandleDiemBanDetailSubAction;
use App\Modules\TingBoxVAMC\Models\PlanEvent;

class CronJobSyncQrSoundBoxAction
{
	protected SoundBox $soundBox;
	protected MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public function __construct(SoundBox $soundBox, MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->soundBox = $soundBox;
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run()
	{
		for ($i = 1; $i <= 30; $i++) {
			$r = $this->handle();
			if (isset($r) && $r == 'EMPTY') {
				break;
			}
		}
	}

	public function handle()
	{
		$dataResponse = [];

		$merchantShopBank = MerchantShopBank::query()->where([
			'is_default' => 1,
			'status_link' => 1,
			'is_sync_tingbox' => 1,
		])->where('number_sync_tingbox', '<', 3)
			->first();


		if (!$merchantShopBank) {
			return 'EMPTY';
		}

		$merchantShopBank->is_sync_tingbox = 3;
		$merchantShopBank->save();

		$countMcShopBankDuyNhat = MerchantShopBank::query()->where('shop_id', $merchantShopBank->shop_id)
																									  ->where('id', '!=', $merchantShopBank->id)
																										->where('merchant_id', $merchantShopBank->merchant_id)
																										->where('account_type', MerchantShopBank::LOAI_TK_TRUC_TIEP)
																										->where('status_link', MerchantShopBank::STT_LINK_DA_LIEN_KET)
																										->count();

		$params = [
			"mcId" => $merchantShopBank->merchant_id,
			"mobileUserName" => $merchantShopBank->shop_id,
			"qrCode" => $merchantShopBank->account_qr,
			"qrType" => "VIETQR",
			"bankCode" => $merchantShopBank->merchantBank->bank_code,
			"accountNumber" => $merchantShopBank->account_number_partner,   // stk ngân hàng vaBankNumber
			"accountName" => $merchantShopBank->account_qr_display, // Tên hiển thị Qr cửa hàng
			'vaNextPayNumber' => $merchantShopBank->getVaNextPayNumber(),
			"mcBankAccountHolderName" => $merchantShopBank->merchantBank->account_holder, // Họ tên chủ tài khoản
			"mcBankAccountNumber" => $merchantShopBank->merchantBank->account_number, // STK ngân hàng mà MC liên kết
			"mcBankAccountMobile" => $merchantShopBank->merchantBank->bank_mobile, // SĐT mà MC đã đăng ký với NH
			"integratedMethod" => $merchantShopBank->account_type == 1 ? "VAMC" : "VANP",
			'partnerCode' => Helper::getPartnerCode($merchantShopBank->merchant_id),
		];

		if ($countMcShopBankDuyNhat < 1) {
			$params['assignDefault'] = true;
		}

		$logRequestTingBox = LogRequest::query()->forceCreate([
			'merchant_id' => $merchantShopBank->merchant_id,
			'partner' => 'tingbox',
			'func' => 'syncTingBox',
			'request' => json_encode($params, JSON_UNESCAPED_UNICODE),
			'created_at' => now()->format('Y-m-d H:i:s'),
			'updated_at' => now()->format('Y-m-d H:i:s'),
		]);


		$sendSoundBox = $this->soundBox->createVAMC($params);
		logger()->channel('stdout')->info('requestSyncSoundBox', ['param' => $params]);
		logger()->channel('stdout')->info('reponseSyncSoundBox', ['response' => $sendSoundBox]);
		$logRequestTingBox->update(['response' => json_encode($sendSoundBox, JSON_UNESCAPED_UNICODE), 'updated_at' => now()->format('Y-m-d H:i:s')]);

		if (empty($sendSoundBox)) {
			$merchantShopBank->number_sync_tingbox = (int)$merchantShopBank->number_sync_tingbox + 1;
			$merchantShopBank->time_updated = time();
			$merchantShopBank->is_sync_tingbox = 1;
			if ($merchantShopBank->number_sync_tingbox == 3) {
				TelegramAlertWarning::sendMessage(sprintf('Lỗi không gửi được QR (VA Nextpay number: %s)', $merchantShopBank->partner_request_id));
			}
			$merchantShopBank->save();
			throw new BusinessException(sprintf('Lỗi không gửi được QR (VA Nextpay number: %s)', $merchantShopBank->partner_request_id));
		};

		$merchantShopBank->is_sync_tingbox = 2;
		$merchantShopBank->time_updated = time();
		$merchantShopBank->save();



		return $dataResponse;
	}
}
