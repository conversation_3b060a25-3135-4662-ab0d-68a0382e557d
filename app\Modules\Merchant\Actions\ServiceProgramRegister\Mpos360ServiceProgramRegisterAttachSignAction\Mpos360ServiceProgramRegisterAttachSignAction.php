<?php

namespace App\Modules\Merchant\Actions\ServiceProgramRegister\Mpos360ServiceProgramRegisterAttachSignAction;

use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Mpos360PhuongThucBuoc3;
use App\Modules\Merchant\Enums\ServiceProgramRegisterEnum;
use App\Modules\Merchant\Model\Mpos360MerchantRequestServiceProgramRegister;
use App\Modules\Merchant\Requests\ServiceProgramRegister\Mpos360ServiceProgramRegisterAttachSignRequest;

class Mpos360ServiceProgramRegisterAttachSignAction
{

	public function run(Mpos360ServiceProgramRegisterAttachSignRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		$merchantId = $deviceSession->getMerchantId();

		$yeuCauDangKyDichVu = Mpos360MerchantRequestServiceProgramRegister::query()->firstWhere([
			'merchant_id' => $merchantId,
			'id' => $request->json('data.request_id')
		]);

		if (!$yeuCauDangKyDichVu) {
			throw new BusinessException('Không tìm thấy yêu cầu đăng ký dịch vụ');
		}

		$dataRequest = json_decode($yeuCauDangKyDichVu->data_request, true);
		$dataRequest['signatureUrl'] = $request->json('data.signatureUrl');
		
		$r = $yeuCauDangKyDichVu->update([
			'data_request' => json_encode($dataRequest),
			'time_updated' => now()->timestamp
		]);

		if (!$r) {
			throw new BusinessException('Lỗi không thêm được chữ ký vào yêu cầu của bạn');
		}

		$listPhuongThucScanBuoc3 = Mpos360PhuongThucBuoc3::getPhuongThucBuoc3();
		
		$listPhuongThucScanBuoc3 = $listPhuongThucScanBuoc3->filter(function (Mpos360PhuongThucBuoc3 $pt)  {
			return $pt['method_code'] == 'SDK';
		})->map(function (Mpos360PhuongThucBuoc3 $pt) {
			return [
				'method_code' => $pt->method_code,
				'method_name' => $pt->method_code,
				'is_main' => $pt->priority > 0 ? 'YES' : 'NO'
			];
		})
		->values()
		->toArray();
		
		return [
			'can' => ServiceProgramRegisterEnum::CAN_SPR_GOTO_HOAN_THANH,
			'data' => [
				'request_id' => $yeuCauDangKyDichVu->id,
				'step3' => $listPhuongThucScanBuoc3
			],
			'msg' => 'Bạn cần làm xác thực bước 3'
		]; 
	}
} // End class