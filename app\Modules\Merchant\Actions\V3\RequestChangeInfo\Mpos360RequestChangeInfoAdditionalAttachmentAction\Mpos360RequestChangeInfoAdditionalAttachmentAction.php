<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction;

use Exception;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoAdditionalAttachmentRequest;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\SubAction\MappingDinhKemV3SA;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\SubAction\KiemTraPhuongThucKyV3SA;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\SubAction\ValidateProfileDinhKemSA;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\SubAction\XuLyUploadBoSungThongTinV3SA;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\SubAction\GetPhuongThucQuetB3Ver2SubAction;

class Mpos360RequestChangeInfoAdditionalAttachmentAction
{
	public function run(Mpos360RequestChangeInfoAdditionalAttachmentRequest $request)
	{
		$currentDeviceSession = $request->getCurrentDeviceSession();

		$mpos360McRequest = Mpos360MerchantRequest::query()
		->with('mpos360McSupplementNew')
		->firstWhere([
			'id' => $request->json('data.id'),
			'merchant_id' => $currentDeviceSession->getMerchantId()
		]);

		if (!$mpos360McRequest) {
			throw new BusinessException(vmsg('Không tìm được bản ghi yêu cầu thay đổi'));
		}


		$additionalProfiles = app(ValidateProfileDinhKemSA::class)->run($mpos360McRequest, $request);
		$attachments = $request->json('data.attachments', []);

		$profilesImgUrl = app(MappingDinhKemV3SA::class)->run($attachments);
		
		if ($mpos360McRequest->mpos360McSupplementNew) {
			return app(XuLyUploadBoSungThongTinV3SA::class)->run($mpos360McRequest, $profilesImgUrl, $request);
		}

		$dataRequest = json_decode($mpos360McRequest->data_request, true);

		foreach ($additionalProfiles as $profile) {
			$profileKey = $profile['profileKey'];
			$profileValue = $profile['value'];

			$dataRequest[0]['profiles'][$profileKey] = $profileValue;
		}

		foreach ($profilesImgUrl as $profileKey => $profileValue) {
			$dataRequest[0]['profiles'][$profileKey] = $profileValue;
		}

		if (empty($dataRequest[0]['scan_method'])) {
			$dataRequest[0]['scan_method'] = (object)[];
		}

		$dataRequest[0]['raw_attachments'] = $attachments;
		
		$qtsRequestId = $request->json('data.qts_request_id', '');
		
		if (!empty($qtsRequestId)) {
			$dataRequest[0]['qts_request_id_nguoi_dc_uy_quyen'] = $qtsRequestId;
		}


		$mpos360McRequest->data_request = json_encode($dataRequest);

		/**
		 * API dùng chung cho luồng tạo và luồng bổ sung hồ sơ
		 * Nên cần check: nếu đã thực hiện B3 thì không cần update trường này nữa
		 */
		if ($mpos360McRequest->status_verify != Mpos360Enum::MPOS360_MC_VERIFY_STT_DA_THUC_HIEN_BUOC3) {
			$mpos360McRequest->status_verify = Mpos360Enum::MPOS360_MC_VERIFY_STT_DA_XAC_THUC;
		}
		
		$savedResult = $mpos360McRequest->save();

		if (!$savedResult) {
			throw new BusinessException('Lỗi không thể bổ sung thông tin');
		}

		$kiemTraPhuongThucKy = app(KiemTraPhuongThucKyV3SA::class)->run($mpos360McRequest);

		return [
			'id'               => $mpos360McRequest->id,
			'msg'              => vmsg('Bổ sung tài liệu đính kèm thành công'),
			'status'           => 'SUCCESS',
			'can'              => $kiemTraPhuongThucKy['can'],
			'list_sign_method' => $kiemTraPhuongThucKy['sign_method'],
			'scan_method'      => app(GetPhuongThucQuetB3Ver2SubAction::class)->run($mpos360McRequest->merchant_id)
		];
	} // End method
} // End class
