<?php

namespace App\Modules\Merchant\Controllers\Transaction;

use App\Lib\Helper;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionBAFReceiveListRequest;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionBAFReceiveDetailRequest;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransactionBAFReceiveDetail\Mpos360DetailPhieuChiAction;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransactionBAFReceiveDetail\Mpos360TransactionBAFReceiveDetailAction;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransactionBAFReceiveListAction\Mpos360TransactionBAFReceiveListAction;

// giao dichj nhan tien ve TKNH
class Mpos360TransactionBAFReceiveController extends Controller
{
	public function Mpos360TransactionBAFReceiveList(Mpos360TransactionBAFReceiveListRequest $request)
	{
		try {
			$result = app(Mpos360TransactionBAFReceiveListAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}



	public function Mpos360TransactionBAFReceiveDetail(Mpos360TransactionBAFReceiveDetailRequest $request)
	{
		try {
			$result = app(Mpos360DetailPhieuChiAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
} // End class
