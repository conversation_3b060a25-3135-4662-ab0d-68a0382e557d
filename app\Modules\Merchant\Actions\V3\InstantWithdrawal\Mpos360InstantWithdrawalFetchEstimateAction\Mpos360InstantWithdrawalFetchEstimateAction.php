<?php

namespace App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalFetchEstimateAction;

use App\Lib\DBConnectionHelper;
use App\Lib\Logs;
use Exception;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use App\Modules\Merchant\Model\Setting;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360Logs\LogRequest;
use App\Modules\Merchant\Requests\InstantWithdrawal\V3\Mpos360InstantWithdrawalFetchEstimateRequest;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalFetchEstimateAction\SubAction\GetThongTinTamTinhSubAction;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalFetchEstimateAction\SubAction\XuLyGiaoDichVietQrHopLeSubAction;

class Mpos360InstantWithdrawalFetchEstimateAction
{
	public function returnStayThere($message='', $alignment='center') {
		return [
			'is_show_create_rq_btn' => '0',
			'msg' => '',
			'other_data' => (object) [
				'msg' => [
					'text' => '{message}',
					'alignment' => $alignment,
					'data' => [
						'message' => [
							'text' => $message,
							'text_color' => '#404041'
						]
					]
				]
			],

			'banking_info' => (object) [],
			'status' => 'ERROR',
			'can' => Mpos360Enum::MPOS360_RTN_CAN_STAY_THERE,
			'data' => []
		];
	}

	public function run(Mpos360InstantWithdrawalFetchEstimateRequest $request)
	{
		Logs::writeInfo("Mpos360InstantWithdrawalFetchEstimateAction", $request->all());
		
		$deviceSession = $request->getCurrentDeviceSession();
		$merchantId = $deviceSession->getMerchantId();

		$settingCreateIW = Setting::query()->firstWhere(['key' => 'CONFIG_RUT_TIEN_NGAY']);
		DBConnectionHelper::closeIfExist();
		
		$estimateResult = app(GetThongTinTamTinhSubAction::class)->run($deviceSession);
		$mposApiCode = $estimateResult['data']['error']['code'] ?? false;
		

		if (!empty($mposApiCode) && $mposApiCode == Mpos360Enum::API_OVER_HAN_MUC_RUT_TIEN_NGAY) {
			return $this->returnStayThere(
				$estimateResult['data']['error']['message']
			);
		}

		if (
			empty($mposApiCode) || 
			( !empty($mposApiCode) && $mposApiCode != Mpos360Enum::API_SUCCESS_CODE)
		) {
			$msg = $estimateResult['data']['error']['message'] ?? 'Mpos Err: Unknow Info';
			$code = $mposApiCode ?? '00';
			$msg = sprintf('%s (%s)', $msg, $code);
			return $this->returnStayThere($msg);
		}

		// Không có giao dịch
		if (empty($estimateResult['data']['countTransaction'])) {
			return $this->returnStayThere(
				__('rtn.Hiện chưa có giao dịch nào đủ điều kiện nhận tiền nhanh về tài khoản ngân hàng. Qúy khách vui lòng quay lại sau!')
			);
		} 

		// Validate khoảng thời gian rút tiền
		// if (!$this->isSupportTime($settingCreateIW)) {
		// 	return $this->returnStayThere(
		// 		__('rtn.Tính năng chỉ hỗ trợ trong giờ hành chính: Thứ 2 - Thứ 6: 08:30 - 18:00, riêng Thứ 7: 08:00 - 11:30')
		// 	);
		// }

		// Validate số lần tạo yc rút  trong ngày
		if (!$this->isSoLanTaoYcLaHopLe($settingCreateIW, $merchantId)) {
			return $this->returnStayThere(
				__('rtn.Bạn đã thực hiện quá số lần tạo yêu cầu rút tiền ngay hôm nay rồi. Vui lòng quay lại vào ngày mai')
			);
		}
		
		// Validate min-max
		$soTienToiDaCoTheRut = $estimateResult['data']['sumAmountAfterFee'];
		if (!$this->isThoaManMinMax($settingCreateIW, $soTienToiDaCoTheRut)) {
			return $this->returnStayThere(
				__('rtn.Số tiền thực nhận của bạn đang nhỏ hơn mức min mà hệ thống cấu hình.')
			);
		}

		// Cho phép rút tiền
		$bankingAccountAsText = $this->buildTextBankAccount($estimateResult);

		$returnData = [
			'is_show_create_rq_btn' => '1',
			'msg' => '',
			'other_data' => (object) [
				'msg' => [
					'text' => '{message}',
					'alignment' => 'center',
					'data' => [
						'message' => [
							'text' => $this->handleIfAgribank($bankingAccountAsText),
							'text_color' => '#dd8523',
						]
					]
				],
				'banking_info' => [
					'text' => '{message}:
				{bankingAccountContent}',
					'alignment' => 'center',
					'data' => [
						'message' => [
							'text' => 'Tài khoản nhận tiền',
							'text_color' => '#808890',
							'font_style' => 'italic'
						],

						'bankingAccountContent' => [
							'text' => $bankingAccountAsText,
							'font_weight' => 'bold',
							'text_color' => '#404041',
							'font_size' => '16'
						]
					]
				] 
			],
			'banking_info' => '',
			'status' => 'SUCCES',
			'can' => Mpos360Enum::MPOS360_RTN_CAN_CREATE_YC,
			'data' => app(XuLyGiaoDichVietQrHopLeSubAction::class)->run(
				$estimateResult, 
				$this->getSoTienMaxCoTheRut($settingCreateIW, $soTienToiDaCoTheRut)
			)
		];
		
		return $returnData;
	} // End method

	public function buildTextBankAccount($estimateResult): string {
		if (!empty($estimateResult['data']['data']['bankAccountNumber'])) {
			return sprintf(
				'%s - %s' .PHP_EOL. '%s - %s',
				$estimateResult['data']['data']['bankAccountNumber'],
				$estimateResult['data']['data']['bankAccountHolderName'],
				$estimateResult['data']['data']['bankName'],
				$estimateResult['data']['data']['bankBranch'],
			);
		}

		return __('rtn.Không có thông tin TKNH');
	}
	
	public function isSupportTime(Setting $settingCreateIW): bool
	{
		$settingValue = json_decode($settingCreateIW->value, true);
		$currentDayName = now()->format('l');
		if (empty($settingValue['date'][$currentDayName])) {
			return false;
		}

		$rangeTime = $settingValue['date'][$currentDayName];
		$startTime = Carbon::createFromFormat('H:i', $rangeTime[0]);
		$endTime = Carbon::createFromFormat('H:i', $rangeTime[1]);
		
		$expression = now()->gte($startTime) && now()->lt($endTime);
		return $expression;
	}

	public function isSoLanTaoYcLaHopLe(Setting $settingCreateIW, $merchantId): bool {
		$settingValue = json_decode($settingCreateIW->value, true);
		$soLanTaoYcMax = $settingValue['number_of_times_per_day'] ?? 1;

		if ($soLanTaoYcMax == 'NONE') {
			return true;
		}

		// Đoạn này cần acount DB Logs
		$count = LogRequest::query()->where('partner', 'mpos')
																->where('func', 'PAYMENT_NOW')
																->where('merchant_id', $merchantId)
																->whereNotNull('partner_record_id')
																->whereDate('created_at', now()->format('Y-m-d'))
																->count();

		return $count < $soLanTaoYcMax;
	}

	public function isThoaManMinMax(Setting $settingCreateIW, $soTienToiDaCoTheRut=0) {
		$settingValue = json_decode($settingCreateIW->value, true);
		$rangeAmount = $settingValue['range_amount'];

		if ($rangeAmount[0] == 'NONE' || $rangeAmount[1] == 'NONE') {
			return true;
		}

		if ($soTienToiDaCoTheRut < Arr::first($rangeAmount)) {
			return false;
		}

		return true;
	}

	public function getSoTienMaxCoTheRut(Setting $settingCreateIW, $soTienToiDaCoTheRut=0) {
		$settingValue = json_decode($settingCreateIW->value, true);
		$rangeAmount = $settingValue['range_amount'];
		
		if ($rangeAmount[0] == 'NONE' || $rangeAmount[1] == 'NONE') {
			return ['is_over' => false, 'max_amount' => $soTienToiDaCoTheRut];
		}

		if ($soTienToiDaCoTheRut >= Arr::last($rangeAmount)) {
			return ['is_over' => true, 'max_amount' => Arr::last($rangeAmount)];
		}

		return ['is_over' => false, 'max_amount' => $soTienToiDaCoTheRut];
	}

	public function handleIfAgribank($bankingAccountAsText='') {
		$lowerCase = mb_strtolower($bankingAccountAsText);

		// Là agribank
		// if (Str::contains($lowerCase, ['agribank', 'agr'])) {
		// 	return 'Đối với tài khoản nhận tiền là ngân hàng Agribank, Các yêu cầu được tạo từ 8h30 - 17h30, thứ 2 đến thứ 6, sẽ được xử lý trong vòng 2 giờ làm việc.' .PHP_EOL. 'Ngoài khung giờ trên sẽ xử lý vào ngày hôm sau, không hỗ trợ thứ 7 và Chủ nhật';
		// }

		if (Str::contains($lowerCase, ['co-opbank', 'coopbank'])) {
			return 'Đối với tài khoản nhận tiền là ngân hàng CO-OPBANK, Các yêu cầu được tạo từ 8h30 - 17h30, thứ 2 đến thứ 6, sẽ được xử lý trong vòng 2 giờ làm việc.' .PHP_EOL. 'Ngoài khung giờ trên sẽ xử lý vào ngày hôm sau, không hỗ trợ thứ 7 và Chủ nhật';
		}

		return 'Nhận tiền ngay với các yêu cầu tạo trong khoảng thời gian từ 8h30 - 21h00 hằng ngày.' .PHP_EOL. 'Các yêu cầu sau 21h00 sẽ được xử lý và chuyển khoản vào 8h30 sáng ngày hôm sau.';
	}
} // End clas
