<?php

namespace App\Modules\TingBoxVAMC\Actions\MerchantBank\GetInfoBankAction;

use Illuminate\Support\Str;
use App\Lib\MnpOnboardNewMcHelper;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Setting;
use App\Modules\TingBoxVAMC\Models\BankVAMC;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\GetInfoBankRequest;
use Illuminate\Database\Eloquent\Collection;

class GetInfoBankAction
{
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public string $isShowTrungGian = 'YES';

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run(GetInfoBankRequest $request)
	{
		$nganhNgheNganHangThanhPho = $this->mnpOnboardNewMcHelper->getNganhNgheNganHangThanhPho(true);

		$detailMc = $this->mnpOnboardNewMcHelper->detailMc([
			'mposMcId' => $request->json('data.merchantId')
		]);


		if ( empty($detailMc['data']) ) {
			throw new BusinessException('Lỗi không lấy được thông tin merchant');
		}

		$holderName = '';

		if (!empty($detailMc['data']['holderName'])) {
			$holderName = trim($detailMc['data']['holderName']);
		} elseif (!empty($detailMc['data']['customerName'])) {
			$holderName = Str::of($detailMc['data']['customerName'])->slug(' ')->upper()->trim()->__toString();
		}

		if (!empty($detailMc['data']['bankId'])) {
			$this->isShowTrungGian = 'NO';
		}

		$listBankVamc = BankVAMC::query()->where('isDisplay', '!=', BankVAMC::DISPLAY_NONE)
																	   ->where('blockOldVersion', 0)
																	   ->orderBy('sort', 'DESC')
																		 ->get();

		$listDisplay = $listBankVamc->filter(function (BankVAMC $it) {
			return $it->isDisplay == BankVAMC::DISPLAY_PROD;
		});

		$settingDungThuVamc = Setting::query()->firstWhere(['key' => 'LIST_USERNAME_DUNG_THU_VAMC']);

		$listBanhThuNghiem = Collection::make([]);

		if ($settingDungThuVamc) {
			$listUsernameDungThu = json_decode($settingDungThuVamc->value, true);
			$listBanhThuNghiem = $listBankVamc->filter(function (BankVAMC $b) use ($detailMc, $listUsernameDungThu) {
				return $b->isDisplay == BankVAMC::DISPLAY_DEV_MODE && in_array($detailMc['data']['username'], $listUsernameDungThu);
			});
		}

		$warningText = 'Khi xác nhận thay đổi, mã QR trên loa Tingbox sẽ tự động cập nhật, mã QR dán giấy cũ (nếu có) sẽ không được sử dụng được. Vui lòng tải mã QR mới tại menu \'QR cửa hàng\', in và dán lại';
		
		$listBankVamc = $listDisplay->concat($listBanhThuNghiem);
		
		$listBankVamc = $listBankVamc->map(function (BankVAMC $it) use ($request) {
			$it->termsLink = $it->getTermLinkByVersionApp($request->get('versionApp'));
			return $it;
		})->values()->toArray();

		$returnData = [
			'listBankCommon' => $nganhNgheNganHangThanhPho['banks'],
			'listBankDirect' => $listBankVamc,
			'holderName' => $holderName,
			'cardIdentity' => isset($detailMc['data']) && isset($detailMc['data']['passport']) ? $detailMc['data']['passport'] : '',
			'isShowTrungGian' => $this->isShowTrungGian,
			'warningText' => $warningText
		];


		return $returnData;
	}
}
