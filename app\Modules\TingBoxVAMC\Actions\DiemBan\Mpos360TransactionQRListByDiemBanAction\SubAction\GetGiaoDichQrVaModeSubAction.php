<?php

namespace App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360TransactionQRListByDiemBanAction\SubAction;

use Carbon\Carbon;
use App\Lib\Helper;
use Illuminate\Support\Str;
use App\Lib\partner\MposAWS;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransQRListAction\Mpos360TransQRListSubAction\TransQRMappingSubAction;

class GetGiaoDichQrVaModeSubAction
{
	public MposAWS $mposAWS;

	public function __construct(MposAWS $mposAWS)
	{
		$this->mposAWS = $mposAWS;
	}

	public function getData($params)
	{
		$data = [];
		$result = $this->mposAWS->getListGiaoDichQr($params);
		
		if (isset($result['status_code_partner']) && $result['status_code_partner'] == 1000) {
			$result['data']['data'] = collect($result['data']['data'])->filter(function ($item) {
				return !empty($item['txid']);
			})
				->sortByDesc(function ($item) {
					return Carbon::createFromFormat('d/m/Y H:i:s', $item['createdDate'])->timestamp;
				})
				->values()
				->all();

			$data['data'] = $result['data']['data'];
			$data['total'] = count($result['data']['data']);
		};

		$data = $this->mappingData($data);

		return $data;
	}

	public function getTenChuTheVa4SoCuoi($tranItem)
	{
		try {
			$returnString = '';
			if (!empty($tranItem['cardHolderName'])) {
				$returnString = Str::limit($tranItem['cardHolderName'], 20) . '....';
				if (!empty($tranItem['pan'])) {
					$returnString .= substr($tranItem['pan'], -4);
					return $returnString;
				}
			}

			return $tranItem['pan'] ?? "";
		} catch (\Throwable $th) {
			@mylog(['ErrPan' => Helper::traceError($th)]);
			return $tranItem['pan'] ?? "";
		}
	}

	public function getStatusViaMode($statusArr, $tranStatus) {
		// if (request()->json('data.vaMode') == 'VANP') {
		// 	return $statusArr[$tranStatus] ?? [];
		// }

		return $statusArr['SUCCESS'];
	}
	
	public function mappingData($data)
	{
		$statusArr = app(TransQRMappingSubAction::class)->mappingStatusTransaction();
		$transTypeArr = app(TransQRMappingSubAction::class)->mappingTransactionType();
		$dataArr = [];
		$returnData = [];
		if (!empty($data['data'])) {
			foreach ($data['data'] as $key => $tranItem) {
				$carbon = Carbon::createFromFormat('d/m/Y H:i:s', $tranItem['createdDate']);
				
				$timeD = $carbon->format('d-m-Y');
				
				if ($carbon->isToday()) {
					$timeD = __('trans.title.Hôm nay');
				}

				$note = $tranItem['reason'] ?? '';
				if (!empty($tranItem['paymentTime'])) {
					$paymentTimeCarbon = Carbon::createFromFormat('d/m/Y H:i:s', $tranItem['paymentTime']);
					if (!$paymentTimeCarbon->isSameDay($carbon)) {
						$note = sprintf('KH thanh toán: %s, %s', $paymentTimeCarbon->format('H:i'), $paymentTimeCarbon->format('d/m/Y'));
					}
				}

				$stt = $this->getStatusViaMode($statusArr, $tranItem['status']);

				$dataArr[$timeD][] = [
					'raw_date' => $carbon->format('d-m-Y'),
					'txid' => $tranItem['txid'],
					'type' => $transTypeArr[$tranItem['transactionType']]['label'] ?? $tranItem['transactionType'] ?? '',
					'icon' => '',
					'amount' => !empty($tranItem['amount']) ? Helper::priceFormat($tranItem['amount']) : '',
					'card' => $this->getTenChuTheVa4SoCuoi($tranItem),
					'time_created' => $carbon->format('H:i'),
					'status' => $stt['label'] ?? '',
					'note' => $note,
					'user_mobile' => $tranItem['muid'],
					'other_data' => (object)[
						'status' => [
							'text' => $stt['label'] ?? '',
							'text_color' => $stt['text_color'] ?? '',
							'bg_color' => $stt['bg_color'] ?? '',
							'display_type' => 'pills'
						],
					],
				];
			}

			foreach ($dataArr as $key => $tranItem) {
				$returnData[] = [
					'date' => $key,
					'total' => count($tranItem),
					'list' => $tranItem,
				];
			}
		}

		return $returnData;
	}
}
