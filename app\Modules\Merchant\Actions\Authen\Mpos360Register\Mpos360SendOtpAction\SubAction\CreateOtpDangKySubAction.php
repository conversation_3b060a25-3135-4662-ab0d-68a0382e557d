<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360SendOtpAction\SubAction;

use App\Lib\OtpHelper;
use Illuminate\Http\Request;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360CodeOtp;

class CreateOtpDangKySubAction
{
	public int $timeExpiredOtp = 5*60;

	// có $otpId thì được tính là gửi lại
	public function run(Request $request, string $otpId=''): Mpos360CodeOtp
	{
		if ( empty($otpId) ) {
			$mpos360CodeOtp = Mpos360CodeOtp::query()->forceCreate([
				'command_code' => 'REGISTER',
				'service_code' => strtoupper($request->json('data.channel')), // kênh nhận otp
				'user_id'      => '',
				'obj_value'    => $request->json('data.username'),
				'message'      => base64_encode('Noi dung'),
				'otp'          => generateRandomNumber(6),
				'reference_id' => '',
				'status'       => Mpos360Enum::MPOS360_OTP_CHUA_SU_DUNG,
				'time_out'     => now()->addSeconds($this->timeExpiredOtp)->timestamp,
				'time_created' => now()->timestamp,
				'time_updated' => now()->timestamp,
			]);
	
			if (!$mpos360CodeOtp) {
				throw new BusinessException('Lỗi không tạo được bản ghi OTP');
			}
		}
		
		if ( !empty($otpId) ) {
			$mpos360CodeOtp = Mpos360CodeOtp::query()
																			->where('command_code', 'REGISTER')
																			->where('obj_value', $request->json('data.username'))
																			->where('id', $otpId)
																			->first();
			if (!$mpos360CodeOtp) {
				throw new BusinessException('Không tìm được mã otp của bạn');
			}

			$mpos360CodeOtp->status = Mpos360Enum::MPOS360_OTP_CHUA_SU_DUNG;
			$mpos360CodeOtp->time_out = now()->addSeconds($this->timeExpiredOtp)->timestamp;
			$mpos360CodeOtp->time_updated = now()->timestamp;
			$mpos360CodeOtp->otp = generateRandomNumber(6);
			$r = $mpos360CodeOtp->save();

			if (!$r) {
				throw new BusinessException('Lỗi không thể gửi lại mã otp đăng ký mới');
			}
		}

		return $mpos360CodeOtp;
	}
} // End clas