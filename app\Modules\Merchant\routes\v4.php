<?php

use Illuminate\Support\Facades\Route;
use App\Http\Middleware\MakeSureThatRequestIsJsonMiddleware;
use App\Modules\Merchant\Controllers\Authen\Mpos360\V4\Mpos360LogV4Controller;
use App\Modules\Merchant\Controllers\Authen\Mpos360\V4\Mpos360AuthV4Controller;

/**
 * Big update 18.11.2024 với các tính năng bên dưới
		Version iếp theo là triển khai cái gì
		
		1-B<PERSON> xung thông tin luồng yêu cầu điều chỉnh
		Chi tiết YC->bổ xung->không phải xác thực (không on/off)
		MyNP bào T4 mới hỗ trợ
		Liên quan đến luồng ủy quyền thì cần bổ xung thêm màn hình cho upload thêm giấy tờ
		(hỗ trợ chụp ảnh, chọn ảnh)

		C1: (<PERSON>h<PERSON><PERSON> giấy ờ CCCD mặt trước ơ màn hình giấy tờ liên quan)
		C2: Đ<PERSON>a vào thành 1 màn hình mới --->OK 



		2-Dung lượng upload video (lv với myNP)
		(cấu hình dung lượng file được upload/lần đưa vào setting)

		3-Push notification
		(t3 là có API + test)

		4-Màn hình luồng NTN cần review điều chỉnh lại
		(cung cấp API để MPOS gửi lại mã lỗi)
		-----> Update các màn hình liên quan đên giao diện  ĐoànĐT lv với anh Đ.AnhTD  
		-----> Bổ xung chọn thời gian được rút đến thời gian nào trong quá khứ. (VD hiện tại là 11h30, tôi chỉ muốn rút đến 9j thôi). Nếu có time bắt đầu và kết thúc được thì OK hơn

		5-Đến giao dịch (count, sum)
		(đã có API+doc chờ mobile)

		6-Quay trục tiếp video
		10h hôm nay có thể test
		- Thời gian <để 1p>
		- Nén tối đã bao nhiêu MB?
 

		7-Đăng nhập theo luồng MC chỉ dụng dv VietQR
		8-Quên MK đưa về app
		9-Sau khi đăng nhập thành công mà MC chưa bật sing trắc thì bật thông báo và đưa đến chức năng khai báo sinh trắc
 */
Route::group([
	'prefix' => 'v4', 
	'middleware' => [MakeSureThatRequestIsJsonMiddleware::class]
], function () {
	// Kiểm tra sinh trắc học (có thể làm ngoài đăng nhập hoặc )
	Route::any('/Mpos360GetBioMetric', [
		'uses' => Mpos360AuthV4Controller::class . '@Mpos360GetBioMetric',
		'as' => 'Mpos360GetBioMetricAction' 
	])->middleware('checkSumForAnyMobile:email|os|deviceToken');  

	// Cập nhật sinh trắc học
	Route::any('/Mpos360UpdateBioMetric', [
		'uses' => Mpos360AuthV4Controller::class . '@Mpos360UpdateBioMetric',
		'as' => 'Mpos360UpdateBioMetricAction'
	])->middleware('checkSumForAnyMobile:email|os|deviceToken|bioMetricType|bioMetricData');

/*----------------luồng đăng nhập (VietQR) + quên mật khẩu dùng chung---------------*/ 
	Route::any('/Mpos360AuthGetHinhThucNhanOtp', [
		'uses' => Mpos360AuthV4Controller::class . '@Mpos360AuthGetHinhThucNhanOtp',
		'as' => 'Mpos360AuthGetHinhThucNhanOtpAction'
	])->middleware('checkSumForAnyMobile:username|hasLogined'); 

	Route::any('/Mpos360AuthGetOtpForgotPassword', [
		'uses' => Mpos360AuthV4Controller::class . '@Mpos360AuthGetOtpWithHinhThuc',
		'as' => 'Mpos360AuthGetOtpWithHinhThucAction'
	])->middleware('checkSumForAnyMobile:username|channel|value|merchant_id|otp_id');

	Route::any('/Mpos360AuthVerifyOtpForgotPassword', [
		'uses' => Mpos360AuthV4Controller::class . '@Mpos360AuthVerifyOtpForgotPassword',
		'as' => 'Mpos360AuthVerifyOtpForgotPasswordAction'
	])->middleware('checkSumForAnyMobile:username|otp|otp_id|merchant_id');;

	Route::any('/Mpos360AuthForgotGetNewPassword', [
		'uses' => Mpos360AuthV4Controller::class . '@Mpos360AuthForgotGetNewPassword',
		'as' => 'Mpos360AuthForgotGetNewPasswordAction'
	])->middleware('checkSumForAnyMobile:username|otp_id|password|password_confirmation');
/*----------------/luồng đăng nhập (VietQR) + quên mật khẩu dùng chung---------------*/ 
});



