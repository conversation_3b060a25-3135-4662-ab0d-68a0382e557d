<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction;

use Illuminate\Support\Arr;

class BuildListNganHangCoLogoSubAction
{
	public function run($listBankFromMnp = [])
	{
		$returnData = [];
		foreach ($listBankFromMnp as $bankId => $bankItem) {
			$explodeName = explode(' - ', $bankItem);
			$bankCode = trim(Arr::first($explodeName));

			$returnData[] = [
				'bankId' => $bankId,
				'bankCode' => $bankCode,
				'bankNameDisplay' => trim(Arr::last($explodeName)),
				'logoUrl' => ''
			];
		}
		
		return $returnData;
	}
} // End class