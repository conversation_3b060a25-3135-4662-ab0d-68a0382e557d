<?php

namespace App\Modules\Merchant\Actions\ServiceProgramRegister\Mpos360ServiceProgramRegisterPushRecordAction;

use App\Exceptions\BusinessException;
use Exception;
use App\Lib\Helper;
use App\Modules\Merchant\Actions\ServiceProgramRegister\Mpos360ServiceProgramRegisterPushRecordAction\SubAction\PushYcDangKyDichVuSubAction;
use App\Modules\Merchant\Enums\ServiceProgramRegisterEnum;
use App\Modules\Merchant\Model\Mpos360MerchantRequestServiceProgramRegister;
use Illuminate\Support\Facades\DB;

class Mpos360ServiceProgramRegisterPushRecordAction
{
	public array $returnData = [];

	private array $__exceptIds = [];

	public int $maxPushCount = 3;

	public function run(): array
	{
		for ($i = 1; $i <= 50; $i++) {
			try {
				$result = $this->handle();

				if ($result === 'EMPTY') {
					$this->returnData[] = 'EMPTY';
					break;
				}

				if (optional($result)->id) {
					$this->returnData[] = $result->id;
				}
			} catch (\Throwable $th) {
				mylog(['Loi xu ly chung thuc' => Helper::traceError($th)]);
				continue;
			}
		}

		return $this->returnData;
	}

	public function handle()
	{
		$mpos360RequestServiceProgram = Mpos360MerchantRequestServiceProgramRegister::query()
			->where('status', ServiceProgramRegisterEnum::SPR_STT_TAO_XONG_YC)
			->where('record_push_count', '<', $this->maxPushCount);

		if (!empty($this->__exceptIds)) {
			$mpos360RequestServiceProgram = $mpos360RequestServiceProgram->whereNotIn('id', $this->__exceptIds);
		}

		$mpos360RequestServiceProgram = $mpos360RequestServiceProgram->first();

		if (!$mpos360RequestServiceProgram) {
			return 'EMPTY';
		}

		$this->__exceptIds[] = $mpos360RequestServiceProgram->id;

		mylog([
			'id' => 'PushYc_' . $mpos360RequestServiceProgram->id,
			'service' => $mpos360RequestServiceProgram->service_program_id
		]);

		// Update thành đang đẩy yc
		$r = $mpos360RequestServiceProgram->where(['id' => $mpos360RequestServiceProgram->id, 'status' => ServiceProgramRegisterEnum::SPR_STT_TAO_XONG_YC])
																 			->update([
																				'status' => ServiceProgramRegisterEnum::SPR_STT_DANG_DAY_YC,
																				'record_push_count' => DB::raw('record_push_count+1')
																			]);
		
		if (!$r) {
			throw new BusinessException('Lỗi: không cập nhật lên được là đang đẩy yêu cầu');
		}

		$record = $mpos360RequestServiceProgram->refresh();
		
		try {
			$pushYc = app(PushYcDangKyDichVuSubAction::class)->run($record);
			// Update về thành công
			if (!empty($pushYc['result'])) {
				$wasUpdateSuccess = Mpos360MerchantRequestServiceProgramRegister::query()
									->where('id', $record->id)
									->update([
										'status' => ServiceProgramRegisterEnum::SPR_STT_DA_DAY_YC,
										'time_updated' => now()->timestamp,
										'response' => json_encode($pushYc)
									]);

				return $record;
			}

			// Nếu còn lần thì đưa về trạng thái Tạo xong yc để đẩy sau
			if ($record->record_push_count <= $this->maxPushCount) {
				$wasUpdateReadyAgain = Mpos360MerchantRequestServiceProgramRegister::query()
									->where('id', $record->id)
									->where('status', ServiceProgramRegisterEnum::SPR_STT_DANG_DAY_YC)
									->update([
										'status' => ServiceProgramRegisterEnum::SPR_STT_TAO_XONG_YC,
										'time_updated' => now()->timestamp,
										'response' => json_encode($pushYc)
									]);

				return $record;
			}else {
				// Đưa về trạng thái hủy
				$wasUpdateReadyAgain = Mpos360MerchantRequestServiceProgramRegister::query()
									->where('id', $record->id)
									->where('status', ServiceProgramRegisterEnum::SPR_STT_DANG_DAY_YC)
									->update([
										'status' => ServiceProgramRegisterEnum::SPR_STT_DAY_YC_LOI,
										'time_updated' => now()->timestamp,
										'response' => json_encode($pushYc)
									]);

				return $record;
			}
		}catch(\Throwable $th) {
			mylog(['Err' => Helper::traceError($th)]);
		}

		return $record;
	} // End method
} // End class