<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthGetOtpWithHinhThucV4Action\SubAction;

use Exception;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360CodeOtp;

class TaoOtpTheoHinhThucSubAction
{
	/**
	 * Class này mục đích fake ra 1 bản ghi otp để làm cầu nối gửi vào hàm gửi mail, sms/ zalo
	 */
	public function run($params = [], $getOtpMposResult, string $otpId): Mpos360CodeOtp
	{
		// Tạo otp mới
		if (empty($otpId)) {
			$paramCreate = [
				'command_code' => $params['command_code'],
				'service_code' => $params['channel'], // kênh nhận otp
				'user_id'      => $params['user_id'], // userId
				'obj_value'    => $params['value'], // giá trị
				'message'      => base64_encode('Noi dung'),
				'otp'          => $getOtpMposResult['otp'],
				'reference_id' => $params['merchant_id'], // giá trị
				'status'       => Mpos360Enum::MPOS360_OTP_CHUA_SU_DUNG,
	
				// Đoạn này phải lấy thời gian của mpos mà hành xử
				'time_out'     => now()->timestamp + $getOtpMposResult['countdown_time_get_new_otp'],
	
				'time_created' => now()->timestamp,
				'time_updated' => now()->timestamp,
			];
	
			$mpos360CodeOtp = Mpos360CodeOtp::query()->forceCreate($paramCreate);
		}

		// Lấy lại mã otp khác
		if (!empty($otpId)) {
			$mpos360CodeOtp = Mpos360CodeOtp::query()->firstWhere([
				'command_code' => $params['command_code'],
				'service_code' => $params['channel'],
				'id' => $otpId
			]);

			if (!$mpos360CodeOtp) {
				throw new BusinessException('Otp không tồn tại');
			}
			
			$mpos360CodeOtp->otp = $getOtpMposResult['otp'];
			$mpos360CodeOtp->status = Mpos360Enum::MPOS360_OTP_CHUA_SU_DUNG;
			$mpos360CodeOtp->time_out = now()->timestamp + $getOtpMposResult['countdown_time_get_new_otp'];
			$mpos360CodeOtp->time_updated = now()->timestamp;
			$r = $mpos360CodeOtp->save();

			if (!$r) {
				throw new BusinessException('Lỗi không lưu được otp cấp lại. Bạn vui lòng thử lại');
			}
		}
		
		return $mpos360CodeOtp;
	}
} // End class