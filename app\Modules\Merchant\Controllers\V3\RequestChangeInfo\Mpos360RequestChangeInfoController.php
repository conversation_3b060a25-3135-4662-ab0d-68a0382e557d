<?php

namespace App\Modules\Merchant\Controllers\V3\RequestChangeInfo;

use App\Lib\Helper;
use Illuminate\Support\Facades\DB;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoDetailV3Request;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoMarkAsDoneRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoListProfileRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoSaveChangesRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoUpdateProfileRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\V2\Mpos360RequestChangeInfoAttachSignatureRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoPickSignMethodV3Request;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoUploadPhuLucKyV3Request;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoCheckProfileBankingRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoAdditionalAttachmentRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoAllOtpProfileSuccessRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoValidateEmailAndMobileRequest;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoDetailV3Action\Mpos360RequestChangeInfoDetailV3Action;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoMarkAsDoneAction\Mpos360RequestChangeInfoMarkAsDoneAction;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\Mpos360RequestChangeInfoListProfileAction;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoSaveChangesAction\Mpos360RequestChangeInfoSaveChangesAction;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestGetAdditionalProfilesAction\Mpos360RequestGetAdditionalProfilesAction;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\Mpos360RequestChangeInfoUpdateProfileAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAttachSignatureAction\Mpos360RequestChangeInfoAttachSignatureAction;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoPickSignMethodV3Action\Mpos360RequestChangeInfoPickSignMethodV3Action;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoUploadPhuLucKyV3Action\Mpos360RequestChangeInfoUploadPhuLucKyV3Action;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoAttachSignatureV3Action\Mpos360RequestChangeInfoAttachSignatureV3Action;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoCheckProfileBankingAction\Mpos360RequestChangeInfoCheckProfileBankingAction;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\Mpos360RequestChangeInfoAdditionalAttachmentAction;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessV3Action\Mpos360RequestChangeInfoAllOtpProfileSuccessV3Action;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoValidateEmailAndMobileAction\Mpos360RequestChangeInfoValidateEmailAndMobileAction;

class Mpos360RequestChangeInfoController extends Controller
{
	public function Mpos360RequestChangeInfoListProfile(Mpos360RequestChangeInfoListProfileRequest $request)
	{
		try {
			$result = app(Mpos360RequestChangeInfoListProfileAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360RequestChangeInfoCheckProfileBanking(Mpos360RequestChangeInfoCheckProfileBankingRequest $request)
	{
		try {
			$result = app(Mpos360RequestChangeInfoCheckProfileBankingAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360RequestChangeInfoUpdateProfile(Mpos360RequestChangeInfoUpdateProfileRequest $request)
	{
		try {
			$result = app(Mpos360RequestChangeInfoUpdateProfileAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360RequestChangeInfoSaveChanges(Mpos360RequestChangeInfoSaveChangesRequest $request)
	{
		DB::beginTransaction();
		try {
			$result = app(Mpos360RequestChangeInfoSaveChangesAction::class)->run($request);
			DB::commit();
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			DB::rollBack();
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360RequestChangeInfoAdditionalAttachment(Mpos360RequestChangeInfoAdditionalAttachmentRequest $request)
	{
		try {
			$result = app(Mpos360RequestChangeInfoAdditionalAttachmentAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
	
	public function Mpos360RequestChangeInfoMarkAsDone(Mpos360RequestChangeInfoMarkAsDoneRequest $request)
	{
		DB::beginTransaction();
		try {
			$result = app(Mpos360RequestChangeInfoMarkAsDoneAction::class)->run($request);
			DB::commit();
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			DB::rollBack();
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360RequestChangeInfoAttachSignature(Mpos360RequestChangeInfoAttachSignatureRequest $request)
	{
		try {
			$deviceSession = $request->getCurrentDeviceSession();
			
			$result = app(Mpos360RequestChangeInfoAttachSignatureAction::class)->run(
				$deviceSession,
				$request->json('data.signature_id'),
				$request->json('data.request_id')
			);

			$result = app(Mpos360RequestChangeInfoAttachSignatureV3Action::class)->run(
				$deviceSession,
				$request->json('data.signature_id'),
				$request->json('data.request_id')
			);

			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360RequestChangeInfoAllOtpProfileSuccess(Mpos360RequestChangeInfoAllOtpProfileSuccessRequest $request)
	{
		try {
			$result = app(Mpos360RequestChangeInfoAllOtpProfileSuccessV3Action::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
	
	public function Mpos360RequestChangeInfoDetail(Mpos360RequestChangeInfoDetailV3Request $request)
	{
		try {
			$result = app(Mpos360RequestChangeInfoDetailV3Action::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	// Chọn phương thức ký
	public function Mpos360RequestChangeInfoPickSignMethod(Mpos360RequestChangeInfoPickSignMethodV3Request $request)
	{
		try {
			$result = app(Mpos360RequestChangeInfoPickSignMethodV3Action::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360RequestChangeInfoUploadPhuLucKy(Mpos360RequestChangeInfoUploadPhuLucKyV3Request $request)
	{
		try {
			$result = app(Mpos360RequestChangeInfoUploadPhuLucKyV3Action::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360RequestChangeInfoValidateEmailAndMobile(Mpos360RequestChangeInfoValidateEmailAndMobileRequest $request)
	{
		try {
			$result = app(Mpos360RequestChangeInfoValidateEmailAndMobileAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360RequestGetAdditionalProfiles()
	{
		$request = request();
		try {
			$result = app(Mpos360RequestGetAdditionalProfilesAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
} // End class
