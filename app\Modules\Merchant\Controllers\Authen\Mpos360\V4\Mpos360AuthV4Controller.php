<?php

namespace App\Modules\Merchant\Controllers\Authen\Mpos360\V4;

use App\Lib\Helper;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\Authen\Mpos360\V4\Mpos360GetBioMetricV4Request;
use App\Modules\Merchant\Requests\Authen\Mpos360\V4\Mpos360UpdateBioMetricV4Request;
use App\Modules\Merchant\Requests\Authen\Mpos360\V4\Mpos360AuthGetHinhThucNhanOtpV4Request;
use App\Modules\Merchant\Requests\Authen\Mpos360\V4\Mpos360AuthGetOtpWithHinhThucV4Request;
use App\Modules\Merchant\Requests\Authen\Mpos360\V4\Mpos360AuthForgotGetNewPasswordV4Request;
use App\Modules\Merchant\Requests\Authen\Mpos360\V4\Mpos360AuthVerifyOtpForgotPasswordV4Request;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360GetBioMetricV4Action\Mpos360GetBioMetricV4Action;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360UpdateBioMetricV4Action\Mpos360UpdateBioMetricV4Action;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthGetHinhThucNhanOtpV4Action\Mpos360AuthGetHinhThucNhanOtpV4Action;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthGetOtpWithHinhThucV4Action\Mpos360AuthGetOtpWithHinhThucV4Action;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthForgotGetNewPasswordV4Action\Mpos360AuthForgotGetNewPasswordV4Action;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthVerifyOtpForgotPasswordV4Action\Mpos360AuthVerifyOtpForgotPasswordV4Action;

class Mpos360AuthV4Controller extends Controller
{

	public function Mpos360GetBioMetric(Mpos360GetBioMetricV4Request $request)
	{
		try {
			$result = app(Mpos360GetBioMetricV4Action::class)->run($request);
			return $this->successResponse($result, $request, 200, $result['msg'] ?? '');
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360UpdateBioMetric(Mpos360UpdateBioMetricV4Request $request)
	{
		try {
			return $this->successResponse(['id' => '9999999', 'msg' => 'Cập nhật sinh trắc thành công'], $request, 200);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360AuthGetHinhThucNhanOtp(Mpos360AuthGetHinhThucNhanOtpV4Request $request)
	{
		try {
			$result = app(Mpos360AuthGetHinhThucNhanOtpV4Action::class)->run($request);
			return $this->successResponse($result, $request, 200);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360AuthGetOtpWithHinhThuc(Mpos360AuthGetOtpWithHinhThucV4Request $request)
	{
		try {
			$result = app(Mpos360AuthGetOtpWithHinhThucV4Action::class)->run($request);
			return $this->successResponse($result, $request, 200);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360AuthVerifyOtpForgotPassword(Mpos360AuthVerifyOtpForgotPasswordV4Request $request)
	{
		try {
			$result = app(Mpos360AuthVerifyOtpForgotPasswordV4Action::class)->run($request);
			return $this->successResponse($result, $request, 200);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360AuthForgotGetNewPassword(Mpos360AuthForgotGetNewPasswordV4Request $request)
	{
		try {
			$result = app(Mpos360AuthForgotGetNewPasswordV4Action::class)->run($request);
			return $this->successResponse($result, $request, 200);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
} // End class
