<?php

namespace App\Modules\Merchant\Controllers\Device;

use Exception;
use App\Lib\Helper;
use App\Modules\Merchant\Model\Device;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\Device\Mpos360DeviceUpdateSerialRequest;
use App\Modules\Merchant\Requests\Device\Mpos360DeviceGetTypeReceiverTingTingRequest;
use App\Modules\Merchant\Requests\Device\Mpos360DeviceUpdateTypeReceiverTingTingRequest;
use App\Modules\Merchant\Actions\Device\Mpos360DeviceGetTypeReceiverTingTingAction\Mpos360DeviceGetTypeReceiverTingTingAction;
use App\Modules\Merchant\Actions\Device\Mpos360DeviceUpdateTypeReceiverTingTingAction\Mpos360DeviceUpdateTypeReceiverTingTingAction;

class DeviceTingTingController extends Controller
{
	public function Mpos360DeviceUpdateSerial(Mpos360DeviceUpdateSerialRequest $request)
	{
		try {
			return $this->successResponse(['id' => 1], $request, 200, 'Cập nhật mã serial thành công');
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function Mpos360DeviceGetTypeReceiverTingTing(Mpos360DeviceGetTypeReceiverTingTingRequest $request)
	{
		try {
			$result = app(Mpos360DeviceGetTypeReceiverTingTingAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function Mpos360DeviceUpdateTypeReceiverTingTing(Mpos360DeviceUpdateTypeReceiverTingTingRequest $request)
	{
		try {
			$result = app(Mpos360DeviceUpdateTypeReceiverTingTingAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
