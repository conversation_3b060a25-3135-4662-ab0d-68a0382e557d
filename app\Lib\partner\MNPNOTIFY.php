<?php

namespace App\Lib\partner;

use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlertWarning;

class MNPNOTIFY
{
    private $baseUrl;
    private $appId;
    private $authToken;
		private $commonUrl;

    public function __construct($authToken)
    {
				$this->commonUrl = env('API_PARTNER_MNP_COMMON_URL');
        $this->baseUrl = env('API_PARTNER_MNP_NOTIFY_URL');
        $this->appId = env('API_PARTNER_MNP_NOTIFY_APP_ID');
        $this->authToken = $authToken;
    }

    private function sendRequest($endpoint, $method = 'GET', $data = null)
    {
			$log = [
				'method' => $method,
				'data' => $data,
			];

        $url = $this->baseUrl . $endpoint;
				$log['url'] = $url;

        $curl = curl_init();
        $headers = [
            'app-id: ' . $this->appId,
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->authToken
        ];
				$log['headers'] = $headers;
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $headers,
        ));

				try {
					$response = curl_exec($curl);
					$log['response'] = $response;
				}catch(\Throwable $th) {
                    TelegramAlertWarning::sendMessage(Helper::traceError($th));

					return [
            "success" => 500,
            "code" => "ERROR",
            "httpStatus" =>  "ERROR",
            "message" =>  "Lỗi kết nối tới đối tác",
        	];
				}

				mylog($log);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        if ($httpCode == 200) {
            return json_decode($response, true);
        }
        if($httpCode > 200) {
            $res = @json_decode($response, true);
            if($res) {
                return [
                    "success" =>  $httpCode,
                    "code" => "ERROR",
                    "httpStatus" =>  "ERROR",
                    "message" =>  isset($res['error'])?$res['error']:"LỖI KHÔNG XÁC ĐỊNH",
                ];
            }
            
        }
        return [
            "success" =>  $httpCode,
            "code" => "ERROR",
            "httpStatus" =>  "ERROR",
            "message" =>  "LỖI KHÔNG XÁC ĐỊNH",
        ];
    }


    public function getNotificationList($userName, $params = [])
    {
        $allowedKeys = ['groupCode', 'cateCode', 'subCateCode', 'pageNum', 'pageSize'];
        $data = [
            'userName' => $userName,
            'groupCode' => null,
            'cateCode' => null,
            'subCateCode' => null,
            'pageNum' => 1,
            'pageSize' => 20
        ];

        // Chỉ merge các key được phép
        foreach ($allowedKeys as $key) {
            if (isset($params[$key])) {
                $data[$key] = $params[$key];
            }
        }
        // Loại bỏ các tham số null
        $data = array_filter($data, function ($value) {
            return $value !== null;
        });
				$data['channel'] = 'MPOS360';
        $result = $this->sendRequest('/list', 'POST', $data);
        return $this->returnData($result);
    }


    public function markAllAsRead($userName,$params)
    {
        $allowedKeys = ['id_list', 'groupCode','cateCode','subCateCode'];
        $data = ['userName' => $userName];
        foreach ($allowedKeys as $key) {
            if (isset($params[$key])) {
                $data[$key] = $params[$key];
            }
        }
				$data['channel'] = 'MPOS360';
        $result = $this->sendRequest('/read-all', 'PUT', $data);
        return $this->returnData($result);
    }

    public function getNotificationDetail($notificationId)
    {
        $result = $this->sendRequest("/detail/$notificationId");
        return $this->returnData($result);
    }

    public function getUnreadCount($userName, $groupCodes = [])
    {
        $data = [
            'userName' => $userName,
            'groupCodes' => $groupCodes,
						'channel' => 'MPOS360',
        ];

        $result = $this->sendRequest('/count-unread', 'POST', $data);
        return $this->returnData($result);
    }

		public function subscribe($fcmToken)
    {
			$this->baseUrl = $this->commonUrl;
			$data = [ 'token' => $fcmToken ];

			$result = $this->sendRequest('/firebase/subscribe-topics', 'POST', $data);
			return $result;
    }

		public function unSubscribe($fcmToken)
    {
			$this->baseUrl = $this->commonUrl;
			$data = [ 'token' => $fcmToken ];

			$result = $this->sendRequest('/firebase/unsubscribe-topics', 'POST', $data);
			return $result;
    }

    public function returnData($result)
    {
        // Xử lý và trả về dữ liệu theo định dạng mong muốn
        // Ví dụ:
        if (isset($result['code']) && $result['code'] === 'SUCCESS') {
            return [
                'status' => true,
                'data' => $result['data'] ?? null,
                'message' => $result['message'] ?? 'Success'
            ];
        } else {
            return [
                'status' => false,
                'data' => null,
                'message' => $result['message'] ?? 'An error occurred'
            ];
        }
    }
}
