<?php
namespace App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360Register;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class Mpos360SendOtpRequest extends FormRequest
{
 /**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.username' => ['required', 'string'],
			'data.channel' => ['required', 'string', 'max:25', Rule::in(['ZALO', 'SMS'])],
			'data.otp_id' => ['present', 'string']
		];
	}

	public function messages() {
		return [
			'data.username.required' => 'Username là bắt buộc',
			'data.username.string' => 'Username phải là kiểu chuỗi ký tự',
			'data.channel.required' => 'Kênh nhận mã OPT là bắt buộc',
			'data.channel.string' => 'Kênh nhận mã phải là kiểu chuỗi ký tự',
		];
	}

	public function isGuiLaiOtpDangKy(): bool {
		return !empty($this->json('data.otp_id'));
	}

	public function isTaoMoiOtpDangKy(): bool {
		return !$this->isGuiLaiOtpDangKy();
	}
}