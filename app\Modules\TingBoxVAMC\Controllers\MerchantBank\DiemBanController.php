<?php

namespace App\Modules\TingBoxVAMC\Controllers\MerchantBank;

use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\WebBackend\Controllers\Controller;
use App\Modules\TingBoxVAMC\Requests\DiemBan\Mpos360ListDiemBanRequest;
use App\Modules\TingBoxVAMC\Requests\DiemBan\Mpos360DetailDiemBanRequest;
use App\Modules\TingBoxVAMC\Requests\DiemBan\Mpos360GetStepKhaiBaoDiemBanRequest;
use App\Modules\TingBoxVAMC\Requests\DiemBan\Mpos360GetHomePageInfoDiemBanRequest;
use App\Modules\TingBoxVAMC\Requests\DiemBan\Mpos360GetStepKhaiBaoDiemBanV2Request;
use App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360ListDiemBanAction\Mpos360ListDiemBanAction;
use App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360DetailDiemBanAction\Mpos360DetailDiemBanAction;
use App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360ListDiemBanAction\Mpos360ListDiemBanCaiThienAction;
use App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360GetStepKhaiBaoDiemBanAction\Mpos360GetStepKhaiBaoDiemBanAction;
use App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360GetHomePageInfoDiemBanAction\Mpos360GetHomePageInfoDiemBanAction;
use App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360GetStepKhaiBaoDiemBanV2Action\Mpos360GetStepKhaiBaoDiemBanV2Action;

class DiemBanController extends Controller
{
	public function Mpos360ListDiemBan(Mpos360ListDiemBanRequest $request) {
		try {
			// $result = app(Mpos360ListDiemBanAction::class)->run($request);
			$result = app(Mpos360ListDiemBanCaiThienAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function Mpos360DetailDiemBan(Mpos360DetailDiemBanRequest $request) {
		try {
			$result = app(Mpos360DetailDiemBanAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function Mpos360GetStepKhaiBaoDiemBan(Mpos360GetStepKhaiBaoDiemBanRequest $request) {
		try {
			$result = app(Mpos360GetStepKhaiBaoDiemBanAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function Mpos360GetHomePageInfoDiemBan(Mpos360GetHomePageInfoDiemBanRequest $request) {
		try {
			$result = app(Mpos360GetHomePageInfoDiemBanAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function Mpos360GetStepKhaiBaoDiemBanV2(Mpos360GetStepKhaiBaoDiemBanV2Request $request)
	{
		try {
			$result = app(Mpos360GetStepKhaiBaoDiemBanV2Action::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
