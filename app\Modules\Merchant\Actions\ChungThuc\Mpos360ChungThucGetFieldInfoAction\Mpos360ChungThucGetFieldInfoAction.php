<?php

namespace App\Modules\Merchant\Actions\ChungThuc\Mpos360ChungThucGetFieldInfoAction;

use App\Lib\partner\MNP;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Requests\ChungThuc\Mpos360ChungThucGetFieldInfoRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\GetMNPAccessTokenSubAction;
use App\Modules\Merchant\Actions\ChungThuc\Mpos360ChungThucGetFieldInfoAction\SubAction\GetHoSoNguoiDaiDienCanChungThucSubAction;
use App\Modules\Merchant\Model\Mpos360ChungThuc;

// class Mpos360ChungThucGetFieldInfoAction
// {
// 	public MNP $mnp;

// 	public function __construct(MNP $mnp)
// 	{
// 		$this->mnp = $mnp;
// 	}
	
// 	public function run(Mpos360ChungThucGetFieldInfoRequest $request)
// 	{
// 		// Đ<PERSON><PERSON>n này, cần gọi api sang bên anh Hi<PERSON>u để lấy được dữ liệu chứng thực realTime
// 		$deviceSession = $request->getCurrentDeviceSession();
// 		$deviceSessionWithToken = app(GetMNPAccessTokenSubAction::class)->run($deviceSession);
// 		$merchantId = $deviceSession->getMerchantId();

// 		$getChungThuc = $this->mnp->getVerifyInfo($merchantId, $deviceSessionWithToken->mnp_token);

// 		$listMpos360ChungThuc = app(GetHoSoNguoiDaiDienCanChungThucSubAction::class)->run($deviceSessionWithToken);
// 		if ($listMpos360ChungThuc->isEmpty()) {
// 			return [
// 				'warning' => [
// 					'msg' => vmsg('BanCanPhaiChungThucCccdLanDauTienDeCoTheTaoYeuCauThayDoi'),
// 				],
// 				'data' => []
// 			];
// 		}

// 		$returnData = [
// 			'warning' => [
// 				'msg' => vmsg('DeDamBaoAnToanChoTaiKhoanQuyKhachVuiLongXacThucCacThongTinDuoiDay'),
// 			],

// 			'data' => []
// 		];

// 		$isDaChungThuc3ThongTin = $listMpos360ChungThuc->every(function (Mpos360ChungThuc $it) {
// 			return $it->status == Mpos360Enum::MPOS360_CHUNG_THUC_STT_DA_XAC_NHAN;
// 		});

// 		if ($isDaChungThuc3ThongTin) {
// 			$returnData['warning']['msg'] = '';
// 		}

// 		if (!empty($getChungThuc['data'])) {
// 			$collect = collect($getChungThuc['data'])->map(function ($item) {
// 				if ($item['key'] == 'representPassport') {
// 					$item['key_code'] = 'CCCD';
// 				}
	
// 				if ($item['key'] == 'representMobile') {
// 					$item['key_code'] = 'MOBILE';
// 				}
	
// 				if ($item['key'] == 'representEmail') {
// 					$item['key_code'] = 'EMAIL';
// 				}
	
// 				return $item;
// 			})->keyBy('key_code');

// 			foreach ($listMpos360ChungThuc as $mpos360ChungThuc) {
// 				$mpos360ChungThuc->status = Mpos360Enum::MPOS360_CHUNG_THUC_STT_CHUA_XAC_NHAN;
				
// 				if ($collect->has($mpos360ChungThuc->key_code)) {
// 					$mpos360ChungThuc->status = Mpos360Enum::MPOS360_CHUNG_THUC_STT_DA_XAC_NHAN;
// 				}

// 				$r = $mpos360ChungThuc->save();
// 				$returnData['data'][] = $mpos360ChungThuc->forMobile();
// 			}
// 		}

// 		// MC mới, chưa chứng thực gì cả
// 		if (empty($getChungThuc['data'])) {
// 			foreach ($listMpos360ChungThuc as $mpos360ChungThuc) {
// 				$mpos360ChungThuc->status = Mpos360Enum::MPOS360_CHUNG_THUC_STT_CHUA_XAC_NHAN;
// 				$returnData['data'][] = $mpos360ChungThuc->forMobile();
// 			}

// 		}
		
// 		return $returnData;
// 	}
// }
