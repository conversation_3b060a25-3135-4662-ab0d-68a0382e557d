<?php

namespace App\Modules\Merchant\Requests\TingBox;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360GetCauHinhByMerchantRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.username' => ['required', 'string'],
			'data.merchantId' => ['required', 'string'],
			'data.deviceToken' => ['required', 'string'],
			'data.os' => ['required', 'string']
		];
	}
}
