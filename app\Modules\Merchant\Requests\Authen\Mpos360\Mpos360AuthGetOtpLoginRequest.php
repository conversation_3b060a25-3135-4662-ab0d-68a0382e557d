<?php

namespace App\Modules\Merchant\Requests\Authen\Mpos360;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360AuthGetOtpLoginRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.email' => ['required', 'string', 'email'],
			'data.otp_id' => ['present', 'string']
		];
	}

	public function isResendOtp(): bool {
		return !empty($this->json('data.otp_id'));
	}
} // End class
