<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoSaveChangesAction;

use App\Exceptions\BusinessException;
use Exception;
use App\Lib\partner\MNPExtend;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360ChungThuc;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\GetMNPAccessTokenSubAction;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoSaveChangesRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction\MnpGetMerchantProfileSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestCronPushRecordAction\SubAction\GetCccdInfoByQtsRequestIdSubAction;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForRepresentationSA\SA\AddAdditionalProfileV3SA;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForRepresentationSA\SA\TaoThayDoiTaiKhoanMposSA;

class Mpos360RequestChangeInfoSaveChangesAction
{
	public $action;

	public MNPExtend $mnpExtend;

	public bool $isThayDoiEmailOrSdt = false;

	public $isDoiSdt = false;

	public $isDoiEmail = false;

	public function __construct(GetCccdInfoByQtsRequestIdSubAction $action, MNPExtend $mnpExtend)
	{
		$this->action = $action;
		$this->mnpExtend = $mnpExtend;
	}

	public function run(Mpos360RequestChangeInfoSaveChangesRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		$merchantId = $deviceSession->getMerchantId();

		$mpos360McRequest = Mpos360MerchantRequest::query()->firstWhere([
			'id' => $request->json('data.id'),
			'merchant_id' => $merchantId
		]);


		$deviceSessionWithToken = app(GetMNPAccessTokenSubAction::class)->run($deviceSession);

		if (!$mpos360McRequest) {
			throw new BusinessException(vmsg('YC không tồn tại'));
		}

		if (!$mpos360McRequest->isYeuCauDoiNguoiDaiDienMoi()) {
			throw new BusinessException(vmsg('Lỗi: YC không phải loại đổi người đại diện mới'));
		}


		$detailQtsResultDto = $this->action->run($request->getProfileValueByKey('qts_request_id'));

		$mpos360ChungThuc = Mpos360ChungThuc::query()->firstWhere([
			'merchant_id' => $merchantId,
			'key_code' => 'CCCD'
		]);

		$isTrungCccdChungThuc = $detailQtsResultDto->isMatchingChungThuc($mpos360ChungThuc);
		mylog(['isTrungCccdChungThuc' => $isTrungCccdChungThuc]);

		$requestVerify = [];

		if ($isTrungCccdChungThuc) {
			$wasUpdateChungThuc = $mpos360ChungThuc->where([
				'status' => Mpos360Enum::MPOS360_CHUNG_THUC_STT_CHUA_XAC_NHAN,
				'merchant_id' => $merchantId,
				'id' => $mpos360ChungThuc->id
			])
			->update([
				'qts_request_id' => $detailQtsResultDto->requestId,
				'status' => Mpos360Enum::MPOS360_CHUNG_THUC_STT_DANG_XAC_NHAN,
				'time_confirmed' => now()->timestamp
			]);

			$requestVerify[] = [
				'field' => 'passport',
				'value' => $detailQtsResultDto->requestId,
				'status_verify' => '1',
				'date_verify' => now()->timestamp
			];
		}

		$profileLayTuCccd = $detailQtsResultDto->mapQtsDataAsNguoiDaiDienProfile(
			$mpos360McRequest->isYeuCauDoiNguoiDaiDienMoi()
		);

		$profileLayTuCccd['representEmail'] = $request->getProfileValueByKey('authoriserEmail');
		$profileLayTuCccd['representMobile'] = $request->getProfileValueByKey('authoriserContactNumber');
		
		$dataRequest = $mpos360McRequest->getDataRequestV3();
		$dataRequest[0]['profiles'] = $profileLayTuCccd;

		if ($isTrungCccdChungThuc) {
			$dataRequest[0]['scan_method']['QTS'] = [
				'status' => 'DONE',
				'other_data' => [
					'is_matching_facescan' => 1,
					'matching_percent' => 100
				]
			];
		}

		// Khớp qts thì sét là đã chứng thực luôn
		if ($isTrungCccdChungThuc) {
			$paramUpdate['method_code'] = 'QTS';
			$paramUpdate['status_verify'] = Mpos360Enum::MPOS360_MC_VERIFY_STT_DA_THUC_HIEN_BUOC3;
		}


		// Gọi vào api detail để biết được thông tin nào thay đổi
		$mnpMerchantDetail = app(MnpGetMerchantProfileSubAction::class)->run(
			$merchantId, 
			$deviceSessionWithToken->mnp_token
		);

		
		// Không thay mobile ng đại diện
		if ($mnpMerchantDetail['data']['authoriserContactNumber']['value'] == $profileLayTuCccd['representMobile']) {
			$requestVerify[] = [
				'field' => 'representMobile',
				'value' => $profileLayTuCccd['representMobile'],
				'status_verify' => '1',
				'date_verify' => now()->timestamp
			];
		}else {
			$requestVerify[] = [
				'field' => 'representMobile',
				'value' => $profileLayTuCccd['representMobile'],
				'status_verify' => '0',
				'date_verify' => now()->timestamp
			];

			$this->isThayDoiEmailOrSdt = true;
			$this->isDoiSdt = true;
		}

		// Không thay email ng đại diện
		if ($mnpMerchantDetail['data']['authoriserEmail']['value'] == $profileLayTuCccd['representEmail']) {
			$requestVerify[] = [
				'field' => 'representEmail',
				'value' => $profileLayTuCccd['representEmail'],
				'status_verify' => '1',
				'date_verify' => now()->timestamp
			];
		}else {
			$requestVerify[] = [
				'field' => 'representEmail',
				'value' => $profileLayTuCccd['representEmail'],
				'status_verify' => '0',
				'date_verify' => now()->timestamp
			];

			$this->isThayDoiEmailOrSdt = true;
			$this->isDoiEmail = true;
		}

		$dataRequest[0]['request_vefify'] = $requestVerify;
		$paramUpdate['data_request'] = json_encode($dataRequest);

		$wasUpdated = $mpos360McRequest->forceFill($paramUpdate)->update();
		
		if (!$wasUpdated) {
			throw new BusinessException(vmsg('Lỗi không cập nhật được thông tin CCCD vào yêu cầu'));
		}

		// Kiem tra co truong thay doi tai khoan mpos hay khong
		if (!empty($request->json('data.replace_mpos_account_field'))) {
			if (!$this->isDoiEmail && !$this->isDoiSdt) {
				throw new BusinessException('Bạn cần phải đổi ít nhất Email hoặc SĐT khi muốn thay thế tài khoản mpos');
			}

			$r = $this->mnpExtend->validateEmailAndMobile(
				$deviceSessionWithToken->mnp_token,
				$this->isDoiEmail ? $profileLayTuCccd['representEmail'] : '',
				$this->isDoiSdt ? $profileLayTuCccd['representMobile'] : '',
			);

			if (empty($r['result'])) {
				$msg = $r['message'] ?? 'Can not query to mnp system';
				$err = sprintf('MNP Err: %s (Code: %s)', $msg, $r['code'] ?? '00');
				throw new BusinessException($err);
			}
			
			$mpos360McRequest->refresh();
			
			$replaceFieldAccount = [];
			if ($this->isDoiEmail) {
				$replaceFieldAccount[] = 'authoriserEmail';
			}

			if ($this->isDoiSdt) {
				$replaceFieldAccount[] = 'authoriserContactNumber';
			}

			$mpos360McRequest = app(TaoThayDoiTaiKhoanMposSA::class)->run(
				$mpos360McRequest,
				$replaceFieldAccount,
				$request->json('data.profiles')
			);
		}

		$can = Mpos360Enum::MPOS360_CAN_SUBMIT_ADDITIONAL_ATTACHMENT;

		// Có thay đổi email hoặc sdt -> thì phải ra màn otp luôn
		if ($this->isThayDoiEmailOrSdt) {
			$can = Mpos360Enum::MPOS360_CAN_NEED_VERIFY_OTP;
		}
		
		$mpos360McRequest->refresh();
		
		$returnData = [
			'request_id' => $mpos360McRequest->id,
			'status' => 'SUCCESS',
			'can' => $can,
			'additional_profiles' => app(AddAdditionalProfileV3SA::class)->run($mpos360McRequest),
		];

		return $returnData;
	} // End method
} // End class
