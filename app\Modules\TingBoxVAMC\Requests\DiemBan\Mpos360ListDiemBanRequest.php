<?php

namespace App\Modules\TingBoxVAMC\Requests\DiemBan;

use Illuminate\Foundation\Http\FormRequest;

class Mpos360ListDiemBanRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.username' => ['required', 'string'],
      'data.merchantId' => ['required', 'string'],
    ];
  }
}
