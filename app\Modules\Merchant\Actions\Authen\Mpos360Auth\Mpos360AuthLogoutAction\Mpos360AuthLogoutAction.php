<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLogoutAction;

use Exception;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360AuthLogoutRequest;

class Mpos360AuthLogoutAction
{
	public function run(Mpos360AuthLogoutRequest $request): DeviceSession
	{
		$curentDeviceSession = $request->getCurrentDeviceSession();
		return $curentDeviceSession;
	}
} // End class
