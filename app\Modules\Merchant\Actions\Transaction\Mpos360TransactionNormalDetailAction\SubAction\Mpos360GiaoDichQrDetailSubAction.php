<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransactionNormalDetailAction\SubAction;

use App\Modules\Merchant\Actions\Transaction\subAction\Mpos360TransactionDefineConfigSubAction;

class Mpos360GiaoDichQrDetailSubAction
{
	public $_type = 'DETAIL_TRANSACTION';

	public function __convertDataReturn($listTransaction = [])
	{
		$data = $this->__defaultReturn();

		if ($listTransaction) {
			if (isset($listTransaction['data']['mc360DetailTransaction'])) {
				$dataDetail = $listTransaction['data']['mc360DetailTransaction'];
				$data['data'][] = $this->__commonInfo($dataDetail); // 0
				$data['data'][] = $this->__orderInfo($dataDetail); // 1

				if (isset($dataDetail['cardholderName'])) {
					$data['data'][] = $this->__customerInfo($dataDetail); //4
				}

				$data['data'][] = $this->__otherInfo($dataDetail); //5
			}
		}

		return $data;
	}

	private function __commonInfo($detail)
	{
		$mapData = (new Mpos360TransactionDefineConfigSubAction())->getStatusTrans();

		$data  = [
			'key' => 'common_info',
			'name' => trans_choice_fallback('trans.title.common_info', 'Thông tin chung'),
			'list' => [],
		];

		$mapData = (new Mpos360TransactionDefineConfigSubAction())->getPaymentMethod();

		foreach ($mapData as $key1 => $value1) {
			$transMethodNameArr[$value1['value']] = $value1['label'];
		}

		$data['list'] = [
			[
				'key' => 'transType',
				'label' => 'Loại GD',
				'value' => 'VietQR',
				'other_data' => (object) [],
			],
			[
				'key' => 'paymentMethod',
				'label' => 'Hình thức',
				'value' => 'Qua công ty Vimô',
				'other_data' => (object) [],
			],
			[
				'key' => 'txid',
				'label' => 'Mã giao dịch',
				'value' => $detail['txid'],
				'other_data' => (object) [],
			],

			[
				'key' => 'createdDate',
				'label' => 'Thời gian',
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'createdDate', 'datetime'),
				'other_data' => (object) [],
			],

			[
				'key' => 'status',
				'label' => 'Trạng thái',
				'value' => 'Thành công',
				'other_data' => ['value' => '100', 'label' => trans_choice_fallback('trans.status.100', 'Thành công'), 'text_color' => '#ffffff', 'bg_color' => '#3BB54A', 'display_type' => 'pills'],
			],
		];

		$withDrawStatus = (new Mpos360TransactionDefineConfigSubAction())->getWithdrawStatusTrans2();
		$currentWithDrawStatus = collect($withDrawStatus)->where('value', $detail['withdrawStatus'] ?? '')->first();
		$hasBalance = collect($withDrawStatus)->where('value', 'HAS_BALANCE')->first();
		$chuaThanhToan = collect($withDrawStatus)->where('value', 'CHUA_THANH_TOAN')->first();

		$data['list'][] = [
			'key' => 'statusPayment',
			'label' => 'Trạng thái thanh toán',
			'value' => @$currentWithDrawStatus['value'] == 'HAS_BALANCE' ? 'Đã thanh toán' : 'Chưa thanh toán',
			'other_data' => @$currentWithDrawStatus['value'] == 'HAS_BALANCE' ? $hasBalance : $chuaThanhToan,
		];

		return $data;
	}

	private function __orderInfo($detail)
	{
		$discountValue =  (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'discountValue', 'amount');
		if ($discountValue) {
			$discountValue = '-' . $discountValue;
		}
		$detail['totalPaymentAmount2'] = (int)$detail['amount'] + (int)$detail['customerPaymentFee']  - (int)$detail['discountValue'];
		$data  = [
			'key' => 'order_info',
			'name' => trans_choice_fallback('trans.title.order_info', 'Thông tin đơn hàng'),
			'list' => [],
		];
		$data['list'] = [
			[
				'key' => 'amount_order',
				'label' => trans_choice_fallback('trans.title.amount_order',  'Số tiền đơn hàng'),
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'amount', 'amount'),
				'other_data' => (object) [],
			],

			[
				'key' => 'totalPaymentAmount2',
				'label' => trans_choice_fallback('trans.title.totalPaymentAmount2', 'Tổng thanh toán'),
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'totalPaymentAmount2', 'amount'),
				'other_data' => (object) [
					'font_weight' => 'bold',
				],
			],
		];

		if (!empty($detail['description'])) {
			$data['list'][] = [
				'key' => 'MoTa',
				'label' => '',
				'value' => $detail['description'],
				'other_data' => (object) [
					"display_type" => "block"
				],
			];
		}
		return $data;
	}

	private function __customerInfo($detail)
	{
		$cardType = '';
		if (isset($detail['issuerCode'])) {
			$cardType = (new Mpos360TransactionDefineConfigSubAction())->getTypeCard($detail['issuerCode']);
		}
		$pan = $cardType . ': ' . $detail['pan'];
		$data  = [
			'key' => 'customer',
			'name' => trans_choice_fallback('trans.title.customer', 'Khách hàng'),
			'list' => [
				[
					'key' => 'cardholderName',
					'label' => trans_choice_fallback('trans.title.cardholderName', 'Chủ thẻ'),
					'value' => $detail['cardholderName'],
					'other_data' => (object) [],
				],
				[
					'key' => 'pan',
					'label' => trans_choice_fallback('trans.title.pan', 'Thẻ/TKNH/Ví điện tử'),
					'value' =>  $pan,
					'other_data' => (object) [],
				]
			],
		];
		return $data;
	}

	private function __otherInfo($detail)
	{
		$data  = [
			'key' => 'other',
			'name' => trans_choice_fallback('trans.title.other', 'Khác'),
			'list' => [
				[
					'key' => 'rrn',
					'label' => trans_choice_fallback('trans.title.rrn', 'Số tham chiếu'),
					'value' => $detail['rrn'],
					'other_data' => (object) [
						'font_weight' => 'bold'
					],
				],
				[
					'key' => 'muid',
					'label' => trans_choice_fallback('trans.title.muid', 'Mobile user'),
					'value' => $detail['muid'],
					'other_data' => (object) [],
				]
			],
		];
		return $data;
	}

	private function __defaultReturn()
	{
		return [
			'warning' => [],
			'data' => [],
			'other_data' => (object)[],
		];
	}
} // End class
