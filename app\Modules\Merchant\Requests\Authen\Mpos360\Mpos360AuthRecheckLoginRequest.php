<?php

namespace App\Modules\Merchant\Requests\Authen\Mpos360;

use Illuminate\Foundation\Http\FormRequest;

class Mpos360AuthRecheckLoginRequest extends FormRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.sessionId' => ['required', 'numeric', 'integer', 'min:1'],
			'data.userId' => ['required', 'numeric', 'integer', 'min:1'],
			'data.sessionApiKey' => ['required', 'string']
		];
	}
}
