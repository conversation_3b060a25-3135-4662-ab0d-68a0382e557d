<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthGetHinhThucNhanOtpV4Action;

use App\Lib\Helper;
use Illuminate\Support\Str;
use App\Modules\Merchant\Requests\Authen\Mpos360\V4\Mpos360AuthGetHinhThucNhanOtpV4Request;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthGetHinhThucNhanOtpV4Action\SubAction\GetThongTinMerchantByParamAction;

class Mpos360AuthGetHinhThucNhanOtpV4Action
{
	public bool $isEmailDaXacThuc = false;

	public string $userNameDisplay = '';

	public function maskEmail($email) {
		$beforeDomain = Str::before($email, '@');
					
		$length = strlen($beforeDomain);
		$firstTwo = substr($beforeDomain, 0, 2);
		$maskedPart = str_repeat('*', $length - 2);
		$output = $firstTwo . $maskedPart;

		$afterDomain = Str::after($email, '@');

		return $output . '@' . $afterDomain;
	}

	public function run(Mpos360AuthGetHinhThucNhanOtpV4Request $request)
	{
		$mposUserName = trim($request->json('data.username'));
		
		// Đã đăng nhập
		if ($request->hasLogined()) {

		}

		/**
		 * Chưa đăng nhập -> là tính năng quên mật khẩu
		 * -> cần check thông tin email trước
		 */
		if (!$request->hasLogined()) {
			$mcInfo = app(GetThongTinMerchantByParamAction::class)->run($mposUserName);

			if ( empty($mcInfo['username']) ) {
				return [
					'status' => 'ERROR',
					'username' => '',
					'merchant_id' => '',
					'msg_title' => 'Tài khoản không đúng',
					'msg_desc' => 'Email hoặc SĐT đã đăng ký với mPOS không đúng. Vui lòng kiểm tra lại hoặc liên hệ chuyên viên hỗ trợ',
					'channels' => [],
					'other_data' => (object) []
				];
			}

			$channels = [];
			if (Helper::isUserNameEqualEmail($mcInfo['username'])) {
				$this->userNameDisplay = $this->maskEmail($mcInfo['username']);

				$channels[] = [
					'channel' => 'EMAIL',
					'label' => 'Email',
					'value' => $mcInfo['mcEmail'],
					'checked' => 'YES',
					'value_display' => $this->maskEmail($mcInfo['username']),
					'other_data' => (object) [],
				];
				

				$channels[] = [
					'channel' => 'SMS',
					'label' => 'Tin nhắn văn bản',
					'value' => $mcInfo['mcMobile'],
					'checked' => 'YES',
					'value_display' => mask_string($mcInfo['mcMobile'], '*', 4),
					'other_data' => (object) []
				];
			}else {
				$this->userNameDisplay = mask_string($mcInfo['mcMobile'], '*', 4);

				$channels[] = [
					'channel' => 'SMS',
					'label' => 'Tin nhắn văn bản',
					'value' => $mcInfo['mcMobile'],
					'checked' => 'YES',
					'value_display' => mask_string($mcInfo['mcMobile'], '*', 4),
					'other_data' => (object) []
				];
			}

			$returnData = [
				'status' => 'SUCCESS',
				'username' => $mposUserName,
				'username_display' => $this->userNameDisplay,
				'merchant_id' => $mcInfo['mcId'],
				'msg_title' => 'Hình thức nhận OTP',
				'msg_desc' => 'Bạn đang lấy lại mật khẩu cho tài khoản {email}, vui lòng nhập mã OTP sẽ được gửi đến',
				'channels' => $channels,
				'other_data' => (object) [
					'msg_desc' => [
						'email' => [
							'text' => $mposUserName,
							'text_color' => '#73ae4a',
							'font_weight' => 'bold'
						]
					]
				]
			];

			return $returnData;
		}
	}
} // End class
