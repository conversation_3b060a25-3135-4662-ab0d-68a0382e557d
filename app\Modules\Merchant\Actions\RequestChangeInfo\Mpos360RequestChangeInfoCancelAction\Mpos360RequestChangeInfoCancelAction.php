<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoCancelAction;

use Exception;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoCancelRequest;

class Mpos360RequestChangeInfoCancelAction
{
	public function run(Mpos360RequestChangeInfoCancelRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		$mpos360McRequest = Mpos360MerchantRequest::query()->find($request->json('data.id'));

		if (!$mpos360McRequest) {
			throw new BusinessException('Không tìm thấy yc của bạn');
		}

		if ($mpos360McRequest->merchant_id != $deviceSession->getMerchantId()) {
			throw new BusinessException('Lỗi sai chủ sở hữu bản ghi');
		}

		if ($mpos360McRequest->isDaLamBuoc3()) {
			throw new BusinessException('Yêu cầu đã xác thực thì không thể hủy');
		}

		$updateVeTrangThaiHuy = Mpos360MerchantRequest::query()
			->where('id', $mpos360McRequest->id)
			->where('status_verify', '!=', Mpos360Enum::MPOS360_MC_VERIFY_STT_DA_THUC_HIEN_BUOC3)
			->update([
				'status' => Mpos360Enum::MPOS360_MC_REQUEST_STT_MC_TU_HUY,
				'time_updated' => now()->timestamp
			]);

		if (!$updateVeTrangThaiHuy) {
			throw new BusinessException('Lỗi không cập nhật được về trạng thái Hủy');
		}

		return [
			'id' => $mpos360McRequest->id,
			'status' => '1',
			'msg' => 'Đã hủy yêu cầu thành công'
		];
	}
} // End class
