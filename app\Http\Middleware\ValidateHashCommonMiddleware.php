<?php

namespace App\Http\Middleware;

use App\Exceptions\BusinessException;
use Closure;
use App\Traits\ApiResponser;
use App\Lib\DeviceSessionManualHelper;
use App\Exceptions\ValidateAsJsonException;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Model\PartnerConfig;

class ValidateHashCommonMiddleware
{
	use ApiResponser;

	public function getApiSecret(string $apiKey, string $currentPath): string
	{
		$deviceSession = DeviceSession::query()->with('partnerConfig')->firstWhere(['api_key' => $apiKey]);
		$partnerConfig = PartnerConfig::query()->firstWhere(['api_key' => $apiKey]);
		
		$partner = optional($deviceSession)->partnerConfig ? $deviceSession->partnerConfig : $partnerConfig;
		
		throw_if(
			!$partner,
			new BusinessException('Lỗi không tìm thấy api user', 422)
		);
		
		if (!$partner->hasPermission($currentPath)) {
			throw new BusinessException(vmsg('ValidateHashMiddleware_BanKhongCoQuyenTruyCapApiChucNangNay'), 403);
		}

		try {
			$apiSecret = decrypt($partner->api_secret);
		} catch (\Throwable $th) {
			throw new BusinessException(vmsg('ValidateHashMiddleware_UserApiNayKhongTheDecryptDuocThongTin'), 403);
		}

		return $apiSecret;
	}

	public function handle($request, Closure $next, $fieldHash)
	{
		if ( 
			env('APP_ENV') === 'local' && env('APP_DEBUG') == true 
																 && empty(env('APP_DEBUG_CHECK_SUM')) 
		) {
			return $next($request);
		}

		throw_if(
			empty($request->json('lang')),
			new BusinessException(vmsg('ValidateHashMiddleware_NgonNguLaBatBuoc'), 422)
		);

		throw_if(
			!in_array($request->json('lang'), ['vi', 'en']),
			new BusinessException(vmsg('ValidateHashMiddleware_HeThongChiHoTroNgonNguViVaEn'), 422)
		);

		throw_if(
			empty($request->json('time_request')),
			new BusinessException(vmsg('ValidateHashMiddleware_TimeRequestLaBatBuoc'), 422)
		);

		$apiKey = $request->json('api_key');

		if (strlen($apiKey) > env('MPOS360_DO_DAI_KEY_PHIEN_CU', 36)) {
			// ăn vào logic mới
			$dv = DeviceSessionManualHelper::makeInstance($apiKey);
			return $next($request);
		}
		
		$apiSecret = $this->getApiSecret($apiKey, $request->path());
	

		$fieldExplode = explode('|', $fieldHash);
		if (empty($fieldExplode)) {
			throw new BusinessException(vmsg('ValidateHashMiddleware_LoiApiHashField'), 422);
		}

		$combineString = '';

		foreach ($fieldExplode as $key) {
			$combineString .= sprintf('%s:%s+', $key, $request->json('data.' . $key));
		}

		$combineChecksum = [
			$request->method(),
			$request->path(),
			$request->json('time_request'),
			$request->json('lang'),
			$combineString,
			$apiKey
		];

		$stringBeforeHash = implode('|', $combineChecksum);

		$checksum = hash_hmac('sha512', $stringBeforeHash, $apiSecret);

		$clientCheckSum = $request->json('checksum');

		if (empty($clientCheckSum) || empty($checksum)) {
			throw new BusinessException(vmsg('ValidateHashMiddleware_CheckSumLaBatBuoc'), 422);
		}

		if ($clientCheckSum == $checksum) {
			return $next($request);
		}

		throw new BusinessException(vmsg('ValidateHashMiddleware_CheckSumLaKhongHopLe'), 412);
	}
} // End class
