<?php

namespace App\Lib\partner;

use Exception;

class VMMC
{
    private $authenKey;
    private $merchantId;
    private $encriptionKey;
    private $userid;
    private $__eMethod = 'AES';
    private $baseUrl;

    public function __construct()
    {
        $this->baseUrl = env('API_PARTNER_VMMC_API_URL');
        $this->authenKey = env('API_PARTNER_VMMC_AUTHEN_KEY');
        $this->merchantId = env('API_PARTNER_VMMC_MERCHANT_ID');
        $this->encriptionKey = env('API_PARTNER_VMMC_ENCRIPTION_KEY');
        $this->userid = env('API_PARTNER_VMMC_USER_ID');
        $this->__eMethod = env('API_PARTNER_VMMC_METHOD');
    }
    private function __convertBankId($bankId)
    {
        return $bankId;
    }
    public function checkBankAccountMerchant($param)
    {
        $fnc = 'CheckBankAccountMerchant';
        $type = '';
        $data = array(
            'order_code' => 'MPOS360-' . time() . '-' . rand('********', ********),
            'bank_account_holder' => $param['bank_account_holder'],
            'bank_account' => $param['bank_account'],
            'bank_id' => $param['bank_id'],
        );

        $data = json_encode($data);
       
        if ($this->__eMethod == 'AES') {
            $data = $this->encryptNew($data, $this->encriptionKey);
        } else {
            $data = $this->Encrypt($data, $this->encriptionKey);
        }
        mylog(['VMMC_Encrypt' => $data]);
        mylog(['VMMC_METHOD' => $this->__eMethod]);
        $md5str = $fnc . $this->merchantId . $this->userid . $data . $this->authenKey;
        mylog(['VMMC_BEFOR_CHECKSUM' => $md5str]);
        $checksum = md5($md5str);
        $params = array(
            'fnc' => $fnc,
            'mid' => $this->merchantId,
            'uid' => $this->userid,
            'type' => $type,
            'data' => $data,
            'checksum' => $checksum,
        );
        // return $this->__gialap($param);
        $result = $this->call($params); 
        return $this->returnData($result); 
    }
    protected function __gialap($params)
    {
        return  [
            'status' => true,
            'status_code_partner' => '00',
            'message' => 'Thành công',
            'data' => [
                'bank_account' => null,
                'bank_account_holder' => $params['bank_account_holder'],
                'bank_account_holder_response' => null,
            ]
        ];
    }
    public function returnData($result)
    {
        // Xử lý và trả về dữ liệu theo định dạng mong muốn
        // Ví dụ:isset($data['error']['code'])
        if (isset($result['error_code']) && $result['error_code']) {
            if ($result['error_code'] == '00') {
                return [
                    'status' => true,
                    'data' => $result ?? null,
                    'status_code_partner' => $result['error_code'],
                    'message' => isset($result['error_description']) ? $result['error_description'] : '',
                ];
            } else {
                return [
                    'status' => true,
                    'data' => [],
                    'status_code_partner' => $result['error_code'],
                    'message' => isset($result['error_description']) ? $result['error_description'] : '',
                ];
            }
        } else {
            return [
                'status' => false,
                'status_code_partner' => isset($result['error_description']) ? $result['error_description'] : 99,
                'data' => null,
                'message' => isset($result['error_description']) ? $result['error_description'] : '',
            ];
        }
    }

    function call($params)
    {
        $curl = curl_init($this->baseUrl);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_ANY);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($curl, CURLOPT_TIMEOUT, 10);
        //curl_setopt($curl, CURLOPT_USERAGENT, 'Sample Code');
        //curl_setopt($curl, CURLOPT_USERPWD, $merchantId.':'.$authenKey);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $params);
        $response = curl_exec($curl);
        $resultStatus = curl_getinfo($curl);
        if ($resultStatus['http_code'] == 200) {
            $data = json_decode($response, true);
            return $data;
        } else {
            return false;
        }
    }

    public function encryptNew($data, $encryptkey, $method = 'AES-256-ECB')
    {
        $key = hash('sha256', $encryptkey);
    
        // ECB mode không sử dụng IV, nên chúng ta bỏ qua phần tạo IV
        $encrypted = openssl_encrypt($data, $method, $key, OPENSSL_RAW_DATA);
        
        // Chúng ta không cần nối IV vào kết quả mã hóa
        $encrypted = base64_encode($encrypted);
        
        return $encrypted;
    }

    public function decryptNew($data, $encryptkey, $method = 'AES-256-ECB')
    {
        $key = hash('sha256', $encryptkey);
        $data = base64_decode($data);
        $ivSize = openssl_cipher_iv_length($method);
        $iv = substr($data, 0, $ivSize);
        $data = openssl_decrypt(substr($data, $ivSize), $method, $key, OPENSSL_RAW_DATA, $iv);
        return $data;
    }

    function Encrypt($input, $keyCode)
    {
        $input = trim($input);

        // Tạo key 24 byte từ md5 của keyCode
        $key = substr(md5($keyCode), 0, 24);

        // Sử dụng thuật toán TripleDES (DES-EDE3-CBC)
        $method = 'DES-EDE3-CBC';

        // Tạo IV (Initialization Vector)
        $ivSize = openssl_cipher_iv_length($method);
        $iv = openssl_random_pseudo_bytes($ivSize);

        // Padding input
        $blockSize = 8; // Block size for TripleDES
        $padding = $blockSize - (strlen($input) % $blockSize);
        $input .= str_repeat(chr($padding), $padding);

        // Mã hóa
        $encryptedData = openssl_encrypt($input, $method, $key, OPENSSL_RAW_DATA, $iv);

        // Kết hợp IV và dữ liệu đã mã hóa
        $combined = $iv . $encryptedData;

        // Mã hóa base64 để dễ lưu trữ và truyền tải
        return base64_encode($combined);
    }


    public function Decrypt($input, $keyCode)
    {
        // $input = base64_decode($input);
        // $key = substr(md5($keyCode), 0, 24);

        // $text = mcrypt_decrypt(MCRYPT_TRIPLEDES, $key, $input, MCRYPT_MODE_ECB, 'Mkd34ajdfka5');
        // $block = mcrypt_get_block_size('tripledes', 'ecb');
        // $packing = ord($text{
        //     strlen($text) - 1});

        // if ($packing && ($packing < $block)) {
        //     for ($P = strlen($text) - 1; $P >= strlen($text) - $packing; $P--) {
        //         if (ord($text{
        //             $P}) != $packing) {
        //             $packing = 0;
        //         }
        //     }
        // }

        // $text = substr($text, 0, strlen($text) - $packing);
        // return $text;
    }
}
