<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360SendOtpAction\SubAction;

use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360CodeOtp;

class GetOtpChuaSuDungSubAction
{
	public function run(string $username, string $commandCode)
	{
		return Mpos360CodeOtp::query()
												 ->where('obj_value', $username)
												 ->where('command_code', $commandCode)
												 ->where('status', Mpos360Enum::MPOS360_OTP_CHUA_SU_DUNG)
												 ->where('time_out', '>', now()->timestamp)
												 ->first();
	}
}
