<?php

namespace App\Modules\Merchant\Actions\MerchantSignature\Mpos360MerchantSignatureCreateAction;

use App\Modules\Merchant\Model\Mpos360MerchantSignature;
use App\Modules\Merchant\Requests\MerchantSignature\Mpos360MerchantSignatureCreateRequest;

class Mpos360MerchantSignatureCreateAction
{
	public function run(Mpos360MerchantSignatureCreateRequest $request): Mpos360MerchantSignature
	{
		$deviceSession = $request->getCurrentDeviceSession();
		$merchantId = $deviceSession->getMerchantId();

		$signature = Mpos360MerchantSignature::query()
			->updateOrCreate(
				[
					'merchant_id' => $merchantId,
					'signature_url' => trim($request->json('data.signature_url'))
				],

				[
					'time_created' => now()->timestamp,
					'time_updated' => now()->timestamp,
					'status' => $request->json('data.status')
				]
			);

		return $signature;
	}
}
