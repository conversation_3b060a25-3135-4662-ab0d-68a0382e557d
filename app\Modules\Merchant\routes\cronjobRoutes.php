<?php

use Illuminate\Support\Facades\Route;
use App\Modules\Merchant\Controllers\Cronjob\CronjobController;
use App\Modules\Merchant\Controllers\ChungThuc\Mpos360ChungThucController;
use App\Modules\Merchant\Controllers\RequestChangeInfo\Mpos360MerchantRequestCronController;
use App\Modules\Merchant\Controllers\RequestChangeInfo\Mpos360RequestChangeInfoController;

Route::any('/Mpos360MerchantRequestCronPushRecord', [
	'uses' => Mpos360MerchantRequestCronController::class . '@Mpos360MerchantRequestCronPushRecord',
	'as' => 'Mpos360MerchantRequestCronPushRecordAction'
])->middleware('checkSumForCronjob:abcdef');

Route::any('/Mpos360MerchantRequestCronPushRecordV3', [
	'uses' => CronjobController::class . '@Mpos360MerchantRequestCronPushRecordV3',
	'as' => 'Mpos360MerchantRequestCronPushRecordV3Action'
])->middleware('checkSumForCronjob:abcdef');

Route::any('/Mpos360MerchantRequestCronPushSign', [
	'uses' => Mpos360MerchantRequestCronController::class . '@Mpos360MerchantRequestCronPushSign',
	'as' => 'Mpos360MerchantRequestCronPushSignAction'
])->middleware('checkSumForCronjob:abcdef');

Route::any('/Mpos360MerchantRequestUpdateFile', [
	'uses' => Mpos360MerchantRequestCronController::class . '@Mpos360MerchantRequestUpdateFile',
	'as' => 'Mpos360MerchantRequestUpdateFileAction'
])->middleware('checkSumForCronjob:abcdef');

Route::any('/Mpos360CronPushChungThuc', [
	'uses' => Mpos360ChungThucController::class . '@Mpos360PushChungThuc',
	'as' => 'Mpos360PushChungThucAction'
])->middleware('checkSumForCronjob:abcdef'); 

Route::any('/Mpos360CronRemoveOldSession', [
	'uses' => Mpos360ChungThucController::class . '@Mpos360CronRemoveOldSession',
	'as' => 'Mpos360CronRemoveOldSessionAction'
]);

// Webhook
Route::post('/Mpos360ReceiveRequestChangeInfoResult', [
	'uses' => Mpos360RequestChangeInfoController::class . '@ReceiveRequestChangeInfoResult',
	'as' => 'Mpos360ReceiveRequestChangeInfoResultAction'
])->middleware('checkSumForCronjob:mynextpay_id|data_feedback|comment|status'); 
