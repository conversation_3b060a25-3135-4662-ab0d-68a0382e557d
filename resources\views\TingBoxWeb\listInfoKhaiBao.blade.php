<html lang="zxx">

<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<title>{{ $title }}</title>
	<link rel="icon" type="image/png" href="{{cumtomAsset('assets/img/favicon-mpos.svg')}}">
	<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
	<link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.15.3/dist/sweetalert2.min.css">
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/choices.js@9.0.1/public/assets/styles/choices.min.css" />

	<link rel="stylesheet" href="{{ cumtomAsset('assets/css/style.css') }}">
	<link rel="stylesheet" href="{{ cumtomAsset('assets/css/style-other.css') }}">
	
	<script src="https://cdnjs.cloudflare.com/ajax/libs/core-js/2.4.1/core.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/es6-promise@4.2.8/dist/es6-promise.auto.min.js"></script>
	
	<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.15.3/dist/sweetalert2.all.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/choices.js@9.0.1/public/assets/scripts/choices.min.js"></script>
	<script src="{{ cumtomAsset('assets/js/style.js') }}"></script>

	<style>
    /* Đặt z-index cao hơn cho dropdown của Choices.js */
    .choices__list {
      z-index: 9999 !important;  /* Đảm bảo rằng dropdown có z-index cao hơn các phần tử khác */
    }

    /* Sửa lỗi chữ bị mờ bằng cách kiểm tra opacity */
    .choices__input {
      opacity: 1 !important;  /* Đảm bảo rằng trường nhập liệu không bị mờ */
      color: #333;  /* Đảm bảo màu chữ rõ ràng */
    }

    .choices__item {
      opacity: 1 !important;  /* Đảm bảo các mục trong dropdown không bị mờ */
      color: #333;  /* Đảm bảo màu chữ rõ ràng */
    }

    /* Có thể thêm các kiểu tùy chỉnh khác ở đây */

		.disabled-step {
			pointer-events: none; /* Disable clicking */
			cursor: not-allowed; /* Show a "not-allowed" cursor */
		}

		.se-pre-con {
        position: fixed;
        left: 0px;
        top: 0px;
        width: 100%;
        height: 100%;
        z-index: 9999;
        opacity: 0.6;
        /* background: url(/mpos360-app-api/public/images/web/loader-64x/Preloader_3.gif) center no-repeat #fff; */
        background: url(/images/web/loader-64x/Preloader_3.gif) center no-repeat #fff;
    }
	</style>
	<script>
		window.addEventListener('beforeunload', (event) => {
      $('#loadingPage').addClass('se-pre-con');
    });
	</script>
</head>

<body>
	<div id="loadingPage"></div>

	<section class="ftco-section login-wap">
		<div class="wrapper-page">
			<div class="head-wrap">
					<label>Đăng ký tài khoản</label>
					<span><img src="{{ cumtomAsset('assets/img/logo-mpos360.svg') }}"></span>
			</div>

			<div class="login-wrap">
				<div class="mpos360-head">
					<h1>Bắt đầu nhận thanh toán</h1>
					<p>Để bắt đầu sử dụng loa TingBox và nhận thanh toán, vui lòng thực hiện 3 bước dưới đây</p>
				</div>
				@php
					$step2 = !empty($listStepInfo['data']['locations']);
					$step3 = !empty($listStepInfo['data']['locations']['0']['deviceDTOs']);
				@endphp
				@php($count=0)

				<div class="mpos360-form_reg mb-4">
					<ul id="box-full">
						@if (!empty($listStepInfo['data']['locations']))
							@php($count++)
							<li class="step-success">
								<span class="mfr-numbe">1</span>
								<span class="mfr-check"><img src="assets/img/icon-check.svg"></span>
								<div class="ss-content">
									<p>
										Khai báo cửa hàng 
										<button 	data-areaId="{{ $listStepInfo['data']['locations'][0]['id'] }}" 
												id="areaId"
												href="javascript:void(0)" 
												onclick="return onEditCuaHang(this)"
												class="btn btn-link">
												Sửa
										</button>
									</p>

									<div class="box-acc">
										<label>
											{{ $listStepInfo['data']['locations'][0]['areaName'] }}
										</label>
										<p>
											{{ $listStepInfo['data']['locations'][0]['areaAddress'] }},
											{{ $listStepInfo['data']['locations'][0]['areaDistrict'] }},
											{{ $listStepInfo['data']['locations'][0]['areaCity'] }}
										</p>
									</div>
								</div>
							</li>
						@else 
							<li class="active">
									<span class="mfr-numbe">1</span>
									<span class="mfr-check">
										<img src="assets/img/icon-check.svg">
									</span>

									<a href="" data-bs-toggle="modal" data-bs-target="#khaiBaoCuaHang">
										<img src="assets/img/favicon-shop.svg"> Khai báo cửa hàng
									</a>
							</li>
						@endif


						@if (!empty($listStepInfo['data']['locations']['0']['deviceDTOs']))
							@php($count++)
							<li class="step-success">
								<span class="mfr-numbe">2</span>
								<span class="mfr-check"><img src="assets/img/icon-check.svg"></span>

								<div class="ss-content">
									<p>
										Loa Tingbox 
										<a href="javascript:void(0)" data-bs-toggle="modal" data-bs-target="#modalGanLoaTingBox">Thêm</a>
									</p>

									@foreach ($listStepInfo['data']['locations']['0']['deviceDTOs'] as $dv)
										<div class="box-success mb-2 d-flex">
											<a href="{{ \App\Lib\Mpos360UrlHelper::buildTingBoxDetailUrl('Mpos360TingBoxDeviceHuongDanSuDungAction', $dv) }}">
												<article>
														<label class="font-weight-bold d-block">{{ $dv['serialNumber'] }}</label>
														@if ($dv['status'] == 'ACTIVE')
															<p class="bs-success">Đã kích hoạt</p>
														@else
															<p class="bs-warning">Đang kích hoạt</p>
														@endif
												</article>
											</a>

											<img src="{{ \App\Lib\Mpos360UrlHelper::getTingBoxThumbnail($dv['serialNumber']) }}">
										</div>
									@endforeach
								</div>
							</li>
						@else
							<li class="{{ $count == 1 ? 'active' : '' }}">
								<span class="mfr-number">2</span>
								<span class="mfr-check"><img src="assets/img/icon-check.svg"></span>
								
								<a class="{{$step2 ? '' : 'disabled-step'}}" href="javascript:void(0);" data-bs-toggle="modal" data-bs-target="#modalGanLoaTingBox">
									<img src="assets/img/favicon-void.svg"> 
									Gán loa TingBox
								</a>
							</li>
						@endif

						@if (!empty($taiKhoanNganHang['data']['bankId']))
							@php($count++)
							<li class="step-success">
								<span class="mfr-numbe">3</span>
								<span class="mfr-check"><img src="assets/img/icon-check.svg"></span>
								<div class="ss-acc">
									<p>Tài khoản ngân hàng nhận tiền</p>

									<div class="acc-show">
										<label>{{ $taiKhoanNganHang['data']['accountNo'] }}</label>
										<p>{{ $taiKhoanNganHang['data']['holderName'] }}</p>
										<p>{{ $taiKhoanNganHang['data']['bankName']}}</p>
									</div>
								</div>
							</li>
						@else 
							<li class="{{ $count == 2 ? 'active' : '' }}">
								<span class="mfr-number">3</span>
								<span class="mfr-check"><img src="assets/img/icon-check.svg"></span>
								<a class={{$step3 ? '' : 'disabled-step'}} href="" data-bs-toggle="modal" data-bs-target="#formTknh"><img
										src="assets/img/favicon-bank.svg"> Khai báo tài khoản nhận tiền</a>
							</li>
						@endif
					</ul>
				</div>

				@if ($count == 3)
					<div class="mpos360-footer">
						<button type="button" class="btn text-center btn-blue w-100 d-block btn-success" onclick="return onHoanTatQuyTrinh()">Hoàn tất</button>
					</div>
				@endif
			</div>
		</div>
	</section>
	
	@include('TingBoxWeb.modal.modalGanLoaTingBox', ['request' => $request])
	@include('TingBoxWeb.modal.modalTknhNhanTien', ['request' => $request])
	@include('TingBoxWeb.modal.modalKhaiBaoCuaHang', ['request' => $request])

	
	<script>
		function onEditCuaHang(element) {
			$(element).attr('disabled', true);
			$('#loadingPage').addClass('se-pre-con');

			$.post('/Mpos360TingBoxGetFormSuaCuaHang', {
				formParams: '<?php echo json_encode($locationEditParam) ?>'
			})
			.then(function (res) {
				$('body').append(res.message);
				$('#modalSuaThongTinCuaHang').modal('show');
				$('#modalSuaThongTinCuaHang').on('hidden.bs.modal', function () {
					$(this).remove();
				});
			}).always(function () {
				$('#loadingPage').removeClass('se-pre-con');
				$(element).removeAttr('disabled');
			})
		}

		function onHoanTatQuyTrinh() {
			return window.location.href = '/dang-ky-thanh-cong';
		}
	</script>
	@stack('jsBot')
</body>

</html>