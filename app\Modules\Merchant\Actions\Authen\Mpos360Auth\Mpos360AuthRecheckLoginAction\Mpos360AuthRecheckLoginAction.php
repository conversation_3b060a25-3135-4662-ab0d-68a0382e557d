<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthRecheckLoginAction;

use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360AuthRecheckLoginRequest;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction\BuildBottomNavAppSubAction;
use App\Modules\Merchant\Model\Mpos360User;

class Mpos360AuthRecheckLoginAction
{
	public function run(Mpos360AuthRecheckLoginRequest $request)
	{
		$deviceSession = DeviceSession::query()->where('id', $request->json('data.sessionId'))
			->where('api_key', $request->json('data.sessionApiKey'))
			->where('user_id', $request->json('data.userId'))
			->where('time_expired', '>', now()->timestamp)
			->first();

		if (!$deviceSession) {
			return $this->__emptySessionHandler();
		}

		$user = Mpos360User::query()->firstWhere('id',  $deviceSession->user_id);
		$dataUser = json_decode($user->data_users, true);

		$returnData = [
			'is_login_allowed' => 'YES',
			'device_id' => $deviceSession->device_id,
			'user_id' => $deviceSession->user_id,
			'api_key' => $deviceSession->api_key,
			'api_secret' => '',
			'time_expired' => $deviceSession->time_expired,
			'time_created' => $deviceSession->time_created,
			'partner_config_id' => $deviceSession->partner_config_id,
			'id' => $deviceSession->id,
			'merchant_info' => json_encode([
				'merchantId' => $user->merchant_id,
				'username' => $dataUser['username'],
				'merchantEmail' => $dataUser['merchantEmail'],
				'merchantName' => $dataUser['merchantName']
			]),
			'can' => Mpos360Enum::CAN_GO_TO_HOME_SCREEN,
			'bottom_nav_config' => app(BuildBottomNavAppSubAction::class)->run()
		];

		return $returnData;
	}

	private function __emptySessionHandler()
	{
		return [
			"is_login_allowed" => 'NO',
			"device_id" => "",
			"user_id" => "",
			"api_key" => "",
			"api_secret" => "",
			"time_expired" => "",
			"time_created" => "",
			"partner_config_id" => "",
			"id" => "",
			"merchant_info" => "",
			"can" => "",
			"bottom_nav_config" => []
		];
	}
} // End class
