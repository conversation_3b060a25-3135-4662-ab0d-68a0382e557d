<?php

namespace App\Lib;

use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Model\Mpos360User;

/**
 * Lớp này sẽ xử lý make clas DeviceSession có relationship là mpos360User luôn
 * mà sẽ không được phép chọc vào DB
 */
class DeviceSessionManualHelper
{
	public static function makeInstance(string $apiKeyBase64): DeviceSession
	{
		try {
			$dc = decrypt($apiKeyBase64);
			$decodeAsArray = json_decode($dc, true);
		}catch(\Throwable $th) {
			$decode = base64_decode($apiKeyBase64);
			$decodeAsArray = json_decode($decode, true);
		}

		try {
			$dataMerchant = json_decode($decodeAsArray['mpos360_user']['data_merchant'], true);
			$mposToken = $dataMerchant['data']['mobileUserToken'];

			$deviceSession = new DeviceSession([
				'id' => $decodeAsArray['id'],
				'device_id' => $decodeAsArray['device_id'],
				'user_id' => $decodeAsArray['user_id'],
				'api_key' => $decodeAsArray['api_key'],
				'api_secret' => $decodeAsArray['api_secret'],
				'time_expired' => $decodeAsArray['time_expired'],
				'time_created' => $decodeAsArray['time_created'],
				'time_updated' => time(),
				'mpos_token' => $mposToken,
				'mnp_token' => '',
				'partner_config_id' => $decodeAsArray['partner_config_id'],
				'signed' => $decodeAsArray['signed'] ?? ''
			]);

			$dv = $deviceSession->setRelation('mpos360User', new Mpos360User($decodeAsArray['mpos360_user']));
			return $dv;
		}catch(\Throwable $th) {
			throw new BusinessException('Lỗi không phân giải được thông tin');
		}
	}
} // End class