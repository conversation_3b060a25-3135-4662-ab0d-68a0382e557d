<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\SubAction;

use Exception;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360ChungThuc;
use App\Modules\Merchant\Model\Mpos360PhuongThucBuoc3;

class GetPhuongThucQuetB3SubAction
{
	public function run($merchantId)
	{
		$listChungThuc = Mpos360ChungThuc::query()
			->where('merchant_id', $merchantId)
			->where('status', Mpos360Enum::MPOS360_CHUNG_THUC_STT_DA_XAC_NHAN)
			->get();

		if ($listChungThuc->isEmpty()) {
			throw new BusinessException('Lỗi: không tìm thấy pt chứng thực phù hợp');
		}

		$methodCodes = $listChungThuc->pluck('method_code')->toArray();

		$listPhuongThucScanBuoc3 = Mpos360PhuongThucBuoc3::getPhuongThucBuoc3();

		$scanMethod = $listPhuongThucScanBuoc3->filter(function (Mpos360PhuongThucBuoc3 $pt) use ($methodCodes) {
			return in_array($pt->method_code, $methodCodes);
		})->map(function (Mpos360PhuongThucBuoc3 $pt) {
			return [
				'method_code' => $pt->method_code,
				'method_name' => $pt->method_code,
				'is_main' => $pt->priority > 0 ? 'YES' : 'NO'
			];
		})
			->values()
			->toArray();


		return $scanMethod;
	}
} // End class