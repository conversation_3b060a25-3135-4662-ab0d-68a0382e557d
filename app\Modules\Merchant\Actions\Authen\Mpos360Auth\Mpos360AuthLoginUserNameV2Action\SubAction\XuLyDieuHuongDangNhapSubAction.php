<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginUserNameV2Action\SubAction;

use App\Lib\Helper;
use App\Lib\PasswordHandler;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\DeviceSession;

class XuLyDieuHuongDangNhapSubAction
{
	public bool $isCoThietBi = false;

	public function listUsernameCanGoHome()
	{
		return [
			"<EMAIL>",
			"0365820650"
		];
	}

	public function checkCoThietBi($loginMpos)
	{
		if (!empty($loginMpos['data']['countSoundBoxOfMc']) || !empty($loginMpos['data']['countReaderBoxOfMc'])) {
			$this->isCoThietBi = true;
		}
	}

	public function run(DeviceSession $deviceSession, $loginMpos = [], string $password)
	{
		$this->checkCoThietBi($loginMpos);

		$deviceSession->can = Mpos360Enum::CAN_GO_TO_HOME_SCREEN;

		if (!$this->isCoThietBi) {
			// Không có thiết bị thì ra màn hình nhận mã OTP qua email
			if (Helper::isUserNameEqualEmail($loginMpos['data']['username'])) {
				$deviceSession->can = Mpos360Enum::CAN_GO_TO_OTP_LOGIN_FLOW_EMAIL;
			}else {
				if ($loginMpos['data']['merchantStatus'] == 'ACTIVE') {
					$deviceSession->can = Mpos360Enum::CAN_GO_TO_HOME_SCREEN;
				}else {
					$deviceSession->can = Mpos360Enum::CAN_GO_TO_TINGBOX_REGISTER_FLOW;
				}
			}
		}

		// Có thiết bị, thì kiểm tra mk
		if ($this->isCoThietBi) {
			$passwordHandler = new PasswordHandler([]);
			if ($passwordHandler->isSimplePassword($password)) {
				$deviceSession->can = Mpos360Enum::CAN_GO_TO_VERIFY_MPOS_DEVICE_SCREEN;
			}
		}

		// Xử lý nếu là merchant mới bị pending -> thì đi vào thẳng màn Home
		if (!empty($loginMpos['data']['merchantStatus'])) {
			if ($loginMpos['data']['merchantStatus'] == 'PENDING_INFORMATION') {
				$deviceSession->can = Mpos360Enum::CAN_GO_TO_TINGBOX_REGISTER_FLOW;
			}
		}

		// MC ngoại lệ thì được vào màn home thoải mái
		if (in_array($loginMpos['data']['username'], $this->listUsernameCanGoHome())) {
			$deviceSession->can = Mpos360Enum::CAN_GO_TO_HOME_SCREEN;
		}

		return $deviceSession;
	}
}
