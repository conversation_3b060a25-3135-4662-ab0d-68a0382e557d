<?php

namespace App\Modules\Merchant\Requests\InstantWithdrawal\V3;

use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Requests\MerchantRequest;
use Illuminate\Validation\Rule;

class Mpos360ListTransByWithdrawalOrderCodeRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.email' => ['required', 'string', 'email'],
			'data.order_code' => ['required', 'string'],
			'data.type' => [
				'required', 
				'string', 
				Rule::in([
					Mpos360Enum::MPOS360_RTN_TYPE_GD_VIETQR_TAO_YC,
					Mpos360Enum::MPOS360_RTN_TYPE_GD_VIETQR_DUOC_DUYET,
					Mpos360Enum::MPOS360_RTN_TYPE_GD_VIETQR_TU_CHOI,
					Mpos360Enum::MPOS360_RTN_TYPE_GD_TRA_LAI,
					Mpos360Enum::MPOS360_RTN_TYPE_GD_THU_THEM,
				])
			],
		];
	}
}
