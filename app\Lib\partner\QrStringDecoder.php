<?php
namespace App\Lib\partner;

class QrStringDecoder {
    public function decode(string $qrString): array {
        $result = [
            'version' => '',           // tag 00
            'initiation_method' => '', // tag 01
            'merchant_info' => [       // tag 38
                'consumer_id' => '',     // subtag 00
                'service_code' => '',    // subtag 01
                'account_number' => '',  // extracted from service_code
                'merchant_id' => ''      // subtag 02
            ],
            'currency' => '',          // tag 53
            'country' => '',           // tag 58
            'additional_data' => '',   // tag 62
            'crc' => ''               // tag 63
        ];

        $position = 0;
        while ($position < strlen($qrString)) {
            $tag = substr($qrString, $position, 2);
            $position += 2;

            $length = (int)substr($qrString, $position, 2);
            $position += 2;

            $value = substr($qrString, $position, $length);
            $position += $length;

            switch ($tag) {
                case '38':
                    $result['merchant_info'] = $this->parseMerchantInfo($value);
                    break;
                case '00':
                    $result['version'] = $value;
                    break;
                case '01':
                    $result['initiation_method'] = $value;
                    break;
                case '53':
                    $result['currency'] = $value;
                    break;
                case '58':
                    $result['country'] = $value;
                    break;
                case '62':
                    $result['additional_data'] = $value;
                    break;
                case '63':
                    $result['crc'] = $value;
                    break;
            }
        }

        return $result;
    }

    private function parseMerchantInfo(string $value): array {
        $parsed = [
            'consumer_id' => '',
            'service_code' => '',
            'account_number' => '',
            'merchant_id' => ''
        ];

        $pos = 0;
        while ($pos < strlen($value)) {
            $subTag = substr($value, $pos, 2);
            $pos += 2;

            $subLength = (int)substr($value, $pos, 2);
            $pos += 2;

            $subValue = substr($value, $pos, $subLength);
            $pos += $subLength;

            switch ($subTag) {
                case '00':
                    $parsed['consumer_id'] = $subValue;
                    break;
                case '01':
                    $parsed['service_code'] = $subValue;
                    // Trích xuất số tài khoản từ service code
                    $parsed['account_number'] = substr($subValue, 14);
                    break;
                case '02':
                    $parsed['merchant_id'] = $subValue;
                    break;
            }
        }

        return $parsed;
    }
}