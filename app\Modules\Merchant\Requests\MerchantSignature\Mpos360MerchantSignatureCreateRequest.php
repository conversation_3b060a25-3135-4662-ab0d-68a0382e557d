<?php

namespace App\Modules\Merchant\Requests\MerchantSignature;

use Illuminate\Validation\Rule;
use App\Modules\Merchant\Requests\MerchantRequest;
use App\Modules\Merchant\Rules\MerchantSignature\ImageUrlRule;

class Mpos360MerchantSignatureCreateRequest extends MerchantRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data.email' => ['required', 'string', 'email'],
      'data.signature_url' => ['required', 'string', 'url', new ImageUrlRule()],
      'data.status' => ['required', Rule::in(['1', '2'])],
    ];
  }

	public function message() {
		return [
			'data.email.required' => vmsg('Mpos360MerchantSignatureCreateRequest_EmailLaBatBuoc'),
			'data.email.string' => vmsg('Mpos360MerchantSignatureCreateRequest_EmailPhaiLaKieuChuoi'),
			'data.email.email' => vmsg('Mpos360MerchantSignatureCreateRequest_EmailKhongDungDinhDang'),

			'data.signature_url.required' => vmsg('Mpos360MerchantSignatureCreateRequest_ChuKyLaBatBuoc'),
			'data.signature_url.string' => vmsg('Mpos360MerchantSignatureCreateRequest_ChuKyPhaiLaMotChuoiKyTu'),
			'data.signature_url.url' => vmsg('Mpos360MerchantSignatureCreateRequest_ChuKyPhaiLaMotUrl'),

			'data.status.required' => vmsg('Mpos360MerchantSignatureCreateRequest_TrangThaiChuKyLaBatBuoc'),
			'data.status.in' => vmsg('Mpos360MerchantSignatureCreateRequest_TrangThaiChuKyKhongHopLe'),
		];
	}
} // End class
