<?php

namespace App\Modules\Merchant\Controllers\Account;

use App\Lib\Helper;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\Account\Mpos360AccountBAFRequest;
use App\Modules\Merchant\Requests\Account\Mpos360AccountCommonInfoRequest;
use App\Modules\Merchant\Requests\Account\Mpos360AccountContractingAgentRequest;
use App\Modules\Merchant\Requests\Account\Mpos360AccountBusinessRegistrationRequest;
use App\Modules\Merchant\Actions\Account\Mpos360AccountBAFAction\Mpos360AccountBAFAction;
use App\Modules\Merchant\Actions\Account\Mpos360AccountCommonInfoAction\Mpos360AccountCommonInfoAction;
use App\Modules\Merchant\Actions\Account\Mpos360AccountContractingAgentAction\Mpos360AccountContractingAgentAction;
use App\Modules\Merchant\Actions\Account\Mpos360AccountBusinessRegistrationAction\Mpos360AccountBusinessRegistrationAction;

class Mpos360AccountController extends Controller
{
	public function Mpos360AccountCommonInfo(Mpos360AccountCommonInfoRequest $request)
	{
		try {
			$result = app(Mpos360AccountCommonInfoAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function Mpos360AccountBAF(Mpos360AccountBAFRequest $request)
	{
		try {
			$result = app(Mpos360AccountBAFAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function Mpos360AccountContractingAgent(Mpos360AccountContractingAgentRequest $request)
	{
		try {
			$result = app(Mpos360AccountContractingAgentAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function Mpos360AccountBusinessRegistration(Mpos360AccountBusinessRegistrationRequest $request)
	{
		try {
			$result = app(Mpos360AccountBusinessRegistrationAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
