<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\SubAction;

use Exception;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;

class CreateMerchantRequestSubAction
{
	public function run($param = [])
	{
		$param['time_expired'] = now()->addHours(3)->timestamp;
		$merchantRequest = Mpos360MerchantRequest::query()->forceCreate($param);
		if (!$merchantRequest) {
			throw new BusinessException(__('dttv3.Lỗi không tạo được bản ghi yêu cầu thay đổi thông tin'));
		}
		$code = sprintf('%s%s', date('ymd'), $merchantRequest->id);
		$merchantRequest->mpos360_code = $code;
		$r = $merchantRequest->save();

		if (!$r) {
			throw new BusinessException('Lỗi không lưu được thông tin YC');
		}
		
		return $merchantRequest;
	}
} // End class