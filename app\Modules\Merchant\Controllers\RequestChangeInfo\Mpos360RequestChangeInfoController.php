<?php

namespace App\Modules\Merchant\Controllers\RequestChangeInfo;

use App\Lib\Helper;
use App\Lib\MnpOnboardNewMcHelper;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\UploadS3MNPSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\GetMNPAccessTokenSubAction;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoListRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoCancelRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoDetailRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoGetOtpRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\ReceiveRequestChangeInfoResultRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoGetConfigRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoResendOtpRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoMarkAsDoneRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoListProfileRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoStep3HandlerRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoUploadProfileRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoVerifyProfileRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoCheckEmailAndMobileRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoCheckProfileBankingRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListAction\Mpos360RequestChangeInfoListAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoCancelAction\Mpos360RequestChangeInfoCancelAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoDetailAction\Mpos360RequestChangeInfoDetailAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoGetOtpAction\Mpos360RequestChangeInfoGetOtpAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoGetConfigAction\Mpos360RequestChangeInfoGetConfigAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoResendOtpAction\Mpos360RequestChangeInfoResendOtpAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoMarkAsDoneAction\Mpos360RequestChangeInfoMarkAsDoneAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\Mpos360RequestChangeInfoListProfileAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoStep3HandlerAction\Mpos360RequestChangeInfoStep3HandlerAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360ReceiveRequestChangeInfoResultAction\Mpos360ReceiveRequestChangeInfoResultAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\Mpos360RequestChangeInfoUpdateProfileAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoVerifyProfileAction\Mpos360RequestChangeInfoVerifyProfileAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoCheckEmailAndMobileAction\Mpos360RequestChangeInfoCheckEmailAndMobileAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoCheckProfileBankingAction\Mpos360RequestChangeInfoCheckProfileBankingAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\Mpos360RequestChangeInfoAdditionalAttachmentAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessAction\Mpos360RequestChangeInfoAllOtpProfileSuccessAction;

class Mpos360RequestChangeInfoController extends Controller
{
	// get config
	public function Mpos360RequestChangeInfoGetConfig(Mpos360RequestChangeInfoGetConfigRequest $request)
	{
		try {
			$result = app(Mpos360RequestChangeInfoGetConfigAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	// nhận kết quả xử lý
	public function ReceiveRequestChangeInfoResult(ReceiveRequestChangeInfoResultRequest $request)
	{
		try {
			$result = app(Mpos360ReceiveRequestChangeInfoResultAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	// danh sách các hồ sơ
	public function Mpos360RequestChangeInfoListProfile(Mpos360RequestChangeInfoListProfileRequest $request)
	{
		try {
			$result = app(Mpos360RequestChangeInfoListProfileAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	// kiểm tra profile banking là ok hay chưa
	public function Mpos360RequestChangeInfoCheckProfileBanking(Mpos360RequestChangeInfoCheckProfileBankingRequest $request)
	{
		try {
			$result = app(Mpos360RequestChangeInfoCheckProfileBankingAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	// tạo yêu cầu
	public function Mpos360RequestChangeInfoUpdateProfile(Mpos360RequestChangeInfoUpdateProfileRequest $request)
	{
		try {
			$result = app(Mpos360RequestChangeInfoUpdateProfileAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	// lấy mã otp
	public function Mpos360RequestChangeInfoGetOtp(Mpos360RequestChangeInfoGetOtpRequest $request)
	{
		try {
			$result = app(Mpos360RequestChangeInfoGetOtpAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	// gửi lại OTP
	public function Mpos360RequestChangeInfoResendOtp(Mpos360RequestChangeInfoResendOtpRequest $request)
	{
		try {
			$result = app(Mpos360RequestChangeInfoResendOtpAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	// xác minh hồ sơ OTP/SMS
	public function Mpos360RequestChangeInfoVerifyProfile(Mpos360RequestChangeInfoVerifyProfileRequest $request)
	{
		try {
			$result = app(Mpos360RequestChangeInfoVerifyProfileAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	// upload s3
	public function Mpos360RequestChangeInfoUploadProfile(Mpos360RequestChangeInfoUploadProfileRequest $request)
	{
		if (!empty($request->input('data.is_register_flow'))) {
			$deviceSessionWithToken = new DeviceSession([]);
			$deviceSessionWithToken->mnp_token = app(MnpOnboardNewMcHelper::class)->getAccessTokenMcDefault();
			$deviceSessionWithToken->merchantId = $request->input('data.merchantId');
		}else {
			$deviceSession = $request->getCurrentDeviceSession();
			$deviceSessionWithToken = app(GetMNPAccessTokenSubAction::class)->run($deviceSession);
		}

		try {
			$uploadedFiles = $request->file('data.attachments');
			$uploadResult = app(UploadS3MNPSubAction::class)->run($deviceSessionWithToken, $uploadedFiles);
			return $this->successResponse($uploadResult, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	// bổ sung đính kèm
	public function Mpos360RequestChangeInfoAdditionalAttachment(Mpos360RequestChangeInfoAdditionalAttachmentRequest $request)
	{
		try {
			$result = app(Mpos360RequestChangeInfoAdditionalAttachmentAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	// chỉ định phương thức quét bước 3
	public function Mpos360RequestChangeInfoStep3Handler(Mpos360RequestChangeInfoStep3HandlerRequest $request)
	{
		try {
			$result = app(Mpos360RequestChangeInfoStep3HandlerAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	// đánh dấu là đã quét xong khuôn mặt
	public function Mpos360RequestChangeInfoMarkAsDone(Mpos360RequestChangeInfoMarkAsDoneRequest $request)
	{
		try {
			$result = app(Mpos360RequestChangeInfoMarkAsDoneAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	// ds yêu cầu
	public function Mpos360RequestChangeInfoList(Mpos360RequestChangeInfoListRequest $request)
	{
		try {
			$result = app(Mpos360RequestChangeInfoListAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	// chi tiết yc
	public function Mpos360RequestChangeInfoDetail(Mpos360RequestChangeInfoDetailRequest $request)
	{
		try {
			$result = app(Mpos360RequestChangeInfoDetailAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	// Đánh dấu các là yêu cầu đã đc verify toàn bộ
	public function Mpos360RequestChangeInfoAllOtpProfileSuccess(Mpos360RequestChangeInfoAllOtpProfileSuccessRequest $request) {
		try {
			$result = app(Mpos360RequestChangeInfoAllOtpProfileSuccessAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	// kiểm tra email và sđt có trùng khớp thật sự hay không
	public function Mpos360RequestChangeInfoCheckEmailAndMobile(Mpos360RequestChangeInfoCheckEmailAndMobileRequest $request) {
		try {
			$result = app(Mpos360RequestChangeInfoCheckEmailAndMobileAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	// hủy yc
	public function Mpos360RequestChangeInfoCancel(Mpos360RequestChangeInfoCancelRequest $request) {
		try {
			$result = app(Mpos360RequestChangeInfoCancelAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
} // End class
