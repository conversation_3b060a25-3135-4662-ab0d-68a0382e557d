<?php

namespace App\Modules\TingBoxVAMC\Actions\MerchantBank\ConfirmOtpBankAction;

use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Lib\partner\SoundBox;
use App\Lib\MnpOnboardNewMcHelper;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\CacheAction;
use App\Modules\TingBoxVAMC\Models\PlanEvent;
use App\Modules\TingBoxVAMC\Enums\TingBoxVAMCEnum;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use App\Modules\Merchant\Model\Mpos360Logs\LogRequest;
use App\Modules\TingBoxVAMC\Actions\VAMC\ConfirmLinkVAMCAction;
use App\Modules\TingBoxVAMC\Actions\VAMC\CloseLinkOtpVAMCAction;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\ConfirmOtpBankRequest;
use App\Modules\TingBoxVAMC\Actions\CloseLinkBankAction\SubAction\GoiHuySangBenTingBoxSubAction;
use App\Modules\TingBoxVAMC\Actions\MerchantBank\LinkBankAction\SubAction\SaveBankTrungGianSubAction;
use App\Modules\TingBoxVAMC\Actions\MerchantBank\ConfirmOtpBankAction\SubAction\SyncAssignTingBoxNowSubAction;

class ConfirmOtpBankAction
{
	protected SoundBox $soundBox;
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;
	protected $isNumberSyncCancel = 0;
	protected $isNumberSyncLink = 0;

	public int $soLanNhapSaiLinking = 2;

	public function __construct(SoundBox $soundBox, MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->soundBox = $soundBox;
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run(ConfirmOtpBankRequest $request)
	{
		$merchantShopBank = MerchantShopBank::query()->where([
			'id' => $request->json('data.merchantShopBankId'),
			'merchant_id' => $request->json('data.merchantId'),
			'shop_id' => $request->json('data.mobileUserId'),
		])->first();

		if (!$merchantShopBank) {
			throw new BusinessException('Không tìm thấy thông tin ngân hàng.');
		}

		if (!empty($merchantShopBank->linkingOtpExpired) && now()->timestamp > $merchantShopBank->linkingOtpExpired) {
			throw new BusinessException('Liên kết đã hết hạn, bạn vui lòng tạo 1 liên kết mới khác');
		}

		$merchantShopBank->load('merchantBank');

		$this->soLanNhapSaiLinking = $merchantShopBank->linkingMaxSubmit; 

		$msg = '';

		switch($request->json('data.action')){
			case 'LINK':
				if ($merchantShopBank->linking_otp_wrong_number >= $this->soLanNhapSaiLinking) {
					$msg = sprintf('Bạn đã sai OTP quá %s lần. Hệ thống từ chối xác nhận liên kết hiện tại bạn', $this->soLanNhapSaiLinking);
					throw new BusinessException($msg);
				}

				$msg = 'Liên kết tài khoản ngân hàng thành công. mPOS sẽ sử dụng để chuyển tiền từ các giao dịch hàng ngày vào tài khoản này.';
				$this->confirmLinkBank($merchantShopBank,$request);
				break;
			case 'CANCEL':
				$msg = 'Huỷ liên kết tài khoản ngân hàng thành công.';
				$this->confirmCloseLinkBank($merchantShopBank,$request);
				break;
			case 'REOPEN':
				break;
			default:
				throw new BusinessException('Không có hành động phù hợp yêu cầu.');
				break;
		}


		return [
			'msg' => $msg,
			'can' => TingBoxVAMCEnum::CAN_GO_TO_TINGBOX_VAMC_SUCCESS,
		];
	}

	public function confirmLinkBank($merchantShopBank,Request $request)
	{
		$params = [
			'mcRequestId' => $request->json('data.requestId'),
			'vaNextpayNumber' => $request->json('data.partnerRequestId'),
			'otp' => $request->json('data.otp')
		];


		$logRequest = LogRequest::query()->forceCreate([
			'merchant_id' => $request->json('data.merchantId'),
			'partner' => 'va',
			'func' => 'confirmLinkBank',
			'request' => json_encode($params, JSON_UNESCAPED_UNICODE),
			'created_at' => now()->format('Y-m-d H:i:s'),
			'updated_at' => now()->format('Y-m-d H:i:s'),
		]);

		if (!$logRequest) {
			throw new BusinessException('Lỗi lưu trữ dữ liệu thất bại.');
		}


		$confirmOtp = app(ConfirmLinkVAMCAction::class)->run($params, $merchantShopBank); 

		$logRequest->update(['response' => json_encode($confirmOtp, JSON_UNESCAPED_UNICODE), 'updated_at' => now()->format('Y-m-d H:i:s')]);

		if (isset($confirmOtp['error_code']) && $confirmOtp['error_code'] == '005') {
			$merchantShopBank->linking_otp_wrong_number += 1;
			$merchantShopBank->save();

			if ($merchantShopBank->linking_otp_wrong_number < $this->soLanNhapSaiLinking) {
				$msg = sprintf(
					'Cảnh báo: Bạn đã nhập sai mã OTP %s lần.%s
						Để đảm bảo an toàn, bạn còn %s lần thử lại. Vượt quá %s lần sai, tính năng liên kết sẽ bị khóa và cần liên hệ ngân hàng để mở lại.%s
						Vui lòng kiểm tra kỹ mã OTP trước khi nhập.
					',
					$merchantShopBank->linking_otp_wrong_number,
					PHP_EOL,
					$this->soLanNhapSaiLinking - $merchantShopBank->linking_otp_wrong_number,
					$this->soLanNhapSaiLinking,
					PHP_EOL
				);
			}else {
				$msg = sprintf('Bạn đã sai OTP quá %s lần. Hệ thống từ chối xác nhận liên kết cho bạn', $this->soLanNhapSaiLinking);
			}
			
			throw new BusinessException($msg);
		}

		if (!isset($confirmOtp['error_code']) || $confirmOtp['error_code'] != '00') {
			$msgDefault = sprintf('Xác nhận OTP để liên kết tài khoản ngân hàng không thành công, hãy kiểm tra lại tin nhắn từ Ngân hàng và thử lại. Nếu không nhận được tin nhắn OTP, Quý khách vui lòng liên hệ Ngân hàng phát hành để được hỗ trợ. (Mã lỗi: %s)', isset($confirmOtp['error_code']) ? $confirmOtp['error_code'] : '-1');
			$msg = isset($confirmOtp['error_code']) && isset(TingBoxVAMCEnum::DEFINE_ERROR_MESSAGE_VAMC[$confirmOtp['error_code']]) ? TingBoxVAMCEnum::DEFINE_ERROR_MESSAGE_VAMC[$confirmOtp['error_code']] : $msgDefault;
			throw new BusinessException(
				$msg,
				isset($confirmOtp['error_code']) ? $confirmOtp['error_code'] : '-1'
			);
		}

		$merchantShopBank->username = $request->json('data.username');
		$merchantShopBank->account_qr = isset($confirmOtp['data']) && isset($confirmOtp['data']['qrCode']) ? $confirmOtp['data']['qrCode'] : '';
		$merchantShopBank->time_updated = now()->timestamp;
		$merchantShopBank->status_link = 1;
		$merchantShopBank->data_linked = json_encode($confirmOtp, JSON_UNESCAPED_UNICODE);
		$merchantShopBank->account_number_partner = isset($confirmOtp['data']) && isset($confirmOtp['data']['vaBankNumber']) ? $confirmOtp['data']['vaBankNumber'] : '';
		$r = $merchantShopBank->save();

		if (!$r) {
			throw new BusinessException('Lỗi không cập nhật được mã QR.');
		};

		try {
			app(SyncAssignTingBoxNowSubAction::class)->run($merchantShopBank);
			if (!empty($merchantShopBank->isNeedCreateTrungGian)) {
				app(SaveBankTrungGianSubAction::class)->run($merchantShopBank, $merchantShopBank->merchantBank);
			}

			@CacheAction::query()->forceCreate([
				'step' => 1,
				'reference_id' => $request->json('data.merchantId'),
				'time_created' => time(),
				'other_data' => '',
				'step_code' => 'KHAI_BAO_TKNH'
			]);
		}catch(\Throwable $th) {
			throw new BusinessException('Tài khoản ngân hàng không thể liên kết sang Tingbox.');
		}
	}

	public function confirmCloseLinkBank(MerchantShopBank $merchantShopBank, Request $request)
	{
		if (!$merchantShopBank->isDaLienKet()) {
			throw new BusinessException('Liên kết này chưa được xác nhận, không thể hủy');
		}
		
		if (!$merchantShopBank->isDongBoSoundBox()) {
			throw new BusinessException('Liên kết chưa được đồng bộ sang Tingbox, không thể hủy');
		}

		$params = [
			'mcRequestId' => $request->json('data.requestId'),
			'vaNextpayNumber' => $request->json('data.partnerRequestId'),
			'otp' => $request->json('data.otp')
		];

		$logRequest = LogRequest::query()->forceCreate([
			'merchant_id' => $request->json('data.merchantId'),
			'partner' => 'va',
			'func' => 'confirmCloseLinkBank',
			'request' => json_encode($params, JSON_UNESCAPED_UNICODE),
			'created_at' => now()->format('Y-m-d H:i:s'),
			'updated_at' => now()->format('Y-m-d H:i:s'),
		]);

		if (!$logRequest) {
			throw new BusinessException('Lỗi lưu trữ dữ liệu thất bại.');
		}

		$confirmOtp = app(CloseLinkOtpVAMCAction::class)->run($params);

		$logRequest->update(['response' => json_encode($confirmOtp, JSON_UNESCAPED_UNICODE), 'updated_at' => now()->format('Y-m-d H:i:s')]);
		
		if (!isset($confirmOtp['error_code']) || $confirmOtp['error_code'] != '00') {
			$msgDefault = sprintf('Xác nhận OTP để huỷ liên kết tài khoản ngân hàng thất bại. Hãy liên hệ CSKH để được hỗ trợ (Code: %s)', isset($confirmOtp['error_code']) ? $confirmOtp['error_code'] : '-1');
			$msg = isset($confirmOtp['error_code']) && isset(TingBoxVAMCEnum::DEFINE_ERROR_MESSAGE_VAMC[$confirmOtp['error_code']]) ? TingBoxVAMCEnum::DEFINE_ERROR_MESSAGE_VAMC[$confirmOtp['error_code']] : $msgDefault;
			throw new BusinessException(
				$msg,
				isset($confirmOtp['error_code']) ? $confirmOtp['error_code'] : '-1'
			);
		}

		$merchantShopBank->time_updated = now()->timestamp;
		$merchantShopBank->status_link = 2;
		$merchantShopBank->data_linked = json_encode($confirmOtp, JSON_UNESCAPED_UNICODE);
		$merchantShopBank->save();
		
		$cancelVATingbox = app(GoiHuySangBenTingBoxSubAction::class)->run($merchantShopBank);
			
		// Nếu không cancel được sang tingbox thì tạo bản ghi job
		if (!$cancelVATingbox) {
			$planEventCancel = PlanEvent::query()->forceCreate([
				'merchant_shop_bank_id' => $merchantShopBank->id,
				'merchant_id' => $merchantShopBank->merchant_id,
				'action' => 'CANCLESOUNDBOX',
				'data' => json_encode($request->json('data'), JSON_UNESCAPED_UNICODE),
				'time_created' => time(),
				'time_updated' => time(),
			]);		
		}
	}
} // End class
