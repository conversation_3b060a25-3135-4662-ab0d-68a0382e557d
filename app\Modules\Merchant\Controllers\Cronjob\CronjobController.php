<?php

namespace App\Modules\Merchant\Controllers\Cronjob;

use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Actions\Cronjob\Mpos360MerchantRequestCronPushRecordV3Action\Mpos360MerchantRequestCronPushRecordV3Action;

class CronjobController extends Controller
{
	public function Mpos360MerchantRequestCronPushRecordV3(Request $request)
	{
		try {
			$result = app(Mpos360MerchantRequestCronPushRecordV3Action::class)->run();
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
} // End class
