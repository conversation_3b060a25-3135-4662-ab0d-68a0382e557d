<?php

namespace App\Modules\Merchant\Model;

use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use App\Modules\Merchant\Enums\Mpos360Enum;

class Mpos360ChungThuc extends Model
{
	protected $connection = 'mpos360_data';

	protected $table      = 'merchant_authenticate_key';

	protected $guarded    = [];

	public $timestamps    = false;

	protected $dates      = [];

	protected $hidden     = [];

	public function getOtherData(): array
	{
		$otherData = json_decode($this->other_data, true);
		
		if (!empty($otherData['data'])) {
			$otherData['data'] = json_decode($otherData['data'], true);
		}
		
		return $otherData;
	}

	public function isKhongTonTaiCCCDCu(): bool
	{
		$otherData = $this->getOtherData();
		return empty($otherData['data']['cardDetailInfo']['oldCitizenIdentify']);
	}

	public function isTonTaiCCCDCu()
	{
		return !$this->isKhongTonTaiCCCDCu();
	}

	public function getSoCCCDCu() {
		$otherData = $this->getOtherData();
		return $otherData['data']['cardDetailInfo']['oldCitizenIdentify'];
	}

	public function isDaHetHanCCCDHienTai(): bool
	{
		$otherData = $this->getOtherData();
		try {
			$dateOfExpiry = $otherData['data']['cardDetailInfo']['dateOfExpiry'];
			$dateOfExpiryAsCarbon = Carbon::createFromFormat('d/m/Y', $dateOfExpiry);
			return !now()->isSameDay($dateOfExpiryAsCarbon) && now()->gt($dateOfExpiryAsCarbon);
		}catch(\Throwable $th) {
			return false;
		}
		
	}

	public function isConHanCCCDHienTai(): bool
	{
		return !$this->isDaHetHanCCCDHienTai();
	}

	public function getOriginalCCCD(): string
	{
		$otherData = $this->getOtherData();
		
		if (!empty($otherData['data']['cardDetailInfo']['citizenIdentify'])) {
			return $otherData['data']['cardDetailInfo']['citizenIdentify'];
		}

		return $this->value_confirmed;
	}

	public function isNotEmptyOtherData(): bool {
		$otherData = json_decode($this->other_data, true);
		return !empty($otherData);
	}

	public function forMobile(): array
	{
		$maskValue = $this->value_confirmed;
		$name = $this->value_confirmed;

		$genMobileStatusAndCan = $this->genMobileStatusAndCan($this);
	
		if ($this->key_code == 'CCCD') {
			if (strlen($maskValue) > 3) {
				$maskValue = mask_string($this->value_confirmed, '*', 3);
				$name = sprintf('%s: ***%s', $this->key_code, substr($this->value_confirmed, -3));
			}

			if ($this->isNotEmptyOtherData()) {
				if ($this->isDaHetHanCCCDHienTai()) {
					$genMobileStatusAndCan['status'] = 'HET_HAN';
					$genMobileStatusAndCan['status_name'] = vmsg('Mpos360ChungThuc_DaHetHan');
					$genMobileStatusAndCan['can'] = 'CAN_PHAI_CHUNG_THUC_CCCD';
				}
			}

			if ($this->status == Mpos360Enum::MPOS360_CHUNG_THUC_STT_TU_CHOI_XAC_NHAN) {
				$genMobileStatusAndCan['status'] = 'HET_HAN';
				$genMobileStatusAndCan['status_name'] = 'Sai CCCD chứng thực';
			}
		};

	
		return [
			'id' => $this->id,
			'key' => $this->key_code,
			'name' => $name,
			'value' => $maskValue,
			'status' => $genMobileStatusAndCan['status'],
			'status_name' => $genMobileStatusAndCan['status_name'],
			'can' => $genMobileStatusAndCan['can'],
		];
	}

	public function genMobileStatusAndCan(Mpos360ChungThuc $mpos360ChungThuc)
	{
		$returnData = [
			'status' => 'CHUA_CHUNG_THUC',
			'status_name' => vmsg('Mpos360ChungThuc_ChuaChungThuc'),
			'can' => sprintf('CAN_PHAI_CHUNG_THUC_%s', $mpos360ChungThuc->key_code)
		];


		if ($mpos360ChungThuc->status == Mpos360Enum::MPOS360_CHUNG_THUC_STT_CHUA_XAC_NHAN) {
			return $returnData;
		}

		if ($mpos360ChungThuc->status == Mpos360Enum::MPOS360_CHUNG_THUC_STT_DANG_XAC_NHAN) {
			$returnData['status'] = 'CHO_CHUNG_THUC';
			$returnData['status_name'] = vmsg('Mpos360ChungThuc_ChoChungThuc');
			$returnData['can'] = 'CAN_CHO_CHUNG_THUC_CCCD';
			return $returnData;
		}

		if ($mpos360ChungThuc->status == Mpos360Enum::MPOS360_CHUNG_THUC_STT_DA_XAC_NHAN) {
			$returnData['status'] = 'DA_CHUNG_THUC';
			$returnData['status_name'] = vmsg('Mpos360ChungThuc_DaChungThuc');
			$returnData['can'] = 'CAN_CO_THE_TAO_YEU_CAU';
			return $returnData;
		}

		return $returnData;
	}

	public static function getChungThucCCCD(string $merchantId) {
		return Mpos360ChungThuc::query()->where('merchant_id', $merchantId)
																		->where('key_code', 'CCCD')
																		->where('status', Mpos360Enum::MPOS360_CHUNG_THUC_STT_DA_XAC_NHAN)
																		->first();
	}

	public function isKhopThongTinChungThuc(string $cccd) {
		$oldCCCD = '';
		
		if ($this->isTonTaiCCCDCu()) {
			$oldCCCD = $this->getSoCCCDCu();
		}

		$originalCCCD = $this->getOriginalCCCD();
		return $cccd == $oldCCCD || $cccd == $originalCCCD;
	}
} // End class
