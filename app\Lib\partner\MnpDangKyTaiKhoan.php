<?php

namespace App\Lib\partner;

class MnpDang<PERSON>yTaiKho<PERSON> extends MNP
{
	public $baseUrl;
	public $appId;
	public $authToken;
	public $commonUrl;

	public function __construct($authToken)
	{
		$this->commonUrl = env('API_PARTNER_MNP_COMMON_URL');
		$this->baseUrl = env('API_PARTNER_MNP_API_CUSTOMER');
		$this->appId = env('API_PARTNER_MNP_NOTIFY_APP_ID');
		$this->authToken = $authToken;
	}

	public function sendRequest($endpoint, $method = 'GET', $data = null)
	{
		$log = [
			'data' => $data,
		];

		$url = $this->baseUrl . $endpoint;
		$log['url'] = $url;

		$curl = curl_init();
		$headers = [
			'app-id: ' . $this->appId,
			'Content-Type: application/json',
			'Authorization: ' . $this->authToken
		];
		
		curl_setopt_array($curl, array(
			CURLOPT_URL => $url,
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 20,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => $method,
			CURLOPT_POSTFIELDS => json_encode($data),
			CURLOPT_HTTPHEADER => $headers,
		));

		$response = curl_exec($curl);
		$log['response'] = $response;

		mylog($log);
		$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
		if ($httpCode == 200) {
			return json_decode($response, true);
		}
		if ($httpCode > 200) {
			$res = @json_decode($response, true);
			if ($res) {
				return [
					"success" =>  $httpCode,
					"code" => "ERROR",
					"httpStatus" =>  "ERROR",
					"message" =>  isset($res['error']) ? $res['error'] : "LỖI KHÔNG XÁC ĐỊNH",
				];
			}
		}
		return [
			"success" =>  $httpCode,
			"code" => "ERROR",
			"httpStatus" =>  "ERROR",
			"message" =>  "LỖI KHÔNG XÁC ĐỊNH",
		];
	}
} // End class
