<?php

namespace App\Modules\TingBoxVAMC\Actions\Mpos360CheckSwitchModeBankAction;

use App\Exceptions\BusinessException;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\Mpos360CheckSwitchModeBankRequest;
use App\Modules\TingBoxVAMC\Actions\SendOtpSwitchModeBankAction\SubAction\KiemTraDieuKienChuyenModeSubAction;

class Mpos360CheckSwitchModeBankAction
{
	public function run(Mpos360CheckSwitchModeBankRequest $request)
	{
		$mcShopBank = MerchantShopBank::query()
			->with('merchantBank')
			->firstWhere([
				'merchant_id' => $request->json('data.merchantId'),
				'id' => $request->json('data.merchantShopBankId')
			]);

		if (!$mcShopBank) {
			throw new BusinessException('Không tìm thấy thông tin liên kết ngân hàng', 5009);
		}

		$r = app(KiemTraDieuKienChuyenModeSubAction::class)->run($mcShopBank);

		return [
			'canSwicthMode' => $r ? 'YES' : 'NO'
		];
	}
}
