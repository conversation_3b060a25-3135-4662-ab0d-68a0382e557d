<html lang="zxx">

<head>
	<!-- Required meta tags -->
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<title>{{ $tingBoxSerial }}</title>

	<!-- Style CSS -->
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
	<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
	<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
	<style type="text/css">
		body {
			font-family: "Be Vietnam Pro", serif;
			font-style: normal;
		}

		.box-tabs {}

		.box-tabs nav {
			background: #E2E2E2;
			border-radius: 50px;
		}

		.box-tabs nav .nav-tabs {
			border-bottom: 0;
			padding: 4px;
		}

		.box-tabs nav .nav-tabs .nav-link {
			width: 50%;
			border-radius: 26px;
			color: #404041;
		}

		.box-tabs nav .nav-tabs .nav-link.active {
			color: #73AE4A;
		}

		.box-content {
			padding: 16px;
			font-size: 14px;
		}

		li::marker {
			font-weight: bold;
		}

		.list-number {
			margin-left: 16px;
		}

		.list-number li {
			list-style: decimal;
			position: relative;
			margin-bottom: 10px;
		}

		.list-number li img {}

		.list-number li b {}

		.list-number li span.icon-list {
			position: absolute;
			left: -48px;
			top: 2px;
		}

		.list-number li i {}

		.list-number li i b {}

		.list-number li ul.list-number-small {
			position: relative;
			padding-left: 0px;
			margin-left: -16px;
		}

		.list-number li ul.list-number-small li {
			list-style: none;
			margin-bottom: 0;
		}

		.list-number li ul.list-number-small li img {}

		.bc-footer {
			text-align: center;
			margin-top: 8px;
		}

		.bc-footer p {
			margin-bottom: 0;
			font-size: 12px;
		}

		.bc-footer label {
			font-size: 12px;
			color: #77A841;
			font-weight: 600;
		}

		.bc-footer label img {}

		.list-dot-small {
			padding-left: 16px;
		}

		.list-dot-small li {
			list-style: disc;
			margin-bottom: 0;
		}

		.list-dot-small li:first-child {
			list-style: none;
		}

		.box-head {
			margin-bottom: 20px;
			display: flex;
			align-items: center;
			padding: 16px;
		}

		.box-head>span {}

		.box-head p {
			margin-bottom: 0;
			margin-left: 10px;
			font-size: 16px;
			color: #404041;
		}

		.box-head p>b {
			font-size: 14px;
			color: #73AE4A;
			position: relative;
			margin-left: 14px;
		}

		.box-head p>b>span {
			position: absolute;
			left: -13px;
			top: 5px;
			display: inline-block;
			width: 9px;
			height: 9px;
			background: #73AE4A;
			border-radius: 50%;
		}
	</style>
</head>

<body>
	<div class="box-all">
		<div class="box-head">
			<span><img src="{{ assetTingBox('img/img-tingbox-v2.svg') }}"></span>
			<p>
				{{ $tingBoxSerial }}<br />
				<b style="color: {{ $tingBoxBgColor }}"><span style="background-color: {{ $tingBoxBgColor }}"></span>{{ $tingBoxStatus }}</b>
			</p>
		</div>
		<div class="box-tabs">
			<nav>
				<div class="nav nav-tabs" id="nav-tab">
					<button class="nav-link active" id="nav-home-tab" data-bs-toggle="tab" data-bs-target="#nav-home" type="button">Kết nối Wifi</button>
					<button class="nav-link" id="nav-profile-tab" data-bs-toggle="tab" data-bs-target="#nav-profile" type="button">Kết nối 4G</button>
				</div>
			</nav>
			<div class="tab-content" id="nav-tabContent">
				<div class="tab-pane fade show active" id="nav-home">
					<div class="box-content">
						<ul class="list-number">
							<li>
								<span class="icon-list"><img src="{{ assetTingBox('img/power.svg') }}"></span> <b class="mb-1 d-inline-block">Bật nguồn và chuyển chế độ truy cập</b>
								<ul class="list-number-small">
									<li><span class="lns-number"><b>1.1.</b></span>Giữ <b>Nút</b> <img src="{{ assetTingBox('img/btn-power.svg') }}"> 5s để <b>Bật nguồn</b> thiết bị</li>
									<li><span class="lns-number"><b>1.2.</b></span>Giữ <b>Nút</b> <img src="{{ assetTingBox('img/btn-review.svg') }}"> 5s để chuyển sang chế độ <b>Điểm truy cập</b></li>
								</ul>
								<i>* Nếu thiết bị đang ở chế độ <b>Mạng di động</b>, giữ <b>Nút</b> <img src="{{ assetTingBox('img/btn-plus.svg') }}"> 5s để chuyển sang <b>Chế độ Wifi</b> và làm lại bước 1.2</i>
							</li>
							<li>
								<span class="icon-list"><img src="{{ assetTingBox('img/wifi.svg') }}"></span> <b class="mb-1 d-inline-block">Kết nối điện thoại với Wifi TingBox</b>
								<div class="d-flex align-items-top ">
									<ul class="list-number-small">
										<li><span class="lns-number"><b>2.1</b></span>Bật Camera điện thoại, quét mã QR bên cạnh để <b>kết nối điện thoại với Wifi của TingBox</b></li>
										<li><span class="lns-number"><b>2.2</b></span>Vào Cài đặt > Wifi để kiểm tra đã kết nối với mạng TingBox chưa?</li>
									</ul>
									<span class="d-inline-block ms-2"><img src="{{ assetTingBox('img/qr-wifi.svg') }}"></span>
								</div>
							</li>
							<li>
								<span class="icon-list"><img src="{{ assetTingBox('img/conect.svg') }}"></span> <b class="mb-1 d-inline-block">Kết nối Wifi cho TingBox</b>
								<div class="d-flex align-items-top " style="margin-left: -16px;">
									<span class="d-inline-block me-2"><img src="{{ assetTingBox('img/qr-conect.svg') }}"></span>
									<ul class="list-number-small ms-0">
										<li>
											<span class="lns-number"><b>3.1</b></span>
											Khi điện thoại đã kết nối với Wifi TingBox, tiếp tục dùng Camera điện thoại <b>quét mã QR bên cạnh</b><br />
											<i>Hoặc truy cập <a href="">http://192.168.1.1</a> bằng trình duyệt điện thoại</i>
										</li>
										<li>
											<span class="lns-number"><b>3.2</b></span>
											Chọn Wifi và nhập mật khẩu, nhấn Xác nhận để kết nối Wifi cho TingBox
										</li>
									</ul>

								</div>
							</li>
						</ul>
						<div class="bc-footer">
							<p>Kết nối mạng thành công thiết bị sẽ phát thông báo</p>
							<label><img src="{{ assetTingBox('img/icon-tingbox.svg') }}"> TING! Kết nối máy chủ thành công!</label>
						</div>
					</div>
				</div>
				<div class="tab-pane fade" id="nav-profile">
					<div class="box-content">
						<ul class="list-number">
							<li>
								<span class="icon-list"><img src="{{ assetTingBox('img/power.svg') }}"></span> <b class="mb-1 d-inline-block">Bật nguồn và chuyển chế độ kết nối mạng di động</b>
								<ul class="list-number-small">
									<li><span class="lns-number"><b>1.1.</b></span>Giữ <b>Nút</b> <img src="{{ assetTingBox('img/btn-power.svg') }}"> 5s để <b>Bật nguồn</b> thiết bị</li>
									<li><span class="lns-number"><b>1.2.</b></span>Giữ <b>Nút</b> <img src="{{ assetTingBox('img/btn-plus.svg') }}"> 5s để chuyển sang chế độ kết nối <b>Mạng di động</b></li>
								</ul>
							</li>
							<li>
								<span class="icon-list"><img src="{{ assetTingBox('img/wifi.svg') }}"></span> <b class="mb-1 d-inline-block">Lắp thẻ SIM vào thiết bị</b>
								<div class="d-flex align-items-top ">
									<span class="d-inline-block me-2"><img src="{{ assetTingBox('img/img-sim2.svg') }}"></span>
									<ul class="list-dot-small">
										<li>* Lưu ý:</li>
										<li>Lắp phần cắt vát của thẻ <b>SIM</b> theo đúng chiều trên hình</li>
										<li>Lắp mặt chíp của <b>SIM xuống dưới</b></li>
									</ul>

								</div>
							</li>
							<li>
								<span class="icon-list"><img src="{{ assetTingBox('img/conect.svg') }}"></span> <b class="mb-1 d-inline-block">Khởi động lại thiết bị để hoàn tất</b>
								<p>Giữ Nút <img src="{{ assetTingBox('img/btn-power.svg') }}"> 5s để Khởi động lại thiết bị. Kết nối mạng di động 4G thành công, thiết bị sẽ phát loa thông báo.</p>
							</li>
						</ul>
						<div class="bc-footer">
							<!-- <p>Kết nối mạng thành công thiết bị sẽ phát thông báo</p> -->
							<label><img src="{{ assetTingBox('img/icon-tingbox.svg') }}"> TING! Kết nối máy chủ thành công!</label>
						</div>
					</div>
				</div>
			</div>
		</div>

	</div>

</body>

</html>