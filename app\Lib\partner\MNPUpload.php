<?php

namespace App\Lib\partner;

use App\Lib\Helper;
use App\Lib\TelegramAlertWarning;
use Illuminate\Http\UploadedFile;

class MNPUpload extends MNP
{
	public array $log = [];
	/**
	 * array:3 [
			"status" => true
			"data" => array:1 [
				0 => "https://nextpay-crm.s3-ap-southeast-1.amazonaws.com/user/mpos360/file/66c46a1b80f78268096fcd20poster_IrzmP1VgEokCK2yS.jpeg"
			]
			"message" => "Thành công"
		]
	 */
	public function uploadFileLaravel(array $files, $token, $folderName = 'user/mpos360/file', $isImage = false)
	{
		$startTime = microtime(true);
		$this->log['StartTime'] = $startTime;
		$this->log['LogUploadS3CaiTien'] = 'YES';

		$url = $this->baseUrlUploadDocuments;

		if ($isImage) {
			$url = $this->baseUrlUpload;
		}

		$curl = curl_init();

		$headers = [
			'Authorization: ' . $token,
			'Content-Type: multipart/form-data'
		];

		$postFields = [
			'folderName' => $folderName,
		];

		foreach ($files as $index => $file) {
			$fName = $file->getClientOriginalName();
			$extFromClient = $file->getClientOriginalExtension();
			$ext = $file->guessExtension();
			if (empty($extFromClient)) {
				$fName = $fName . '.' . $ext;
			}

			$postFields["files[$index]"] = new \CURLFile($file->getPathname(), $file->getMimeType(), $fName);
		}

		curl_setopt_array($curl, array(
			CURLOPT_URL => $url,
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 20,
			CURLOPT_CONNECTTIMEOUT => 20,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'POST',
			CURLOPT_POSTFIELDS => $postFields,
			CURLOPT_HTTPHEADER => $headers,
		));

		try {
			$response = curl_exec($curl);
			$result = json_decode($response, true);

			$endTime = microtime(true);
			$this->log['EndTime'] = $endTime;
			$this->log['Duration'] = $endTime - $startTime;

			if (!empty($result['result']) && $result['code'] == 1000) {
				$this->log['LogThanhCong'] = $result;
				mylog($this->log);
				return [
					'status' => true,
					'data' => $result['data'],
					'message' => $result['message'] ?? 'Thanh cong'
				];
			}

			$this->log['LogThatBai'] = $result;
			mylog($this->log);
			return $this->returnUploadError();
		} catch (\Throwable $th) {
			TelegramAlertWarning::sendMessage(Helper::traceError($th));
			$this->log['LogUploadLoi'] = $th->getMessage();
			mylog($this->log);
			return $this->returnUploadError();
		} finally {
			curl_close($curl);
		}
	}

	public function returnUploadError($httpCode=500) {
		$returnData = [
			'status' => false,
			'data' => null,
			'message' => 'Upload failed. HTTP Code: ' . $httpCode
		];

		return $returnData;
	}
} // End class
