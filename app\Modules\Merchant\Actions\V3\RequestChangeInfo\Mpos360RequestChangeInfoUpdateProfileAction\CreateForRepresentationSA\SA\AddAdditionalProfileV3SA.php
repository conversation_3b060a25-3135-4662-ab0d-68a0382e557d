<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForRepresentationSA\SA;

use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;


class AddAdditionalProfileV3SA
{
	public function run(Mpos360MerchantRequest $mpos360McRequest)
	{
		$additionalProfiles = [];

		// Người đại diện mới <Công ty> - Chức vụ
		$chucVu_DoiNguoiDaiDienMoi = [
			'profileKey' => 'representPosition',
			'value' => '',
			'label' => vmsg('Chức vụ của người đại diện mới'),
			'other_data' => [
				'list' => [
					['key' => 'giam_doc', 'label' => vmsg('Giám đốc'), 'value' => 'Giám đốc', 'slot' => []],
					['key' => 'pho_giam_doc', 'label' => vmsg('Phó giám đốc'), 'value' => 'Phó giám đốc', 'slot' => []],
					['key' => 'truong_phong', 'label' => vmsg('Trưởng phòng'), 'value' => 'Trưởng phòng', 'slot' => []],
					['key' => 'khac', 'label' => vmsg('Khác'), 'value' => 'Khác', 'slot' => []],
				]
			],
		];

		$moiQuanHe_DoiNguoiDaiDienMoi = [
			'profileKey' => 'representMutualRelation',
			'value' => '',
			'label' => 'Mối quan hệ giữa hai bên',
			'other_data' => [
				'list' => [
					['key' => 'bo_me_con', 'label' => vmsg('Bố/Mẹ - Con'), 'value' => 'Bố/Mẹ - Con', 'slot' => []],
					['key' => 'anh_chi_em', 'label' => vmsg('Anh - Chị - Em'), 'value' => 'Anh - Chị - Em', 'slot' => []],
					['key' => 'vo_chong', 'label' => vmsg('Vợ - Chồng'), 'value' => 'Vợ - Chồng', 'slot' => []],
					['key' => 'khac', 'label' => vmsg('Khác'), 'value' => 'Khác', 'slot' => []],
				]
			],
		];

		$additionalProfiles = collect([]);
		if ($mpos360McRequest->isYcDoiNguoiDaiDienMoiHKDV3()) {
			// add thêm các trường cần show ra form đổi người đại diện mới

			$additionalProfiles->push($chucVu_DoiNguoiDaiDienMoi)
				->push($moiQuanHe_DoiNguoiDaiDienMoi)
				->push([
					'profileKey' => 'identificationDocument',
					'value' => '',
					'label' => vmsg('Chứng từ đính kèm'),
					'other_data' => [
						'list' => [
							[
								'key' => 'anh_giay_uy_quyen',
								'value' => 'anh_giay_uy_quyen',
								'label' => vmsg('Ảnh giấy ủy quyền được công chứng tại cơ quan có thẩm quyền'),
								'slot' => [
									[
										'substituteCertUrls' => 'substituteCertUrls',
										'label' => vmsg('Ảnh 1')
									],
								],
							],

							[
								'key' => 'anh_giay_khai_sinh',
								'value' => 'anh_giay_khai_sinh',
								'label' => vmsg('Ảnh giấy khai sinh'),
								'slot' => [
									[
										'profileKey' => 'substituteCertUrls',
										'label' => vmsg('Ảnh 1')
									],
								]
							],

							[
								'key' => 'anh_giay_dang_ky_ket_hon',
								'value' => 'anh_giay_dang_ky_ket_hon',
								'label' => vmsg('Ảnh giấy đăng ký kết hôn'),
								'slot' => [
									[
										'profileKey' => 'substituteCertUrls',
										'label' => 'Ảnh 1'
									],
								]
							],

							[
								'key' => 'anh_so_ho_khau',
								'value' => 'anh_so_ho_khau',
								'label' => vmsg('Ảnh tất cả các trang Sổ hộ khẩu giấy'),
								'slot' => [
									[
										'profileKey' => 'substituteCertUrls',
										'label' => vmsg('Ảnh 1')
									],
								]
							],

							[
								'key' => 'anh_man_hinhg_app_vneid',
								'value' => 'anh_man_hinhg_app_vneid',
								'label' => vmsg('Video quay màn hình ứng dụng VNeID từ khi đăng nhập đến các trang thể hiện mối quan hệ giữa người ủy quyền và người được ủy quyền'),
								'slot' => [
									[
										'profileKey' => 'substituteCertUrls',
										'label' => vmsg('Ảnh 1')
									],
								]
							],

							[
								'key' => 'cccd_hai_mat_cua_nguoi_dai_dien_moi',
								'value' => 'cccd_hai_mat_cua_nguoi_dai_dien_moi',
								'label' => '*CCCD 2 mặt của người đại diện mới (bản gốc, rõ nét, có đầy đủ 4 góc, không dùng bản scan, PDF): Bắt buộc  ',
								'slot' => [
									[
										'profileKey' => 'substituteCertUrls',
										'label' => vmsg('Ảnh 1')
									],
								]
							]
						]
					]
				])
				->values()
				->all();
		}

		if ($mpos360McRequest->isYcDoiNguoiDaiDienMoiDoanhNghiepV3()) {
			$additionalProfiles->push($chucVu_DoiNguoiDaiDienMoi)
				->push($moiQuanHe_DoiNguoiDaiDienMoi)
				->push([
					'profileKey' => 'identificationDocument',
					'value' => '',
					'label' => vmsg('Chứng từ đính kèm'),
					'other_data' => [
						'list' => [
							[
								'key' => 'anh_giay_uy_quyen',
								'value' => 'anh_giay_uy_quyen',
								'label' => vmsg('Ảnh Giấy ủy quyền có dấu và chữ ký của công ty'),
								'slot' => [
									[
										'substituteCertUrls' => 'substituteCertUrls',
										'label' => vmsg('Ảnh 1')
									],
								],
							],

							[
								'key' => 'cccd_hai_mat_cua_nguoi_dai_dien_moi',
								'value' => 'cccd_hai_mat_cua_nguoi_dai_dien_moi',
								'label' => '*CCCD 2 mặt của người đại diện mới (bản gốc, rõ nét, có đầy đủ 4 góc, không dùng bản scan, PDF): Bắt buộc  ',
								'slot' => [
									[
										'profileKey' => 'substituteCertUrls',
										'label' => vmsg('Ảnh 1')
									],
								]
							]
						]
					]
				]);
		}

		if ($mpos360McRequest->isYcDoiTknhCaNhanMaHkdUyQuyen()) {
			$additionalProfiles = collect([])
				->push([
					'profileKey' => 'positionAuthBank',
					'value' => '',
					'type' => 'INPUT',
					'label' => vmsg('Vai trò/Vị trí của người được ủy quyền'),
					'other_data' => (object) []
				])->push([
					'profileKey' => 'bankMutualRelation',
					'value' => '',
					'type' => 'DROPDOWN',
					'label' => vmsg('Mối quan hệ giữa hai bên'),
					'other_data' => [
						'list' => [
							['key' => 'bo_me_con', 'label' => vmsg('Bố/Mẹ - Con'), 'value' => 'Bố/Mẹ - Con', 'slot' => []],
							['key' => 'anh_chi_em', 'label' => vmsg('Anh - Chị - Em'), 'value' => 'Anh - Chị - Em', 'slot' => []],
							['key' => 'vo_chong', 'label' => vmsg('Vợ - Chồng'), 'value' => 'Vợ - Chồng', 'slot' => []],
							['key' => 'khac', 'label' => vmsg('Khác'), 'value' => 'Khác', 'slot' => []],
						]
					]
				])
				->push([
					'profileKey' => 'identificationDocument',
					'value' => '',
					'label' => vmsg('Chứng từ đính kèm'),
					'other_data' => [
						'list' => [
							[
								'key' => 'anh_giay_uy_quyen',
								'value' => 'anh_giay_uy_quyen',
								'label' => vmsg('Ảnh giấy ủy quyền được công chứng tại cơ quan có thẩm quyền'),
								'slot' => [
									[
										'substituteCertUrls' => 'substituteCertUrls',
										'label' => vmsg('Ảnh 1')
									],
								],
							],

							[
								'key' => 'anh_giay_khai_sinh',
								'value' => 'anh_giay_khai_sinh',
								'label' => vmsg('Ảnh giấy khai sinh'),
								'slot' => [
									[
										'profileKey' => 'substituteCertUrls',
										'label' => vmsg('Ảnh 1')
									],
								]
							],

							[
								'key' => 'anh_giay_dang_ky_ket_hon',
								'value' => 'anh_giay_dang_ky_ket_hon',
								'label' => vmsg('Ảnh giấy đăng ký kết hôn'),
								'slot' => [
									[
										'profileKey' => 'substituteCertUrls',
										'label' => vmsg('Ảnh 1')
									],
								]
							],

							[
								'key' => 'anh_so_ho_khau',
								'value' => 'anh_so_ho_khau',
								'label' => vmsg('Ảnh tất cả các trang Sổ hộ khẩu giấy'),
								'slot' => [
									[
										'profileKey' => 'substituteCertUrls',
										'label' => vmsg('Ảnh 1')
									],
								]
							],

							[
								'key' => 'anh_man_hinhg_app_vneid',
								'value' => 'anh_man_hinhg_app_vneid',
								'label' => vmsg('Video quay màn hình ứng dụng VNeID từ khi đăng nhập đến các trang thể hiện mối quan hệ giữa người ủy quyền và người được ủy quyền'),
								'slot' => [
									[
										'profileKey' => 'substituteCertUrls',
										'label' => vmsg('Ảnh 1')
									],
								]
							],

							[
								'key' => 'cccd_2mat_sau_cua_ca_nhan_duoc_uy_quyen',
								'value' => 'cccd_2mat_sau_cua_ca_nhan_duoc_uy_quyen',
								'label' => '*CCCD mặt trước và mặt sau của cá nhân được ủy quyền (bản gốc, rõ nét, có đầy đủ 4 góc, không dùng bản scan, PDF). Bắt buộc',
								'slot' => [
									[
										'profileKey' => 'substituteCertUrls',
										'label' => 'Ảnh CCCD mặt trước của cá nhân được ủy quyền'
									],

									[
										'profileKey' => 'substituteCertUrls',
										'label' => 'Ảnh CCCD mặt sau của cá nhân được ủy quyền'
									],
								]
							]
						]
					]
						]);
		}

		if ($mpos360McRequest->isYcDoiTknhCaNhanMaDoanhNghiepUyQuyen()) {
			$additionalProfiles = collect([])
				->push([
					'profileKey' => 'positionAuthBank',
					'value' => '',
					'type' => 'INPUT',
					'label' => vmsg('Vai trò/Vị trí của người được ủy quyền'),
					'other_data' => (object) []
				])->push([
					'profileKey' => 'bankMutualRelation',
					'value' => '',
					'type' => 'DROPDOWN',
					'label' => vmsg('Mối quan hệ giữa hai bên'),
					'other_data' => [
						'list' => [
							['key' => 'bo_me_con', 'label' => vmsg('Bố/Mẹ - Con'), 'value' => 'Bố/Mẹ - Con'],
							['key' => 'anh_chi_em', 'label' => vmsg('Anh - Chị - Em'), 'value' => 'Anh - Chị - Em'],
							['key' => 'vo_chong', 'label' => vmsg('Vợ - Chồng'), 'value' => 'Vợ - Chồng'],
							['key' => 'khac', 'label' => vmsg('Khác'), 'value' => 'Khác'],
						]
					]
				])
				->push([
					'profileKey' => 'identificationDocument',
					'value' => '',
					'label' => vmsg('Chứng từ đính kèm'),
					'other_data' => [
						'list' => [
							[
								'key' => 'anh_giay_uy_quyen',
								'value' => 'anh_giay_uy_quyen',
								'label' => vmsg('Ảnh Giấy ủy quyền có dấu và chữ ký của công ty'),
								'slot' => [
									[
										'substituteCertUrls' => 'substituteCertUrls',
										'label' => vmsg('Ảnh 1')
									],
								],
							],

							[
								'key' => 'cccd_2mat_sau_cua_ca_nhan_duoc_uy_quyen',
								'value' => 'cccd_2mat_sau_cua_ca_nhan_duoc_uy_quyen',
								'label' => '*CCCD mặt trước và mặt sau của cá nhân được ủy quyền (bản gốc, rõ nét, có đầy đủ 4 góc, không dùng bản scan, PDF). Bắt buộc',
								'slot' => [
									[
										'profileKey' => 'substituteCertUrls',
										'label' => 'Ảnh CCCD mặt trước của cá nhân được ủy quyền'
									],

									[
										'profileKey' => 'substituteCertUrls',
										'label' => 'Ảnh CCCD mặt sau của cá nhân được ủy quyền'
									],
								]
							]
						]
					]
				]);
		}
		
		
		$dataRequest = $mpos360McRequest->getDataRequestV3();
		$fieldChucVuViTri = Arr::only($dataRequest[0]['profiles'], ['representPosition', 'representMutualRelation', 'positionAuthBank', 'bankMutualRelation']);
		
		$mpos360McRequest->load('mpos360McSupplementNew');
			$additionalProfiles->transform(function ($item) use ($fieldChucVuViTri, $mpos360McRequest) {
				if ($mpos360McRequest->mpos360McSupplementNew) {
					if (isset($fieldChucVuViTri[$item['profileKey']])) {
						$item['value'] = $fieldChucVuViTri[$item['profileKey']];
					}elseif ($item['profileKey'] != 'identificationDocument') {
						$item['value'] = 'Khác';
					}
				}
				return $item;
			});
		return $additionalProfiles;
	} // End method
} // End class
