<?php

namespace App\Modules\Merchant\DTOs\Authen\Mpos360Auth;

class LoginMposSuccessDto
{
	public string $mobileUserToken;

	public int $merchantId;

	public string $username;

	public string $cid;

	public string $merchantEmail;

	public string $merchantName;

	public function __construct(
		string $mobileUserToken,
		int $merchantId,
		string $username,
		string $merchantEmail = '',
		string $merchantName = ''
	) {
		$this->mobileUserToken = $mobileUserToken;
		$this->merchantId = $merchantId;
		$this->username = $username;
		$this->merchantEmail = $merchantEmail;

		if (filter_var($this->username, FILTER_VALIDATE_EMAIL)) {
			$this->merchantEmail = $this->username;
		}
		
		$this->merchantName = $merchantName;
	}

	public function toJson(): string
	{
		return json_encode([
			'merchantId' => $this->merchantId,
			'username' => $this->username,
			'merchantEmail' => $this->merchantEmail,
			'merchantName' => $this->merchantName,
		]);
	}
}
