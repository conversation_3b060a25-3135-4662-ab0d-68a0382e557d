<?php

namespace App\Modules\Merchant\Requests\RequestChangeInfo;

use App\Modules\Merchant\Requests\MerchantRequest;
use Illuminate\Validation\Rule;

class Mpos360RequestChangeInfoCheckEmailAndMobileRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.new_email' => ['required', 'string', 'email'],
			'data.new_mobile' => ['required', 'string'],
		];
	}
} // End class
