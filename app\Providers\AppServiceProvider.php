<?php

namespace App\Providers;

use App\Lib\Logs;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\ServiceProvider;


class AppServiceProvider extends ServiceProvider
{
	/**
	 * Register any application services.
	 *
	 * @return void
	 */
	public function register()
	{
		$this->app->singleton('mylog', function () {
			return new Logs();
		});
	}

	/**
	 * Bootstrap any application services.
	 *
	 * @return void
	 */
	public function boot()
	{
		require_once app_path('Lib/Support.php');
		require_once app_path('Lib/Security.php');
		
		if (env('INFRASTRUCTURE') == 'vps') {
			$logDir = env('LOG_PATH');
			$logFile = $logDir . DIRECTORY_SEPARATOR . env('APP_SERVICE_NAME') . '-' . date('Y-m-d') . '.log';
			if (!file_exists($logFile)) {
				touch($logFile);
			}
		}
	}
}
