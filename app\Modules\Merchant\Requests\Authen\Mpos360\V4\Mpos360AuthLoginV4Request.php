<?php

namespace App\Modules\Merchant\Requests\Authen\Mpos360\V4;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class Mpos360AuthLoginV4Request extends FormRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.email' => ['required', 'string', 'email', 'max:255'],
			'data.password' => ['required', 'string', 'max:36'],
			'data.os' => ['required', 'string', Rule::in(['ANDROID', 'IOS'])],
			'data.deviceToken' => ['present', 'string'],
			'data.fcmToken' => ['present', 'string']
		];
	}

	protected function passedValidation()
	{
		$params  = $this->all();
		$os = $params['data']['os'];
		$params['data']['os'] = ($os == 'IOS' ? 'iOS' : 'Android');
		$this->merge($params);
	}
	public function messages()
	{
		return [
			'data.email.required' => vmsg('EmailDangNhapLaBatBuoc'), 
			'data.email.string' => vmsg('EmailDangNhapPhaiLaKieuChuoi'), 
			'data.email.email' => vmsg('EmailDangNhapPhaiLa1Email'), 
			'data.email.max' => vmsg('EmailDangNhapKhongDuocVuotQua255KyTu'), 

			'data.password.required' => vmsg('MatKhauDangNhapLaBatBuoc'),
			'data.password.string' => vmsg('MatKhauDangNhapPhaiLaKieuChuoi'),
			'data.password.max' => vmsg('MatKhauDangNhapKhongDuocVuotQua36KyTu'),


			'data.os.required' => vmsg('HeDieuHanhThietBiLaBatBuoc'),
			'data.os.string' => vmsg('HeDieuHanhThietBiPhaiLaKieuChuoi'),
			'data.os.in' => vmsg('HeDieuHanhThietBiPhaiLaAndroidHoacIos'),
			
			'data.token.required' => vmsg('TokenThietBiLaBatBuoc'),
			'data.token.string' => vmsg('TokenThietBiPhaiLaKieuChuoi'),
			'data.token.max' => vmsg('ToKenThietBiKhongVuotQua255KyTu'),

		];
	}

	public function isPasswordEqual123456(): bool {
		return $this->json('data.password') == '123456';
	}
} // End class
