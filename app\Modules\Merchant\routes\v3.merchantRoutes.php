<?php

use Illuminate\Support\Facades\Route;
use App\Http\Middleware\MakeSureThatRequestIsJsonMiddleware;
use App\Modules\Merchant\Controllers\MerchantSignature\Mpos360MerchantSignatureController;
use App\Modules\Merchant\Controllers\V3\RequestChangeInfo\Mpos360RequestChangeInfoController;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoMarkAsDoneAction\Mpos360RequestChangeInfoMarkAsDoneAction;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoSaveChangesAction\Mpos360RequestChangeInfoSaveChangesAction;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestGetAdditionalProfilesAction\Mpos360RequestGetAdditionalProfilesAction;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\Mpos360RequestChangeInfoUpdateProfileAction;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoAttachSignatureV3Action\Mpos360RequestChangeInfoAttachSignatureV3Action;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\Mpos360RequestChangeInfoAdditionalAttachmentAction;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessV3Action\Mpos360RequestChangeInfoAllOtpProfileSuccessV3Action;

Route::group([
	'prefix' => 'v3', 
	'middleware' => [MakeSureThatRequestIsJsonMiddleware::class]
], function () {
	// Danh sách hồ sơ các hồ sơ của tôi
	Route::any('/Mpos360RequestChangeInfoListProfile', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoListProfile',
		'as' => 'Mpos360RequestChangeInfoListProfileAction'
	])->middleware('validateHash:email');
	
	// Kiểm tra STK 
	Route::any('/Mpos360RequestChangeInfoCheckProfileBanking', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoCheckProfileBanking',
		'as' => 'Mpos360RequestChangeInfoCheckProfileBankingAction'
	])->middleware('validateHash:bankId|bankName|accountNo|holderName');

	// Validate Email - SĐT là hợp lệ
	Route::any('/Mpos360RequestChangeInfoValidateEmailAndMobile', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoValidateEmailAndMobile',
		'as' => 'Mpos360RequestChangeInfoValidateEmailAndMobile'
	])->middleware('validateHash:new_email|new_mobile');

	// Tạo yêu cầu
	Route::any('/Mpos360RequestChangeInfoUpdateProfile', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoUpdateProfile',
		'as' => Mpos360RequestChangeInfoUpdateProfileAction::class
	])->middleware('validateHash:email|choice');

	// Lưu thay đổi
	Route::any('/Mpos360RequestChangeInfoSaveChanges', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoSaveChanges',
		'as' => Mpos360RequestChangeInfoSaveChangesAction::class
	])->middleware('validateHash:id|is_change_email_or_mobile');

	// Get additional profiles đính kèm chứng từ
	Route::any('/Mpos360RequestGetAdditionalProfiles', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestGetAdditionalProfiles',
		'as' => Mpos360RequestGetAdditionalProfilesAction::class
	])->middleware('validateHash:id');

	// Add thêm tài liệu đính kèm
	Route::any('/Mpos360RequestChangeInfoAdditionalAttachment', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoAdditionalAttachment',
		'as' => Mpos360RequestChangeInfoAdditionalAttachmentAction::class
	])->middleware('validateHash:email|id');

	// Đánh dấu markDone v3
	Route::any('/Mpos360RequestChangeInfoAllOtpProfileSuccess', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoAllOtpProfileSuccess',
		'as' => Mpos360RequestChangeInfoAllOtpProfileSuccessV3Action::class
	])->middleware('validateHash:id');

	// Mark Done 
	Route::any('/Mpos360RequestChangeInfoMarkAsDone', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoMarkAsDone',
		'as' => Mpos360RequestChangeInfoMarkAsDoneAction::class
	])->middleware('validateHash:id');

	// Chi tiết yc v3
	Route::any('/Mpos360RequestChangeInfoDetail', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoDetail',
		'as' => 'Mpos360RequestChangeInfoDetailV3Action'
	])->middleware('validateHash:id');
/*---------------------------Chữ ký---------------------------*/
	// Danh sách chữ ký của tôi - Tái sử dụng v1
	Route::any('/Mpos360MerchantSignatureList', [
		'uses' => Mpos360MerchantSignatureController::class . '@Mpos360MerchantSignatureList',
		'as' => 'Mpos360MerchantSignatureListAction'
	])->middleware('validateHash:email|status');

	// Tạo chữ ký - Tái sử dụng v1
	Route::any('/Mpos360MerchantSignatureCreate', [
		'uses' => Mpos360MerchantSignatureController::class . '@Mpos360MerchantSignatureCreate',
		'as' => 'Mpos360MerchantSignatureCreateAction'
	])->middleware('validateHash:email|signature_url|status');

	// nhét chữ ký vào yêu cầu - Tái sử dụng
	Route::any('/Mpos360RequestChangeInfoAttachSignature', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoAttachSignature',
		'as' => Mpos360RequestChangeInfoAttachSignatureV3Action::class
	])->middleware('validateHash:signature_id|request_id');

	// pick phương thức ký - Tái sử dụng
	Route::any('/Mpos360RequestChangeInfoPickSignMethod', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoPickSignMethod',
		'as' => 'Mpos360RequestChangeInfoPickSignMethodAction'
	])->middleware('validateHash:id');

	// finish luồng chọn ký (mc phải upload hoặc xác nhận ok)
	Route::any('/Mpos360RequestChangeInfoUploadPhuLucKy', [
		'uses' => Mpos360RequestChangeInfoController::class . '@Mpos360RequestChangeInfoUploadPhuLucKy',
		'as' => 'Mpos360RequestChangeInfoUploadPhuLucKyAction'
	])->middleware('validateHash:id|signature_url');
});



