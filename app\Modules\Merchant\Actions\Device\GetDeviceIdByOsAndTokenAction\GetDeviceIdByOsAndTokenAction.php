<?php

namespace App\Modules\Merchant\Actions\Device\GetDeviceIdByOsAndTokenAction;

use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Device;

class GetDeviceIdByOsAndTokenAction
{
	public function run(string $deviceOS, string $deviceToken, int $mpos360UserId=0): Device
	{
		$device = Device::query()->firstOrCreate([
			'os' => $deviceOS,
			'token' => $deviceToken,
		], [
			'time_created' => now()->timestamp,
			'time_updated' => now()->timestamp,
			'user_id' => $mpos360UserId
		]);

		if (!$device) {
			throw new BusinessException(vmsg('DangNhapLoiKhongLayDuocThongTinThietBi'));
		}

		return $device; 
	}
}
