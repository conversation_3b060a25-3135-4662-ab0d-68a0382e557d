<?php

namespace App\Http\Middleware;

use App\Lib\Helper;
use Closure;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class LoggingMiddleware
{
	public $timeStart;

	protected $excepts = [
		'healthcheck/liveness',
		'healthcheck/readiness',
		
		// 'Mpos360AuthLogin', 
		'Mpos360QtsGetConfig',
		'Mpos360TingBoxDeviceHuongDanSuDung',
		'Mpos360TingBoxDeviceHuongDanLayMaTb',
		'Mpos360HuongDanQuetMaQRNhanTien',
		'Mpos360FormDangKyTaiKhoan',
		'Mpos360CronRemoveOldSession',
		'dang-ky-thanh-cong',
		'Mpos360GetSetting',
		'Mpos360GetHomePageInfoDiemBan',
		'Mpos360GetBioMetric',
		'Mpos360DeviceGetTypeReceiverTingTing',
		'Mpos360GetFormTingBoxStep1'
	];

	/**
	 * Handle an incoming request.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  \Closure  $next
	 * @return mixed
	 */
	public function handle($request, Closure $next)
	{
		app()->setLocale($request->json('lang', $request->input('lang', 'vi')));

		if (Helper::isLocal()) {
			DB::enableQueryLog();
			DB::connection('mpos360_data')->enableQueryLog();
		}
		
		// Rút gọn log
		$requestParams = $request->all();
		if (isset($requestParams['api_key']) && strlen($requestParams['api_key']) > 36) {
			$requestParams['api_key'] = Str::limit($requestParams['api_key'], 40);
		}

		if (isset($requestParams['checksum'])) {
			unset($requestParams['checksum']);
		}
		

		app('mylog')->concatData([
			'path' => $request->path(),
			'uri' => $request->getRequestUri(),
			'request' => $requestParams,
		]);
		return $next($request);
	}

	public function terminate($request, $response)
	{
		app('mylog')->concatData(['response' => $response]);

		app('mylog')->concatData(['timing_second' => microtime(true) - LARAVEL_START]);

		if ($this->isNeedWriteLogForRouting($request)) {
			app('mylog')->logging();
		}
	}

	public function isNeedWriteLogForRouting($request): bool
	{
		foreach ($this->excepts as $exceptPath) {
			$path = $request->fullUrl();
			if (Str::contains($path, $exceptPath)) {
				return false;
			}
		}

		return true;
	}
}
