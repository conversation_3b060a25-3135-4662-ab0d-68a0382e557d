<?php

namespace App\Modules\TingBoxVAMC\Actions\CronJobUpdateOrCreateBankAction;

use App\Exceptions\BusinessException;
use App\Lib\MnpOnboardNewMcHelper;
use App\Lib\TelegramAlert;
use App\Lib\TelegramAlertWarning;
use App\Modules\TingBoxVAMC\Actions\MerchantBank\LinkBankAction\SubAction\SaveBankTrungGianSubAction;
use App\Modules\TingBoxVAMC\Models\MerchantBank;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use App\Modules\TingBoxVAMC\Models\PlanEvent;

class CronJobUpdateOrCreateBankAction
{
	protected MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	/**
	 * Job này chỉ thực hiện gọi sang mnp chứ không được tạo hay update vào bất cứ bảng nào trong hệ thống
	 */
	public function run()
	{
		for ($i=1; $i<=30; $i++) {
			$r = $this->handle();
			if (isset($r) && $r == 'EMPTY') {
				break;
			}
		}
	}

	public function handle() {
		$planEvent = PlanEvent::query()->firstWhere(['status' => PlanEvent::STT_MOI_TAO, 'action' => 'UPDATEBANKMNP']);

		if (!$planEvent) {
			return 'EMPTY';
		}

		// Update len thanh dang xu ly
		$wasUpdated = PlanEvent::query()->where([
			'status' => PlanEvent::STT_MOI_TAO,
			'id' => $planEvent->id
		])->update(['status' => PlanEvent::STT_DANG_XU_LY]);

		if (!$wasUpdated) {
			throw new BusinessException('Lỗi không thể update thành Đang xử lý');
		}

		$detailMc = $this->mnpOnboardNewMcHelper->detailMcV2([
			'mposMcId' => $planEvent->merchant_id
		]);

		// Có bankId rồi thì không cần gọi sang mnp làm gì cho tốn công
		if (!empty($detailMc['data']['bankId'])) {
			$this->completed($planEvent, ['Đã có thông tin banking trung gian bên mnp']);
			return ['Đã xử lý bản ghi: ' => $planEvent->id];
		}

		$eventData = json_decode($planEvent->data, true);
		$mcBank = MerchantBank::query()->find($eventData['merchant_bank']['id']);
		$mcShopBank = MerchantShopBank::query()->find($eventData['merchant_shop_bank']['id']);

		if (!$mcBank || !$mcShopBank) {
			$this->completed($planEvent, ['Không tìm thấy bản ghi mcBank hoặc mcShopBank']);

			return ['Đã xử lý bản ghi: ' => $planEvent->id];
		}

		$r = app(SaveBankTrungGianSubAction::class)->run($mcShopBank, $mcBank);

		if (!empty($r['result'])) {
			$this->completed($planEvent, ['Gọi thành công banking']);
			return true;
		}

		TelegramAlertWarning::sendMessage('Xử lý add tknh trung gian lỗi. Id event là: ' . $planEvent->id);
		return;
	}

	public function completed(PlanEvent $planEvent, $response = [])
	{
		$wasComplete = PlanEvent::query()
			->where(['id' => $planEvent->id])
			->update([
				'status' => PlanEvent::STT_KHONG_XU_LY,
				'response' => json_encode($response, JSON_UNESCAPED_UNICODE)
			]);

		return $wasComplete;
	}
} // End class
