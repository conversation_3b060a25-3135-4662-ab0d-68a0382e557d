<?php

namespace App\Modules\Merchant\Model\Traits;

trait BoSungThongTinTrait
{
	/**
	 * Cần làm rõ: yêu cầu phải bổ sung thông tin không có nghĩa là app mpos360 sẽ hỗ trợ được
	 * vì có 1 số yc không có phần upload chứng từ
	 * 
	 * Những trường hợp phải bổ sung thông tin mà app không hỗ trợ được, thì bắt buộc MC phải liên hệ ngoài
	 */
	public function isYcPhaiBoSungThongTin()
	{
		return $this->mpos360McSupplementNew;
	}

	public function isYcDuocHoTroBoSungThongTin(): bool
	{
		if ($this->isYcPhaiBoSungThongTin()) {
			// Kiểm tra xem là có thuộc 1 trong các loại yc hỗ trợ đổi thông tin hay không?
			if ($this->isYcDoiNguoiDaiDienMoiHKDV3() || $this->isYcDoiNguoiDaiDienMoiDoanhNghiepV3()) {
				return true;
			}

			if ($this->isYcDoiTknhCaNhanMaHkdUyQuyen()) {
				return true;
			}

			if ($this->isYcDoiTknhCaNhanMaDoanhNghiepUyQuyen()) {
				return true;
			}
		}

		return false;
	}

	public function isCanBoSungThongTinNhungAppKhongHoTro(): bool {
		return $this->isYcPhaiBoSungThongTin() && !$this->isYcDuocHoTroBoSungThongTin();
	}

	public function getMessageBoSungThongTinNhungAppKhongHoTro(): string {
		return 'Bạn cần liên hệ nhân viên hỗ trợ để bổ sung thông tin cho yêu cầu này';
	}
} // End class