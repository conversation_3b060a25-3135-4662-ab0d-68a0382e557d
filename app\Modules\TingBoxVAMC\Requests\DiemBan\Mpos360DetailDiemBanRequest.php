<?php

namespace App\Modules\TingBoxVAMC\Requests\DiemBan;

use Illuminate\Foundation\Http\FormRequest;

class Mpos360DetailDiemBanRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.merchantId' => ['required', 'string'],
      'data.muId' => ['required', 'string'], // là muid
			'data.username' => ['present', 'string']
    ];
  }
}
