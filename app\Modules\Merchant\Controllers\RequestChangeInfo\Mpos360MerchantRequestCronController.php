<?php

namespace App\Modules\Merchant\Controllers\RequestChangeInfo;

use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestUpdateFileAction\Mpos360MerchantRequestUpdateFileAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoGetConfigAction\Mpos360RequestChangeInfoGetConfigAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestCronPushSignAction\Mpos360MerchantRequestCronPushSignAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestCronPushRecordAction\Mpos360MerchantRequestCronPushRecordAction;

class Mpos360MerchantRequestCronController extends Controller
{
	// Cronjob đ<PERSON>y bản ghi yêu cầu sang MNP
	public function Mpos360MerchantRequestCronPushRecord(Request $request)
	{
		try {
			$result = app(Mpos360MerchantRequestCronPushRecordAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360MerchantRequestCronPushSign(Request $request)
	{
		try {
			$result = app(Mpos360MerchantRequestCronPushSignAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	// bổ sung thông tin
	public function Mpos360MerchantRequestUpdateFile(Request $request)
	{
		try {
			$result = app(Mpos360MerchantRequestUpdateFileAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
} // End class
