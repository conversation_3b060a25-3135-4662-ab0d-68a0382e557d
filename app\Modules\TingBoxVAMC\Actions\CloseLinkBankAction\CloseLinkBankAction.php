<?php

namespace App\Modules\TingBoxVAMC\Actions\CloseLinkBankAction;

use App\Lib\partner\SoundBox;
use App\Exceptions\BusinessException;
use App\Lib\Logs;
use App\Modules\TingBoxVAMC\Models\PlanEvent;
use App\Modules\TingBoxVAMC\Enums\TingBoxVAMCEnum;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use App\Modules\Merchant\Model\Mpos360Logs\LogRequest;
use App\Modules\TingBoxVAMC\Actions\CloseLinkBankAction\SubAction\GoiHuySangBenTingBoxSubAction;
use App\Modules\TingBoxVAMC\Actions\CloseLinkBankAction\SubAction\KiemTraLaVAMacDinhTingBoxSubAction;
use App\Modules\TingBoxVAMC\Actions\VAMC\CloseLinkVAMCAction;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\CloseLinkBankRequest;

class CloseLinkBankAction
{

	protected SoundBox $soundBox;
	protected $isCan = '';

	public function __construct(SoundBox $soundBox)
	{
		$this->soundBox = $soundBox;
	}

	public function run(CloseLinkBankRequest $request)
	{
		Logs::writeInfo("CloseLinkBankAction", $request->json('data'));
		
		$where = [
			'id' => $request->json('data.merchantShopBankId'),
			'merchant_id' => $request->json('data.merchantId'),
			'shop_id' => $request->json('data.userMobileId'),
		];

		$merchantShopBank = MerchantShopBank::query()->with('merchantBank')->where($where)->first();

		if (!$merchantShopBank) {
			throw new BusinessException('Không tìm thấy thông tin ngân hàng.');
		}


		$params = [
			'mcRequestId' => $merchantShopBank->request_id,
			'vaNextpayNumber' => $merchantShopBank->partner_request_id,
		];

		if ($merchantShopBank->isDaHuyLienKet()) {
			throw new BusinessException('Tài khoản ngân hàng này đã huỷ liên kết, không thể hủy tiếp.');
		}

		if (!$merchantShopBank->isDongBoSoundBox()) {
			throw new BusinessException('Tài khoản ngân hàng này chưa đồng bộ sang Tingbox, không thể hủy');
		}

		app(KiemTraLaVAMacDinhTingBoxSubAction::class)->run($request, $merchantShopBank);

		$logRequest = LogRequest::query()->forceCreate([
			'merchant_id' => $request->json('data.merchantId'),
			'partner' => 'va',
			'func' => 'closeBank',
			'request' => json_encode($params, JSON_UNESCAPED_UNICODE),
			'created_at' => now()->format('Y-m-d H:i:s'),
			'updated_at' => now()->format('Y-m-d H:i:s'),
		]);

		if (!$logRequest) {
			throw new BusinessException('Lỗi lưu trữ dữ liệu thất bại.');
		}


		$closeLinkBank = app(CloseLinkVAMCAction::class)->run($params);
	

		$logRequest->update(['response' => json_encode($closeLinkBank, JSON_UNESCAPED_UNICODE), 'updated_at' => now()->format('Y-m-d H:i:s')]);


		// Không có mã lỗi báo  về thì bắn lỗi liên kết
		if (empty($closeLinkBank['error_code'])) {
			$msgDefault = sprintf('Đối tác ngân hàng thực hiện liên kết không có phản hồi. Vui lòng thử lại sau!');
			throw new BusinessException($msgDefault);
		}

		if ($closeLinkBank['error_code'] == '00') {
			// Không cần OTP mà thành công luôn
			$this->isCan = TingBoxVAMCEnum::CAN_GO_TO_TINGBOX_VAMC_SUCCESS;
		} elseif ($closeLinkBank['error_code'] == '475') {
			// Cần thực hiện otp
			$this->isCan = TingBoxVAMCEnum::CAN_GO_TO_TINGBOX_VAMC_SEND_OTP;
		} else {
			// Mã lỗi khác: Show message kèm thông tin lỗi 
			$listMaLoi = TingBoxVAMCEnum::DEFINE_ERROR_MESSAGE_VAMC;
			$currentMaLoi = $closeLinkBank['error_code'] ?? -1;
			
			if (isset($listMaLoi[$currentMaLoi])) {
				$msgDefault = $listMaLoi[$currentMaLoi] . sprintf(' (Code: %s)', $currentMaLoi);
			}else {
				$msgDefault = sprintf('Huỷ liên kết tài khoản ngân hàng thất bại. Hãy liên hệ CSKH để được hỗ trợ (Code: %s)', $currentMaLoi);
			}
			
			throw new BusinessException($msgDefault);
		}

		if(isset($closeLinkBank['error_code']) && $closeLinkBank['error_code'] == '00'){
			$merchantShopBank->status_link = 2;
			$merchantShopBank->time_updated = now()->timestamp;
			$merchantShopBank->data_linked = json_encode($closeLinkBank, JSON_UNESCAPED_UNICODE);
			$merchantShopBank->save();

			$cancelVATingbox = app(GoiHuySangBenTingBoxSubAction::class)->run($merchantShopBank);
			
			// Nếu không cancel được sang tingbox thì tạo bản ghi job
			if (!$cancelVATingbox) {
				$planEventCancel = PlanEvent::query()->forceCreate([
					'merchant_shop_bank_id' => $merchantShopBank->id,
					'merchant_id' => $merchantShopBank->merchant_id,
					'action' => 'CANCLESOUNDBOX',
					'data' => json_encode($request->json('data'), JSON_UNESCAPED_UNICODE),
					'time_created' => time(),
					'time_updated' => time(),
				]);		
			}
		}

		return [
			'msg' => 'Huỷ liên kết tài khoản ngân hàng thành công.',
			'can' => $this->isCan,
			'merchantShopBankId'         => $merchantShopBank->merchant_bank_id,
			'partnerRequestId'       => $merchantShopBank->partner_request_id,
			'requestId'              => $merchantShopBank->request_id,
			'countdownTimeGetNewOtp' => isset($closeLinkBank['data']) && isset($closeLinkBank['data']['expired']) ? $closeLinkBank['data']['expired'] : '',
			'deepLinkConfirm' => isset($closeLinkBank['data']) && isset($closeLinkBank['data']['deepLinkConfirm']) ? $closeLinkBank['data']['deepLinkConfirm'] : '',
			'urlConffirm' => isset($closeLinkBank['data']) && isset($closeLinkBank['data']['urlConffirm']) ? $closeLinkBank['data']['urlConffirm'] : '',
			'bankCode'               => $merchantShopBank->merchantBank->bank_code,
			'merchantShopBankIdAssign' => $request->json('data.merchantShopBankIdAssign'),
			'confirmOtpParams' => [
				'username' => $request->json('data.username', ''),
				'merchantId' => $merchantShopBank->merchantBank->merchant_id,
				'bankCode' =>  $merchantShopBank->merchantBank->bank_code,
				'mobileUserId' => $merchantShopBank->shop_id,
				'merchantShopBankIdAssign' => $request->json('data.merchantShopBankIdAssign'),
				'action' => 'CANCEL',
				'merchantBankId' => $merchantShopBank->merchantBank->id,
				'partnerRequestId' => $merchantShopBank->partner_request_id,
				'requestId' => $merchantShopBank->request_id,
				'merchantShopBankId' => $merchantShopBank->id,
				'otp' => ''
			]
		];
	}
} // End class
