<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction;

use App\Modules\Merchant\DTOs\Authen\Mpos360Auth\LoginMposSuccessDto;
use App\Modules\Merchant\Model\DeviceSession;

class CreateDeviceSessionSubAction
{
	public function run(array $params = [], LoginMposSuccessDto $loginMposSuccessDto): DeviceSession
	{
		$deviceSessionParams = $params;
		$deviceSessionParams['mpos_token'] = $loginMposSuccessDto->mobileUserToken;

		$deviceSession = DeviceSession::query()->forceCreate($deviceSessionParams);
		return $deviceSession;
	}
}
