<?php

namespace App\Modules\Merchant\Actions\TingBox\Mpos360SaveThietBiTingBoxStep2Action;

use App\Lib\TelegramAlertWarning;
use App\Lib\MnpOnboardNewMcHelper;
use App\Exceptions\BusinessException;
use App\Lib\Logs;
use App\Lib\TelegramAlert;
use App\Modules\Merchant\Actions\TingBox\Mpos360SaveThietBiTingBoxStep2Action\SubAction\DongBoThietBiSauKhiAddSangTingBoxSubAction;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use App\Modules\Merchant\Requests\TingBox\Mpos360SaveThietBiTingBoxStep2Request;

class Mpos360SaveThietBiTingBoxStep2Action
{
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run(Mpos360SaveThietBiTingBoxStep2Request $request)
	{
		
		$merchantId = $request->json('data.merchantId');

		$inputs = [
			'serial' => $request->json('data.tingboxSerial'),
			'mposMcId' => $merchantId,
			'areaId' => $request->json('data.shopId'),
		];

		$assignTingBox = $this->mnpOnboardNewMcHelper->ganThietBiTingBox($inputs);
		
		if ( isset($assignTingBox['result']) && $assignTingBox['code'] == Mpos360Enum::API_SUCCESS_CODE) {
			
			if (!empty($request->get('mobileUserId'))) {
				app(DongBoThietBiSauKhiAddSangTingBoxSubAction::class)->run($request, $request->get('mobileUserId'));
			}
			
			$returnData = [
				'status' => 'SUCCESS',
				'msg' => 'Thành công',
			];

			return $returnData;
		}

		$mnpErr = $assignTingBox['message'] ?? 'Lỗi không xác định';
		$errMsg = sprintf('%s (Code: %s)', $mnpErr, $assignTingBox['code']);

		throw new BusinessException($errMsg, $assignTingBox['code']);
	}
} // End class
