<?php

namespace App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360ListTransByWithdrawalOrderCodeAction\SubAction;

use App\Lib\Helper;
use App\Lib\partner\MPOS;
use Carbon\Carbon;

class GetGiaoDichTraLaiSubAction
{
	/**
	 * $rc = [
	 * 		"refundTransList": [
						{
								"createDate": "2024-05-24 08:50:21",
								"withdrawBackStatus": "",
								"description": "Create-date:24\/05\/2024 08:50,Created-by:<EMAIL>;Refund-approved-by:<EMAIL>,Refuld-approved-date:31\/05\/2024 14:39",
								"lendingId": "",
								"txId": "20231228162911063",
								"type": "REFUND",
								"withdrawBackAmount": -98100
						}
				]
	 * ]
	 */
	public function run($rc = [])
	{
		if (empty($rc['refundTransList'])) {
			return [];
		}

		$resultByDate = collect($rc['refundTransList'])->groupBy(function ($item) {
			return Carbon::parse($item['createDate'])->format('d-m-Y');
		})
		->all();

		$data = [];
		foreach ($resultByDate as $date => $listTrans) {
			$item = [
				'date' => $date,
				'total_trans' => sprintf('%sGD', count($listTrans)),
				'data' => []
			];

			$records = [];
			foreach ($listTrans as $tr) {
				$transactionId = '';
				if (!empty($tr['id'])) {
					$transactionId = $tr['id'];
				}
				
				$amount = abs($tr['withdrawBackAmount']);
				$records[] = [

					'code' => $transactionId,
					'type' => $tr['type'],
					'icon' => cumtomAsset('images/rtn/mpos.png'),
					'amount' => sprintf('+ %s', Helper::priceFormat($amount)),
					'pan' => sprintf('Trả lại tiền: %s', $tr['description'] ?? 'chưa rõ thông tin'),
					'time_created' => Carbon::parse($tr['createDate'])->format('H:i'),
					'note' => $tr['description'],
					'status' => '',
					'other_data' => [
						'amount' => [
							'text_color' => '#dd8523',
							'font-weight' => 'bold'
						],

						'pan' => [
							'text_color' => '#808890'
						],

						'time_created' => [
							'text_color' => '#808890'
						],

						'status' => [
							'bg_color' => '#ffffff'
						]
					]
				];
			}

			$item['data'] = $records;
			$data[] = $item;
		}

		return $data;
	}
} // End class