<?php

namespace App\Modules\Merchant\Requests\RequestChangeInfo\V3;

use App\Modules\Merchant\Requests\MerchantRequest;
use App\Modules\Merchant\Rules\RequestChangeInfoRule\UploadDungLoaiChungTuRule;

class Mpos360RequestChangeInfoAdditionalAttachmentRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.id' => ['required', 'numeric', 'integer', 'min:1'],

			'data.email' => ['required', 'string', 'email'],

			'data.qts_request_id' => ['present', 'string'],
			
			'data.additional_profiles' => ['required', 'array'],
			'data.additional_profiles.*.profileKey' => ['required', 'string'],
			'data.additional_profiles.*.value' => ['required', 'string'],

			'data.attachments' => ['required', 'array'],
			'data.attachments.id_documents' => ['required', 'array', new UploadDungLoaiChungTuRule()],
			'data.attachments.id_documents.*' => ['required', 'array'],
			'data.attachments.id_documents.*.*' => ['required', 'string', 'url'], // item dinh kem
			'data.attachments.other_documents' => ['present', 'array'],
			'data.attachments.other_documents.*' => ['string', 'url'],
		];
	}

	public function messages()
	{
		return [
			// id yc
			'data.id.required' => vmsg('IdYeuCauLaBatBuoc'),
			'data.id.numeric' => vmsg('IdYeuCauPhaiLaDangSo'),
			'data.id.integer' => vmsg('IdYeuCauPhaiLaSoTuNhien'),
			'data.id.min' => vmsg('IdYeuCauPhaiCoGiaTriThapNhatLa1'),

			// email
			'data.email.required' => vmsg('EmailMcLaBatBuoc'),
			'data.email.string' => vmsg('EmailMcPhaiLaKieuChuoi'),
			'data.email.email' => vmsg('EmailMcKhongDungDinhDang'),

			// qts_request_id 
			'data.qts_request_id.present' => vmsg('PhaiCoThamSoQtsRequestId'),
			'data.qts_request_id.string' => vmsg('QtsRequestPhaiLaDangChuoi'),

			// additional_profiles
			'data.additional_profiles.required' => vmsg('HoSoBoSungLaBatBuoc'),
			'data.additional_profiles.array' => vmsg('HoSoBoSungPhaiLaKieuMang'),
			'data.additional_profiles.*.profileKey.required' => vmsg('MaHoSoLaBatBuoc'),
			'data.additional_profiles.*.profileKey.string' => vmsg('MaHoSoPhaiLaKieuChuoi'),
			'data.additional_profiles.*.value.required' => vmsg('GiaTriHoSoLaBatBuoc'),
			'data.additional_profiles.*.value.string' => vmsg('GiaTriHoSoLaKieuChuoi'),

			'data.attachments.required' => vmsg('FileDinhKemLaBatBuoc'),
			'data.attachments.array' => vmsg('FileDinhKemPhaiLaKieuMang'),
			'data.attachments.id_documents.required' => vmsg('ChungTuThayTheLaBatBuoc'),
			'data.attachments.id_documents.array' => vmsg('ChungTuThayThePhaiLaDangMang'),
			'data.attachments.other_documents.present' => vmsg('PhaiCoThamSoChungNhanDiKem'),
			'data.attachments.other_documents.array' => vmsg('ChungNhanDiKemPhaiLaDangMang'),
			'data.attachments.other_documents.*.string' => vmsg('ChungNhanDiKemPhaiLaDangChuoi'),
			'data.attachments.other_documents.*.string' => vmsg('ChungNhanDiKemPhaiLaUrl'),

			'data.attachments.id_documents.*.required' => __('Loại chứng từ là bắt buộc'),
			'data.attachments.id_documents.*.array' => __('Loại chứng từ phải là 1 mảng'),
			'data.attachments.id_documents.*.*.required' => __('Item chứng từ là bắt buộc'),
			'data.attachments.id_documents.*.*.string' => __('Item chứng từ phải là kiểu chuỗi'),
			'data.attachments.id_documents.*.*.url' => __('Item chứng từ phải là 1 url đã upload'),
		];
	}

	protected function passedValidation()
	{
		$params = $this->all();
		foreach ($params['data']['additional_profiles'] as &$profile) {
			
			if ( is_string($profile['value']) ) {
				$profile['value'] = cleanXSS($profile['value']);
			}
		}

		$additionalProfiles = collect($this->json('data.additional_profiles'))->filter(function ($item) {
			return $item['profileKey'] != 'identificationDocument';
		})->all();

		$params['data']['additional_profiles'] = $additionalProfiles;
		$this->merge($params);
	}
} // End class
