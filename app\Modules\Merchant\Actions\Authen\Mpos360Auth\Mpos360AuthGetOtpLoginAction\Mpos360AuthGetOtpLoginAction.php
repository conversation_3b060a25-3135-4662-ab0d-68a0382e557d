<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthGetOtpLoginAction;

use Exception;
use Carbon\Carbon;
use App\Lib\OtpHelper;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Setting;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360CodeOtp;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360AuthGetOtpLoginRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\SendMailOtpMNPSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\GetMNPAccessTokenSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\SendMailCustomizeViaMnpSubAction;

class Mpos360AuthGetOtpLoginAction
{
	public function run(Mpos360AuthGetOtpLoginRequest $request): array
	{
		$deviceSession = $request->getCurrentDeviceSession();
		$deviceSessionWithToken = app(GetMNPAccessTokenSubAction::class)->run($deviceSession);

		$merchantEmail = $deviceSession->getMerchantEmail();

		if ($request->isResendOtp()) {
			$mpos360CodeOtp = Mpos360CodeOtp::query()->firstWhere([
				'id' => $request->json('data.otp_id'),
				'user_id' => $deviceSession->user_id
			]);

			if (!$mpos360CodeOtp) {
				throw new BusinessException(__('otp.Lỗi: không tạo được bản ghi otp'));
			}

			$mpos360CodeOtp->otp = generateRandomNumber(6);
			$mpos360CodeOtp->time_out = now()->addSeconds(OtpHelper::getSoGiayCountdown())->timestamp;
			$mpos360CodeOtp->time_updated = now()->timestamp;
			$mpos360CodeOtp->status = Mpos360Enum::MPOS360_OTP_CHUA_SU_DUNG;
			$r = $mpos360CodeOtp->save();

			if (!$r) {
				throw new BusinessException(__('otp.Lỗi cập nhật gửi lại mã otp'));
			}
		}

		if (!$request->isResendOtp()) {
			$mpos360CodeOtp = Mpos360CodeOtp::query()->forceCreate([
				'command_code' => 'MERCHANT_LOGIN',
				'service_code' => $request->json('data.service_code', 'EMAIL'),
				'user_id'      => $deviceSession->user_id,
				'obj_value'    => $merchantEmail,
				'message'      => base64_encode('Noi dung'),
				'otp'          => generateRandomNumber(6),
				'reference_id' => $deviceSession->getMerchantId(),
				'status'       => 1,
				'time_out'     => now()->addSeconds(OtpHelper::getSoGiayCountdown())->timestamp,
				'time_created' => now()->timestamp,
				'time_updated' => now()->timestamp,
			]);

			if (!$mpos360CodeOtp) {
				throw new BusinessException(__('otp.Lỗi: không tạo được bản ghi otp'));
			}
		}


		// $sendOtpResult = app(SendMailOtpMNPSubAction::class)->run(
		// 	$mpos360CodeOtp,
		// 	$deviceSessionWithToken,
		// 	'content_for_login',
		// 	'Mã otp đăng nhập của bạn'
		// );

		// $msg = __('otp.HeThongDaGuiOtpToiEmailCuaBan', ['email' => $merchantEmail]);

		/**
		 * Tùy biến lại mail template otp login
		 */
		$mposUserName = $mpos360CodeOtp->obj_value;
		$settingHdsd = Setting::query()->firstWhere(['key' => 'HOST_ASSET_MAIL_TEMPLATE']);
		$settingHdsdValue = json_decode($settingHdsd->value, true);

		$bindParams = array_merge([
			'title' => 'Đăng nhập vào ứng dụng ' . __('setting.appName'),
			'merchantName' => $mpos360CodeOtp->mpos360User->getUserMcName(),
			'username' => $mposUserName,
			'otp' => $mpos360CodeOtp->otp,
			'otpExpiredAt' => Carbon::createFromTimestamp($mpos360CodeOtp->time_out)->format('d/m/Y H:i:s')
		], $settingHdsdValue['common']);
		
		$mailMeta = [
			'subject' => sprintf('[%s] - Mã otp đăng nhập vào ứng dụng %s', $mpos360CodeOtp->otp, __('setting.appName')),
			'to' => [$mpos360CodeOtp->obj_value],
			'cc' => [],
			'content' => view('EmailTemplate.EmailOtpLogin', $bindParams)->render()
		];

		$sendOtpResult = app(SendMailCustomizeViaMnpSubAction::class)->run($deviceSessionWithToken->mnp_token, $mailMeta);
		$msg = __('otp.HeThongDaGuiOtpToiEmailCuaBan', ['email' => $merchantEmail]);

		return [
			'otp_id' => $mpos360CodeOtp->id,
			'msg' => $msg,
			'countdown_time_get_new_otp' => OtpHelper::getSoGiayCountdown()
		];
	}
}
