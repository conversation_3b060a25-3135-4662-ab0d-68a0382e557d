<?php

namespace App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360ListDiemBanAction\SubAction;

class LoaiBoMobileUserNullSubAction
{
	public function run($data)
	{
		if (!isset($data["data"]["locations"]) || !is_array($data["data"]["locations"])) {
			return $data;
		}

		foreach ($data["data"]["locations"] as &$location) {
			if (isset($location["deviceDTOs"]) && is_array($location["deviceDTOs"])) {
				// Lọc deviceDTOs, chỉ giữ lại những phần tử có mobileUserId không null
				$location["deviceDTOs"] = array_values(array_filter($location["deviceDTOs"], function ($device) {
					return !is_null($device["mobileUserId"]);
				}));
			} else {
				// Đ<PERSON>m bảo deviceDTOs luôn là một mảng
				$location["deviceDTOs"] = [];
			}
		}

		return $data;
	}
}
