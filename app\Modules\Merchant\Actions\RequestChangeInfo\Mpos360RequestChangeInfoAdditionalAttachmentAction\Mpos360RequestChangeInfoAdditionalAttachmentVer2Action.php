<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction;

use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessAction\SubAction\MappingDinhKemSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessAction\SubActionV2\KiemTraPhuongThucKySubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\SubAction\GetPhuongThucQuetB3Ver2SubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\GetMNPAccessTokenSubAction;

class Mpos360RequestChangeInfoAdditionalAttachmentVer2Action
{
	public function run(Mpos360RequestChangeInfoAdditionalAttachmentRequest $request)
	{
		$mpos360MerchantRequest = Mpos360MerchantRequest::query()->find($request->json('data.id'));

		if (!$mpos360MerchantRequest) {
			throw new BusinessException('Không tìm được bản ghi yêu cầu thay đổi');
		}

		$attachments = $request->json('data.attachments', []);

		$profilesImgUrl = app(MappingDinhKemSubAction::class)->run($attachments);

		$dataRequest = json_decode($mpos360MerchantRequest->data_request, true);

		$additionalProfile = $request->json('data.additional_profiles');
		$positionAuthBank = collect($additionalProfile)->where('profileKey', 'positionAuthBank')->first();
		
		if (empty($positionAuthBank['value'])) {
			throw new BusinessException('Lỗi thiếu thông tin chức vụ');
		}

		if (!empty($positionAuthBank['value'])) {
			if (strlen($positionAuthBank['value']) < 3) {
				throw new BusinessException('Thông tin chức vụ phải có độ dài tối thiểu là 3 ký tự');
			}

			if (strlen($positionAuthBank['value']) > 255) {
				throw new BusinessException('Thông tin chức vụ phải có độ dài tối đa là 255 ký tự');
			}
		}

		// Validate bank relation
		$bankMutualRelation = collect($additionalProfile)->where('profileKey', 'bankMutualRelation')->first();
		
		if (empty($bankMutualRelation['value'])) {
			throw new BusinessException('Lỗi thiếu thông tin mối quan hệ');
		}

		if (!empty($bankMutualRelation['value'])) {
			if (strlen($bankMutualRelation['value']) < 3) {
				throw new BusinessException('Mối quan hệ phải có độ dài tối thiểu là 3 ký tự');
			}

			if (strlen($bankMutualRelation['value']) > 255) {
				throw new BusinessException('Mối quan hệ phải có độ dài tối đa là 255 ký tự');
			}
		}

		foreach ($additionalProfile as $profile) {
			$profileKey = $profile['profileKey'];
			$profileValue = $profile['value'];

			$dataRequest[0]['profiles'][$profileKey] = $profileValue;
		}

		foreach ($profilesImgUrl as $profileKey => $profileValue) {
			$dataRequest[0]['profiles'][$profileKey] = $profileValue;
		}

		if (empty($dataRequest[0]['scan_method'])) {
			$dataRequest[0]['scan_method'] = (object)[];
		}


		$mpos360MerchantRequest->data_request = json_encode($dataRequest);
		$mpos360MerchantRequest->status_verify = Mpos360Enum::MPOS360_MC_VERIFY_STT_DA_XAC_THUC;
		$savedResult = $mpos360MerchantRequest->save();

		if (!$savedResult) {
			throw new BusinessException('Lỗi không thể bổ sung thông tin');
		}

		$deviceSesion = $request->getCurrentDeviceSession();
		$deviceSesionWithToken = app(GetMNPAccessTokenSubAction::class)->run($deviceSesion);
		$kiemTraPhuongThucKy = app(KiemTraPhuongThucKySubAction::class)->run($mpos360MerchantRequest, $deviceSesionWithToken);
		
		return [
			'id' => $mpos360MerchantRequest->id,
			'msg' => 'Bổ sung tài liệu đính kèm thành công',
			'status' => 'SUCCESS',
			'can' => $kiemTraPhuongThucKy['can'],
			'list_sign_method' => $kiemTraPhuongThucKy['sign_method'],
			'scan_method' => app(GetPhuongThucQuetB3Ver2SubAction::class)->run($mpos360MerchantRequest->merchant_id)
		];
	}
}
