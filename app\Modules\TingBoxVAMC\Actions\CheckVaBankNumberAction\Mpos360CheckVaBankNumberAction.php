<?php

namespace App\Modules\TingBoxVAMC\Actions\CheckVaBankNumberAction;

use App\Lib\Logs;
use App\Lib\partner\SoundBox;
use App\Lib\MnpOnboardNewMcHelper;
use App\Exceptions\BusinessException;
use App\Modules\TingBoxVAMC\Models\BankVAMC;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\Mpos360CheckVaBankNumberRequest;
use App\Modules\TingBoxVAMC\Actions\MerchantBank\LinkBankAction\SubAction\CheckRuleLienKetSubAction;

class Mpos360CheckVaBankNumberAction
{
	private SoundBox $soundBox;
	private MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public function __construct(
		SoundBox $soundBox,
		MnpOnboardNewMcHelper $mnpOnboardNewMcHelper
	) {
		$this->soundBox = $soundBox;
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run(Mpos360CheckVaBankNumberRequest $request): array
	{
		Logs::writeInfo('Mpos360CheckVaBankNumberAction-request', $request->json('data'));

		$response = $this->soundBox->checkVaBankNumber([
			'mcId' => $request->json('data.merchantId'),
			'qrCode' => $request->json('data.qrCode')
		]);

		if (empty($response)) {
			throw new BusinessException('Không thể kết nối tới hệ thống kiểm tra VA Bank. Vui lòng thử lại sau.');
		}

		if (empty($response['data']['verify'])) {
			$errorMessage = $response['result']['msg'] ?? 'Mã QR dán của bạn không hợp lệ hoặc đã được sử dụng';
			
			return [
				'isValid' => 'NO',
				'message' => $errorMessage,
				'vaBankNumber' => $response['data']['vaBankNumber'],
				'bankVamc' => (object) []
			];
		}

		if (empty($response['data']['bankCode'])) {
			throw new BusinessException('Lỗi đối tác trả thiếu thông tin banking.');
		}
		
		$bankVamc = BankVAMC::query()->where('bankCode', $response['data']['bankCode'])->first();
		
		return [
			'isValid' => !empty($response['result']) ? 'YES' : 'NO',
			'message' => $response['message'] ?? '',
			'vaBankNumber' => $response['data']['vaBankNumber'],
			'bankVamc' => (object) $bankVamc->toArray()
		];
	}
}
