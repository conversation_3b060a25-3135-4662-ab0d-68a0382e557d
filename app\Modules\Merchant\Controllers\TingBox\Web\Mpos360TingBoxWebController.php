<?php

namespace App\Modules\Merchant\Controllers\TingBox\Web;

use Exception;
use Carbon\Carbon;
use App\Lib\OtpHelper;
use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use App\Lib\MnpOnboardNewMcHelper;
use App\Modules\Merchant\Model\Setting;
use Illuminate\Support\Facades\Validator;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\CacheAction;
use App\Modules\Merchant\Model\Mpos360User;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Model\Mpos360CodeOtp;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\SendSmsOtpMNPSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\UploadS3MNPForRegisterSubAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360RegisterUploadAction\Mpos360RegisterUploadAction;

class Mpos360TingBoxWebController extends Controller
{
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function TBWebMpos360SubmitDangKy(Request $request)
	{
		$message = '';
		$boolean = true;
		$phone = '';

		if ($request->json('signed')) {
			$data = decrypt($request->json('signed'));
			if (!$data || empty($data) || !isset($data['phone']) || !isset($data['password'])) {
				$boolean = false;
				$message = "Có lỗi xảy ra, Hãy thông báo kỹ thuật hỗ trợ xử lý.";
			}
			$phone = $data['phone'];
			$data['otp_id'] = $request->json('otp_id');
			$encryptData = encrypt($data);

		} else {
			// Validation
			$validator = Validator::make($request->all(), [
				'phone' => 'required|regex:/^0[0-9]{9}$/',
				'password' => [
					'required', 
					'min:6', 
					function ($attribute, $value, $fail) {
						if (preg_match('/\s/', $value)) {
							$fail('Trường mật khẩu không được có khoảng trắng');
						}
					}
				],
				'confirm_password' => 'required|same:password',
				'qa' => 'required|string',
				'qaId' => 'required|string',
			]);

			if ($validator->fails()) {
				return response()->json([
					'success' => false,
					'html' => $validator->errors()->first(),
					'data' => '',
					'message' => $validator->errors()->first()
				]);
			}

			$listSimplePassword = config('simple_password');
			if (in_array($request->input('password'), $listSimplePassword)) {
				return response()->json([
					'success' => false,
					'html' => 'Mật khẩu của bạn quá đơn giản. Vui lòng sử dụng mật khẩu khác',
					'data' => '',
					'message' => 'Mật khẩu của bạn quá đơn giản. Vui lòng sử dụng mật khẩu khác'
				]);
			}

			$qaId = decrypt($request->json('qaId'));
			$qa = $request->json('qa');
			$listCaptcha = config('captcha');
			$findCaptcha = collect($listCaptcha)->firstWhere('qaId', $qaId);
			if(!$findCaptcha){
				$boolean = false;
				$message = "Câu hỏi bảo mật không chính xác";
			}

			if(!empty($findCaptcha) && $findCaptcha['a'] == $qa){
				$phone = $request->json('phone');

				$checkPhoneExist =  @$this->mnpOnboardNewMcHelper->checkSdtHopLe(
					$phone,
					$request->input('password')
				);

				if (!$checkPhoneExist['result'] || $checkPhoneExist['code'] != 1000) {
					$boolean = false;
					$message = sprintf("Rất tiếc, đã có tài khoản TingBox <span class='BE-bold'>{$phone}</span>, Quý khách vui lòng sử dụng số điện thoại khác để đăng ký hoặc đăng nhập bằng tài khoản đã có (Code: %s)", $checkPhoneExist['code']);
				}
			}else {
				$boolean = false;
				$message = "Vui lòng nhập chính xác câu hỏi bảo mật";
			}

			$encryptData = encrypt($request->all());
		}

		$htmlSendOtp = view('TingBoxWeb.guiotp', ['phone' => $phone, 'data' => $encryptData])->render();

		if (!empty($checkPhoneExist['data']['mposMcId'])) {
			session(['DangKyMcId' => $checkPhoneExist['data']['mposMcId']]);
		}

		return response()->json([
			'success' => $boolean,
			'html' => $htmlSendOtp,
			'data' => $encryptData,
			'message' => $message,
			'merchant' => [
				'merchantId' => $checkPhoneExist['data']['mposMcId'] ?? null,
				'redirectUrl' => route('TBWebMpos360FormAuthenAccountAction', [
					'merchantId' => $checkPhoneExist['data']['mposMcId'] ?? '',
				'signed' => encrypt(['merchantId' => $checkPhoneExist['data']['mposMcId'] ?? ''])
				]
				)
			]
		]);
	}

	public function TBWebMpos360FormDangKy(Request $request)
	{
		$listCaptcha = config('captcha');
		$randomCaptcha = Arr::random($listCaptcha);
		$questionExplode = explode(' ', $randomCaptcha['q']);
		$questionExplode[] = '=';
		$questionExplode[] = '?';

		return view('TingBoxWeb.dangky', [
			'qaId' => encrypt($randomCaptcha['qaId']),
			'questionExplode' => $questionExplode
		]);
	}

	public function TBWebMpos360SendOtp(Request $request)
	{
		$boolean = true;
		$message = '';
		$htmlSendOtp = '';
		$channelSupport = [
			'zalo' => "Zalo",
			'sms' => "SMS",
		];

		// Validation
		$request->validate([
			'phone' => 'required|string',
			'channel' => 'required|string',
			'data' => 'required|string',
		]);
		


		$data = decrypt($request->json('data'));
		$second = OtpHelper::getSoGiayCountdown();

		if($data['phone'] != $request->json('phone')){
			return response()->json([
				'success' => false,
				'html' => '',
				'message' => 'Số điện thoại không chính xác'
			]);
		}

		// Nếu số bản ghi otp register của sđt này quá 3 lần chưa sử dụng thì cảnh báo ngay
		$lastOtpChuaSuDung = Mpos360CodeOtp::query()->where('command_code', 'REGISTER')
																							 ->where('status', Mpos360Enum::MPOS360_OTP_CHUA_SU_DUNG)
																							 ->where('obj_value', $data['phone'])
																							 ->orderBy('id', 'DESC')
																							 ->first();
		
		if ($lastOtpChuaSuDung) {
			$soGiayChenhLech = now()->timestamp - $lastOtpChuaSuDung->time_created;
			if ($soGiayChenhLech <= 90) {
				return response()->json([
					'success' => false,
					'html' => 'Bạn đã tạo OTP cách đây 90 giây rồi. Vui lòng chờ hết 90 giây rồi hãy lấy mã otp mới',
					'message' => 'Bạn đã tạo OTP cách đây 90 giây rồi. Vui lòng chờ hết 90 giây rồi hãy lấy mã otp mới',
				]);
			}
		}

		if(isset($data['otp_id']) && !empty($data['otp_id'])){
			$mpos360CodeOtp = Mpos360CodeOtp::query()->where([
				'id' => $data['otp_id'],
				'command_code' => 'REGISTER'
			])->first();
			if(!$mpos360CodeOtp){
				$boolean = false;
				$message = 'Lỗi không tìm thấy OTP';
			}

			if($mpos360CodeOtp){
				$mpos360CodeOtp->otp = generateRandomNumber(6);
				$mpos360CodeOtp->status = Mpos360Enum::MPOS360_OTP_CHUA_SU_DUNG;
				$mpos360CodeOtp->time_out = now()->addSeconds($second)->timestamp;
				$mpos360CodeOtp->time_updated = now()->timestamp;

				$mpos360CodeOtp->save();
			}
		
		}else{

			$mpos360CodeOtp = Mpos360CodeOtp::query()->forceCreate([
				'command_code' => 'REGISTER',
				'service_code' => strtoupper($request->json('channel')), // kênh nhận otp
				'user_id'      => '',
				'obj_value'    => $data['phone'],
				'message'      => base64_encode('Noi dung'),
				'otp'          => generateRandomNumber(6),
				'reference_id' => '',
				'status'       => Mpos360Enum::MPOS360_OTP_CHUA_SU_DUNG,
				'time_out'     => now()->addSeconds($second)->timestamp,
				'time_created' => now()->timestamp,
				'time_updated' => now()->timestamp,
			]);
	
			if (!$mpos360CodeOtp) {
				$boolean = false;
				$message = 'Lỗi không tạo được bản ghi OTP';
			}

		}

		$message = 'Lỗi không gửi được OTP';

		if ($mpos360CodeOtp) {
			$deviceSession = new DeviceSession();
			$deviceSession->mnp_token = @$this->mnpOnboardNewMcHelper->getAccessTokenMcDefault();
			$channel = strtolower($request->json('channel')) == 'zalo' ? true : false;

			try {
				$sendOtp = app(SendSmsOtpMNPSubAction::class)->run($mpos360CodeOtp,$deviceSession,$channel);
			}catch(\Throwable $th) {
				$mpos360CodeOtp->status = -1;
				$mpos360CodeOtp->save();
				if (strtolower($request->input('channel')) == 'zalo') {
					$message = 'Hệ thống không thể gửi otp qua kênh Zalo. Bạn vui lòng chuyển hình thức nhận otp sang kênh SMS';
					$sendOtp = false;
				}
			}

			$boolean = false;
			
			if($sendOtp){
				$boolean = true;
				
				$newTimeContext = '';
				if ($second >= 60) {
					$minutes = floor($second / 60);
					$remainingSeconds = $second % 60;
					$newTimeContext = sprintf('%02d %s %02d %s', $minutes, 'phút', $remainingSeconds, 'giây');
				} else {
					$newTimeContext = sprintf('%02d %s', $second, 'giây');
				}
				
				$htmlSendOtp = view('TingBoxWeb.xacthucotp', [
					'phone' => $data['phone'],
					'channel' => $channelSupport[strtolower($request->json('channel'))],
					'count_time' => $second,
					'data' => $request->json('data'),
					'otp_id' => $mpos360CodeOtp->id,
					'newTimeContext' => $newTimeContext,
				])->render();

				$message = sprintf('Đã gửi otp cho %s qua kênh %s. Bạn vui lòng kiểm tra và tiếp tục thao tác', $data['phone'], strtoupper($request->input('channel')));
			}

		}


		return response()->json([
			'success' => $boolean,
			'html' => $htmlSendOtp,
			'message' => $message
		]);
	}


	public function TBWebMpos360VerifyOtp(Request $request)
	{

		$boolean = false;
		$message = '';
		$link = '';

		// Validation
		$request->validate([
			'phone' => 'required|string',
			'otp_id' => 'required|string',
			'otp' => 'required|string|max:6',
			'signed' => 'required|string',
		]);

		$data = decrypt($request->json('signed'));

		if($data['phone'] != $request->json('phone')){
			return response()->json([
				'success' => false,
				'link' => '',
				'message' => 'Số điện thoại không chính xác'
			]);
		}

		if (isset($data['phone']) && !empty($data['phone']) && isset($data['password']) && !empty($data['password'])) {
			// B1: Xác thực OTP
			$otp = trim($request->json('otp'));
			$otpId = $request->json('otp_id');

			$settingFakeOtp = Setting::query()->firstWhere(['key' => 'FAKE_OTP_PASSED', 'value' => '1']);

			$mpos360CodeOtp = Mpos360CodeOtp::query()->where([
				'id' => $otpId,
				'command_code' => 'REGISTER'
			])->first();

			if (optional($settingFakeOtp)->id) {
				goto HAS_PASS_OTP;
			}

			if (!$mpos360CodeOtp) {
				$message = 'Không tìm thấy bản ghi OTP';
			}

			if ($mpos360CodeOtp->isFinalStatus()) {
				$message = 'Mã OTP đã được sử dụng';
			}

			if ($mpos360CodeOtp->isExpiredOtp()) {
				$message = 'Mã OTP đã hết hạn';
			}

			if ($mpos360CodeOtp->otp != $otp) {
				$message = 'Mã OTP không chính xác';
			}else {
				// Đúng OTP
				$mpos360CodeOtp->status = Mpos360Enum::MPOS360_OTP_DA_SU_DUNG;
				$r = $mpos360CodeOtp->save();
				if (!$r) {
					$message = 'Lỗi không cập nhật được OTP';
				}
			}

			// pass otp thì message = '';

			HAS_PASS_OTP:
			if ($message == '') {
				// B2: Gọi sang Mynextpay để tạo yêu cầu mở tài khoản
				$createAccount = @$this->mnpOnboardNewMcHelper->taoTaiKhoanMerchant($data['phone'], $data['password']);
				if (!$createAccount['result'] || $createAccount['code'] != 1000 || (!isset($createAccount['data']['mposMcId']) || empty($createAccount['data']['mposMcId']))) {
					$message = isset($createAccount['message']) && !empty($createAccount['message']) ? $createAccount['message'] : "Có lỗi xảy ra";
					$message = sprintf('MNP Err: %s (Code: %s)', $message, $createAccount['code']);
				}

				// B3: Lưu tạm vào DB step 1 sau khi đăng ký
				if (isset($createAccount['data']['mposMcId']) && !empty($createAccount['data']['mposMcId'])) {
					$cacheAction = CacheAction::query()->forceCreate([
						'step' => 1,
						'reference_id' => $createAccount['data']['mposMcId'],
						'time_created' => now()->timestamp,
					]);
					$message = "Có lỗi xảy ra";

					if ($cacheAction) {
						$boolean = true;
						$message = "Thành công";
					}
				}
			}

		}

		if ($boolean) {
			session(['DangKyMcId' => $createAccount['data']['mposMcId']]);
			$link = route('TBWebMpos360FormAuthenAccountAction',['merchantId' => $createAccount['data']['mposMcId'],
			'signed' => encrypt(['merchantId' => $createAccount['data']['mposMcId']])
		]);
		}
		
		return response()->json([
			'success' => $boolean,
			'message' => $message,
			'link' => $link
		]);
	}

	public function TBWebMpos360FormAuthenAccount(Request $request)
	{
		// Validation
		$request->validate([
			'merchantId' => 'required|string',
			'signed' => 'nullable|string',
		]);

		MnpOnboardNewMcHelper::handleSecurity($request->get('merchantId'), $request->get('signed'));

		$user = Mpos360User::query()->where([
			'merchant_id' => $request->get('merchantId'),
		])->first();

		$cacheAction = CacheAction::query()->where([
			'reference_id' => $request->get('merchantId'),
		])->get();

		if(!$cacheAction){
			return redirect()->route('TBWebMpos360FormDangKyAction');
		}

		if(!$user){
			$existStep1 = $cacheAction->contains('step', 1);
			if(!$existStep1){
				return redirect()->route('TBWebMpos360FormDangKyAction');
			}
		}


		$existStep2 = $cacheAction->contains('step', 2);
		if($existStep2){
			return redirect()->route('Mpos360TingBoxKhaiBaoListInfoAction', [
				'merchantId' => $request->get('merchantId'),
				'sessionId' => $request->get('sessionId'),
				'signed' => $request->get('signed')
			]);
		}

		$cacheStepTru2 = $cacheAction->where('step', '-2')->first();
		if ($cacheStepTru2) {
			$dataStepTru2 = json_decode($cacheStepTru2->other_data, true);
		}else {
			$dataStepTru2 = [];
		}
		
		session(['DangKyMcId' => $request->get('merchantId')]);

		return view('TingBoxWeb.xacthuctaikhoan',[
			'merchantId' => $request->get('merchantId'),
			'sessionId' => $request->get('sessionId'),
			'signed' => $request->get('signed'),
			'dataStepTru2' => $dataStepTru2
		]);
	}

	public function TBWebMpos360SubmitAuthenAccountStep1(Request $request)
	{
		$boolean = true;
		$message = '';
		$html = '';
		$genderData = [
			'male' => 'Nam',
			'female' => 'Nữ',
		];

		// Validation
		$request->validate([
			'sessionId' => 'nullable|string',
			'merchantId' => 'required|string',
			'signed' => 'nullable|string',
			'name' => 'required|string|max:255',
			'cccd' => 'required|digits:12',
			'gender' => 'required|in:male,female',
			'address' => 'required|string|max:255',
			'birthDay' => 'required|string',
		]);


		$data = $request->all();
		$data['gender'] = $genderData[$data['gender']];
		
		$ngaySinh = $data['birthDay'];
		$year = substr($ngaySinh, 6);
		if (now()->format('Y') - $year < 18) {
			return response()->json([
				'success' => false,
				'message' => 'MC chưa đủ 18 tuổi. Hãy điều chỉnh lại ngày sinh của bạn',
				'html' => 'MC chưa đủ 18 tuổi. Hãy điều chỉnh lại ngày sinh của bạn',
			]);
		}
		
		$html = view('TingBoxWeb.xacthuctaikhoanview',[
			'data' => $data
		])->render();
		
		// Luu du lieu vao cacheAction
		$cacheActionStep2 = CacheAction::query()->where('step', '-2')->where('reference_id', $data['merchantId'])->first();
		if (!$cacheActionStep2) {
			CacheAction::query()->forceCreate([
				'step' => '-2',
				'other_data' => json_encode($data),
				'reference_id' => $data['merchantId'],
				'time_created' => now()->timestamp
			]);
		}
		
		if ($cacheActionStep2) {
			$otherData = json_decode($cacheActionStep2->other_data, true);
			$otherData['sessionId'] = $data['sessionId'];
			$otherData['merchantId'] = $data['merchantId'];
			$otherData['signed'] = $data['signed'];
			$otherData['name'] = $data['name'];
			$otherData['cccd'] = $data['cccd'];
			$otherData['gender'] = $data['gender'];
			$otherData['address'] = $data['address'];
			$otherData['birthDay'] = $data['birthDay'];
			$cacheActionStep2->other_data = json_encode($otherData);
			$cacheActionStep2->save();
		}

		session(['DangKyMcId' => $data['merchantId']]);

		return response()->json([
			'success' => $boolean,
			'message' => $message,
			'html' => $html
		]);
	}

	public function TBWebMpos360EditAuthenAccount(Request $request)
	{
		$boolean = true;
		$message = '';
		$html = '';
		$genderData = [
			'Nam' => 'male',
			'Nữ' => 'female',
		];
		// Validation
		$request->validate([
			'sessionId' => 'nullable|string',
			'merchantId' => 'required|string',
			'signed' => 'nullable|string',
			'name' => 'required|string|max:255',
			'cccd' => 'required|digits:12',
			'gender' => 'required|string',
			'address' => 'required|string|max:255',
			'birthDay' => 'required|string',
		]);

		$data = $request->all();
		$data['gender'] = $genderData[$data['gender']];

		$html = view('TingBoxWeb.xacthuctaikhoanedit',[
			'data' => $data
		])->render();;

		return response()->json([
			'success' => $boolean,
			'message' => $message,
			'html' => $html
		]);
	}

	public function TBWebMpos360SubmitAuthenAccountStep2(Request $request)
	{
		$boolean = true;
		$message = '';
		$html = '';
		$linkResult = '';
		$genderData = [
			'Nam' => 'MALE',
			'Nữ' => 'FEMALE',
		];


		// Validation
		$validator = Validator::make($request->all(), [
			'merchantId' => 'required|string',
			'signed' => 'nullable|string',
			'name' => 'required|string|max:255',
			'cccd' => 'required|digits:12',
			'gender' => 'required|string',
			'address' => 'required|string|max:255',
			'birthDay' => 'required|string',
			'files' => 'required|array',
			'files.*' => [
				'required',
				'file',
				'mimetypes:image/jpeg,image/png,image/svg+xml',
				'max:5000'
			]
		], [
			'merchantId.required' => 'Trường merchantId là bắt buộc.',
			'merchantId.string' => 'Trường merchantId phải là chuỗi.',
			'name.required' => 'Vui lòng nhập tên.',
			'name.string' => 'Tên phải là một chuỗi.',
			'name.max' => 'Tên không được vượt quá 255 ký tự.',
			'cccd.required' => 'Vui lòng nhập số CCCD.',
			'cccd.digits' => 'CCCD phải bao gồm đúng 12 chữ số.',
			'gender.required' => 'Vui lòng chọn giới tính.',
			'gender.string' => 'Giới tính phải là một chuỗi.',
			'address.required' => 'Vui lòng nhập địa chỉ.',
			'address.string' => 'Địa chỉ phải là một chuỗi.',
			'address.max' => 'Địa chỉ không được vượt quá 255 ký tự.',
			'birthDay.required' => 'Vui lòng nhập ngày sinh.',
			'birthDay.string' => 'Ngày sinh phải là một chuỗi.',
			'files.required' => 'Vui lòng tải lên ít nhất một tệp.',
			'files.array' => 'Tệp tải lên phải là một mảng.',
			'files.*.required' => 'Mỗi tệp trong danh sách đều phải được cung cấp.',
			'files.*.file' => 'Mỗi mục tải lên phải là một tệp hợp lệ.',
			'files.*.mimetypes' => 'Tệp phải có định dạng JPEG, PNG hoặc SVG.',
			'files.*.max' => 'Kích thước mỗi tệp không được vượt quá 5MB'
		]);

		if ($validator->fails()) {
			return redirect()->route('TBWebMpos360FormAuthenAccountAction', [
				'merchantId' => $request->input('merchantId'),
				'message' => $validator->errors()->first(),
				'signed' => $request->input('signed'),
			]);
		}



		$data = $request->all();
		$data['gender'] = $genderData[$data['gender']];
		$milliseconds = Carbon::createFromFormat('d/m/Y', $data['birthDay'])->valueOf();

		$postData = [
			'address' => $data['address'],
			'customerName' => $data['name'],
			'gender' => $data['gender'], // MALE, FEMALE
			'merchantName' => $data['name'],
			'mposMcId' => $data['merchantId'],
			'passport' => $data['cccd'],
			'birthDay' => $milliseconds
		];
		
		$tokenDefault = @$this->mnpOnboardNewMcHelper->getAccessTokenMcDefault();

		try {
			$r = app(Mpos360RegisterUploadAction::class)->run(
				$data['merchantId'],
				$tokenDefault,
				$data['files']
			);
	
			if (empty($r['success'])) {
				$message = 'Lỗi không upload được CCCD 2 mặt';
	
				return redirect()->route('TBWebMpos360FormAuthenAccountAction', [
					'merchantId' => $data['merchantId'],
					'message' => $message,
					'signed' => isset($data['signed']) ? $data['signed'] : ''
				]);
			}
			
			$postData['passportRepresentFrontUrl'] = Arr::first($r['success']);
			$postData['passportRepresentBackUrl'] = Arr::last($r['success']);
		}catch(\Throwable $th) {
			$message = sprintf('MNP Err: %s (Code: %s)', $th->getMessage(), $th->getCode());

			return redirect()->route('TBWebMpos360FormAuthenAccountAction', [
				'merchantId' => $data['merchantId'],
				'message' => $message,
				'signed' => isset($data['signed']) ? $data['signed'] : ''
			]);
		}
		
		$updateAccount = @$this->mnpOnboardNewMcHelper->updateThongTinMc($postData);
		
		if (!$updateAccount['result'] || $updateAccount['code'] != 1000) {
			if(isset($updateAccount['code']) && $updateAccount['code'] == 2034){
				return redirect()->route('Mpos360TingBoxKhaiBaoListInfoAction', [
					'merchantId' => $data['merchantId'],
					'signed' => encrypt(['merchantId' => $data['merchantId']])
				]);
			}
			
			$message =  isset($updateAccount['message']) && !empty($updateAccount['message']) ? $updateAccount['message'] : "Có lỗi xảy ra";
			$message = sprintf('MNP Err: %s (Code: %s)', $message, $updateAccount['code']);
			return redirect()->route('TBWebMpos360FormAuthenAccountAction', [
				'merchantId' => $data['merchantId'],
				'message' => $message,
				'signed' => isset($data['signed']) ? $data['signed'] : ''
			]);
		}

		if ($updateAccount['result'] && $updateAccount['code'] == 1000) {
			$cacheAction = @CacheAction::query()->forceCreate([
				'step' => 2,
				'reference_id' => $data['merchantId'],
				'other_data' => json_encode($data,JSON_UNESCAPED_UNICODE),
				'time_created' => now()->timestamp,
			]);

			session(['DangKyMcId' => $data['merchantId']]);
			return redirect()->route('Mpos360TingBoxKhaiBaoListInfoAction', [
				'merchantId' => $data['merchantId'],
				'signed' => encrypt(['merchantId' => $data['merchantId']])
			]);
		}
	}
} // End class
