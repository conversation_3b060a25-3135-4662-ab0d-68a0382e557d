<?php

namespace App\Modules\Merchant\Actions\Notification\Mpos360MaskNotificationAsReadAction;

use App\Exceptions\BusinessException;
use Illuminate\Support\Str;
use App\Lib\partner\MNPNOTIFY;
use App\Modules\Merchant\Requests\Notification\Mpos360MaskNotificationAsReadRequest;

class Mpos360MaskNotificationAsReadAction
{
	public function run(Mpos360MaskNotificationAsReadRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();

		$mposToken = $deviceSession->getMposToken();
		$mposEmail = $deviceSession->getMerchantEmail();

		if (
			empty($request->json('data.id_list')) 
			&& empty($request->json('data.groupCode'))
		) {
			throw new BusinessException('thông tin idlist và group không được để trống');
		}

		$params = $request->json('data');
		$params = array_filter($params, function ($item) {
			return !empty($item);
		});

		// Nếu đánh dấu đã đọc cho "ALL bản ghi hộp thư"?
		$groupCodeRequest = Str::of($params['groupCode'])->upper()->trim()->__toString();

		if ($groupCodeRequest == 'ALL') {
			unset($params['groupCode']);
		}

		mylog([
			'mposEmail' => $mposEmail, 'mposToken' => $mposToken,
			'mposToken' => $mposToken,
			'params' => $params
		]);

		$mnpNotify = new MNPNOTIFY($mposToken);

		$makrAsReadResult = $mnpNotify->markAllAsRead($deviceSession->getMerchantUserName(), $params);
		mylog(['makrAsReadResult' => $makrAsReadResult]);

		return $makrAsReadResult;
	}
} // End class