<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction;

use Exception;
use App\Lib\partner\MNP;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Setting;
use App\Modules\Merchant\Model\Mpos360ChungThuc;
use App\Modules\Merchant\DTOs\RequestChangeInfo\CreateMerchantRequestDto;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\GetMNPAccessTokenSubAction;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForBankingSA\CreateForBankingSA;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\SubAction\ValidateBankAccountSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\SubAction\MappingProfileWithMnpSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\SubAction\KiemTraQuyenTaoYeuCauThayDoiSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForRepresentationSA\CreateForRepresentationSA;

class Mpos360RequestChangeInfoUpdateProfileAction
{
	public MNP $mnp;

	public string $myNextpayId;

	public function __construct(MNP $mnp)
	{
		$this->mnp = $mnp;
		$this->myNextpayId = '';
	}

	public function run(Mpos360RequestChangeInfoUpdateProfileRequest $request)
	{
		// Build param hồ sơ để sẵn sàng đẩy sang MNP
		$profileGroup = app(MappingProfileWithMnpSubAction::class)->run($request);
		mylog(['profileGroup' => $profileGroup]);

		
		// Build DTO
		$deviceSession = $request->getCurrentDeviceSession();
		$merchantId = $deviceSession->getMerchantId();

		// Kiểm tra đã chứng thực CCCD chưa, nếu chưa thì báo lỗi luôn
		$mpos360ChungThucCccd = Mpos360ChungThuc::getChungThucCCCD($merchantId);
		if (!$mpos360ChungThucCccd) {
			throw new BusinessException(vmsg('Bạn cần làm chứng thực CCCD trước khi tạo yêu cầu đổi thông tin'));
		}

		$deviceSessionWithToken = app(GetMNPAccessTokenSubAction::class)->run($deviceSession);
		$listMcCanSpamCreateRequest = Setting::query()->where('key', 'LIST_MERCHANT_CAN_SPAM_REQUEST')->first();
		
		$emailsCanSpamCreate = [];
		
		if (!empty(optional($listMcCanSpamCreateRequest)->value)) {
			$emailsCanSpamCreate = json_decode($listMcCanSpamCreateRequest->value, true);
		}

		$merchantEmail = $deviceSession->getMerchantEmail();

		if (!in_array($merchantEmail, $emailsCanSpamCreate)) {
			$validate = app(KiemTraQuyenTaoYeuCauThayDoiSubAction::class)->run($deviceSessionWithToken, $request);
		}

		$createMerchantRequestDto = new CreateMerchantRequestDto(
			$merchantId,
			$deviceSession->getMerchantUserName(),
			json_encode([$profileGroup]),
			'{}',
			''
		);


		if ($request->isChangeThongTinNganHang()) {
			app(ValidateBankAccountSubAction::class)->run(
				$profileGroup['profiles']['bankId'],
				$profileGroup['profiles']['accountNo'],
				$profileGroup['profiles']['holderName'],
			);

			return app(CreateForBankingSA::class)->run(
				$createMerchantRequestDto,
				$request
			);
		}


		// Đổi info người đại diện
		if ($request->isChangeNguoiDaiDien()) {
			return app(CreateForRepresentationSA::class)->run(
				$createMerchantRequestDto,
				$request
			);
		}
	} // End method
} // End class
