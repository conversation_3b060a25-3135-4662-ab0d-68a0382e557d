<?php

namespace App\Modules\TingBoxVAMC\Actions\MerchantBank\LinkBankAction\SubAction;

use App\Lib\Helper;
use Illuminate\Support\Str;
use App\Lib\MnpOnboardNewMcHelper;
use App\Modules\TingBoxVAMC\Models\PlanEvent;
use App\Modules\TingBoxVAMC\Models\MerchantBank;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;

class SaveBankTrungGianSubAction
{
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public int $tries = 0;

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run(MerchantShopBank $merchantShopBank, MerchantBank $merchantBank)
	{
		$listBankingMnp = $this->mnpOnboardNewMcHelper->getNganhNgheNganHangThanhPho(true);

		$bankItem = collect($listBankingMnp['banks'])->first(function ($it) use ($merchantBank) {
			$bankCode = strtoupper($merchantBank->bank_code);
			$mnpBankCode = Helper::getBankCode($it['bankName']); 
			return trim($bankCode) == trim(strtoupper($mnpBankCode));
		});

		$result = false;

		if (!empty($bankItem['bankId'])) {
			$p = [
				'bankId' => $bankItem['bankId'],
				'mposMcId' => $merchantBank->merchant_id,
				'accountNo' => $merchantBank->account_number,
				'holderName' => Str::of($merchantBank->account_holder)->upper()->trim()->__toString(),
				'bankVerify' => true
			];

			while (!$result && $this->tries <= 2) {
				$r = $this->mnpOnboardNewMcHelper->updateThongTinBanking($p);
				if (!empty($r['result']) && isset($r['code']) && $r['code'] == 1000) {
					return $r;
				} else {
					$planEvent = PlanEvent::query()->forceCreate([
						'merchant_shop_bank_id' => $merchantShopBank->id,
						'merchant_id' => $merchantShopBank->merchant_id,
						'action' => 'UPDATEBANKMNP',
						'data' => json_encode([
							'merchant_shop_bank' => $merchantShopBank->toArray(),
							'merchant_bank' => $merchantBank->toArray(),
							'store' => '',
							'responseResult' => $r,
						], JSON_UNESCAPED_UNICODE),
						'time_created' => time(),
						'time_updated' => time(),
					]);
					
					++$this->tries;
				}
			}

			return;
		} else {
			// tries 3 lần rồi mà vẫn xuống đây thì tạo event add TKNH trung gian
			return PlanEvent::query()->forceCreate([
				'merchant_shop_bank_id' => $merchantShopBank->id,
				'merchant_id' => $merchantShopBank->merchant_id,
				'action' => 'UPDATEBANKMNP',
				'data' => json_encode([
					'merchant_shop_bank' => $merchantShopBank->toArray(),
					'merchant_bank' => $merchantBank->toArray(),
					'store' => ''
				], JSON_UNESCAPED_UNICODE),
				'time_created' => time(),
				'time_updated' => time(),
			]);
		}
	}
}
