<?php

namespace App\Modules\Merchant\Actions\Notification\Mpos360NotifySubOrUnsubAction;

use App\Exceptions\BusinessException;
use App\Lib\partner\MNPNOTIFY;
use App\Modules\Merchant\Requests\Notification\Mpos360NotifySubOrUnsubRequest;

class Mpos360NotifySubOrUnsubAction
{
	public function run(Mpos360NotifySubOrUnsubRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		$mposToken = $deviceSession->getMposToken();


		$fcmToken = $request->json('data.fcmDeviceToken');
		$mnpNotify = new MNPNOTIFY($mposToken);

		if ($request->json('data.type') == 'SUB') {
			$sub = $mnpNotify->subscribe($fcmToken);
			if (empty($sub['code']) || $sub['code'] != 200) {
				throw new BusinessException('Lỗi không subscribe được hệ thống gửi notify');
			}
			return $sub;
		}

		if ($request->json('data.type') == 'UNSUB') {
			$unSub = $mnpNotify->unSubscribe($fcmToken);
			if (empty($unSub['code']) || $unSub['code'] != 200) {
				throw new BusinessException('Lỗi không un-subscribe được hệ thống gửi notify');
			}
			return $unSub;
		}
	}
} // End class