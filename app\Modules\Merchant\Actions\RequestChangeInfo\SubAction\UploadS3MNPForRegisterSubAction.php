<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\SubAction;

use App\Lib\Logs;
use Exception;
use App\Lib\partner\MNP;
use Illuminate\Support\Str;
use Illuminate\Http\UploadedFile;
use App\Modules\Merchant\Model\DeviceSession;

class UploadS3MNPForRegisterSubAction
{
	public MNP $mnp;

	public array $uploadFileFormat = [];

	public function __construct(MNP $mnp)
	{
		$this->mnp = $mnp;
	}

	public function run(string $mnpAccessToken, $merchantId, array $uploadedFiles)
	{
		

		$returnData = [
			'success' => [],
			'error' => []
		];

		collect($uploadedFiles)->each(function (UploadedFile $file) {
			$fullFileName             = $file->getClientOriginalName();
			$fileNameWithoutExtension = pathinfo($fullFileName, PATHINFO_FILENAME);
			$extension                = $file->getClientOriginalExtension();

			$fileNameWithoutExtension = sprintf(
				'%s_%s.%s',
				Str::slug($fileNameWithoutExtension),
				Str::random(16),
				$extension
			);

			$this->uploadFileFormat[] = [
				'name'     => $fileNameWithoutExtension,
				'type'     => $file->getMimeType(),
				'tmp_name' => $file->getPathname(),
				'error'    => $file->getError(),
				'size'     => $file->getSize(),
				'ext'      => $file->getClientOriginalExtension(),
			];
		});

		
		foreach ($this->uploadFileFormat as $f) {
			(new Logs())->writeFileLog(json_encode([
				'merchantId' => $merchantId,
				'start' => now()->toDateTimeString()
			]));
			/**
			 * array:3 [
					"status" => true
					"data" => array:1 [
						0 => "https://nextpay-crm.s3-ap-southeast-1.amazonaws.com/user/mpos360/file/66c46a1b80f78268096fcd20poster_IrzmP1VgEokCK2yS.jpeg"
					]
					"message" => "Thành công"
				]
			 */
			$fileType = 'FILE';
			if (Str::contains($f['type'], 'image/')) {
				$fileType = 'IMAGE';
			}elseif (in_array($f['ext'], ['jpg', 'jpeg', 'png', 'gif', 'bmp'])) {
				$fileType = 'IMAGE';
			}else {
				$fileType = 'FILE';
			}
			
			(new Logs())->writeFileLog(json_encode([
				'f' => $f,
				'merchantId' => $merchantId,
			]));

			$uploadResult = $this->mnp->uploadFileToS3(
				$f, 
				$mnpAccessToken,
				sprintf('user/mpos360/%s/%s', $merchantId, $fileType),
				$fileType
			);

			(new Logs())->writeFileLog(json_encode([
				'merchantId' => $merchantId,
				'end' => now()->toDateTimeString()
			]));
			
			if (!empty($uploadResult['status'])) {
				foreach ($uploadResult['data'] as $attachmentUrl) {
					$returnData['success'][] = $attachmentUrl;
				}
			}

			if (empty($uploadResult['status'])) {
				$msg = sprintf('MNP Err: Lỗi upload tài liệu (Code: %s)', $uploadResult['code'] ?? '00');
				$returnData['error'][] = $uploadResult['message'];
			}
		} // end foreach

		return $returnData;
	} // End method
} // End clasds
