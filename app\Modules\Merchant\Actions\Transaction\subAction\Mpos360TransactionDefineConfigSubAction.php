<?php

namespace App\Modules\Merchant\Actions\Transaction\subAction;

use App\Lib\partner\MPOS;
use App\Lib\Helper;

class Mpos360TransactionDefineConfigSubAction
{
	const DETAIL_QUICKWITHDRAW = 'DETAIL_QUICKWITHDRAW';
	const DETAIL_WITHDRAW = 'DETAIL_WITHDRAW';
	const DETAIL_TRANSACTION = 'DETAIL_TRANSACTION';
	const DETAIL_INSTALLMENT_TRANSACTION = 'DETAIL_INSTALLMENT_TRANSACTION';
	public function requestTransDetail(array $params)
	{
		$listTransaction = (new MPOS())->getTransDetail($params);
		mylog([
			'params' => $params,
			'listTransaction' => $listTransaction
		]);
		return $listTransaction;
	}
	public function requestTransList(array $params)
	{
		// var_dump($params);
		$listTransaction = (new MPOS())->getTrans($params);
		mylog([
			'params' => $params,
			'listTransaction' => $listTransaction
		]);
		return $listTransaction;
	}

	public function getTypeCard($value)
	{
		$ex = explode('_', $value);
		return $ex[0];
	}
	public function getStatusTrans()
	{
		return  [
			// ['value' => '97', 'label' => 'Thất bại', 'text_color' => '#ffffff', 'bg_color' => '#DA2128', 'display_type' => 'pills'],
			['value' => '98', 'label' =>trans_choice_fallback('trans.status.98', 'Đang xử lý'), 'text_color' => '#ffffff', 'bg_color' => '#E99323', 'display_type' => 'pills'],
			['value' => '99', 'label' => trans_choice_fallback('trans.status.99', 'Hoàn trả'), 'text_color' => '#ffffff', 'bg_color' => '#E923DC', 'display_type' => 'pills'],
			['value' => '100', 'label' => trans_choice_fallback('trans.status.100', 'Thành công'), 'text_color' => '#ffffff', 'bg_color' => '#3BB54A', 'display_type' => 'pills'],
			['value' => '101', 'label' => trans_choice_fallback('trans.status.101', 'Đảo'), 'text_color' => '#ffffff', 'bg_color' => '#23C5E9', 'display_type' => 'pills'],
			['value' => '102', 'label' => trans_choice_fallback('trans.status.102', 'Bị từ chối'), 'text_color' => '#ffffff', 'bg_color' => '#DA2128', 'display_type' => 'pills'],
			['value' => '103', 'label' => trans_choice_fallback('trans.status.103', 'Chờ chữ ký chủ thẻ'), 'text_color' => '#ffffff', 'bg_color' => '#E95E23', 'display_type' => 'pills'],
			['value' => '104', 'label' => trans_choice_fallback('trans.status.104', 'Đã kết toán'), 'text_color' => '#ffffff', 'bg_color' => '#008BF4', 'display_type' => 'pills'],
			['value' => '105', 'label' => trans_choice_fallback('trans.status.105', 'Đang chờ hoàn thành'), 'text_color' => '#ffffff', 'bg_color' => '#EE9C00', 'display_type' => 'pills'],
			['value' => '106', 'label' => trans_choice_fallback('trans.status.106', 'Đã thanh toán'), 'text_color' => '#ffffff', 'bg_color' => '#3023E9', 'display_type' => 'pills'],
		];
	}
	public function getWithdrawStatusTrans()
	{
		return  [
			// ['value' => 'ACTIVE', 'label' => 'Hoạt động', 'text_color' => '#ffffff', 'bg_color' => '#3BB54A', 'display_type' => 'pills'],
			['value' => 'APPROVED', 'label' => trans_choice_fallback('trans.withdraw_status.APPROVED', 'Đã duyệt'), 'text_color' => '#ffffff', 'bg_color' => '#008BF4', 'display_type' => 'pills'],
			// ['value' => 'CANCEL', 'label' => 'Đã hủy', 'text_color' => '#ffffff', 'bg_color' => '#EE9C00', 'display_type' => 'pills'],
			['value' => 'DENIED', 'label' => trans_choice_fallback('trans.withdraw_status.DENIED', 'Bị từ chối'), 'text_color' => '#ffffff', 'bg_color' => '#EE9C00', 'display_type' => 'pills'],
			['value' => 'PENDING', 'label' => trans_choice_fallback('trans.withdraw_status.PENDING','Chờ duyệt chi'), 'text_color' => '#ffffff', 'bg_color' => '#EE9C00', 'display_type' => 'pills'],
			['value' => 'HAS_BALANCE', 'label' => trans_choice_fallback('trans.withdraw_status.HAS_BALANCE', 'Đã thanh toán'), 'text_color' => '#ffffff', 'bg_color' => '#3BB54A', 'display_type' => 'pills'],
		];
	}
	public function getWithdrawStatusTrans2()
	{
		return  [
			// ['value' => 'ACTIVE', 'label' => 'Hoạt động', 'text_color' => '#ffffff', 'bg_color' => '#3BB54A', 'display_type' => 'pills'],
			// ['value' => 'APPROVED', 'label' => 'Đã duyệt', 'text_color' => '#ffffff', 'bg_color' => '#3BB54A', 'display_type' => 'pills'],
			// ['value' => 'CANCEL', 'label' => 'Đã hủy', 'text_color' => '#ffffff', 'bg_color' => '#EE9C00', 'display_type' => 'pills'],
			// ['value' => 'DENIED', 'label' => 'Bị từ chối', 'text_color' => '#ffffff', 'bg_color' => '#EE9C00', 'display_type' => 'pills'],
			['value' => 'CHUA_THANH_TOAN', 'label' => trans_choice_fallback('trans.withdraw_status.CHUA_THANH_TOAN', 'Chưa thanh toán'), 'text_color' => '#ffffff', 'bg_color' => '#EE9C00', 'display_type' => 'pills'],
			['value' => 'HAS_BALANCE', 'label' => trans_choice_fallback('trans.withdraw_status.HAS_BALANCE', 'Đã thanh toán'), 'text_color' => '#ffffff', 'bg_color' => '#3BB54A', 'display_type' => 'pills'],
		];
	}
	public function getQuickWithdrawStatusTrans()
	{
		return  [
			['value' => 'APPROVAL', 'label' => __('trans.quick_withdraw_order_status.APPROVAL'), 'text_color' => '#ffffff', 'bg_color' => '#008BF4', 'display_type' => 'pills'],
			['value' => 'DENIED', 'label' => __('trans.quick_withdraw_order_status.DENIED'), 'text_color' => '#ffffff', 'bg_color' => '#DA2128', 'display_type' => 'pills'],
			['value' => 'CANCEL', 'label' => __('trans.quick_withdraw_order_status.CANCEL'), 'text_color' => '#ffffff', 'bg_color' => '#DA2128', 'display_type' => 'pills'],
			['value' => 'PENDING', 'label' => __('trans.quick_withdraw_order_status.PENDING'), 'text_color' => '#ffffff', 'bg_color' => '#EE9C00', 'display_type' => 'pills'],
			['value' => 'SUCCESS', 'label' => __('trans.quick_withdraw_order_status.SUCCESS'), 'text_color' => '#ffffff', 'bg_color' => '#3BB54A', 'display_type' => 'pills'],
		];
	}
	public function getStatusInstallment()
	{
		return  [
			['value' => 'APPROVED_MAIL', 'label' => trans_choice_fallback('trans.installment.status_approved', 'Đã tiếp sửa'), 'text_color' => '#ffffff', 'bg_color' => '#E99323', 'display_type' => 'pills'],
			['value' => 'SENT', 'label' => trans_choice_fallback('trans.installment.status_sent', 'Đã đăng ký TG'), 'text_color' => '#ffffff', 'bg_color' => '#008BF4', 'display_type' => 'pills'],
			['value' => 'DENIED', 'label' => trans_choice_fallback('trans.installment.status_denied', 'Bị từ chối'), 'text_color' => '#ffffff', 'bg_color' => '#D12D29', 'display_type' => 'pills'],
			['value' => 'SUCCESS', 'label' => trans_choice_fallback('trans.installment.status_success','Thành công'), 'text_color' => '#ffffff', 'bg_color' => '#73AE4A', 'display_type' => 'pills'],
		];
	}
	public function getMethodInstallMent()
	{
		return [
			[
				'label' => trans_choice_fallback('trans.all','Tất cả'),
				'value' => 'ALL',
			],

			[
				'label' => trans_choice_fallback('trans.installment.method_cardswipe','Quẹt thẻ trả góp'),
				'value' => 'INSTALLMENT_CARD_SWIPE',
			],

			[
				'label' => trans_choice_fallback('trans.installment.method_REMOTE_GENRERATE_LINK','Tạo link từ xa'),
				'value' => 'REMOTE_GENRERATE_LINK',
			],
		];
	}
	public function getTransInstallmentMethodCode($value)
	{
		if (isset($value['paymentMethod'])) {
			if ($value['paymentMethod'] == 'Thẻ') {
				return 'INSTALLMENT_CARD_SWIPE';
			} elseif ($value['paymentMethod'] == 'QR Code') {
				return 'INSTALLMENT_QR_CODE';
			} elseif ($value['paymentMethod'] == 'Nhập thẻ') {
				return 'INSTALLMENT_REMOTE_GENRERATE_LINK';
			} else {
				return 'INSTALLMENT_QR_EWALLET';
			}
		}
		return '';
	}
	public function getPaymentMethod()
	{
		return [
			['value' => 'ALL', 'label' => trans_choice_fallback('trans.all', 'Tất cả')],
			['value' => 'CARDS_WIPE', 'label' => trans_choice_fallback('trans.nomarl.CARDS_WIPE', 'Qụet thẻ')],
			['value' => 'REMOTE_GENRERATE_LINK', 'label' => trans_choice_fallback('trans.nomarl.REMOTE_GENRERATE_LINK', 'Tạo link từ xa')],
			// ['value' => 'QR_CODE', 'label' => trans_choice_fallback('trans.nomarl.QR_CODE','QR Code')],
			// ['value' => 'QR_EWALLET', 'label' => trans_choice_fallback('trans.nomarl.QR_EWALLET', 'QR Ví điện tử')],
		];
	}

	public function getPaymentMethodCode($value)
	{
		if (isset($value['paymentMethod'])) {
			if ($value['paymentMethod'] == 'Thẻ') {
				return 'CARDS_WIPE';
			} elseif ($value['paymentMethod'] == 'QR Code') {
				return 'QR_CODE';
			} elseif ($value['paymentMethod'] == 'Nhập thẻ') {
				return 'REMOTE_GENRERATE_LINK';
			} elseif ($value['paymentMethod'] == 'Số dư ví DV') {
				return 'QR_EWALLET';
			} else {
				return 'dưới dev với có';
			}
		}
		return '';
	}

	
	
	public function __convertData($data, $key, $type)
	{
		if (isset($data[$key]) && $data[$key]) {
			if ($type == 'datetime') {
				return $this->__convertTime($data[$key]);
			}
			if ($type == 'amount') {
				return $this->__convertAmout($data[$key]);
			}
		}
		if ($type == 'amount') {
			return '0 VND';
		}
		return '';
	}
	private function __convertAmout($amount)
	{
		try {
			return Helper::numberFormat($amount) . ' VND';
		} catch (\Throwable $th) {
			return $amount . ' VND';
		}
	}
	private function __convertTime($time)
	{
		try {
			if ($time) {
				$strtotime = strtotime($time);
				return date('H:i d-m-Y', $strtotime);
			}
			return '';
		} catch (\Throwable $th) {
			return $time;
		}
	}
} // End class
