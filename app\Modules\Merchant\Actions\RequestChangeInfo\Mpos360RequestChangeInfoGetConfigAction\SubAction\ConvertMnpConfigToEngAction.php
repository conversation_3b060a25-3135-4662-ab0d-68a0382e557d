<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoGetConfigAction\SubAction;

use Illuminate\Support\Str;


/**
 * array:3 [
  "status" => true
  "data" => array:12 [
    "typeChangeInfo" => array:2 [
      0 => array:2 [
        "code" => "CHANGE_BANK_ACCOUN_INFO"
        "name" => "Thay đổi thông tin tài khoản ngân hàng nhận tiền"
      ]
      1 => array:2 [
        "code" => "CHANGE_REPRESENT_INFO"
        "name" => "Thay đổi thông tin người đại diện kí hợp đồng với bên VIMO"
      ]
    ]
    "documentLost" => array:3 [
      0 => array:2 [
        "code" => "LOST_PASSPORT"
        "name" => "MC không còn giữ CMT/CCCD cũ"
      ]
      1 => array:2 [
        "code" => "LOST_MOBILE"
        "name" => "MC không còn giữ số điện thoại cũ"
      ]
      2 => array:2 [
        "code" => "LOST_EMAIL"
        "name" => "MC không còn giữ email cũ"
      ]
    ]
    "cities" => array:63 [
      "vn-lan" => "Long An"
      "vn-hag" => "Hậu Giang"
      "vn-nan" => "Nghệ An"
			...
    ]
    "typeChangeRepresentInfo" => array:2 [
      0 => array:2 [
        "code" => "CHANGE_NEW_REPRESENT"
        "name" => "Thay đổi người đại diện mới"
      ]
      1 => array:2 [
        "code" => "CHANGE_CURRENT_REPRESENT_INFO"
        "name" => "Thay đổi thông tin người đại diện hiện tại"
      ]
    ]
    "mcBankAccType" => array:2 [
      0 => array:2 [
        "code" => "MC_COMPANY"
        "name" => "Công ty"
      ]
      1 => array:2 [
        "code" => "MC_INDIVIDUAL"
        "name" => "Cá nhân"
      ]
    ]
    "banks" => array:60 [
      "5fb22b462cc8fd12d0623356" => "DAB - DONGABANK - NH TMCP ĐÔNG Á"
      "5fb22b492cc8fd12d0623395" => "TCB - TECHCOMBANK - NH TMCP KỸ THƯƠNG VN"
      ...
    ]
    "imageExt" => "jpg|jpeg|png|gif|bmp|svg|webp|tiff|heic|heif"
    "signForm" => array:2 [
      0 => array:2 [
        "code" => "E_CONTRACT"
        "name" => "Ký điện tử"
      ]
      1 => array:2 [
        "code" => "PAPER_CONTRACT"
        "name" => "Ký giấy"
      ]
    ]
    "signProcess" => array:3 [
      0 => array:2 [
        "code" => "E_CONTRACT"
        "name" => "Ký điện tử"
      ]
      1 => array:2 [
        "code" => "PAPER_CONTRACT"
        "name" => "Ký phụ lục giấy(Tải file mẫu yêu cầu thay đổi thông tin, điền, đóng dấu và tải lên)"
      ]
      2 => array:2 [
        "code" => "SALE_SUPPORT"
        "name" => "Gửi yêu cầu chuyên viên chăm sóc của mMpos liên hệ, hỗ trợ ký phụ lục"
      ]
    ]
    "stateChangeDetailInfo" => array:8 [
      0 => array:2 [
        "code" => "NEW"
        "name" => "Mới tạo"
      ]
      1 => array:2 [
        "code" => "SIGNING"
        "name" => "Đang ký"
      ]
      2 => array:2 [
        "code" => "SIGNED_WAITING_APPROVED"
        "name" => "Đã ký - Chờ duyệt"
      ]
      3 => array:2 [
        "code" => "ADDITIONAL"
        "name" => "Cần bổ sung"
      ]
      4 => array:2 [
        "code" => "WAITING_SUPPORT"
        "name" => "Chờ hỗ trợ"
      ]
      5 => array:2 [
        "code" => "CANCELED"
        "name" => "Đã hủy"
      ]
      6 => array:2 [
        "code" => "REJECTED"
        "name" => "Bị từ chối"
      ]
      7 => array:2 [
        "code" => "COMPLETE"
        "name" => "Hoàn thành"
      ]
    ]
    "mcc" => []
    "stateChangeOverview" => array:4 [
      0 => array:2 [
        "code" => "WAITING_PROCESS"
        "name" => "Chờ xử lý"
      ]
      1 => array:2 [
        "code" => "COMPLETE"
        "name" => "Hoàn thành"
      ]
      2 => array:2 [
        "code" => "CANCELED"
        "name" => "Đã hủy"
      ]
      3 => array:2 [
        "code" => "REJECTED"
        "name" => "Bị từ chối"
      ]
    ]
  ]
  "message" => "Thành công"
]
 */
class ConvertMnpConfigToEngAction
{

	public function run(&$mnpConfig=[])
	{
		// tinh thanh
		foreach ($mnpConfig['data']['cities'] as &$city) {
			$cityName = Str::of($city)->slug(' ')->trim()->__toString();
			$city = ucwords($cityName);
		}

		// banks
		foreach ($mnpConfig['data']['banks'] as &$bank) {
			$explode = explode('-', $bank);

			foreach ($explode as &$e) {
				$e = Str::of($e)->slug(' ')->upper()->trim()->__toString();
			}

			$bankName = sprintf(
				'%s - %s - %s', 

				trim($explode[0]),
				trim($explode[1] ?? ''), 
				trim($explode[2] ?? '')
			);

			$bank = rtrim($bankName, ' - ');
		}

		
		return $mnpConfig;
	}
} // End class
