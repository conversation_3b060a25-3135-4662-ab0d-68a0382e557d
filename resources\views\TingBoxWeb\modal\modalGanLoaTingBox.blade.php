<div 	class="modal action-sheet"
			id="modalGanLoaTingBox"
			tabindex="-1"
			role="dialog"
			aria-labelledby="exampleModalCenterTitle"
			aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Gán loa TingBox</h5>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>


			<div class="modal-body">
				<div class="mpos360-scan mpos360-scan-qr">
					<span><img src="{{ cumtomAsset('assets/img/qr-tingbox.png') }}"></span>
				</div>
				<p class="cl890 text-center mt-3"><PERSON><PERSON> được dán ở mặt sau thiết bị của bạn. </p>

				<div class="form-floating mb-3">
					<input type="text" class="form-control add_serialId" id="floatingtk" placeholder="" value="" id="maThietBiMuonGan" onkeyup="return handleKeyUpThietBi(event)">
					<label for="floatingtk">Nhập mã Serial thiết bị TingBox</label>
					<div class="error-message text-danger mt-1" id="add-error-serialId"></div>
				</div>

				<p class="mt-4 mb-0">
					<button type="button" class="btn text-center btn-blue btn-success w-100 d-block submit_ting_box d-flex align-items-center justify-content-center" onclick="return onAddThietBi(this)">
						<div class="spinner-border d-none" role="status" id="spinnerGanThietBi">
							<span class="visually-hidden">Loading...</span>
						</div>

						<span id="ganThietBiActionName">Gán thiết bị</span>
					</button>
				</p>
			</div>
		</div>
	</div>
</div>

@push('jsBot')
<script>
	function handleKeyUpThietBi(event) {
		const input = event.target;
		const value = input.value;

		// Xóa tất cả ký tự không phải số hoặc chữ cái
		const sanitizedValue = value.replace(/[^a-zA-Z0-9]/g, '');

		// Chuyển chữ thường thành chữ in hoa
		const uppercaseValue = sanitizedValue.toUpperCase();

		// Cập nhật lại giá trị của ô input
		input.value = uppercaseValue;
}

	var $add_serialId = $('.add_serialId');
	var $add_errorSerialId = $('#add-error-serialId');

	function validateSerial() {
		const add_serialId = $add_serialId.val().trim();
		if (add_serialId === "") {
			$add_errorSerialId.text("Bạn cần nhập mã Serial thiết bị TingBox");
			$add_serialId.addClass("is-invalid");
		} else {
			$add_errorSerialId.text("");
			$add_serialId.removeClass("is-invalid");
		}
	}
	$add_serialId.on("input", validateSerial);

	function onAddThietBi(element) {

		validateSerial();

		var params = {
			serial: $('#floatingtk').val().trim(),
			mposMcId: "{{ $request->get('merchantId') }}",
			areaId: $('#areaId').attr('data-areaId'),
		};

		if ($add_errorSerialId.text() === "") {
			// $(element).attr('disabled', true);
			// $('#spinnerGanThietBi').addClass('d-block');
			// $('#ganThietBiActionName').hide();
			$('#loadingPage').addClass('se-pre-con');
			$.post('/Mpos360TingBoxKhaiBaoThietBi', params).then(function (res) {
				$('#loadingPage').removeClass('se-pre-con');
				if(res.success){
					alert("Loa TingBox của Quý khách đã được kích hoạt thành công. Để bắt đầu nhận thanh toán, vui lòng bật nguồn thiết bị và kết nối loa TingBox với mạng Internet. Sau khi hoàn tất, Quý khách có thể ngay lập tức sử dụng loa TingBox để nhận thanh toán một cách tiện lợi và hiệu quả.");
					return location.reload();
				}else{
					alert(res.message);
				}
				
			}).always(function () {
				// $('#spinnerGanThietBi').removeClass('d-block');
				// $('#ganThietBiActionName').show();
				$('#modalGanLoaTingBox').modal('hide');
				// $(element).removeAttr('disabled');
			});
		}

	}
</script>
@endpush