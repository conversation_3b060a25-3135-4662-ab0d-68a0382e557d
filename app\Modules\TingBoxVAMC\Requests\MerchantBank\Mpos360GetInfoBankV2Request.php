<?php

namespace App\Modules\TingBoxVAMC\Requests\MerchantBank;

use Illuminate\Foundation\Http\FormRequest;

class Mpos360GetInfoBankV2Request extends FormRequest
{
	public function rules(): array
	{
		return [
			'data.merchantId' => ['required', 'numeric'],
			'data.username' => ['required', 'string']
		];
	}

	public function messages(): array
	{
		return [
			'data.merchantId.required' => 'Mã <PERSON> không được để trống',
			'data.merchantId.numeric' => 'Mã <PERSON> phải là kiểu số',
			'data.username.required' => 'Username là bắt buộc',
			'data.username.string' => 'Username phài là kiểu chuỗi'
		];
	}
}

