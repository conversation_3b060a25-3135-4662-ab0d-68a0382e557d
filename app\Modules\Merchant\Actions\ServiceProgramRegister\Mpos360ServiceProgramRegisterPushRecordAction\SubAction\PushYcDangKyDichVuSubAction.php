<?php

namespace App\Modules\Merchant\Actions\ServiceProgramRegister\Mpos360ServiceProgramRegisterPushRecordAction\SubAction;

use App\Exceptions\BusinessException;
use Exception;
use App\Lib\partner\MNPServiceProgram;
use App\Modules\Merchant\Model\Mpos360MerchantRequestServiceProgramRegister;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestCronPushRecordAction\SubAction\GetMnpTokenByMerchantIdSubAction;

class PushYcDangKyDichVuSubAction
{
	public MNPServiceProgram $mnpServiceProgram;

	public function __construct(MNPServiceProgram $mnpServiceProgram)
	{
		$this->mnpServiceProgram = $mnpServiceProgram;
	}

	public function run(Mpos360MerchantRequestServiceProgramRegister $rq)
	{
		$mnpToken = app(GetMnpTokenByMerchantIdSubAction::class)->run($rq->merchant_id);

		if (empty($mnpToken)) {
			throw new BusinessException('Lỗi không có thông tin mnp token');
		}

		$pushYcResult = $this->mnpServiceProgram->sendRequest(
			'/api/scon/partner/sign',
			'POST', 
			[
				'mposId' => $rq->merchant_id,
				'signature' => $rq->getSignatureUrl(),
			],
			$mnpToken
		);

		return $pushYcResult;
	}
} // End class