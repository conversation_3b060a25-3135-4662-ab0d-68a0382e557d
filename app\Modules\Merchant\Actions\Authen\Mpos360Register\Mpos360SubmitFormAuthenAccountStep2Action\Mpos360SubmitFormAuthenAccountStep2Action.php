<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360SubmitFormAuthenAccountStep2Action;

use Carbon\Carbon;
use Illuminate\Support\Str;
use App\Lib\MnpOnboardNewMcHelper;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360Register\Mpos360SubmitFormAuthenAccountStep2ActionRequest;

class Mpos360SubmitFormAuthenAccountStep2Action
{
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run(Mpos360SubmitFormAuthenAccountStep2ActionRequest $request): array
	{
		$data = $request->json('data');

		// Kiểm tra xem MC hiện tại đã có thông tin TKNH trung gian chưa -> có rồi thì nghỉ không cho update
		$detaiMc = $this->mnpOnboardNewMcHelper->detailMc([
			'mposMcId' => $data['merchant_id']
		]);

		if (empty($detaiMc['data'])) {
			throw new BusinessException($detaiMc['message']);
		}


		if (!empty($detaiMc['data']['bankId'])) {
			$message = 'Bạn đã khai báo tài khoản ngân hàng nhận tiền. Không thể cập nhật lại CCCD';
			throw new BusinessException($message);
		}

		if ($this->hasTknhTrucTiep($data['merchant_id'])) {
			throw new BusinessException('Bạn đã liên kết tài khoản ngân hàng trực tiếp. Không thể cập nhật lại CCCD');
		}

		$postData = [
			'address' => $data['address'],
			'customerName' => $data['fullname'],
			'gender' => $data['gender'],
			'merchantName' => $data['fullname'],
			'mposMcId' => $data['merchant_id'],
			'passport' => $data['cccd'],
			'birthDay' => Carbon::createFromFormat('d/m/Y', $data['birthday'])->startOfDay()->getTimestampMs(),
			'passportRepresentFrontUrl' => $data['cccd_image_before'],
			'passportRepresentBackUrl' => $data['cccd_image_after'],
			'qrDisplayName' => Str::of($data['fullname'])->slug(' ')->upper()->trim()->__toString()
		];

		$updateAccount = $this->mnpOnboardNewMcHelper->updateThongTinMc($postData);

		if (!$updateAccount['result'] || $updateAccount['code'] != 1000) {
			$message =  isset($updateAccount['message']) && !empty($updateAccount['message']) ? $updateAccount['message'] : "Có lỗi xảy ra";
			$message = sprintf('MNP Err: %s (Code: %s)', $message, $updateAccount['code']);
			throw new BusinessException($message);
		}


		return [
			'can' => Mpos360Enum::CAN_GO_TO_FORM_NHAN_THANH_TOAN,
			'status' => 'SUCCESS',
			'message' => $updateAccount['message'] ?? 'Thành công'
		];
	}

	public function hasTknhTrucTiep($mcId): bool {
		$count = MerchantShopBank::query()->where('merchant_id', $mcId)
																			->where('account_type', MerchantShopBank::LOAI_TK_TRUC_TIEP)		
																			->where('status_link', MerchantShopBank::STT_LINK_DA_LIEN_KET)
																			->count();
		return $count > 0;
	}
} // End class
