<?php

namespace App\Modules\Merchant\Actions\Banner\Mpos360GetMyBannersAction;

use App\Lib\Mpos360UrlHelper;
use App\Modules\Merchant\Model\CacheAction;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Model\Setting;

class Mpos360GetMyBannersAction
{
	public bool $isChoThietLapThongTin = false;

	public function run(DeviceSession $deviceSession)
	{
		$banners = [
			[
				'id' => '26',
				'name' => 'Thiết bị thanh toán',
				'img' => 'https://mpos.vn/public/media?fpath=MjAyMzEwMDktQURNSU4tbXBvczoxMDAwLTAtYmFubmVyX2FwcF9taW5fMV8=.jpeg',
				'description' => '',
				'type' => 'webviewUrl',
				'target' => 'webviewUrl',
				'value' => [
					'appScreen' => [
						'screenName' => '',
						'params' => (object) [],
					],
					'webviewUrl' => 'https://chuyendoiso.nextpay.vn/danh-muc/mpos-guide/thiet-bi-thanh-toan/',
					'browserUrl' => 'https://chuyendoiso.nextpay.vn/danh-muc/mpos-guide/thiet-bi-thanh-toan/'
				],
			],
		];

		if ($deviceSession->isChoThietLapThongTin()) {
			$this->isChoThietLapThongTin = true;
			// $banners[] = $this->__getBannerDangKy($deviceSession);
		}

		if (!$this->isChoThietLapThongTin) {
			$listActionAction = CacheAction::query()->where('reference_id', $deviceSession->getMerchantId())
																				 			->get();

			// Có trong luồng đăng ký thì add thêm banner
			if ($listActionAction->isNotEmpty()) {
				$this->isChoThietLapThongTin = true;
				// $banners[] = $this->__getBannerDangKy($deviceSession);
			}
		}
		

		$settingUserDuyetApp = Setting::query()->firstWhere(['key' => 'LIST_USERNAME_CAN_GO_HOME_AFTER_LOGIN']);
		if (!$settingUserDuyetApp) {
			$banners = collect($banners)->filter(function ($bn) {
				return $bn['id'] == '26';
			})->values()->all();
		}

		$listUserDuyetApp = json_decode($settingUserDuyetApp->value, true);
		$currentUsername = $deviceSession->getMerchantUserName();
		if (in_array($currentUsername, $listUserDuyetApp)) {
			$banners = collect($banners)->filter(function ($bn) {
				return $bn['id'] == '26';
			})->values()->all();
		}else {
			if ($this->isChoThietLapThongTin) {
				$banners = collect($banners)->filter(function ($bn) {
					return $bn['id'] == '1';
				})->values()->all();
			}

			$banners[] = [
				'id' => '1001', 
				'name' => 'CHÍNH THỨC RA MẮT LOA THÔNG BÁO CHUYỂN KHOẢN TINGBOX VER 2025',
				'img' => "https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/test/67c828fa4465eb435f2b1956photo_2025-03-05_17-28-40.jpg",
				'description' => '',
				'type' => 'webviewUrl',
				'target' => 'webviewUrl',
				'value' => [
					'appScreen' => [
						'screenName' => '',
						'params' => (object) [],
					],
					'webviewUrl' => 'https://mpos.vn/tin-tuc/chinh-thuc-ra-mat-loa-thong-bao-chuyen-khoan-tingbox-ver-2025',
					'browserUrl' => 'https://mpos.vn/tin-tuc/chinh-thuc-ra-mat-loa-thong-bao-chuyen-khoan-tingbox-ver-2025'
				],
			];

			$banners[] = [
				'id' => '1000', 
				'name' => 'THÔNG BÁO: ỨNG DỤNG mPOS360 CHÍNH THỨC ĐỔI TÊN THÀNH TINGBOX.VN',
				'img' => "https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/test/67be72b54465eb435f2a46e4DoiTenUngDungThanhTingBoxVn.jpg",
				'description' => '',
				'type' => 'webviewUrl',
				'target' => 'webviewUrl',
				'value' => [
					'appScreen' => [
						'screenName' => '',
						'params' => (object) [],
					],
					'webviewUrl' => 'https://mpos.vn/tin-tuc/thong-bao-ung-dung-mpos360-chinh-thuc-doi-ten-thanh-tingboxvn',
					'browserUrl' => 'https://mpos.vn/tin-tuc/thong-bao-ung-dung-mpos360-chinh-thuc-doi-ten-thanh-tingboxvn'
				],
			];
	
			$banners[] = [
				'id' => '999',
				'name' => 'THANH TOÁN VISA - VI VU NEW ZEALAND CỰC ĐÃ',
				'img' => 'https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/test/67aaf2c24465eb435f28a178ThanhToanVISA_VivuCucDa.jpg',
				'description' => '',
				'type' => 'webviewUrl',
				'target' => 'webviewUrl',
				'value' => [
					'appScreen' => [
						'screenName' => '',
						'params' => (object) [],
					],
					'webviewUrl' => 'https://mpos.vn/tin-tuc/thanh-toan-visa-vi-vu-new-zealand-cuc-da',
					'browserUrl' => 'https://mpos.vn/tin-tuc/thanh-toan-visa-vi-vu-new-zealand-cuc-da'
				],
			];
		}
		

		$banners = collect($banners)->sortByDesc(function ($it) {
			return $it['id'];
		})->values()->all();

		$returnData = [
			'position' => 'home',
			'banners' => $banners
		];

		return $returnData;
	}

	private function __getBannerDangKy(DeviceSession $deviceSession): array {
		return [
			'id' => '1',
			'name' => 'Gán thiết bị TingBox',
			'img' => cumtomAsset('images/home/<USER>/GanThietBiTbNewBanner.png'),
			'description' => '',
			'type' => 'browserUrl',
			'target' => 'browserUrl',
			'value' => [
				'appScreen' => [
					'screenName' => 'transactionDetail',
					'params' => [
						'transactionId' => '123'
					],
				],
				'webviewUrl' => Mpos360UrlHelper::buildRouteSecurity('TBWebMpos360FormAuthenAccountAction', [
					'merchantId' => $deviceSession->getMerchantId()
				]),
				'browserUrl' => Mpos360UrlHelper::buildRouteSecurity('TBWebMpos360FormAuthenAccountAction', [
					'merchantId' => $deviceSession->getMerchantId()
				])
			]
		];
	}
} // End class