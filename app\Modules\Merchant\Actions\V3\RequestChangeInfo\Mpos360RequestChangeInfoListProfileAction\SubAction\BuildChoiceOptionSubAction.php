<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction;

class BuildChoiceOptionSubAction
{
	/**
	 * Mặc định: khi đổi thông tin người đại diện thì có 3 option:
	 * 1-đổi người đại diện mới
	 * 2-đổi cccd mới
	 * 3-đổi thông tin liên hệ (option này muốn làm thì lúc get detail ra phải có giá trị cccd trước mới ko bị lỗi)
	 * 
	 * Class này sẽ check xem lúc get detail profiles đã có hay chưa
	 * 
	 * @param array $mnpMerchantDetail
	 * array:3 [
			"status" => true
			"data" => array:26 [
				"bank" => array:8 [
					"editable" => 1
					"display" => "ABB - ABBANK - NH TMCP AN BÌNH"
					"format" => "TEXT"
					"position" => 0
					"label" => "Ngân hàng"
					"type" => "DROPDOWN"
					"value" => "5fb22b472cc8fd12d0623364"
					"group" => "CHANGE_BANK_ACCOUN_INFO"
				]
				"passport" => array:8 [
					"editable" => 1
					"display" => "***********"
					"format" => "TEXT"
					"position" => 0
					"label" => "Số CMND/CCCD/Hộ chiếu"
					"type" => "INPUT"
					"value" => "***********"
					"group" => "CHANGE_REPRESENT_INFO"
				]
				....
			]
			"message" => "DO_SERVICE_SUCCESS"
		]
	 */
	public function run(string $groupInfoCode = 'CHANGE_BANK_ACCOUN_INFO', array $mnpMerchantDetail): array
	{
		$loaiMc = $this->__getLoaiMc($mnpMerchantDetail);

		if ($groupInfoCode == 'CHANGE_BANK_ACCOUN_INFO') {
			if ($loaiMc == 'HO_KINH_DOANH') {
				$choice = [
					[
						'code' => 'DOI_STK_KHAC_CUA_HKD',
						'sub_choice' => '',
						'name' => vmsg('Đổi số TKNH khác của hộ kinh doanh'),
						'desc' => vmsg('Cùng người đại diện pháp luật trên ĐKKD')
					],

					[
						'code' => 'DOI_SO_TKNH_CA_NHAN_DUOC_HKD_UY_QUYEN',
						'sub_choice' => '',
						'name' => vmsg('Đổi số TKNH cá nhân được ủy quyền'),
						'desc' => vmsg('Cung cấp giấy tờ của người mới & giấy ủy quyền')
					],
				];

				return $choice;
			}

			// Doanh nghiep
			$choice = [
				[
					'code' => 'DOI_STK_KHAC_CUA_DOANH_NGHIEP',
					'sub_choice' => '',
					'name' => vmsg('Đổi số TKNH khác của doanh nghiệp'),
					'desc' => vmsg('Cùng người đại diện pháp luật trên ĐKKD')
				],

				[
					'code' => 'DOI_SO_TKNH_CA_NHAN_DUOC_DOANH_NGHIEP_UY_QUYEN',
					'sub_choice' => '',
					'name' => vmsg('Đổi số TKNH cá nhân được ủy quyền'),
					'desc' => vmsg('Cung cấp giấy tờ của người mới & giấy ủy quyền')
				],
			];

			return $choice;
		}

		if ($groupInfoCode == 'CHANGE_REPRESENT_INFO') {
			if ($loaiMc == 'HO_KINH_DOANH') {
				$choice = [
					[
						'code' => 'DOI_NGUOI_DAI_DIEN_MOI',
						'sub_choice' => 'DOI_NGUOI_DAI_DIEN_MOI_HKD',
						'name' => vmsg('Đổi người đại diện khác'),
						'desc' => vmsg('Cung cấp CCCD của người đại diện mới & giấy ủy quyền')
					],
	
					[
						'code' => 'DOI_CCCD_MOI',
						'sub_choice' => 'DOI_CCCD_MOI_HKD',
						'name' => vmsg('Đổi/Cập nhật CCCD mới'),
						'desc' => vmsg('Của người đại diện hiện tại, khi làm mới hoặc cấp lại giấy tờ')
					],
	
					[
						'code' => 'DOI_THONG_TIN_LIEN_HE',
						'sub_choice' => 'DOI_THONG_TIN_LIEN_HE_HKD',
						'name' => vmsg('Đổi thông tin liên hệ'),
						'desc' => vmsg('Thông tin người đại diện hiện tại')
					]
				];
			}

			if ($loaiMc == 'DOANH_NGHIEP') {
				$choice = [
					[
						'code' => 'DOI_NGUOI_DAI_DIEN_MOI',
						'sub_choice' => 'DOI_NGUOI_DAI_DIEN_MOI_DN',
						'name' => vmsg('Đổi người đại diện khác'),
						'desc' => vmsg('Cung cấp CCCD của người đại diện mới & giấy ủy quyền')
					],
	
					[
						'code' => 'DOI_CCCD_MOI',
						'sub_choice' => 'DOI_CCCD_MOI_DN',
						'name' => vmsg('Đổi/Cập nhật CCCD mới'),
						'desc' => vmsg('Của người đại diện hiện tại, khi làm mới hoặc cấp lại giấy tờ')
					],
	
					[
						'code' => 'DOI_THONG_TIN_LIEN_HE',
						'sub_choice' => 'DOI_THONG_TIN_LIEN_HE_DN',
						'name' => vmsg('Đổi thông tin liên hệ'),
						'desc' => vmsg('Thông tin người đại diện hiện tại')
					]
				];
			}
			

			return $choice;
		}

		if ($groupInfoCode == 'CHANGE_MPOS_ACCOUNT_INFO') {
			$choice = [];
			return $choice;
		}

		return [];
	}

	private function __getLoaiMc($mnpMerchantDetail = [])
	{
		$customerType = $mnpMerchantDetail['data']['customerType']['value'];
		if ($customerType == 'COMPANY') {
			return 'DOANH_NGHIEP';
		}

		return 'HO_KINH_DOANH';
	}
} // End class
