<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoDetailAction\SubAction;

use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360ChungThuc;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Model\Mpos360PhuongThucBuoc3;

class XuLyNoiTiepYeuDoiThongTinSubAction
{
	public array $returnData = [];
	/**
	 * Xử lý nối tiếp, trả ra 
	 * @return array = [
	 * 	'screen' => string,
	 * 	'params' => object {}
	 * ]
	 */
	public function run(Mpos360MerchantRequest $mpos360McRequest)
	{

		$isDoiThongTinNganHang = $mpos360McRequest->isDoiThongTinNganHang();
		$isDoiThongTinLienHe = $mpos360McRequest->isDoiThongTinLienHe();
		$isDoiCccdMoi = $mpos360McRequest->isYeuCauDoiCccdMoi();
		$isDoiHanNguoiDaiDienMoi = $mpos360McRequest->isYeuCauDoiNguoiDaiDienMoi();


		$mpos360ChungThuc = Mpos360ChungThuc::getChungThucCCCD($mpos360McRequest->merchant_id);

		if ($isDoiCccdMoi) {
			if ($mpos360McRequest->isDaXacThucNhungChuaLamBuoc3()) {
				return $this->dieuHuongVeManHinhChonPhuongThucXacThucB3($mpos360ChungThuc, $mpos360McRequest);
			}
			
			// Đoạn này là đang làm dở sdk và thoát app
		}

		if ($isDoiThongTinLienHe) {
			if ($mpos360McRequest->isChuaXacThuc()) {
				return $this->dieuHuongVeManHinhOtp($mpos360McRequest);
			}

			if ($mpos360McRequest->isDaXacThucNhungChuaLamBuoc3()) {
				return $this->dieuHuongVeManHinhChonPhuongThucXacThucB3($mpos360ChungThuc, $mpos360McRequest);
			}
		}
	
		if ($isDoiThongTinNganHang) {
			if ($mpos360McRequest->isDaXacThucNhungChuaLamBuoc3()) {
				return $this->dieuHuongVeManHinhChonPhuongThucXacThucB3($mpos360ChungThuc, $mpos360McRequest);
			}

			if ($mpos360McRequest->isChuaXacThuc()) {
				return $this->dieuHuongVeManHinhBoSungDinhKem($mpos360McRequest);
			}
		} // end isDoiThongTinNganHang

		return [
			'screen' => '',
			'params' => (object)[]
		];
	} // End method

/*------------------------------Màn hình otp------------------------------*/
	public function dieuHuongVeManHinhOtp(Mpos360MerchantRequest $mpos360McRequest) {
		$dataRequest = json_decode($mpos360McRequest->data_request, true);
		$listHoSoDaVerify = collect($dataRequest[0]['request_vefify'])->pluck('field')->values()->toArray();
		
		$email = '';
		$phone = '';

		foreach ($dataRequest[0]['profiles'] as $profileKey => $profileValue) {
			if ($profileKey == 'representEmail') {
				if (in_array($profileKey, $listHoSoDaVerify)) {
					$email = $profileValue;
				}
			}

			if ($profileKey == 'representMobile') {
				if (in_array($profileKey, $listHoSoDaVerify)) {
					$phone = $profileValue;
				}
			}
		}

		$returnData = [
			'screen' => 'authentication_info_by_otp_screen',
			'params' => [
				'requestID' => $mpos360McRequest->id,
				'profileKey' => $dataRequest[0]['request_vefify'],
				'email' => $email,
				'phone' => $phone
			]
		];

		return $returnData;
	}
/*------------------------------Bổ sung đính kèm------------------------------*/
	public function dieuHuongVeManHinhBoSungDinhKem(Mpos360MerchantRequest $mpos360McRequest) {
		$additionalProfiles = collect([])->push([
			'profileKey' => 'positionAuthBank',
			'value' => '',
			'label' => vmsg('Chức vụ của người ủy quyền tại ngân hàng'),
			'other_data' => (object) []
		])->push([
			'profileKey' => 'bankMutualRelation',
			'value' => '',
			'label' => vmsg('Mối quan hệ giữa chủ tài khoản và người có thẩm quyền tại ngân hàng'),
			'other_data' => (object) []
		])->push([
			'profileKey' => 'identificationDocument',
			'value' => '',
			'label' => vmsg('CCCD/GP lái xe/Hộ khẩu/Hộ chiếu'),
			'other_data' => [
				// Quản lý slot update và có tên của từng slot trên mobile app
				'list' => [
					[
						'value' => 'cccd',
						'label' => 'CCCD',
						'slot' => [
							[
								'profileKey' => 'passportRepresentFrontUrl',
								'label' => 'Ảnh chụp CCCD mặt trước'
							],

							[
								'profileKey' => 'passportRepresentBackUrl',
								'label' => 'Ảnh chụp CCCD mặt sau'
							],
						]
					],

					[
						'value' => 'gp_lai_xe',
						'label' => 'GP lái xe',
						'slot' => [
							[
								'substituteCertUrls' => 'substituteCertUrls',
								'label' => 'GPLX mặt trước'
							],

							[
								'substituteCertUrls' => 'substituteCertUrls',
								'label' => 'GPLX mặt sau'
							],
						],
					],

					[
						'value' => 'ho_chieu',
						'label' => 'Hộ chiếu',
						'slot' => [
							[
								'profileKey' => 'substituteCertUrls',
								'label' => 'Ảnh 1'
							],

							[
								'profileKey' => 'substituteCertUrls',
								'label' => 'Ảnh 2'
							],

							[
								'profileKey' => 'substituteCertUrls',
								'label' => 'Ảnh 3'
							],
							[
								'profileKey' => 'substituteCertUrls',
								'label' => 'Ảnh 4'
							],
						]
					],

					[
						'value' => 'ho_khau',
						'label' => 'Hộ khẩu',
						'slot' => [
							[
								'profileKey' => 'substituteCertUrls',
								'label' => 'Ảnh 1'
							],

							[
								'profileKey' => 'substituteCertUrls',
								'label' => 'Ảnh 2'
							],

							[
								'profileKey' => 'substituteCertUrls',
								'label' => 'Ảnh 3'
							],
							[
								'profileKey' => 'substituteCertUrls',
								'label' => 'Ảnh 4'
							],
						]
					],
				]
			]
		])->values()->toArray();

		$returnData = [
			'screen' => 'attach_document_screen',
			'params' => [
				'requestID' => $mpos360McRequest->id,
				'additionalProfiles' => $additionalProfiles
			]
		];

		return $returnData;
	}
/*------------------------------Màn hình B3------------------------------*/
	public function dieuHuongVeManHinhChonPhuongThucXacThucB3(
		$mpos360ChungThuc=null,
		Mpos360MerchantRequest $mpos360McRequest
	) {
		$listPhuongThucScanBuoc3 = Mpos360PhuongThucBuoc3::getPhuongThucBuoc3();
		
		
		if (!$mpos360ChungThuc) {
			$listPhuongThucScanBuoc3 = $listPhuongThucScanBuoc3->filter(function ($item) {
				return $item['method_code'] == 'SDK';
			})
			->values()->toArray();

			$returnData = [
				'screen' => 'authentication_changes_multi_method_screen',
				'params' => [
					'requestID' => $mpos360McRequest->id,
					'qtsRequestID' => '',
					'method' => $listPhuongThucScanBuoc3
				]
			];

			return $returnData;
		}
		

		$listPhuongThucScanBuoc3->transform(function (Mpos360PhuongThucBuoc3 $item) {
			return [
				'method_code' => $item->method_code,
				'is_main' => $item->priority > 0 ? 'YES' : 'NO'
			];
		})->values()->toArray();

		$returnData = [
			'screen' => 'authentication_changes_multi_method_screen',
			'params' => [
				'requestID' => $mpos360McRequest->id,
				'qtsRequestID' => $mpos360ChungThuc->qts_request_id,
				'method' => $listPhuongThucScanBuoc3
			]
		];

		return $returnData;
	}
} // End class