<?php

namespace App\Modules\Merchant\Actions\Notification\Mpos360GetNotificationDetailAction;

use App\Lib\partner\MNPNOTIFY;
use App\Modules\Merchant\Requests\Notification\Mpos360GetNotificationDetailRequest;

class Mpos360GetNotificationDetailAction
{
	public function run(Mpos360GetNotificationDetailRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();

		$mposToken = $deviceSession->getMposToken();

		$notficationId = $request->json('data.notificationId');

		$mnpNotify = new MNPNOTIFY($mposToken);

		$detailNotify = $mnpNotify->getNotificationDetail($notficationId);
		return $detailNotify;
	}
} // End class