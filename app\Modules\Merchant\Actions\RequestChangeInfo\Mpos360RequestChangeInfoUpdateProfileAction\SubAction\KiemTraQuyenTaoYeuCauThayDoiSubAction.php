<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\SubAction;

use Exception;
use App\Lib\Helper;
use App\Lib\partner\MNP;
use Illuminate\Http\Request;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileRequest;

class KiemTraQuyenTaoYeuCauThayDoiSubAction
{
	public MNP $mnp;

	public function __construct(MNP $mnp)
	{
		$this->mnp = $mnp;
	}

	public function run(
		DeviceSession $deviceSessionWithToken,
		Request $request
	): bool {

		mylog(['Enter validate thong tin' => 'ok']);

		$mposId = $deviceSessionWithToken->getMerchantId();
		$typeChanges = $request->json('data.request_change_info.code');

		$profiles = [];

		if ($request->isDoiNguoiDaiDienMoi()) {
			$profiles['typeChangeRepresent'] = 'CHANGE_NEW_REPRESENT';
		}

		if ($request->isDoiThongTinLienHe() || $request->isDoiCccdMoi()) {
			$profiles['typeChangeRepresent'] = 'CHANGE_CURRENT_REPRESENT_INFO';
		}

		$validateResult = $this->mnp->validate(
			$mposId,
			$typeChanges,
			$deviceSessionWithToken->mnp_token,
			$profiles
		);

		mylog(['Ket qua validate' => $validateResult]);

		if (empty($validateResult['status'])) {
			$msg = __('dttv3.Lỗi truy vấn đối tác MNP');

			if (!empty($validateResult['message'])) {
				$messages = explode(',', $validateResult['message']);
				$msg = $messages[0];
			}

			throw new BusinessException($msg);
		}

		return true;
	}
} // End class
