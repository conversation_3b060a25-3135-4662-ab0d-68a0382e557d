<?php

namespace App\Modules\TingBoxVAMC\Actions\Mpos360VAMCForVCBAction;

use App\Lib\Logs;
use Illuminate\Http\Request;
use App\Lib\partner\SoundBox;
use App\Exceptions\BusinessException;
use App\Modules\TingBoxVAMC\Actions\Mpos360VAMCForVCBAction\SubAction\ActiveNotifyAcbBankSubAction;
use App\Modules\TingBoxVAMC\Models\MerchantBank;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use App\Modules\TingBoxVAMC\Actions\Mpos360VAMCForVCBAction\SubAction\VCBCloseBankSubAction;
use App\Modules\TingBoxVAMC\Actions\Mpos360VAMCForVCBAction\SubAction\VCBReopenBankSubAction;
use App\Modules\TingBoxVAMC\Actions\Mpos360VAMCForVCBAction\SubAction\VCBConfirmBankSubAction;
use App\Modules\TingBoxVAMC\Actions\Mpos360VAMCForVCBAction\SubAction\VCBClosePendingSubAction;
use App\Modules\TingBoxVAMC\Actions\Mpos360VAMCForVCBAction\SubAction\MarkIsWaitToCloseSubAction;
use App\Modules\TingBoxVAMC\Actions\Mpos360VAMCForVCBAction\SubAction\VCBCloseConfirmBankSubAction;
use App\Modules\TingBoxVAMC\Actions\Mpos360VAMCForVCBAction\SubAction\VCBReopenConfirmBankSubAction;

class Mpos360VAMCForVCBAction
{
	protected SoundBox $soundBox;

	public function __construct(SoundBox $soundBox)
	{
		$this->soundBox = $soundBox;
	}

	public function run(Request $request)
	{
		$checksumHeader = $request->header('x-tbmc-checksum');

		if ($checksumHeader != env('X_TBMC_CHECKSUM')) {
			throw new BusinessException('Mã so khớp không đúng', 403);
		}

		Logs::writeInfo('Mpos360VAMCForVCBAction', $request->json()->all());
		
		if ($request->json('type') == 'LINK_BANK') {
			return $this->linkBank($request);
		}

		if ($request->json('type') == 'CONFIRM_BANK') {
			return app(VCBConfirmBankSubAction::class)->run($request);
		}

		if ($request->json('type') == 'CLOSE_BANK') {
			return app(VCBCloseBankSubAction::class)->run($request);
		}

		if ($request->json('type') == 'CONFIRM_CLOSE') {
			return app(VCBCloseConfirmBankSubAction::class)->run($request);
		}

		if ($request->json('type') == 'REOPEN') {
			return app(VCBReopenBankSubAction::class)->run($request);
		}

		if ($request->json('type') == 'CONFIRM_REOPEN') {
			return app(VCBReopenConfirmBankSubAction::class)->run($request);
		}

		// Hủy bỏ LK mà LK đó đang là "chờ xác nhận" (chỉ áp dụng cho VCB)
		if ($request->json('type') == 'CLOSE_PENDING') {
			return app(VCBClosePendingSubAction::class)->run($request);
		}

		// Đưa về LK đang là thành công -> về trạng thái "chờ hủy" (chỉ áp dụng cho VCB)
		if ($request->json('type') == 'WAIT_CLOSE_BANK') {
			return app(MarkIsWaitToCloseSubAction::class)->run($request);
		}

		// ACB
		if ($request->json('type') == 'ACTIVE_NOTIFY') {
			return app(ActiveNotifyAcbBankSubAction::class)->run($request);
		}
	}

	public function linkBank($request)
	{
		$vaRequest = $request->json('vaRequest');

		$mcBank = MerchantBank::query()->where([
			'bank_code' => $vaRequest['bankCode'],
			'merchant_id' => $vaRequest['merchantId'],
			'account_number' => $vaRequest['bankAccountNo'],
			'bank_mobile' => $vaRequest['bankMobile'],
		])->first();

		if (!$mcBank) {
			$mcBank = MerchantBank::query()->forceCreate([
				'merchant_id' => $vaRequest['merchantId'],
				'bank_code' => $vaRequest['bankCode'],
				'account_cat' => 'ACCOUNT',
				'account_number' => $vaRequest['bankAccountNo'],
				'account_holder' => $vaRequest['bankAccountName'],
				'bank_branch' => '',
				'partner_code' => 'VA',
				'bank_mobile' => $vaRequest['bankMobile'],
				'bank_email' => $vaRequest['bankEmail'] ?? '',
				'bank_identity' => $vaRequest['bankIdentity'],
				'status' => '1',
				'time_created' => time(),
				'time_updated' => time(),
			]);

			if (!$mcBank) {
				throw new BusinessException('Lỗi không tạo được thông tin BankMC');
			}
		}

		$vaResponse = $request->json('vaResponse');

		$currentMcShopBank = MerchantShopBank::query()->firstWhere([
			'partner_request_id' => $vaResponse['data']['vaNextpayNumber']
		]);

		if ($currentMcShopBank) {
			return $currentMcShopBank;
		}
		
		// Create luôn MC shopbank
		$p = [
			'username' => $vaRequest['bankMobile'],
			'merchant_id' => $vaRequest['merchantId'],
			'merchant_bank_id' => $mcBank->id,
			'shop_id' => $vaRequest['mobileUserId'],
			'is_default' => !empty($vaRequest['assignDefault']) ? 1 : 0,
			'account_type' => '1',
			'account_qr' => $vaResponse['data']['qrCode'],
			'account_qr_display' => $vaRequest['bankAccountName'],
			'data_linked' => json_encode($vaResponse),
			'request_id' => $vaResponse['data']['mcRequestId'],
			'partner_request_id' => $vaResponse['data']['vaNextpayNumber'],
			'account_number_partner' => $vaResponse['data']['vaBankNumber'],
			'partner_code' => 'VA',
			'is_sync_tingbox' => '1',
			'number_sync_tingbox' => '0',
			'status_link' => '0',
			'time_created' => time(),
			'time_updated' => time(),
			'linking_otp_wrong_number' => '0',
			'linkingMaxResend' => $vaResponse['data']['maxResend'],
			'linkingTempLockedTime' => $vaResponse['data']['tempLockedTime'],
			'linkingOtpExpired' => !empty($vaResponse['data']['expired']) ? time() + $vaResponse['data']['expired'] : 0,
			'linkingMaxSubmit' => $vaResponse['data']['maxSubmit'] ?? 0,
			'isNeedCreateTrungGian' => '1',
		];

		if (array_key_exists('verifyBank', $request->json()->all())) {
			$verifyBank = $request->json('verifyBank');
			if ($verifyBank == false) {
				$p['isVerifyBankAccount'] = 0; // chua xac thuc
			}
		}

		if (!empty($request->json('hasResendOtp'))) {
			$p['hasResendOtp'] = 'YES';
		}

		if (!empty($request->json('notificationRequired'))) {
			$p['notificationRequired'] = 'YES';
		}

		$mcShopBank = MerchantShopBank::query()->forceCreate($p);

		if (!$mcShopBank) {
			throw new BusinessException('Lỗi không tạo được bản ghi VA');
		}

		return ['id' => $mcShopBank->id];
	}
} // End class
