<?php

namespace App\Modules\Merchant\Controllers\Setting;

use App\Lib\Logs;
use App\Lib\Helper;
use App\Lib\Mpos360UrlHelper;
use App\Modules\Merchant\Model\Setting;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\Setting\Mpos360GetSettingRequest;

class SettingController extends Controller
{
	public function Mpos360GetSetting(Mpos360GetSettingRequest $request)
	{
		$result = [
			'is_force_update' => '0',
			'enable_update' => '0'
		];

		try {
			$setting = Setting::query()->firstWhere(['key' => 'MOBILE_VERSION_FORCE_UPDATE']);

			if (!$setting) {
				return $this->errorResponse(404, 'Không có thông tin setting');
			}

			$os = $request->json('data.os', 'OTHER');
			$cauHinhMobileApp = $setting->getCauHinhMobileApp($os);
			
			$currentVersion = $request->json('data.current_version');
			$latestVersion = $cauHinhMobileApp['latest_version'];
			$minimunVersion = $cauHinhMobileApp['minimum_version']; 

			$result['wording'] = $this->__getWordingViaScreen();

			$result['update_url'] = $cauHinhMobileApp['update_url'];

			if ($currentVersion >= $latestVersion) {
				return $this->successResponse($result, $request);
			}

			if ($currentVersion < $minimunVersion) {
				$result['is_force_update'] = 1;
				$result['enable_update'] = 1;
				return $this->successResponse($result, $request);
			}

			if ($currentVersion >= $minimunVersion && $currentVersion < $latestVersion) {
				$result['enable_update'] = 1;
				return $this->successResponse($result, $request);
			}

			mylog([
				'Khong biet la loi gi' => [
					'currentVersion' => $currentVersion,
					'latestVersion' => $latestVersion,
					'minimunVersion' => $minimunVersion,
				]
			]);
			
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	private function __getWordingViaScreen(): array {
		$wording = [
			'app_version' => 'Version 1.1.1.1',
			'contact_channel' => [
				[
					'text' => '1900-63-64-88',
					'value' => '1900636488',
					'type' => 'tel',
					'text_color' => '#808890',
					'icon' => "https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/mpos360/app/67fca3034465eb435f2ee456contact-phone.png",
				],
				[
					'text' => __('setting.mPOS Việt Nam'),
					'value' => 'https://zalo.me/2910216453995933825',
					'type' => 'link',
					'text_color' => '#808890',
					'icon' => "https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/mpos360/app/67fca3034465eb435f2ee457contact-zalo.png"
				],
				[
					'text' => '<EMAIL>',
					'value' => '<EMAIL>',
					'type' => 'mailto',
					'text_color' => '#808890',
					'icon' => "https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/mpos360/app/67fca3034465eb435f2ee455contact-email.png"
				],
			],
			'popup' => [
				'forgot_password' => [
					'title' => __('setting.Quên mật khẩu'),
					'desc' => __('setting.Quên mật khẩu desc'),
					'other_data' => (object) [
						'submitBtn' => [
							'link' => 'https://mpos.vn/forget-password',
							'text' => __('setting.Lấy lại mật khẩu'),
						] 
					]
				],

				'upgrade_password' => [
					'title' => __('setting.Nâng cấp mật khẩu'),
					'desc' => __('setting.Nâng cấp mật khẩu desc'),
					'other_data' => (object)[]
				],

				'first_login' => [
					'title' => __('setting.Tài khoản đăng nhập'),
					'desc' => sprintf('Qúy đơn vị vui lòng sử dụng Email hoặc SĐT đã đăng ký cùng với mật khẩu tài khoản mPOS để truy cập ứng dụng %s', __('setting.appName')),
					'other_data' => (object)[]
				]
			],
			'links' => [
				'policy' => [
					'title' => __('setting.điều khoản và chính sách'),
					'url' => 'https://tingbox.vn/policy.html'
				],

				'get_device_code_tingbox' => [
					'title' => __('setting.Hướng dẫn lấy mã thiết bị'),
					'url' => Mpos360UrlHelper::convertToHttps(url('/Mpos360TingBoxDeviceHuongDanLayMaTb'))
				],

				'get_qr_code_receive' => [
					'title' => 'Hướng dẫn quét mã QR nhận tiền',
					'url' => Mpos360UrlHelper::convertToHttps(url('/Mpos360HuongDanQuetMaQRNhanTien'))
				],
			]
		];
		return $wording;
	}
} // End class
