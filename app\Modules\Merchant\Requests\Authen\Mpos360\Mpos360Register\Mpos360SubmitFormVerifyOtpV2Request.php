<?php

namespace App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360Register;

use Illuminate\Foundation\Http\FormRequest;

class Mpos360SubmitFormVerifyOtpV2Request extends FormRequest
{
    	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.otp_id' => ['required', 'string'],
			'data.otp' => ['required', 'string', 'max:6'],
			'data.username' => ['required', 'string'],
			'data.password' => ['required', 'string', 'min:6'],
			'data.partnerInviteCode' => ['present', 'string'],
		];
	}

	public function messages() {
		return [
			'data.otp_id.required' => 'otp_id là bắt buộc',
			'data.otp_id.string' => 'otp_id phải là kiểu chuỗi ký tự',
			'data.otp.required' => 'otp là bắt buộc',
			'data.otp.string' => 'otp phải là kiểu chuỗi',
			'data.otp.max' => 'otp tối đa có 6 ký tự',
			'data.username.required' => 'Username là bắt buộc',
			'data.username.string' => 'Username phải là kiểu chuỗi ký tự',
			'data.password.required' => 'Mật khẩu là bắt buộc',
			'data.password.string' => 'Mật khẩu phải là kiểu chuỗi',
			'data.password.min' => 'Mật khẩu phải ít nhất có 6 ký tự',
			'data.password.confirmed' => 'Xác nhận mật khẩu phải trùng khớp với mật khẩu',
			'data.password.regex' => 'Mật khẩu chỉ được chứa chữ cái, số và các ký tự hợp lệ: "/", ",", ".", "-", "_", "@", "!", "*". Không được chứa khoảng trắng.',
			'data.partnerInviteCode.present' => 'Mã giới thiệu là bắt buộc',
			'data.partnerInviteCode.string' => 'Mã giới thiệu phải là kiểu chuỗi',
		];
	}
}