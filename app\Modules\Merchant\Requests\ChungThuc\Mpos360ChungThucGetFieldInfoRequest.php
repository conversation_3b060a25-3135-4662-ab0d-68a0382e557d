<?php

namespace App\Modules\Merchant\Requests\ChungThuc;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360ChungThucGetFieldInfoRequest extends MerchantRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data.email' => ['required', 'string', 'email'],
    ];
  }

	public function messages()
	{
		return [
			'data.email.required' => vmsg('ChungThucEmailMerchantLaBatBuoc'),
			'data.email.string' => vmsg('ChungThucEmailMerchantPhaiLaKieuChuoi'),
			'data.email.email' => vmsg('ChungThucEmailMerchantPhaiLaEmailDungDinhDang'),
		];
	}
}
