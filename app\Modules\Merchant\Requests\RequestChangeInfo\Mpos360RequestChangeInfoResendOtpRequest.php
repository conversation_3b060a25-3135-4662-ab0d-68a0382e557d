<?php

namespace App\Modules\Merchant\Requests\RequestChangeInfo;

use App\Modules\Merchant\Requests\MerchantRequest;
use App\Modules\Merchant\Rules\RequestChangeInfoRule\GroupProfileRequiredRule;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;

class Mpos360RequestChangeInfoResendOtpRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}


	public function rules()
	{
		return [
			'data.otp_id' => [
				'required',
				'string',
			],
		];
	}
} // End class
