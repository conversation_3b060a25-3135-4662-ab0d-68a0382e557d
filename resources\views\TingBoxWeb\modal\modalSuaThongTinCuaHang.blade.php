<div class="modal fade action-sheet" id="modalSuaThongTinCuaHang" tabindex="-1" role="dialog" style="display: none;" data-bs-backdrop="static">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				<h5>Thông tin cửa hàng</h5>
			</div>

			<div class="modal-body">
				<div class="mpos360-form">
					<div class="notifi-item">
						<div class="form-floating mb-3">
							<input type="text" class="form-control edit_name_cua_hang" id="areaNameEdit" placeholder="" value="{{ $paramEditCuaHang['areaName'] }}">
							<label for="floatingInputValue">Tên cửa hàng</label>
							<div class="error-message text-danger mt-1" id="edit_error-name"></div>
						</div>

						<div class="form-floating mb-3">
							<select class="form-select edit_job" id="mccEdit"
								aria-label="Floating label select example">
								<option selected="" value="">-- Chọn ngành nghề --</option>
								@foreach ($listNganHangThanhPho['industries'] as $item)
									<option value="{{ $item['industryId'] }}"
										@if ($item['industryId'] == $paramEditCuaHang['mccId'])
											selected
										@endif
									>
										{{ $item['industryName'] }}
									</option>
								@endforeach
							</select>
							<label for="floatingSelect">Ngành nghề kinh doanh</label>
							<div class="error-message text-danger mt-1" id="edit_error-job"></div>
						</div>

						<div class="form-floating mb-3">
							<select class="form-select edit_cities" id="areaCityCodeEdit"
								aria-label="Floating label select example" onchange="return onChangeTinhThanh(this)">
								<option value="">-- Chọn tỉnh/Thành phố --</option>
								@foreach ($listNganHangThanhPho['cities'] as $item)
									<option value="{{ $item['cityId'] }}"
										@if ($item['cityId'] == $paramEditCuaHang['areaCityCode']) selected @endif>
										{{ $item['cityName'] }}
									</option>
								@endforeach
							</select>
							<label for="floatingSelect">Tỉnh/Thành phố</label>
							<div class="error-message text-danger mt-1" id="edit_error-cities"></div>
						</div>

						<div class="form-floating mb-3">
							<select class="form-select edit_district" id="areaDistrictCodeEdit"
								aria-label="Floating label select example">
								<option selected="" value="">-- Chọn quận/Huyện --</option>
								@foreach ($listQuanHuyen['data'] as $item)
									<option value="{{ $item['code'] }}"
										@if ($item['code'] == $paramEditCuaHang['areaDistrictCode']) selected @endif>
										{{ $item['name'] }}
									</option>
								@endforeach
							</select>
							<label for="floatingSelect">Quận/Huyện</label>
							<div class="error-message text-danger mt-1" id="edit_error-district"></div>
						</div>

						<div class="form-floating mb-3">
							<input type="text" class="form-control edit_address" id="areaAddressEdit" placeholder="" value="{{ $paramEditCuaHang['areaAddress'] }}">
							<label for="floatingInputValue">Địa chỉ</label>
                            <div class="error-message text-danger mt-1" id="edit_error-address"></div>
						</div>

						<p class="mt-4 mb-0">
							<button type="button" class="btn text-center btn-blue btn-success w-100 d-block submit_ting_box d-flex align-items-center justify-content-center" onclick="return onUpdateCuaHang(this)">
								<div class="spinner-border d-none" role="status" id="spinnerEditCuaHang">
									<span class="visually-hidden">Loading...</span>
								</div>

									<span id="editCuaHangActionName">Lưu thay đổi</span>
							</button>
						</p>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>


<script>
	function onChangeTinhThanh(element) {
		$('#loadingPage').addClass('se-pre-con');
		$.post('/Mpos360TingBoxOnChangeTinhThanh', {
			cityId: element.value
		}).then(function (res) {
			$('#loadingPage').removeClass('se-pre-con');
			$('#areaDistrictCodeEdit').html(res.message);
		});
	}

	var $edit_name_cua_hang = $(".edit_name_cua_hang");
	var $edit_error_name_cua_hang = $("#edit_error-name");

	function validateName() {
		var edit_name_cua_hang = $edit_name_cua_hang.val().trim();
		if (edit_name_cua_hang === "") {
			$edit_error_name_cua_hang.text("Vui lòng nhập tên cửa hàng");
			$edit_name_cua_hang.addClass("is-invalid");
		} else {
			$edit_error_name_cua_hang.text("");
			$edit_name_cua_hang.removeClass("is-invalid");
		}
	}

	var $edit_job = $(".edit_job");
	var $edit_errorJob = $("#edit_error-job");

	function validateJob() {
		var edit_job = $edit_job.val().trim();
		if (edit_job === "") {
			$edit_errorJob.text("Vui lòng chọn ngành nghề");
			$edit_job.addClass("is-invalid");
		} else {
			$edit_errorJob.text("");
			$edit_job.removeClass("is-invalid");
		}
	}

	var $edit_cities = $(".edit_cities");
	var $edit_errorCities = $("#edit_error-cities");

	function validateCities() {
		var edit_cities = $edit_cities.val().trim();
		if (edit_cities === "") {
			$edit_errorCities.text("Vui lòng chọn tỉnh/thành phố");
			$edit_cities.addClass("is-invalid");
		} else {
			$edit_errorCities.text("");
			$edit_cities.removeClass("is-invalid");
		}
	}

	var $edit_district = $(".edit_district");
	var $edit_errorDistrict = $("#edit_error-district");

	function validateDistrict() {
		var edit_district = $edit_district.val().trim();
		if (edit_district === "") {
			$edit_errorDistrict.text("Vui lòng chọn quận/huyện");
			$edit_district.addClass("is-invalid");
		} else {
			$edit_errorDistrict.text("");
			$edit_district.removeClass("is-invalid");
		}
	}

    var $edit_address = $(".edit_address");
	var $edit_errorAddress = $("#edit_error-address");

	function validateAddress() {
		const regexAddress = /^[a-zA-Z0-9\s\/,\u00C0-\u1EF9]*$/u;

		var edit_district = $edit_address.val().trim();
		if (edit_district === "") {
			$edit_errorAddress.text("Vui lòng nhập địa chỉ");
			$edit_address.addClass("is-invalid");
		}else if (edit_district.length > 0 && !regexAddress.test(edit_district)) {
			$edit_errorAddress.text("Vui lòng Không sử dụng các ký tự đặc biệt như:  (-), @, #, $, %, ! *… trong trường địa chỉ.");
			$edit_address.addClass("is-invalid");
		}
		else {
			$edit_errorAddress.text("");
			$edit_address.removeClass("is-invalid");
		}
	}

	$edit_name_cua_hang.on("input", validateName);
	$edit_cities.on("input", validateAddress);
	$edit_job.on("change", validateJob);
	$edit_district.on("change", validateDistrict);
	$edit_address.on("change", validateAddress);


	function onUpdateCuaHang(element) {
		validateName();
		validateJob();
		validateCities();
		validateDistrict();
		validateAddress();

		var params = {
			areaId: "{{ $paramEditCuaHang['id'] }}",
			mcc: $('#mccEdit').val(),
			areaName: $('#areaNameEdit').val(),
			areaAddress: $('#areaAddressEdit').val(),
			mposMcId: "{{ $paramEditCuaHang['mposMcId'] }}",
			areaCityCode: $('#areaCityCodeEdit').val(),
			areaDistrictCode: $('#areaDistrictCodeEdit').val(),
		};

		 if (
             $edit_error_name_cua_hang.text() === "" &&
             $edit_errorJob.text() === "" &&
             $edit_errorCities.text() === "" &&
             $edit_errorDistrict.text() === "" &&
             $edit_errorAddress.text() === ""
         ) {
             // $(element).attr('disabled', true);
             // $('#spinnerEditCuaHang').addClass('d-block');
             // $('#editCuaHangActionName').hide();
			 $('#loadingPage').addClass('se-pre-con');
			 $.post('/Mpos360TingBoxUpdateCuaHang', params).then(function (res) {
				 $('#loadingPage').removeClass('se-pre-con');
			if (res.success) {
				alert(res.message);
				return location.reload();
			}

			return alert(res.message);

		}).always(function () {
			// $(element).removeAttr('disabled');
			// $('#spinnerEditCuaHang').removeClass('d-block');
			// $('#editCuaHangActionName').show();
		});
		 }



	}
</script>

