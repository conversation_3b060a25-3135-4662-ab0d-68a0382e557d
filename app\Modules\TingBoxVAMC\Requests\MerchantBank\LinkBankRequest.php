<?php

namespace App\Modules\TingBoxVAMC\Requests\MerchantBank;

use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use App\Modules\TingBoxVAMC\Rules\ChiMotVABRule;
use App\Modules\Merchant\Requests\MerchantRequest;
use App\Modules\TingBoxVAMC\Rules\TknhKhongLienKet2LanTrongCuaHangRule;

class LinkBankRequest extends MerchantRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
			'data.username' => ['present', 'string'],
      'data.bankAccountName' => ['required', 'string'],
      'data.bankAccountNo' => ['required', 'string', new TknhKhongLienKet2LanTrongCuaHangRule()],
      'data.bankIdentity' => ['required', 'string'],
      'data.bankMobile' => ['required', 'string', 'digits:10'],
      'data.bankEmail' => ['present', 'required_if:data.channelCode,OCBVAMC', 'string', 'email'],
      // 'data.bankEmail' => ['present'],
      'data.merchantId' => ['required', 'string'],
      'data.channelCode' => ['required', 'string'],
      'data.bankCode' => ['required', 'string', new ChiMotVABRule()],
      'data.mobileUserId' => ['required', 'string'],
      'data.default' => ['required', 'integer', Rule::in([1,2])],
    ];
  }

  public function messages()
	{
		return [
			'data.bankAccountName.required' => 'Tên chủ tài khoản là bắt buộc',
			'data.bankAccountName.string' => 'Tên chủ tài khoản phải là kiểu chuỗi',
			'data.bankAccountNo.required' => 'Số tài khoản là bắt buộc',
			'data.bankAccountNo.string' => 'Số tài khoản phải là kiểu chuỗi',
			'data.merchantId.required' => 'Id merchant là bắt buộc',
			'data.merchantId.string' => 'Id merchant phải là kiểu chuỗi',
			'data.bankMobile.required' => 'Số điện thoại đã đăng ký với ngân hàng là bắt buộc',
			'data.bankMobile.string' => 'Số điện thoại đã đăng ký với ngân hàng là kiểu chuỗi',
			'data.bankMobile.digits' => 'Số điện thoại đã đăng ký với ngân hàng phải đủ 10 số',
			'data.bankEmail.required' => 'Email đã đăng ký với ngân hàng là bắt buộc',
			'data.bankEmail.string' => 'Email đã đăng ký với ngân hàng là kiểu chuỗi',
			'data.bankEmail.required_if' => 'Email để nhận thông báo kết quả liên kết là bắt buộc khi thực hiện liên kết với ngân hàng OCB.', 
			'data.bankEmail.email' => 'Email phải đúng định dạng',
			'data.bankEmail.regex' => 'Email đã đăng ký với ngân hàng không đúng định dạng',
      'data.channelCode.required' => 'Mã chuyển tiền của đối tác là bắt buộc',
			'data.channelCode.string' => 'Mã chuyển tiền của đối tác là kiểu chuỗi',
			'data.channelCode.in' => 'Mã chuyển tiền của đối tác chưa chính xác',
      'data.bankCode.required' => 'Mã ngân hàng là bắt buộc',
			'data.bankCode.string' => 'Mã ngân hàng là kiểu chuỗi',
      'data.bankIdentity.required' => 'Mã CCCD là bắt buộc',
			'data.bankIdentity.string' => 'Mã CCCD là kiểu chuỗi',
      'data.mobileUserId.required' => 'Mã định danh mobile là bắt buộc',
			'data.mobileUserId.string' => 'Mã định danh mobile là kiểu chuỗi',
      'data.default.required' => 'Chọn tài khoản mặc định là bắt buộc',
			'data.default.integer' => 'Chọn tài khoản mặc định là kiểu số',
			'data.default.in' => 'Chọn tài khoản mặc định chưa chính xác',
		];
	}

	protected function passedValidation()
	{
		$params = $this->all();
		$params['data']['bankAccountName'] = Str::of($params['data']['bankAccountName'])->slug(' ')->title()->__toString();
		$params['data']['default'] = 1; // lien ket moi thi lam mac dinh luon
		$this->merge($params);
	}
} // End class
