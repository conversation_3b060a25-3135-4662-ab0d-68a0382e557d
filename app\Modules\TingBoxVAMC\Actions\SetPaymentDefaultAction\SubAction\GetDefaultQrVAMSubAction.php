<?php

namespace App\Modules\TingBoxVAMC\Actions\SetPaymentDefaultAction\SubAction;


class GetDefaultQrVAMSubAction
{
	public function run($mcShopBankArr, $mcTingBox = [])
	{
		return collect($mcTingBox['data']['mcQr'])
		->filter(function ($item) {
			return $item['integratedMethod'] == 'VAMC';
		})
		->first(function ($item) use ($mcShopBankArr) {
			return $mcShopBankArr['partner_request_id'] == $item['vaNextPayNumber'] && $item['state'] == 'DEFAULT' && $item['status'] == 'ACTIVE';
		});
	}
}
