<?php

namespace App\Modules\Merchant\Actions\TingBox\Mpos360GetFormTingBoxStep1Action;

use App\Lib\MnpOnboardNewMcHelper;
use App\Modules\Merchant\Requests\TingBox\Mpos360GetFormTingBoxStep1Request;

class Mpos360GetFormTingBoxStep1Action
{
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run(Mpos360GetFormTingBoxStep1Request $request)
	{
		$returnData = [
			'banks' => [],
			'cities' => [],
			'industries' => []
		];

		$nganhNgheNganHangThanhPho = $this->mnpOnboardNewMcHelper->getNganhNgheNganHangThanhPho(true);
		if ($nganhNgheNganHangThanhPho) {
			return $nganhNgheNganHangThanhPho;
		}

		return $returnData;
	}
}
