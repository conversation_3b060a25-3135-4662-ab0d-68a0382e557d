<?php

namespace App\Modules\Merchant\Requests\RequestChangeInfo\V3;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360RequestChangeInfoDetailV3Request extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.id' => ['required', 'numeric', 'integer', 'min:1'],
		];
	}

	public function messages()
	{
		return [
			'data.id.required' => 'Id yêu cầu là bắt buộc',
			'data.id.numeric' => 'Id yêu cầu phải là dạng số',
			'data.id.integer' => 'Id yêu cầu phải là số nguyên',
		];
	}
} // End class
