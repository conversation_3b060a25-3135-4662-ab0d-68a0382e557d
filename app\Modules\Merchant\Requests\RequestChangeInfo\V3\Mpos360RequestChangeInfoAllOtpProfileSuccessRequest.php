<?php

namespace App\Modules\Merchant\Requests\RequestChangeInfo\V3;

use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use App\Modules\Merchant\Requests\MerchantRequest;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;

class Mpos360RequestChangeInfoAllOtpProfileSuccessRequest extends MerchantRequest
{
	
	public function authorize()
	{
		return true;
	}

	public function rules()
	{
		return [
			'data.id' => ['required', 'numeric', 'integer', 'min:1'],
			'data.request_vefify' => ['required', 'array'],
			'data.request_vefify.*.profileKey' => ['required', 'string'],
			'data.request_vefify.*.value' => ['required', 'string', 'min:2'],
			'data.request_vefify.*.status_verify' => ['required', 'string', Rule::in(['1'])],
			'data.request_vefify.*.date_verify' => ['required', 'numeric', 'integer'],

			'data.attachments' => ['present', 'array']
		];
	}

  public function messages()
  {
    return [
      'data.id.required' => __('dttv3.Id là bắt buộc'),
      'data.id.numeric' => __('dttv3.Id phải là dạng số'),
      'data.id.integer' => __('dttv3.Id phải là số nguyên'),
      'data.id.min' => __('dttv3.Id phải có giá trị là 1'),

      'data.request_vefify.required' => __('dttv3.Trường xác thực là bắt buộc'),
      'data.request_vefify.array' => __('dttv3.Trường xác thực phải là kiểu mảng'),
      'data.request_vefify.*.profileKey.required' => __('dttv3.profileKey là bắt buộc'),
      'data.request_vefify.*.profileKey.string' => __('dttv3.profileKey phải là kiểu chuỗi'),

      'data.request_vefify.*.value.required' => __('dttv3.giá trị hồ sơ là bắt buộc'),
      'data.request_vefify.*.value.string' => __('dttv3.giá trị hồ sơ phải là kiểu chuỗi'),
      'data.request_vefify.*.value.min' => __('dttv3.giá trị hồ sơ phải có độ dài tối thiểu là 2 ký tự'),

      'data.request_vefify.*.status_verify.required' => __('dttv3.StatusVerify là bắt buộc'),
      'data.request_vefify.*.status_verify.in' => __('dttv3.StatusVerify phải thuộc giá trị cho phép: 1'),

      'data.request_vefify.*.date_verify.required' => __('dttv3.Thời gian xác thực là bắt buộc'),
      'data.request_vefify.*.date_verify.numeric' => __('dttv3.Thời gian xác thực phải là kiểu số'),
      'data.request_vefify.*.date_verify.integer' => __('dttv3.Thời gian xác thực phải là kiểu số nguyên'),
    ];
  }

  public function withValidator($validator)
	{
		$mpos360McRequest = Mpos360MerchantRequest::query()->find($this->json('data.id'));
		$dataRequest = json_decode($mpos360McRequest->data_request, true);
		$requestVefifyOnRecord = $dataRequest[0]['request_vefify'] ?? [];

		$isCanCoDanhDauSuccessEmail = collect($requestVefifyOnRecord)->contains(function ($item) {
			return $item['field'] == 'representEmail';
		});

		$isCanCoDanhDauSuccessMobile = collect($requestVefifyOnRecord)->contains(function ($item) {
			return $item['field'] == 'representMobile';
		});

		$requestVerify = $this->json('data.request_vefify');
		$requestVerifyCollect = collect($requestVerify);

		$validator->after(function ($validator) use ($requestVerifyCollect, $mpos360McRequest, $isCanCoDanhDauSuccessEmail, $isCanCoDanhDauSuccessMobile) {
			// Validate các trường bên trong cho thông tin liên hệ
			if ($mpos360McRequest->isDoiThongTinLienHe()) {
				// Có thông tin email mà MC muốn thay đổi
				if ($mpos360McRequest->isDoiThongTinLienHeEmailV3()) {
					$isCoThongTinEmail = $requestVerifyCollect->where('profileKey', 'authoriserEmail')->first();
					if (empty($isCoThongTinEmail['value']) && !$isCanCoDanhDauSuccessEmail) {
						$validator->errors()->add('authoriserEmail', __('dttv3.Thiếu thông tin Email cần thay đổi (authoriserEmail)'));
					}
				}

				if ($mpos360McRequest->isDoiThongTinLienHeMobileV3()) {
					$isCoThongTinMobile = $requestVerifyCollect->where('profileKey', 'authoriserContactNumber')->first();
					if (empty($isCoThongTinMobile['value']) && !$isCanCoDanhDauSuccessMobile) {
						$validator->errors()->add('authoriserContactNumber', __('dttv3.Thiếu thông tin SĐT cần thay đổi (authoriserContactNumber)'));
					}
				}
			} // End check doi thong tin lien he

			// Validate các trường bên trong cho việc đổi CCCD mới
			if ($mpos360McRequest->isYeuCauDoiCccdMoi()) {
				// Có duy nhất 1 thông tin là passport
				if ($requestVerifyCollect->count() > 1) {
					$validator->errors()->add('passport', __('dttv3.Trường chứng thực thông tin chỉ được phép có 1 phần tử duy nhất (passport)'));
				}

				$qtsRequestId = $requestVerifyCollect->where('profileKey', 'passport')->first();
				if (empty($qtsRequestId['value'])) {
					$validator->errors()->add('passport', __('dttv3.Thiếu thông tin passport (qtsRequestId)'));
				}

				if (!empty($qtsRequestId['value']) && !Str::isUuid($qtsRequestId['value'])) {
					$validator->errors()->add('passport', __('dttv3.passport phải là 1 uuid'));
				}
			} // End check passport

			// Validate cho case đổi hẳn người đại diện mới
			if ($mpos360McRequest->isYeuCauDoiNguoiDaiDienMoi()) {
				if ($requestVerifyCollect->count() < 1) {
					$validator->errors()->add('passport', __('dttv3.Phải có ít nhất 1 thông tin (passport)'));
				}

				$qtsRequestId = $requestVerifyCollect->where('profileKey', 'passport')->first();
				if (empty($qtsRequestId['value'])) {
					$validator->errors()->add('passport', __('dttv3.Thiếu thông tin passport (qtsRequestId)'));
				}

				// email
				// $authoriserEmail = $requestVerifyCollect->where('profileKey', 'authoriserEmail')->first();
				// if (empty($authoriserEmail['value'])) {
				// 	$validator->errors()->add('authoriserEmail', 'Thiếu thông tin Email cần thay đổi (authoriserEmail)');
				// }

				// mobile
				// $authoriserContactNumber = $requestVerifyCollect->where('profileKey', 'authoriserContactNumber')->first();
				// if (empty($authoriserContactNumber['value'])) {
				// 	$validator->errors()->add('authoriserContactNumber', 'Thiếu thông tin SĐT cần thay đổi (authoriserContactNumber)');
				// }

				// chuc vu - vi tri
				// $representPosition = $requestVerifyCollect->where('profileKey', 'representPosition')->first();
				// if (empty($representPosition['value'])) {
				// 	$validator->errors()->add('representPosition', 'Thiếu thông tin chức vụ/vị trí (representPosition)');
				// }

				// moi quan he giua nguoi uy quyen va nguoi duoc uy quyen
				// $representMutualRelation = $requestVerifyCollect->where('profileKey', 'representMutualRelation')->first();
				// if (empty($representMutualRelation['value'])) {
				// 	$validator->errors()->add('representMutualRelation', 'Thiếu thông tin mối quan hệ của người ủy quyền & người được ủy quyền (representMutualRelation)');
				// }
			} // End cho nguoi dai dien moi
		}); // End for
	} // End class

	public function getRequestVefifyAsArray()
	{
		$verifyResult = $this->json('data.request_vefify', []);
		$verifyInsertDataRequest = [];

		foreach ($verifyResult as $item) {
			$verifyInsertDataRequest[] = [
				'field'         => config('profilemnp.profile.' . $item['profileKey']),
				'value'         => $item['value'],
				'status_verify' => $item['status_verify'],
				'date_verify'   => $item['date_verify']
			];
		}

		return $verifyInsertDataRequest;
	}

	protected function passedValidation()
	{
		$params = $this->all();
		foreach ($params['data']['request_vefify'] as &$verifiedInfo) {
			if (is_string($verifiedInfo['value'])) {
				$verifiedInfo['value'] = trim(strip_tags($verifiedInfo['value']));
			}
		}

		$this->merge($params);
	}
} // End class
