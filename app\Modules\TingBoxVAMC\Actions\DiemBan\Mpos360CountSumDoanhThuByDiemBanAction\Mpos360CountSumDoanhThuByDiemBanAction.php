<?php

namespace App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360CountSumDoanhThuByDiemBanAction;

use App\Lib\Helper;
use App\Lib\partner\MposAWS;
use App\Modules\TingBoxVAMC\Requests\Transaction\Mpos360CountSumDoanhThuByDiemBanRequest;

class Mpos360CountSumDoanhThuByDiemBanAction
{
	public MposAWS $mposAws;

	public function __construct(MposAWS $mposAws)
	{
		$this->mposAws = $mposAws;
	}


	public function run(Mpos360CountSumDoanhThuByDiemBanRequest $request)
	{
		try {
			$params = [
				'serviceName' => 'DAILY_COUNT_SUM_TRANSACTION',
				'typeTransaction' => 'HISTORY_QR',
				'merchantFk' => $request->json('data.merchantId'),
				'vaMode' => $request->json('data.vaMode'),
				'rangeTime' => $request->json('data.rangeTime'),
				'muid' =>  $request->json('data.muid')
			];

			$result = $this->mposAws->countSumTransByDate($params);
			$countSum = [];

			$wording = sprintf('GD: 0   Tổng tiền: 0');


			$countSum[] = [
				'transactionCount' => 0,
				'formatDate' => $request->json('data.rangeTime'),
				'totalAmount' => 0,
				'wording' => $wording
			];

			if (!empty($result['data']['data'])) {
				$countSum[0]['transactionCount'] = $result['data']['data'][0]['transactionCount'];
				$countSum[0]['totalAmount'] = $result['data']['data'][0]['totalAmount'];

				$wording = sprintf(
					'GD: %s   Tổng tiền: %s',
					$result['data']['data'][0]['transactionCount'],
					Helper::priceFormat($result['data']['data'][0]['totalAmount']),
				);

				$countSum[0]['wording'] = $wording;
			}

			$returnData['statistic'] = $countSum;
			return $returnData;
		} catch (\Throwable $th) {
			mylog(['Err' => $th->getMessage()]);
			$returnData['statistic'] = $countSum;
			return $returnData;
		}
	}
} // End class
