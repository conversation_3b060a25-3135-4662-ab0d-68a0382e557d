<?php

namespace App\Modules\Merchant\Controllers\Authen\Mpos360;

use App\Lib\Helper;
use App\Lib\TelegramAlert;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Model\PartnerConfig;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360AuthLoginRequest;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360AuthLogoutRequest;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360AuthRegisterRequest;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360ChangePasswordRequest;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360ForgotPasswordRequest;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360AuthRecheckLoginRequest;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360AuthLoginUserNameRequest;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360AuthRemoveAccountRequest;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360AuthVerifyMposDeviceRequest;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360AuthGetDeviceTutorialRequest;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\CheckAuthenAction\Mpos360ForgotPasswordAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\Mpos360AuthLoginAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360GetMCInfoAction\Mpos360GetMCInfoAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLogoutAction\Mpos360AuthLogoutAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\Mpos360AuthLoginEmailAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\Mpos360AuthLoginMobileAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthRegisterAction\Mpos360AuthRegisterAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\Mpos360AuthLoginUserNameAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360ChangePasswordAction\Mpos360ChangePasswordAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\Mpos360AuthLoginHasMobileUserAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginPartnerAction\Mpos360AuthLoginPartnerAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthRecheckLoginAction\Mpos360AuthRecheckLoginAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthRemoveAccountAction\Mpos360AuthRemoveAccountAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginUserNameOnly\Mpos360AuthLoginUserNameOnlyAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginUserNameV2Action\Mpos360AuthLoginUserNameV2Action;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthVerifyMposDeviceAction\Mpos360AuthVerifyMposDeviceAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthGetDeviceTutorialAction\Mpos360AuthGetDeviceTutorialAction;

class Mpos360AuthController extends Controller
{
	public function Mpos360AuthRegister(Mpos360AuthRegisterRequest $request)
	{
		try {
			$linkRegister = app(Mpos360AuthRegisterAction::class)->run($request);
			return $this->successResponse(['mpos_register_link' => $linkRegister], $request, 200, __('lấy link đăng ký thành công'));
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360AuthLogin(Mpos360AuthLoginRequest $request)
	{
		try {
			$deviceSession = app(Mpos360AuthLoginAction::class)->run($request);
			$result = $deviceSession->toArray();

			// Lấy mnp access token, bỏ qua lỗi nếu bị lỗi
			// $getMnpAccessToken = app(Mpos360AuthLoginAction::class)->getMnpTokenAfterLoginSuccess($deviceSession);
			
			return $this->successResponse($result, $request, 200, vmsg('LoginDangNhapThanhCong'));
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360AuthLoginHasMobileUser(Mpos360AuthLoginRequest $request)
	{
		try {
			$deviceSession = app(Mpos360AuthLoginHasMobileUserAction::class)->run($request);
			$result = $deviceSession->toArray();

			return $this->successResponse($result, $request, 200, vmsg('LoginDangNhapThanhCong'));
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360AuthLoginUserName(Mpos360AuthLoginUserNameRequest $request)
	{
		try {
			$deviceSession = app(Mpos360AuthLoginUserNameAction::class)->run($request);
			$result = $deviceSession->toArray();
			return $this->successResponse($result, $request, 200, vmsg('LoginDangNhapThanhCong'));
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360AuthLoginUserNameV2(Mpos360AuthLoginUserNameRequest $request)
	{
		try {
			$deviceSession = app(Mpos360AuthLoginUserNameV2Action::class)->run($request);
			$result = $deviceSession->toArray();
			return $this->successResponse($result, $request, 200, vmsg('LoginDangNhapThanhCong'));
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360AuthLoginUserNameOnly(Mpos360AuthLoginUserNameRequest $request)
	{
		try {
			$deviceSession = app(Mpos360AuthLoginUserNameOnlyAction::class)->run($request);
			$result = $deviceSession->toArray();
			return $this->successResponse($result, $request, 200, vmsg('LoginDangNhapThanhCong'));
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360AuthVerifyMposDevice(Mpos360AuthVerifyMposDeviceRequest $request)
	{
		try {
			$result = app(Mpos360AuthVerifyMposDeviceAction::class)->run($request);
			
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360ForgotPassword(Mpos360ForgotPasswordRequest $request)
	{
		try {
			$deviceSession = app(Mpos360ForgotPasswordAction::class)->run($request);
			return $this->successResponse($deviceSession->toArray(), $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360ChangePassword(Mpos360ChangePasswordRequest $request)
	{
		try {
			$result = app(Mpos360ChangePasswordAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360AuthRemoveAccount(Mpos360AuthRemoveAccountRequest $request)
	{
		try {
			$result = app(Mpos360AuthRemoveAccountAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}


	public function Mpos360AuthLogout(Mpos360AuthLogoutRequest $request)
	{
		try {
			$deviceSession = app(Mpos360AuthLogoutAction::class)->run($request);
			return $this->successResponse(['id' => $deviceSession->id], $request, 200, __('Đăng xuất thành công'));
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360AuthGetDeviceTutorial(Mpos360AuthGetDeviceTutorialRequest $request)
	{
		try {
			$result = app(Mpos360AuthGetDeviceTutorialAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	// public route
	public function Mpos360AuthRecheckLogin(Mpos360AuthRecheckLoginRequest $request)
	{
		try {
			$result = app(Mpos360AuthRecheckLoginAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360AuthLoginPartner(Mpos360AuthLoginUserNameRequest $request)
	{
		try {
			$result = app(Mpos360AuthLoginPartnerAction::class)->run($request);
			return $this->successResponse($result->toArray(), $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
} // End class
