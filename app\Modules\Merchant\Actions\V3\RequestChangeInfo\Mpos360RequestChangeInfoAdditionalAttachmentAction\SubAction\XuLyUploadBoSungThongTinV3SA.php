<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\SubAction;

use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use Exception;

class XuLyUploadBoSungThongTinV3SA
{
	/**
	 * $profilesImgUrl: array:1 [
			"substituteCertUrls" => array:2 [
				0 => "https://nextpay-crm.s3-ap-southeast-1.amazonaws.com/user/mpos360/50232380/FILE/6713739db164104faeb13e181729328028489-42468_uTIg0y6va9oCZHiZ.pdf"
				1 => "https://nextpay-crm.s3-ap-southeast-1.amazonaws.com/user/mpos360/50232380/FILE/671373a1b164104faeb13e191729328031936-64962_71m11GWUSQQjenIx.pdf"
			]

		]
	 */
	public function run(Mpos360MerchantRequest $mpos360McRequest, $profilesImgUrl, $request) {
		$mpos360McRqSupplement = $mpos360McRequest->mpos360McSupplementNew;

		$dataRequest = json_decode($mpos360McRqSupplement->data_request, true);
		$dataRequest['attachments'] = $profilesImgUrl;
		$dataRequest['raw_attachments'] = $request->json('data.attachments');
		$dataRequest['additional_profiles'] = $request->json('data.additional_profiles');

		$mpos360McRqSupplement->data_request = json_encode($dataRequest);
		$r = $mpos360McRqSupplement->save();

		if (!$r) {
			throw new BusinessException('Lỗi không cập nhật được thông tin giấy tờ bổ sung');
		}

		// Về màn hình done luôn
		return [
			'id'               => $mpos360McRequest->id,
			'msg'              => 'Bổ sung hồ sơ thành công',
			'status'           => 'SUCCESS',
			'can'              => Mpos360Enum::MPOS360_CAN_MARK_DONE_REQUEST,
			'list_sign_method' => [],
			'scan_method'      => []
		];
	}
} // End class