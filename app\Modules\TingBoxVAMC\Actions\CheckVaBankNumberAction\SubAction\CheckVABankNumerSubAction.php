<?php

namespace App\Modules\TingBoxVAMC\Actions\CheckVaBankNumberAction\SubAction;

use App\Lib\partner\VA;

class CheckVABankNumerSubAction
{
	public VA $va;

	public function __construct(VA $va)
	{
		$this->va = $va;
	}

	public function run()
	{
		return [
			'isValid' => true,
			'message' => 'Mã VA Bank hợp lệ'
		];

		$p = [];
		$dataAsJson = json_encode($p);
		$encrypt = $this->va->encrypt($dataAsJson);

		$checksum = $this->va->createChecksum($encrypt);

		$dataPost = [
			'app_id' => VAG_APP_ID,
			'data' => $encrypt,
			'checksum' => $checksum,
		];

		$url = VAG_BASE_URL . '/v1/merchant/va-mc/request-link';
		try {
			$r = $this->va->call($url, $dataPost, 5);
		} catch (\Throwable $th) {
			$r = [];
		}

		return $r;
	}
}
