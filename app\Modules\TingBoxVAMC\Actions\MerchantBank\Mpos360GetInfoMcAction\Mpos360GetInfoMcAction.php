<?php

namespace App\Modules\TingBoxVAMC\Actions\MerchantBank\Mpos360GetInfoMcAction;

use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Lib\MnpOnboardNewMcHelper;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\Mpos360CheckVaBankNumberRequest;

class Mpos360GetInfoMcAction
{
	private MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public function __construct( MnpOnboardNewMcHelper $mnpOnboardNewMcHelper ) {
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run(Request $request): array
	{
		$detailMc = $this->mnpOnboardNewMcHelper->detailMcV2([
			'mposMcId' => $request->json('data.merchantId')
		]);

		if (empty($detailMc['data'])) {
			return [ 'hasBankVanp' => 'NO', 'holderName' => '' ];
		}

		$holderName = $detailMc['data']['passportObject']['customerName'] ?? $detailMc['data']['mcName'];
		$holderName = Str::of($holderName)->ascii()->upper()->trim()->__toString();

		return [
			'hasBankVanp' => !empty($detailMc['data']['bankId']) ? 'YES' : 'NO',
			'holderName' => $holderName
		];
	}
}
