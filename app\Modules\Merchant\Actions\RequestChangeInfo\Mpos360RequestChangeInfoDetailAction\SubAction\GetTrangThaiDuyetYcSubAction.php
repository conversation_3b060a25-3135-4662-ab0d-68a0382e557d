<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoDetailAction\SubAction;

use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;

class GetTrangThaiDuyetYcSubAction
{
	public function run(Mpos360MerchantRequest $rq)
	{
		$returnData = [];

		if ($rq->isHetHanVaChuaTaoYc()) {
			return $returnData;
		}

		if ($rq->status >= Mpos360Enum::MPOS360_MC_REQUEST_STT_DA_GUI_SANG_MNP) {
			switch ($rq->status) {
				case Mpos360Enum::MPOS360_MC_REQUEST_STT_DA_GUI_SANG_MNP:
					$returnData[] = [
						'label' => vmsg('Trạng thái duyệt'),
						'value' => vmsg('Chờ duyệt'),
						'other_data' => (object) [
							'text_color' => '#ffffff',
							'bg_color' => '#e99323',
							'display_type' => 'pills'
						]
					];
					break;

				case Mpos360Enum::MPOS360_MC_REQUEST_STT_DA_XU_LY:
					$returnData[] = [
						'label' => 'Trạng thái duyệt',
						'value' => vmsg('Đã duyệt'),
						'other_data' => (object) [
							'text_color' => '#ffffff',
							'bg_color' => '#018bf4',
							'display_type' => 'pills'
						]
					];
					break;

				case Mpos360Enum::MPOS360_MC_REQUEST_STT_DA_TU_CHOI:
					$returnData[] = [
						'label' => 'Trạng thái duyệt',
						'value' => vmsg('Bị từ chối'),
						'other_data' => (object) [
							'text_color' => '#ffffff',
							'bg_color' => '#d12e29',
							'display_type' => 'pills'
						]
					];

					$returnData[] = [
						'label' => vmsg('Lý do bị từ chối'),
						'value' => '',
						'other_data' => (object) [
							'text_color' => '#d12e29',
							'bg_color' => '#ffffff',
						]
					];
					break;

				case Mpos360Enum::MPOS360_MC_REQUEST_STT_CAP_NHAT_LOI:
					$returnData[] = [
						'label' => 'Trạng thái duyệt',
						'value' => vmsg('Cập nhật lỗi'),
						'other_data' => (object) [
							'text_color' => '#ffffff',
							'bg_color' => '#d12e29',
							'display_type' => 'pills'
						]
					];
					break;

				default:
					// code
					break;
			}
		}

		if ($rq->status < Mpos360Enum::MPOS360_MC_REQUEST_STT_DA_GUI_SANG_MNP) {
			$returnData[] = [
				'label' => 'Trạng thái duyệt',
				'value' => vmsg('Không duyệt'),
				'other_data' => (object) [
					'text_color' => '#ffffff',
					'bg_color' => '#000000',
					'display_type' => 'pills'
				]
			];

			if ($rq->status_verify != Mpos360Enum::MPOS360_MC_VERIFY_STT_DA_THUC_HIEN_BUOC3) {
				$returnData[] = [
					'label' => vmsg('Lý do'),
					'value' => vmsg('Cần xác thực thay đổi thông tin (selfie)'),
					'other_data' => (object) [
						'text_color' => '#d22e2a',
						'bg_color' => '#ffffff',
					]
				];
			}
		}

		$comment = json_decode($rq->comment ?? '[]', true);
		if (!empty($comment)) {
			$commentReason = '';
			foreach ($comment as $cmt) {
				$commentReason .= sprintf('%s. ', $cmt);
			}  

			$returnData[] = [
				'label' => vmsg('Thông tin khác'),
				'value' => trim($commentReason),
				'other_data' => (object) [
					'text_color' => '#d22e2a',
					'bg_color' => '#ffffff',
				]
			];
		}
		

		return $returnData;
	} // End method
} // End class
