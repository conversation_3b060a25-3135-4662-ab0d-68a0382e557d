<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransQRListAction\Mpos360TransQRListSubAction;

use App\Lib\Logs;
use Carbon\Carbon;
use App\Lib\Helper;
use App\Lib\partner\MPOS;
use Illuminate\Support\Str;
use App\Lib\partner\MposAWS;

class TransQRGetDataSubAction
{
	public MPOS $mpos;
	public MposAWS $mposAWS;

	public function __construct(MPOS $mpos, MposAWS $mposAWS)
	{
		$this->mpos = $mpos;
		$this->mposAWS = $mposAWS;
	}
	
	public function getData($params)
	{
		$data = [];
		// $result = $this->mpos->getTransQRList($params);
		$result = $this->mposAWS->getListGiaoDichQr($params);

		if (isset($result['status_code_partner']) && $result['status_code_partner'] == 1000) {
			$result['data']['data'] = collect($result['data']['data'])->filter(function ($item) {
				return !empty($item['txid']);
			})
			->sortByDesc(function ($item) {
				return Carbon::createFromFormat('d/m/Y H:i:s', $item['createdDate'])->timestamp;
			})
			->values()
			->all();

			$data['data'] = $result['data']['data'];
			$data['total'] = count($result['data']['data']);
		};

		$data = $this->mappingData($data);

		return $data;
	}

	public function getTenChuTheVa4SoCuoi($tranItem) {
		try {
			$returnString = '';
			if (!empty($tranItem['cardHolderName'])) {
				$returnString = Str::limit($tranItem['cardHolderName'], 20) . '....';
				if (!empty($tranItem['pan'])) {
					$returnString .= substr($tranItem['pan'], -4);
					return $returnString;
				}
			}

			return $tranItem['pan'] ?? "";
		}catch(\Throwable $th) {
			@mylog(['ErrPan' => Helper::traceError($th)]);
			return $tranItem['pan'] ?? "";
		}
	}

	public function mappingData($data)
	{
		$statusArr = app(TransQRMappingSubAction::class)->mappingStatusTransaction();
		$transTypeArr = app(TransQRMappingSubAction::class)->mappingTransactionType();
		$dataArr = [];
		$returnData = [];
		if (!empty($data['data'])) {
			foreach ($data['data'] as $key => $tranItem) {
				$timestamp = strtotime(Carbon::createFromFormat('d/m/Y H:i:s', $tranItem['createdDate']));
				$timeD = date('d-m-Y', $timestamp);
				if ($timeD == date('d-m-Y')) {
					$timeD = __('trans.title.Hôm nay');
				}
				$dataArr[$timeD][] = [
					'raw_date' => $tranItem['createdDate'],
					'txid' => $tranItem['txid'],
					'type' => $transTypeArr[$tranItem['transactionType']]['label'] ?? $tranItem['transactionType'] ?? '',
					'icon' => $transTypeArr[$tranItem['transactionType']]['icon'] ?? cumtomAsset('images/transaction/transaction-qr/QR_THE_QT.png'),
					'amount' => !empty($tranItem['amount']) ? Helper::priceFormat($tranItem['amount']) : '',
					'card' => $this->getTenChuTheVa4SoCuoi($tranItem),
					'time_created' => date('H:i', $timestamp),
					// 'status' => @$statusArr[$tranItem['status']]['label'] ?? '',
					'status' => '',
					'note' => $tranItem['reason'] ?? "",
					'other_data' => (object)[
						'status' => [
							// 'text' => @$statusArr[$tranItem['status']]['label'] ?? '',
							'text' => '',
							// 'text_color' => @$statusArr[$tranItem['status']]['text_color'] ?? '',
							// 'bg_color' => @$statusArr[$tranItem['status']]['bg_color'] ?? '',
							'text_color' => '#ffffff',
							'bg_color' => '#ffffff',
							'display_type' => 'pills'
						],

						'note' => [
							'text' => $tranItem['reason'] ?? "",
							'text_color' => '#DA2128',
							'font_style' => 'italic'
						],

						'amount' => [
							'text' => !empty($tranItem['amount']) ? Helper::priceFormat($tranItem['amount']) : '',
							'text_color' => '#DD8523',
							'font_weight' => 'bold'
						],

						'card' => [
							'text_color' => '#808890'
						],

						'time_created' => [
							'text_color' => '#808890'
						]
					],
				];
			}

			foreach ($dataArr as $key => $tranItem) {
				$returnData[] = [
					'date' => $key,
					'total' => count($tranItem),
					'list' => $tranItem,
				];
			}
		}

		return $returnData;
	}
}
