<?php

namespace App\Modules\TingBoxVAMC\Rules;

use App\Modules\TingBoxVAMC\Models\MerchantBank;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\DB;

class ChiMotVABRule implements Rule
{
	/**
	 * Validate đường link phía mobile gửi lên phải là link hình ảnh chứ ko phải link website
	 *
	 * @param  string  $attribute
	 * @param  mixed  $value: Link hình ảnh
	 * @return bool
	 */
	public function passes($attribute, $bankCode): bool
	{
		if ($bankCode != 'VAB') {
			return true;
		}

		$mcId = request()->json('data.merchantId');

		// Nếu là VAB thì check đã tồn tại trong hệ thống của MC đó chưa, nếu có bản ghi liên kết rồi thì báo lỗi
		$mcBank = MerchantBank::query()->where('merchant_id', $mcId)
																	 ->where('bank_code', 'VAB')
																	 ->first();
		
		if (!$mcBank) {
			return true;
		}

		$mcShopBank = MerchantShopBank::query()->where('merchant_bank_id', $mcBank->id)
																					 ->where('merchant_id', $mcId)
																					 ->where('status_link', MerchantShopBank::STT_LINK_DA_LIEN_KET)
																					 ->first();
		if ($mcShopBank) {
			return false;
		}

		return true;
	}

	/**
	 * Get the validation error message.
	 *
	 * @return string
	 */
	public function message()
	{
		return 'Bạn đã liên kết tài khoản Việt Á Bank cho 1 điểm bán rồi. Không thể tạo thêm liên kết cho điểm bán khác';
	}
}
