<?php
namespace App\Modules\TingBoxVAMC\Requests\MerchantBank;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\Merchant\Requests\MerchantRequest;

class SendOtpSwitchModeBankRequest extends MerchantRequest
{
    /**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.username' => ['required', 'string'],
			'data.channel' => ['required', 'string', 'max:25', Rule::in(['ZALO', 'SMS'])],
			'data.otp_id' => ['present', 'string'],
			'data.merchantId' => ['required', 'numeric'],
			'data.otpType' => ['required', 'string', 'in:CHUYEN_MODE,SET_MAC_DINH,HUY_LIEN_KET'],
			'data.mobileUserId' => ['required', 'string'],
		];
	}

	public function messages() {
		return [
			'data.username.required' => 'Username là bắt buộc',
			'data.username.string' => 'Username phải là kiểu chuỗi ký tự',
			'data.channel.required' => 'Kênh nhận mã OPT là bắt buộc',
			'data.channel.string' => 'Kênh nhận mã phải là kiểu chuỗi ký tự',
			'data.merchantId.required' => 'Id merchant là bắt buộc',
			'data.merchantId.string' => 'Id merchant phải là kiểu chuỗi',
			'data.otpType.required' => 'Loại otp là bắt buộc',
			'data.otpType.string' => 'Loại otp phải là kiểu chuỗi',
			'data.otpType.in' => 'Otp phải được sử dụng trong các ngữ cảnh: Chuyển mode nhận tiền; Sét mặc định; hoặc Hủy liên kết',
			'data.mobileUserId.required' => 'Mã định danh mobile là bắt buộc',
			'data.mobileUserId.string' => 'Mã định danh mobile là kiểu chuỗi',
		];
	}

	public function isGuiLaiOtpDangKy(): bool {
		return !empty($this->json('data.otp_id'));
	}

	public function isTaoMoiOtpDangKy(): bool {
		return !$this->isGuiLaiOtpDangKy();
	}

	public function isChuyenModeVAMC(): bool {
		return $this->json('data.otpType') == 'CHUYEN_MODE' && $this->json('data.merchantShopBankId') != '-1';
	} 

	public function isChuyenModeVANP(): bool {
		return $this->json('data.otpType') == 'CHUYEN_MODE' && $this->json('data.merchantShopBankId') == '-1';
	}

	public function isSetMacDinh(): bool {
		return $this->json('data.otpType') == 'SET_MAC_DINH';
	}
} // End class