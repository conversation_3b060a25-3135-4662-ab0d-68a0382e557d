<html lang="zxx">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title><PERSON><PERSON><PERSON> k<PERSON> t<PERSON><PERSON></title>
    <link rel="icon" type="image/png" href="{{ cumtomAsset('assets/img/favicon-mpos.svg') }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">

		<script src="https://cdn.jsdelivr.net/npm/promise-polyfill"></script>
		<script src="https://cdn.jsdelivr.net/npm/fetch-polyfill"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous">
    </script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.0/jquery.min.js"></script>
    <link rel="stylesheet" href="{{ cumtomAsset('assets/css/style.css') }}">
    <link rel="stylesheet" href="{{ cumtomAsset('assets/css/style-other.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<style>
    .se-pre-con {
        position: fixed;
        left: 0px;
        top: 0px;
        width: 100%;
        height: 100%;
        z-index: 9999;
        opacity: 0.6;
        /* background: url(/mpos360-app-api/public/images/web/loader-64x/Preloader_3.gif) center no-repeat #fff; */
        background: url(/images/web/loader-64x/Preloader_3.gif) center no-repeat #fff;
    }

    .ui-datepicker {
        z-index: 99 !important;
    }
</style>

<body>

    <div id="main-content">
        <section class="ftco-section login-wap">

            <div class="wrapper-page">
                <div class="head-wrap">
                    <label>Đăng ký tài khoản</label>
                    <span><img src="assets/img/logo-mpos360.svg"></span>
                </div>
                <div class="login-wrap">

                    <form id="registerForm">
                        <div class="form-floating mb-3">
                            <input type="tel" class="form-control" id="phone" name="phone" placeholder="Nhập số điện thoại" autocomplete="tel" required>
                            <label for="phone">Nhập số điện thoại</label> <!-- Đặt nội dung trong label giống placeholder -->
                            <div class="error-message text-danger mt-1" id="error-phone"></div>
                        </div>
                        <div class="form-floating mb-3 position-relative auth-pass-inputgroup mb-3">
                            <input type="password" class="form-control password-input" id="password" name="password" placeholder="Nhập mật khẩu" autocomplete="off" onkeyup="return this.value = this.value.trim();" required>
                            <label for="password">Mật khẩu</label>
                            <button class="btn btn-link position-absolute end-0 text-decoration-none text-muted password-addon" type="button">
                                <img src="assets/img/hidden-eye.svg">
                            </button>
                            <div class="error-message text-danger mt-1" id="error-password"></div>
                        </div>
                        <div class="form-floating mb-3 position-relative auth-pass-inputgroup mb-3">
                            <input type="password" class="form-control password-input" id="confirm_password" name="confirm_password"
                                placeholder="Nhập lại mật khẩu" autocomplete="off" onkeyup="return this.value = this.value.trim();" required>
                            <label for="confirm_password">Nhập lại mật khẩu</label>
                            <button class="btn btn-link position-absolute end-0 text-decoration-none text-muted password-addon" type="button">
                                <img src="assets/img/hidden-eye.svg">
                            </button>
                            <div class="error-message text-danger mt-1" id="error-confirm-password"></div>
                        </div>
                        <div class="d-flex flex-column f16"> <!-- Thêm flex-column để xếp theo cột -->
                            <div class="d-flex">
                                <input class="form-check-input me-2" type="checkbox" id="agree" name="agree" required>
                                <span id="agree-text">Tôi đồng ý với</span> <a href="#" data-bs-toggle="modal" data-bs-target="#dieu-khoan" class="ms-1">điều khoản và chính sách</a>
                            </div>
                            <div class="error-message text-danger mt-1 ms-2" id="error-agree"></div> <!-- Lỗi hiển thị dưới -->
                        </div>

												<div class="d-flex">
													<div class="form-floating mt-3 position-relative w-100" style="margin-right: 10px;">
														<input type="tel" class="form-control password-input" id="qa" name="qa" placeholder="Nhập câu trả lời" autocomplete="off" required>
														<div class="error-message text-danger mt-1 ms-2" id="error-qa"></div> <!-- Lỗi hiển thị dưới -->
														<label for="qa">Câu hỏi bảo mật</label>
													</div>

													<div class="form-floating position-relative align-items-center bg-body-secondary d-flex f20 justify-content-center ml-auto mt-3 w-50" style="border-radius: 4px; height: 60px;">
														@foreach ($questionExplode as $index => $element)
															<strong class="wrap-{{ rand(0, count($questionExplode)) }}-{{ \Illuminate\Support\Str::random(6)}}">
																<span class="logical-{{ \Illuminate\Support\Str::random(4)}}-{{ rand(0, count($questionExplode)) }}">&nbsp;{!! $element !!}</span>
															</strong>
														@endforeach 
													</div>
												</div>

												<p class="text-primary d-none" id="textHelp">Vui lòng nhập kết quả là chữ số</p>

												<input type="hidden" name="qaId" id="qaId" value="{{ $qaId }}">

                        <div class="d-flex mt-5">
                            <!-- <a href="#" class="text-center btn-blue w-100" data-bs-toggle="modal" data-bs-target="#link-me">Đăng ký</a> -->
                            <button type="submit" class="text-center btn-blue w-100" style="border: none;">Đăng ký</button>
                        </div>
                    </form>

                </div>
            </div>

        </section>
    </div>

    <div id="loadingPage"></div>


    <div class="modal fade action-sheet" id="sendOtpModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content" id="sendOtpModalContent">

            </div>
        </div>
    </div>

    <!-- Popup: Báo số điện thoại đã đăng ký -->
    <div class="modal fade" id="phone-regis" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle"
        >
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-body">
                    <p class="text-center"><img src="assets/img/waring.svg"> </p>
                    <p class="text-center f16 message">Rất tiếc, đã có tài khoản TingBox <span class="BE-bold"> 0905949494</span>, Quý khách vui lòng sử dụng số điện
                        thoại khác để đăng ký hoặc đăng nhập bằng tài khoản đã có.</p>

                    <p class="text-center">
                        <a href="#" id="close-phone-regis" class="text-center btn-blue w-100 d-block">OK</a>
                    </p>
                </div>

            </div>
        </div>
    </div>

    <div class="modal fade" id="no-received" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle"
        >
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">

                <div class="modal-body">
                    <p class="text-center"><img src="assets/img/no-otp.svg"> </p>
                    <p class="text-center f16">Nếu không nhận được mã OTP, Quý khách vui lòng kiểm tra lại tin nhắn Zalo/SMS và
                        đảm bảo tín hiệu mạng di
                        động ổn định. </p>
                    <p class="text-center f16"> Trong trường hợp vẫn chưa nhận được mã OTP sau khi kiểm tra, vui lòng liên hệ với
                        bộ phận Chăm sóc Khách
                        hàng để được hỗ trợ kịp thời.</p>

                    <p class="mt-4 mb-0"> <a href="#" id="no-received-close" class="text-center btn-blue w-100 d-block" data-dismiss="modal">OK</a></p>
                </div>

            </div>
        </div>
    </div>
    <div class="modal fade" id="incorrect-code" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle"
        >
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-body">
                    <p class="text-center"><img src="assets/img/otp-notcorrect.svg"> </p>
                    <p class="text-center f16">Mã OTP không chính xác hoặc đã hết thời gian hiệu lực. Xin vui lòng kiểm tra lại mã
                        trong tin nhắn hoặc yêu cầu một mã mới.</p>

                    <div class="d-flex mt-4">
                        <a href="#" class="text-center btn-gray w-100 d-block me-2 close-incorrect-code">Bỏ qua</a>
                        <a href="#" class="text-center btn-blue w-100 d-block" id="resend-otp">Lấy mã khác</a>
                    </div>
                </div>

            </div>
        </div>
    </div>
    @include('TingBoxWeb.modal.modalDieuKhoan')

    <script>
        $(document).ready(function() {

            const APP_URL = "";
            const $phoneInput = $("#phone");
            const $passwordInput = $("#password");
            const $confirmPasswordInput = $("#confirm_password");
            const $agreeCheckbox = $("#agree");
            const $qaInput = $("#qa");

            const $errorPhone = $("#error-phone");
            const $errorPassword = $("#error-password");
            const $errorConfirmPassword = $("#error-confirm-password");
            const $errorAgree = $("#error-agree");
            const $errorQa = $("#error-qa");

            function validatePhone() {
                const phone = $phoneInput.val().trim();
                if (!/^0[0-9]{9}$/.test(phone)) {
                    $errorPhone.text("Số điện thoại không hợp lệ (phải là 10 số và bắt đầu bằng 0).");
                    $phoneInput.addClass("is-invalid");
                } else {
                    $errorPhone.text("");
                    $phoneInput.removeClass("is-invalid");
                }
            }

            function validatePassword() {
                const password = $passwordInput.val().trim();
                if (password.length < 6) {
                    $errorPassword.text("Mật khẩu tối thiểu từ 6 ký tự.");
                    $passwordInput.addClass("is-invalid");
                } else {
                    $errorPassword.text("");
                    $passwordInput.removeClass("is-invalid");
                }
            }

            function validateConfirmPassword() {
                const password = $passwordInput.val().trim();
                const confirmPassword = $confirmPasswordInput.val().trim();

                // Kiểm tra nếu mật khẩu xác nhận và mật khẩu chính khớp
                if (confirmPassword && confirmPassword !== password) {
                    $errorConfirmPassword.text("Mật khẩu không trùng khớp.");
                    $confirmPasswordInput.addClass("is-invalid");
                } else {
                    $errorConfirmPassword.text("");
                    $confirmPasswordInput.removeClass("is-invalid");
                }
            }

            function validateAgree() {
                if (!$agreeCheckbox.is(":checked")) {
                    $errorAgree.text("Bạn phải đồng ý với điều khoản và chính sách.");
                } else {
                    $errorAgree.text("");
                }
            }

            function validateQa() {
                const qa = $qaInput.val().trim();
                if (qa.length == 0) {
                    $errorQa.text("Câu trả lời không được để trống.");
                    $qaInput.addClass("is-invalid");
                } else {
                    $errorQa.text("");
                    $qaInput.removeClass("is-invalid");
                }
            }



            // Gắn sự kiện
            $phoneInput.on("input", validatePhone);
            $qaInput.on("input", validateQa);
            $passwordInput.on("input", function() {
                validatePassword();
                validateConfirmPassword();
            });
            $confirmPasswordInput.on("input", validateConfirmPassword);
            $agreeCheckbox.on("change", validateAgree);

            $('.password-addon').on('click', function() {
                // Tìm đến input liên kết với nút được nhấn
                let passwordInput = $(this).siblings('.password-input');
                let passwordType = passwordInput.attr('type');

                // Chuyển đổi type giữa "password" và "text"
                if (passwordType === 'password') {
                    passwordInput.attr('type', 'text');
                    $(this).find('img').attr('src', 'assets/img/eye.svg'); // Thay đổi icon
                } else {
                    passwordInput.attr('type', 'password');
                    $(this).find('img').attr('src', 'assets/img/hidden-eye.svg'); // Thay đổi lại icon
                }
            });

            // Xử lý submit form
            $("#registerForm").on("submit", function(e) {
                e.preventDefault();

                validatePhone();
                validatePassword();
                validateConfirmPassword();
                validateAgree();
                validateQa();

                // Kiểm tra xem có lỗi không
                if (
                    $errorPhone.text() === "" &&
                    $errorPassword.text() === "" &&
                    $errorConfirmPassword.text() === "" &&
                    $errorAgree.text() === "" &&
                    $errorQa.text() === ""
                ) {
                    const formData = {
                        phone: $phoneInput.val().trim(),
                        password: $passwordInput.val().trim(),
                        confirm_password: $confirmPasswordInput.val().trim(),
                        qa: $qaInput.val().trim(),
                        qaId: $('#qaId').val(),
                    };
                    $('#loadingPage').addClass('se-pre-con');

                    $.ajax({
                        url: APP_URL + "/TBWebMpos360SubmitDangKy",
                        type: "POST",
                        headers: {
                            "X-CSRF-TOKEN": "{{ csrf_token() }}",
                        },
                        contentType: "application/json",
                        data: JSON.stringify(formData),
                        success: function(data) {
													if (data.success) {
														if (data.merchant.merchantId) {
															return window.location.href = data.merchant.redirectUrl;
														}else {
															$("#sendOtpModal").modal('show')
																	.find("#sendOtpModalContent")
																	.html(data.html);
														}
													} else {
															$('#phone-regis').modal('show').find('.message').html(data.message);

													}
													$('#loadingPage').removeClass('se-pre-con');
                        },
                        error: function(error) {
                            $('#loadingPage').removeClass('se-pre-con');
                            alert("Có lỗi xảy ra, Hãy thông báo kỹ thuật hỗ trợ xử lý.");

                        },
                    });
                }
            });

            $(document).on('click', '#close-phone-regis', function(e) {
                e.preventDefault();
                $('#phone-regis').modal('hide');
            });

            window.addEventListener('beforeunload', (event) => {
                $('#loadingPage').addClass('se-pre-con');
            });

            $(document).on('click','#agree-text', function(e){
                if ($('#agree').is(':checked')){
                    $('#agree').prop('checked', false);
                }else {
                    $('#agree').prop('checked', true);
                }
            })
        });
    </script>

</body>

</html>