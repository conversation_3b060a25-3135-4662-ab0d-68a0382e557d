<?php

namespace App\Modules\Merchant\Model;

use Illuminate\Database\Eloquent\Model;

class Mpos360User extends Model
{
	protected $connection = 'mpos360_data';

	protected $table      = 'users';
	
	protected $guarded    = [];
	
	public $timestamps    = false;

	protected $dates      = [];

	protected $hidden     = [];

	public function getUserMcName(): string {
		$dataUser = json_decode($this->data_users, true);
		return $dataUser['merchantName'];
	}

	public function getUserMcEmail(): string {
		$dataUser = json_decode($this->data_users, true);
		return $dataUser['merchantEmail'];
	}

	public function getUserName(): string {
		$dataUser = json_decode($this->data_users, true);
		return $dataUser['username'];
	}
} // End class
