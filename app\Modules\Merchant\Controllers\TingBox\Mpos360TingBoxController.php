<?php

namespace App\Modules\Merchant\Controllers\TingBox;

use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Lib\MnpOnboardNewMcHelper;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\TingBox\Mpos360SaveTknhStep3Request;
use App\Modules\Merchant\Requests\TingBox\Mpos360SaveCuaHangStep1Request;
use App\Modules\Merchant\Requests\TingBox\Mpos360GetFormTingBoxStep1Request;
use App\Modules\Merchant\Requests\TingBox\Mpos360GetCauHinhByMerchantRequest;
use App\Modules\Merchant\Requests\TingBox\Mpos360CheckTknhKhaiBaoStep3Request;
use App\Modules\Merchant\Requests\TingBox\Mpos360GetStepKhaiBaoTingBoxRequest;
use App\Modules\Merchant\Requests\TingBox\Mpos360GetFormKhaiBaoTknhStep3Request;
use App\Modules\Merchant\Requests\TingBox\Mpos360SaveThietBiTingBoxStep2Request;
use App\Modules\Merchant\Requests\TingBox\Mpos360DetailThietBiTingBoxStep2Request;
use App\Modules\Merchant\Requests\TingBox\Mpos360SaveThietBiTingBoxStep2V2Request;
use App\Modules\Merchant\Requests\TingBox\Mpos360GetQuanHuyenTuMaTinhThanhStep1Request;
use App\Modules\Merchant\Actions\TingBox\Mpos360SaveTknhStep3Action\Mpos360SaveTknhStep3Action;
use App\Modules\Merchant\Actions\TingBox\Mpos360SaveCuaHangStep1Action\Mpos360SaveCuaHangStep1Action;
use App\Modules\Merchant\Actions\TingBox\Mpos360SaveCuaHangStep1Action\Mpos360SaveCuaHangStep1V2Action;
use App\Modules\Merchant\Actions\TingBox\Mpos360GetFormTingBoxStep1Action\Mpos360GetFormTingBoxStep1Action;
use App\Modules\Merchant\Actions\TingBox\Mpos360GetCauHinhByMerchantAction\Mpos360GetCauHinhByMerchantAction;
use App\Modules\Merchant\Actions\TingBox\Mpos360CheckTknhKhaiBaoStep3Action\Mpos360CheckTknhKhaiBaoStep3Action;
use App\Modules\Merchant\Actions\TingBox\Mpos360GetStepKhaiBaoTingBoxAction\Mpos360GetStepKhaiBaoTingBoxAction;
use App\Modules\Merchant\Actions\TingBox\Mpos360GanThietBiTingBoxStep2Action\Mpos360GanThietBiTingBoxStep2Action;
use App\Modules\Merchant\Actions\TingBox\Mpos360GetFormKhaiBaoTknhStep3Action\Mpos360GetFormKhaiBaoTknhStep3Action;
use App\Modules\Merchant\Actions\TingBox\Mpos360GetStepKhaiBaoTingBoxAction\Mpos360GetStepKhaiBaoTingBoxVamcAction;
use App\Modules\Merchant\Actions\TingBox\Mpos360SaveThietBiTingBoxStep2Action\Mpos360SaveThietBiTingBoxStep2Action;
use App\Modules\Merchant\Actions\TingBox\Mpos360SaveThietBiTingBoxStep2Action\Mpos360SaveThietBiTingBoxStep2V2Action;
use App\Modules\Merchant\Actions\TingBox\Mpos360DetailThietBiTingBoxStep2Action\Mpos360DetailThietBiTingBoxStep2Action;
use App\Modules\Merchant\Actions\TingBox\Mpos360CheckProgressKhaiBaoTingBoxAction\Mpos360CheckProgressKhaiBaoTingBoxAction;

class Mpos360TingBoxController extends Controller
{
	public function Mpos360GetStepKhaiBaoTingBox(Mpos360GetStepKhaiBaoTingBoxRequest $request)
	{
		try {
			$result = app(Mpos360GetStepKhaiBaoTingBoxAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360GetStepKhaiBaoTingBoxVamc(Mpos360GetStepKhaiBaoTingBoxRequest $request)
	{
		try {
			$result = app(Mpos360GetStepKhaiBaoTingBoxVamcAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360CheckProgressKhaiBaoTingBox(Mpos360GetStepKhaiBaoTingBoxRequest $request)
	{
		try {
			$result = app(Mpos360CheckProgressKhaiBaoTingBoxAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360GetFormTingBoxStep1(Mpos360GetFormTingBoxStep1Request $request)
	{
		try {
			$result = app(Mpos360GetFormTingBoxStep1Action::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360GetQuanHuyenTuMaTinhThanhStep1(Request $request)
	{
		try {
			$result = app(MnpOnboardNewMcHelper::class)->getQuanHuyenTuMaTinhThanh($request->json('data.cityId'));
			
			$returnData = [
				'cityId' => $request->json('data.cityId'),
				'listDistrict' => $result['data'] ?? []
			];
			return $this->successResponse($returnData, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360SaveCuaHangStep1(Mpos360SaveCuaHangStep1Request $request)
	{
		try {
			logger()->channel('stdout')->info('Param save cua hang', $request->json('data'));
			$result = app(Mpos360SaveCuaHangStep1Action::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360SaveCuaHangStep1V2(Mpos360SaveCuaHangStep1Request $request)
	{
		try {
			$result = app(Mpos360SaveCuaHangStep1V2Action::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}


	public function Mpos360TingBoxDeviceHuongDanSuDung(Request $request)
	{
		$tingBoxSerial = $request->get('tingBoxSerial', '');

		if (substr($tingBoxSerial, 0, 4) == 'TB01') {
			return view('TingBox.DetailTingBoxVer1', [
				'tingBoxSerial' => $request->get('tingBoxSerial'),
				'tingBoxStatus' => $request->get('tingBoxStatus'),
				'tingBoxBgColor' => '#'.$request->get('tingBoxBgColor'),
			]);
		}

		return view('TingBox.DetailTingBoxVer2', [
			'tingBoxSerial' => $request->get('tingBoxSerial'),
			'tingBoxStatus' => $request->get('tingBoxStatus'),
			'tingBoxBgColor' => '#'.$request->get('tingBoxBgColor'),
		]);
	}

	public function Mpos360SaveThietBiTingBoxStep2(Mpos360SaveThietBiTingBoxStep2Request $request)
	{
		try {
			$result = app(Mpos360SaveThietBiTingBoxStep2Action::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360SaveThietBiTingBoxStep2V2(Mpos360SaveThietBiTingBoxStep2V2Request $request)
	{
		try {
			$result = app(Mpos360SaveThietBiTingBoxStep2V2Action::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360DetailThietBiTingBoxStep2(Mpos360DetailThietBiTingBoxStep2Request $request)
	{
		try {
			$result = app(Mpos360DetailThietBiTingBoxStep2Action::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360GetFormKhaiBaoTknhStep3(Mpos360GetFormKhaiBaoTknhStep3Request $request)
	{
		try {
			$result = app(Mpos360GetFormKhaiBaoTknhStep3Action::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360CheckTknhKhaiBaoStep3(Mpos360CheckTknhKhaiBaoStep3Request $request)
	{
		try {
			$result = app(Mpos360CheckTknhKhaiBaoStep3Action::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360SaveTknhStep3(Mpos360SaveTknhStep3Request $request)
	{
		try {
			$result = app(Mpos360SaveTknhStep3Action::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360GetCauHinhByMerchant(Mpos360GetCauHinhByMerchantRequest $request)
	{
		try {
			$result = app(Mpos360GetCauHinhByMerchantAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
} // End class
