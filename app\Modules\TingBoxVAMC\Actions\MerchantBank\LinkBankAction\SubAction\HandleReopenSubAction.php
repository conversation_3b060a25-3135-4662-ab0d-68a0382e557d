<?php

namespace App\Modules\TingBoxVAMC\Actions\MerchantBank\LinkBankAction\SubAction;

use App\Modules\TingBoxVAMC\Models\MerchantShopBank;

class HandleReopenSubAction
{
	/**
	 * array:4 [
			"error_code" => "00"
			"error_message" => "Thành công"
			"checksum" => "d3c488d1c388b90c194f4db29f082ffb"
			"data" => array:20 [
				"mcRequestId" => "MPOS-*****************"
				"vaNextpayNumber" => "NPZSTXWMQAXHLM8"
				"vaBankNumber" => "962VMS3117896383851"
				"qrCode" => ""
				"qrImage" => ""
				"status" => "INACTIVE"
				"channelCode" => ""
				"vaReference" => "MC7T9FKDA4EIYNP"
				"transferDesc" => "MC7T9FKDA4EIYNP Mua hang"
				"urlConffirm" => ""
				"deepLinkConfirm" => ""
				"methodConfirm" => "OTP"
				"detail1" => ""
				"detail2" => ""
				"detail3" => ""
				"detail4" => ""
				"detail5" => ""
				"expired" => 0
				"maxResend" => 0
				"tempLockedTime" => 0
			]
		]
	 */
	public function run(MerchantShopBank $mcShopBankReopen, $reOpenResult=[], $request)
	{
		return [
			'merchantBankId'         => $mcShopBankReopen->merchantBank->id,
			'partnerRequestId'       => $reOpenResult['data']['vaNextpayNumber'],
			'requestId'              => $reOpenResult['data']['mcRequestId'],
			'countdownTimeGetNewOtp' => $reOpenResult['data']['expired'] ?? 0,
			'deepLinkConfirm' => $reOpenResult['data']['deepLinkConfirm'] ?? '',
			'urlConffirm' => $reOpenResult['data']['urlConffirm'] ?? '',
			'bankCode' => $request->json('data.bankCode'),
			'can' => '',
			'merchantShopBankId'  => $mcShopBankReopen->id,
		];
	}
}
