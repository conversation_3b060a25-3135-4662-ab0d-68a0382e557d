<?php

namespace App\Lib\partner;

use App\Lib\Logs;
use App\Lib\Helper;
use Illuminate\Support\Str;
use App\Lib\TelegramAlertWarning;
use App\Lib\MnpOnboardNewMcHelper;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Modules\Merchant\Model\MerchantOnboard;

define('SOUNDBOX_BASE_URL', env('SOUNDBOX_BASE_URL'));
define('SOUNDBOX_ACCESS_TOKEN', env('SOUNDBOX_ACCESS_TOKEN'));
define('SOUNDBOX_ORIGINAL_URL', env('SOUNDBOX_ORIGINAL_URL'));

class SoundBox
{
	private array $__logData = [];

	public function sendRequest(string $path = '', $data = [])
	{
		try {
			$url = SOUNDBOX_BASE_URL . $path;
			$this->__logData['SoundBoxUrl'] = $url;		
			
			$headers = [
				'Authorization' => SOUNDBOX_ACCESS_TOKEN
			];

			$r = Http::withoutVerifying()->withHeaders($headers)->timeout(5)->post($url, []);

			$result = $r->json();

			$this->__logData['Result'] = $result; 

			if (!empty($result['result'])) {
				return $result;
			}

			return [];
		}catch(\Throwable $th) {
			TelegramAlertWarning::sendMessage(Helper::traceError($th));
			$this->__logData['SoundBox_Err'] = Helper::traceError($th);	
		} finally {
			mylog($this->__logData);
		}
	}

	public function sendRequestAsJson(string $path = '', $data = [], int $timeout=10)
	{
		try {
			$url = SOUNDBOX_BASE_URL . $path;
			$this->__logData['SoundBoxUrl'] = $url;		

			$r = Http::withoutVerifying()->withHeaders([
				'Authorization' => SOUNDBOX_ACCESS_TOKEN,
				'Content-Type' => 'application/json',
				'Cookie' => 'JSESSIONID=C4582A55D129F23E5B9B035EDB97B562'
			])->timeout($timeout)->post($url, $data);

			
			$this->__logData['SoundBox_Data'] = $data;		

			$result = $r->json();
			
			$this->__logData['Result'] = $result; 

			if (!empty($result['result'])) {
				return $result;
			}

			return [];
		}catch(\Throwable $th) {
			TelegramAlertWarning::sendMessage(Helper::traceError($th));
			$this->__logData['SoundBox_Err'] = Helper::traceError($th);	
			return [];
		} finally {
			mylog($this->__logData);
		}
	}

	public function call(string $url, $data = [], int $timeout=10, $isNeedResult=false)
	{
		try {
			$this->__logData['SoundBoxUrl'] = $url;		

			$r = Http::withoutVerifying()->withHeaders([
				'Authorization' => SOUNDBOX_ACCESS_TOKEN,
				'Content-Type' => 'application/json',
				'Cookie' => 'JSESSIONID=C4582A55D129F23E5B9B035EDB97B562'
			])->timeout($timeout)->post($url, $data);

			
			$this->__logData['SoundBox_Data'] = $data;		

			$result = $r->json();
			$this->__logData['Result'] = $result; 
			
			if ($isNeedResult) {
				return $result;
			}

			if (!empty($result['result'])) {
				return $result;
			}

			return [];
		}catch(\Throwable $th) {
			TelegramAlertWarning::sendMessage(Helper::traceError($th));
			$this->__logData['SoundBox_Err'] = Helper::traceError($th);	
			return [];
		} finally {
			mylog($this->__logData);
		}
	}

	public function callMCMN(string $url, $data = [], int $timeout=10, $isNeedResult=false)
	{
		$mnpAccessToken = app(MnpOnboardNewMcHelper::class)->getAccessTokenMcDefault();
		
		try {
			$this->__logData['SoundBoxUrl'] = $url;		
			$this->__logData['SoundBox_Data'] = $data;		

			$r = Http::retry(2, 500)->withoutVerifying()->withHeaders([
				'Authorization' => $mnpAccessToken,
				'Content-Type' => 'application/json',
				'Cookie' => 'JSESSIONID=C4582A55D129F23E5B9B035EDB97B562',
				'X-Trace-Id' => request()->header('X-Trace-Id')
			])->timeout(15)->post($url, $data);


			$result = $r->json();
			$this->__logData['Result'] = $result;  
			
			if ($isNeedResult) {
				return $result;
			}

			if (!empty($result['result'])) {
				return $result;
			}

			return [];
		}catch(\Throwable $th) {
			TelegramAlertWarning::sendMessage(Helper::traceError($th));
			$this->__logData['SoundBox_Err'] = Helper::traceError($th);	
			return [];
		} finally {
			mylog($this->__logData);
		}
	}

	public function createVAMC($params = []) {
		$url = env('SOUNDBOX_MCMN_BASE_URL') . 'api/create-vamc';

		$data = [
			"mcId"           => $params['mcId'], // merchantId
			"mobileUserName" => $params['mobileUserName'], // Mobile UserName
			"qrCode"         => $params['qrCode'], // Tham số qrCode của VAMC trả về
			"qrType"         => $params['qrType'] ?? 'VIETQR',
			"bankCode"       => $params['bankCode'], // mã ngân hàng (lấy của mpos)
			"accountNumber"    => $params['accountNumber'], // stk ngân hàng vaBankNumber,
			"accountName"  => $params['accountName'], // Tên qr cửa hàng
			"vaNextPayNumber"    => $params['vaNextPayNumber'], // stk ngân hàng vaBankNumber,
			"mcBankAccountHolderName" => $params['mcBankAccountHolderName'] ?? '', // Họ tên chủ tài khoản
			"mcBankAccountNumber" => $params['mcBankAccountNumber'] ?? '', // STK ngân hàng mà MC liên kết
			"mcBankAccountMobile" => $params['mcBankAccountMobile'] ?? '', // SĐT mà MC đã đăng ký với NH
			"integratedMethod"    => $params['integratedMethod'], // kênh bank, VA_NP, VA_MC
			"partnerCode" => $params['partnerCode'] ?? "NP",
			'assignDefault' => $params['assignDefault'] ?? false
		];

		Logs::writeInfo('create-vamc', $data);
		$r = $this->callMCMN($url, $data, 5, true);
		Logs::writeInfo('create-vamc-response', $r);
		return $r;
	}

	public function assignDeviceTingBox($params = []) {
		$url = env('SOUNDBOX_MCMN_BASE_URL') . 'api/assign-device';

		$data = [
			"mcId"           => $params['mcId'], // merchantId
			"mcEmail"        => $params['mcEmail'], // SĐT MC hoặc Email MC
			"mobileUserName" => $params['mobileUserName'], // Mobile UserName
			"deviceId"       => $params['deviceId'], // Serial của thiết bị, có nhiều thiết bị thì gọi api này nhiều lần
			"status"         => $params['status'], // Trạng thái: ACTIVE, INACTIVE
			"qrCode"         => $params['qrCode'], // Tham số qrCode của VAMC trả về
			"qrType"         => $params['qrType'] ?? 'VIETQR',
			"bankCode"       => $params['bankCode'], // mã ngân hàng (lấy của mpos)
			"integratedMethod"    => $params['integratedMethod'], // kênh bank, VA_NP, VA_MC
			"accountName"  => $params['accountName'], // Tên qr cửa hàng
			"accountNumber"    => $params['accountNumber'], // stk ngân hàng vaBankNumber,
			"vaNextPayNumber"    => $params['vaNextPayNumber'], // stk ngân hàng vaBankNumber,

			"mcBankAccountHolderName" => $params['mcBankAccountHolderName'] ?? '', // Họ tên chủ tài khoản
			"mcBankAccountNumber" => $params['mcBankAccountNumber'] ?? '', // STK ngân hàng mà MC liên kết
			"mcBankAccountMobile" => $params['mcBankAccountMobile'] ?? '', // SĐT mà MC đã đăng ký với NH
		];

		$r = $this->callMCMN($url, $data, 5);

		return $r;
	}

	public function addThietBi($params = []) {
		$url = env('SOUNDBOX_MCMN_BASE_URL') . 'api/mc-device/add-device-4-tingbox';

		$data = [
			'serial' => $params['serial'],
			'partnerCode' => $params['partnerCode'],
			"mcId"           => $params['mcId'], // merchantId
			"mobileUserName" => $params['mobileUserName'], // Mobile UserName
			"status"         => $params['status'], // Trạng thái: ACTIVE, INACTIVE hoặc null,
		];

		Logs::writeInfo("add-device-4-tingbox", $data);
		$r = $this->callMCMN($url, $data, 5, true);
		Logs::writeInfo("add-device-4-tingbox-response", $r);

		return $r;
	}

	public function setDefaultVamc($params) {
		$url = env('SOUNDBOX_MCMN_BASE_URL') . 'api/set-default-vamc';

		Logs::writeInfo('set-default-vamc', $params);
		$data = [
			'mcId' => $params['mcId'],
			'mobileUserName' => $params['mobileUserName'],
			"vaNextPayNumber" => $params['vaNextPayNumber'], 
			"integratedMethod" => $params['integratedMethod'], 
			"partnerCode"  => $params['partnerCode'],
		];

		$r = $this->callMCMN($url, $data, 5, true);
		Logs::writeInfo('set-default-vamc-response', $r);
		return $r;
	}

	/**
	 *  {
						"mcMcId": "45gte3181a1eec4eb8dce64a",
						"mcUserId": "67a973181a1eec4eb8dce64a",
						"mcUserMobile": "abc",
						"mcStoreId": null,
						},
				"success": true
		}
	 */
	public function getMcTingBox($params = []) {
		$url = env('SOUNDBOX_MCMN_BASE_URL') . 'api/get-mc-profile';

		$data = [
			"mcId" => $params['mcId'], // mcId
			"muName" => $params['muId'] ?? $params['muName'], // muId / muName
			"partnerCode" => $params['partnerCode']
		];

		logger()->channel('stdout')->info('Get Thong Tin Mc TingBox Request ', $data);

		$r = $this->callMCMN($url, $data, 5, true);

		logger()->channel('stdout')->info('Get Thong Tin Mc TingBox Result ', $r);
		return $r;
	}

	public function getConfigBySerial($serial) {
		$url = env('SOUNDBOX_MCMN_BASE_URL') . 'api/get-config-by-serial';

		$data = [
			'serial' => $serial
		];

		Logs::writeInfo("get-config-by-serial-request", $data);
		$r = $this->callMCMN($url, $data, 5, true);
		Logs::writeInfo("get-config-by-serial-response", $r);

		return $r;
	}

	public function switchingTknhNhanTien($params = []) {
		$url = env('SOUNDBOX_MCMN_BASE_URL') . 'api/canceled-vamc';

		$data = [
			"mcId" => $params['mcId'], 
			"mobileUserName" => $params['mobileUserName'],
			"vaNextPayNumber" => $params['vaNextPayNumber'], // Tài khoản VA Nextpay number đang thao tác
			"assignVaNextPayNumber" => $params['assignVaNextPayNumber'], //  Tài khoản VA Nextpay Number được gán
			"integratedMethod" => $params['integratedMethod'], // VAMC, VANP
			"partnerCode" => $params['partnerCode'], // Nên là: NP
			"status" => $params['status']
		];

		$r = $this->callMCMN($url, $data, 5);

		return $r;
	}

	public function cancelVAMC($params = []) {
		$url = env('SOUNDBOX_MCMN_BASE_URL') . 'api/canceled-vamc';

		$data = [
			"mcId" => $params['mcId'], 
			"mobileUserName" => $params['mobileUserName'],
			"vaNextPayNumber" => $params['vaNextPayNumber'], // Tài khoản VA Nextpay number đang thao tác
			"integratedMethod" => $params['integratedMethod'], // VAMC, VANP
			"partnerCode" => $params['partnerCode'], // Nên là: NP
		];

		$r = $this->callMCMN($url, $data, 5, true);

		return $r;
	}

	public function getListMobileUser($params = []) {
		$url = env('SOUNDBOX_MCMN_BASE_URL') . 'api/get-mc-profile-location';

		$data = [
			"mcId" => $params['mcId'], // mcId
			"partnerCode" => $params['partnerCode'], // NP
		];

		$r = $this->callMCMN($url, $data, 5);
		return $r;
	}

	public function getLocationByMcId($params = []) {
		$url = env('SOUNDBOX_MCMN_BASE_URL') . 'api/get-location-by-mcid';

		$data = [
			"mcId" => $params['mcId'], // mcId
			"partnerCode" => $params['partnerCode'], // NP
		];
		
		Logs::writeInfo("get-location-by-mcid", $params);
		$r = $this->callMCMN($url, $data, 5, true);
		Logs::writeInfo("get-location-by-mcid-response", $r);
		return $r;
	}

	public function acticeDeviceApp($params = []) {
		$url = env('SOUNDBOX_MCMN_BASE_URL') . 'api/tingbox/active-device-app';

		$data = [
			"mcId" => $params['mcId'], 
			"mobileUserName" => $params['mobileUserName'],
			"serial" => $params['serial'],
		];

		Logs::writeInfo("acticeDeviceApp", $params);
		$r = $this->callMCMN($url, $data, 5, true);
		Logs::writeInfo("acticeDeviceApp-response", $r);

		return $r;
	}

	public function changeIntegratedMethod($params = []) {
		$url = env('SOUNDBOX_MCMN_BASE_URL') . 'api/change-integrated-method';

		$data = [
			"mcId" => $params['mcId'], 
			"mobileUserName" => $params['mobileUserName'],
			"integratedMethod" => $params['integratedMethod'],
			"partnerCode" => $params['partnerCode'],
			"vaNextPayNumber" => $params['vaNextPayNumber'],
		];

		Logs::writeInfo("changeIntegratedMethod", $data);
		$r = $this->callMCMN($url, $data, 5, true);
		Logs::writeInfo("changeIntegratedMethod-response", $r);

		return $r;
	}

	public function checkDevice($params = []) 
	{
		$url = env('SOUNDBOX_MCMN_BASE_URL') . 'api/mc-device/check-device';

		$data = [
			'serial' => $params['serial'],
			'partnerCode' => $params['partnerCode'],
			'mcId' => $params['mcId'],
			'mobileUserName' => $params['mobileUserName']
		];

		Logs::writeInfo("check-device-request", $data);
		$r = $this->callMCMN($url, $data, 5, true);
		Logs::writeInfo("check-device-response", $r);

		return $r;
	}

	public function checkVaBankNumber($params)
	{
		$url = env('SOUNDBOX_MCMN_BASE_URL') . 'api/check-va-bank-number';

		$data = [
			'mcId' => $params['mcId'],
			'qrCode' => $params['qrCode']
		];

		Logs::writeInfo("check-va-bank-number-request", $data);
		$r = $this->callMCMN($url, $data, 5, true);
		Logs::writeInfo("check-va-bank-number-response", $r);

		return $r;
	}

	public function checkPartnerVsSerial($params = []) {
		$url = env('SOUNDBOX_MCMN_BASE_URL') . 'api/check-partner-vs-serial';

		$data = [
			'partnerCode' => $params['partnerCode'],
			'serial' => $params['serial']
		];

		Logs::writeInfo("check-partner-vs-serial-request", $data);
		$r = $this->callMCMN($url, $data, 5, true);
		Logs::writeInfo("check-partner-vs-serial-response", $r);

		return $r;
	}

	public function getDetailMcPartner($params = []) 
	{
		$url = env('SOUNDBOX_MCMN_BASE_URL') . 'api/detail-mc-partner';

		$data = [
			'code' => $params['code']
		];

		Logs::writeInfo("detail-mc-partner-request", $data);
		$r = $this->callMCMN($url, $data, 5, true);
		Logs::writeInfo("detail-mc-partner-response", $r);

		return $r;
	}

	public function getPartnerCodeFromInviteCode(string $inviteCode) 
	{
		$url = env('SOUNDBOX_AGENT_BASE_URL') . 'api/agent/verify-invite-code';

		$data = ['inviteCode' => $inviteCode];

		Logs::writeInfo("getPartnerCodeFromInviteCode-Request", $data);
		$r = $this->callMCMN($url, $data, 5, true);
		Logs::writeInfo("getPartnerCodeFromInviteCode-Response", $r);

		return $r;
	}

	public function checkDeviceMpos($params = []) 
	{
		$url = env('SOUNDBOX_MCMN_BASE_URL') . 'api/tingbox-app/check-device-mpos';

		$data = [
			'mcId' => $params['mcId'],
			'muName' => $params['muName'],
			'deviceId' => $params['deviceId'],
			'inviteCode' => $params['inviteCode']
		];

		Logs::writeInfo("check-device-mpos-request", $data);
		$r = $this->callMCMN($url, $data, 5, true);
		Logs::writeInfo("check-device-mpos-response", $r);

		return $r;
	}
} // End class
