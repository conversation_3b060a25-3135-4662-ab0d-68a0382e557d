<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthGetDeviceTutorialAction;

use App\Modules\Merchant\Model\Setting;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360AuthGetDeviceTutorialRequest;
use Illuminate\Support\Facades\File;

class Mpos360AuthGetDeviceTutorialAction
{
	public function run(Mpos360AuthGetDeviceTutorialRequest $request): array 
	{
		$setting = Setting::query()->firstWhere(['key' => 'CONFIG_DEVICE_TUTORIAL']);
		$settingValue = json_decode($setting->value, true);

		$returnData = [
			'tutorial_title' => $settingValue['wording_via_lang'][config('app.locale')]['title'],
			'tutorial_images' => $settingValue['tutorial_images']
		];

		$images = collect($returnData['tutorial_images'])->filter(function ($item) {
			$imageTutorial = public_path('images/authen/device/MaThietBiMau_' . $item['device_type'] . '.png');
			return $item['is_display'] == 1 && File::exists($imageTutorial);
		})
		->map(function ($item) {
			$item['url'] = cumtomAsset('images/authen/device/MaThietBiMau_' . $item['device_type'] . '.png');
			$item['name'] = __('otp.' . $item['name']);
			return $item;
		})
		->sortBy(function ($item) {
			return $item['sort'];
		})
		->values()
		->all();

		$returnData['tutorial_images'] = $images;
		return $returnData;
	}
} // End class
