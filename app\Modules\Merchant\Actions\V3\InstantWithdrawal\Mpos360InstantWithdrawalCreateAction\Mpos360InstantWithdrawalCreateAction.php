<?php

namespace App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalCreateAction;

use Exception;
use App\Lib\Helper;
use App\Lib\partner\MPOS;
use Illuminate\Support\Arr;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360Logs\LogRequest;
use App\Modules\Merchant\Requests\InstantWithdrawal\V3\Mpos360InstantWithdrawalCreateRequest;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalCreateAction\SubAction\GetGiaoDichCoTheTaoYcRutSubAction;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalFetchEstimateAction\SubAction\GetThongTinTamTinhSubAction;

class Mpos360InstantWithdrawalCreateAction
{
	public LogRequest $logRequest;

	public MPOS $mpos;

	public function __construct(LogRequest $logRequest, MPOS $mpos)
	{
		$this->logRequest = $logRequest;
		$this->mpos = $mpos;
	}

	public function run(Mpos360InstantWithdrawalCreateRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		$merchantId = $deviceSession->getMerchantId();
		$estimateResult = app(GetThongTinTamTinhSubAction::class)->run($deviceSession);

		if (empty($estimateResult['data']['countTransaction'])) {
			throw new BusinessException('Lỗi: Không tìm thấy danh sách giao dịch VietQR để tạo yc rút');
		}

		$listTranIds = app(GetGiaoDichCoTheTaoYcRutSubAction::class)->run($estimateResult);
		
		$params = [
			'serviceName' => 'PAYMENT_NOW',
			'merchantFk' => $merchantId,
			'tokenLogin' => $deviceSession->getMposToken(),
			'transactionIDList' => $listTranIds
		];

		$response = $this->mpos->paymentNowCreate($params);
		
		// @$this->logRequest->createLog([
		// 	'merchant_id' => $merchantId,
		// 	'request' => json_encode($params),
		// 	'response' => json_encode($response),
		// 	'func' => 'PAYMENT_NOW',
		// 	'partner_record_id' => @$response['data']['data']['id']
		// ]);

		if (empty($response['status_code_partner']) || $response['status_code_partner'] != Mpos360Enum::API_SUCCESS_CODE) {
			$msg = $response['message'] ?? 'Không xác định';
			throw new BusinessException('Mpos Err: ' . $msg);
		}

		$rtnResult = $response['data'];

		$returnData = [
			'msg' => '',
			'desc' => '',
			'success_footer_note' => '',
			'banking_note' => '',
			'other_data' => [
				'msg' => [
					'text' => '{message}',
					'alignment' => 'center',

					'data' => [
						'message' => [
							'text' => __('rtn.Yêu cầu đã được ghi nhận'),
							'font_weight' => 'bold',
							'color' => '#404040',
						],
					]
				],

				'desc' => [
					'text' => __('rtn.Sau khi duyệt tiền sẽ được chuyển vào số {TKNH} đăng ký với mPOS.'),
					'alignment' => 'center',
					'data' => [
						'TKNH' => [
							'text' => __('rtn.Tài khoản ngân hàng'),
							'font_weight' => 'bold',
							'color' => '#404040',
						],
					]
				],

				'banking_note' => [
					'text' => '{BankingNoteHead}
					{BankingNoteDetail}',
					'alignment' => 'center',
					'data' => [
						'BankingNoteHead' => [
							'text' => __('rtn.Sau khi duyệt tiền sẽ được chuyển vào tài khoản ngân hàng:'),
							'font_weight' => 'bold',
							'text_color' => '#808890',
						],

						'BankingNoteDetail' => [
							'text' => $rtnResult['data']['bankAccount'],
							'font_weight' => 'bold',
						],
					]
				],

				'success_footer_note' => [
					'text' => '{FooterNoteHead} {FooterNodeDetail}',
					'alignment' => 'center',
					'data' => [
						'FooterNoteHead' => [
							'text' => '',
							'font_weight' => 'bold',
							'font_style' => 'italic',
							'text_color' => '#808890',
						],

						'FooterNodeDetail' => [
							'text' => '',
							'text_color' => '#808890',
							'font_style' => 'italic',
						],
					]
				],
			],
			'data' => [
				[
					'key' => 'MaYc',
					'label' => __('rtn.Mã YC'),
					'value' => '',
					'other_data' => (object) [
						'value' => [
							'text' => '{message}',
							'data' => [
								'message' => [
									'text' => $rtnResult['data']['id'],
									'font_weight' => 'bold'
								],
							]
						]
					]
				],

				[
					'key' => 'ThoiGianTao',
					'label' => __('rtn.Thời gian tạo'),
					'value' => $rtnResult['data']['createdDate'],
					'other_data' => (object) [
						'value' => [
							'text' => '{message}',
							'data' => [
								'message' => [
									'text' => $rtnResult['data']['createdDate']
								]
							]
						]
					]
				],

				[
					'key' => 'SoYC',
					'label' => __('rtn.Số GD VietQR'),
					'value' => '',
					'other_data' => (object) [
						'value' => [
							'text' => '{message}',
							'data' => [
								'message' => [
									'text' => $rtnResult['data']['transactionCount'],
									'font_weight' => 'bold'
								],
							]
						]
					]
				],

				[
					'key' => 'TongTienGD',
					'label' => __('rtn.Tổng tiền GD'),
					'value' => '',
					'other_data' => (object) [
						'value' => [
							'text' => '{message}',
							'data' => [
								'message' => [
									'text' => Helper::priceFormat($rtnResult['data']['totalAmount']),
									'font_weight' => 'bold'
								],
							]
						]
					]
				],

				[
					'key' => 'TongPhiGD',
					'label' => __('rtn.Phí dịch vụ NTN'),
					'value' => '',
					'other_data' => (object) [
						'value' => [
							'text' => '{message}',
							'data' => [
								'message' => [
									'text' => Helper::priceFormat($rtnResult['data']['fee']),
								],
							]
						]
					]
				],

				[
					'key' => 'ThucNhan',
					'label' => __('rtn.Thực nhận'),
					'value' => '',
					'other_data' => (object) [
						'value' => [
							'text' => '{message}',
							'data' => [
								'message' => [
									'text' => Helper::priceFormat($rtnResult['data']['amountReceived']),
									'font_weight' => 'bold'
								],
							]
						]
					]
				],
			],
			'status' => 'SUCCESS',
			'can' => Mpos360Enum::MPOS360_RTN_CAN_GOTO_LIST_YC
		];

		return $returnData;
	} // End method
} // End clas
