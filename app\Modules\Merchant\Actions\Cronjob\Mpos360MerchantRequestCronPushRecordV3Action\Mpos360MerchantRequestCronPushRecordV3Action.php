<?php

namespace App\Modules\Merchant\Actions\Cronjob\Mpos360MerchantRequestCronPushRecordV3Action;

use App\Lib\Helper;
use App\Lib\TelegramAlert;
use App\Lib\partner\MNPExtend;
use Illuminate\Support\Facades\DB;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestCronPushRecordAction\SubAction\GetMnpTokenByMerchantIdSubAction;

class Mpos360MerchantRequestCronPushRecordV3Action
{
	public MNPExtend $mnp;

	private array $__ids = [];

	public function __construct(MNPExtend $mnp)
	{
		$this->mnp = $mnp;
	}

	public function run()
	{
		$returnData = [];

		for ($i = 1; $i < 20; $i++) {
			try {
				$result = $this->handle();

				if ($result == 'EMPTY') {
					$returnData[] = 'EMPTY';
					break;
				}

				$returnData[] = optional($result)->id;
			} catch (\Throwable $th) {
				mylog(['Loi xu ly ban ghi' => Helper::traceError($th)]);
				TelegramAlert::sendMessage(Helper::traceError($th));
				// throw $th;
			}
		}

		return $returnData;
	}

	public function handle()
	{
		$mpos360McRequest = Mpos360MerchantRequest::query()
																							->where('status', Mpos360Enum::MPOS360_MC_REQUEST_STT_CHUA_GUI)
																							->where('status_verify', Mpos360Enum::MPOS360_MC_VERIFY_STT_DA_THUC_HIEN_BUOC3)
																							->where('record_push_count', '<=', 3)
																							->where('time_expired', '>', now()->timestamp)
																							->where('version', 3);

		if (!empty($this->__ids)) {
			$mpos360McRequest = $mpos360McRequest->whereNotIn('id', $this->__ids);
		}

		$mpos360McRequest = $mpos360McRequest->first();

		if (!$mpos360McRequest) {
			mylog(['EMPTY' => 'khong co thong tin']);
			return 'EMPTY';
		}

		$this->__ids[] = $mpos360McRequest->id;

		mylog(['YeuCauDoiThongTin' => $mpos360McRequest->only(['id'])]);

		$wasUpdateDangGui = Mpos360MerchantRequest::query()
																							->where('id', $mpos360McRequest->id)
																							->where('status', Mpos360Enum::MPOS360_MC_REQUEST_STT_CHUA_GUI)
																							->update([
																								'status' => Mpos360Enum::MPOS360_MC_REQUEST_STT_DANG_GUI,
																								'record_push_count' => DB::raw('record_push_count + 1'),
																							]);

		if (!$wasUpdateDangGui) {
			mylog(['Loi' => 'Cap nhat dang gui khong thanh cong']);
			throw new BusinessException('Cap nhat dang gui khong thanh cong');
		}

		$mpos360McRequest = Mpos360MerchantRequest::query()->find($mpos360McRequest->id);

		if ($mpos360McRequest->status != Mpos360Enum::MPOS360_MC_REQUEST_STT_DANG_GUI) {
			throw new BusinessException('Loi ban ghi khong o trang thai dang gui');
		}

		DB::beginTransaction();
		try {
			$dataRequest = $mpos360McRequest->getDataRequestV3();
			$mnpToken = app(GetMnpTokenByMerchantIdSubAction::class)->run($mpos360McRequest->merchant_id);

			$updateResult = $this->mnp->sendYc($mpos360McRequest, $mnpToken);
			
			// cập nhật thành công
			if (
				!empty($updateResult['status']) && !empty($updateResult['data'])
			) {
				$mpos360McRequest->status = Mpos360Enum::MPOS360_MC_REQUEST_STT_DA_GUI_SANG_MNP;
				$mpos360McRequest->mynextpay_id = $updateResult['data'];
				$mpos360McRequest->data_request = json_encode($dataRequest);
				$mpos360McRequest->order_code = $updateResult['meta']['dataCode'] ?? '';
				$mpos360McRequest->comment = '{}';
				$r = $mpos360McRequest->save();

				if (!$r) {
					throw new BusinessException('Lỗi không cập nhật được bản ghi là `ĐÃ GỬI`');
				}

				DB::commit();
				return $mpos360McRequest;
			}

			// cập nhật bị lỗi
			$danhDauLaLoi = $this->danhDauLaCapNhatLoi(
				$mpos360McRequest,
				$updateResult['message'] ?? 'Lỗi mnp không xác định',
				$dataRequest
			);
			DB::commit();
			return $danhDauLaLoi;
		} catch (\Throwable $th) {
			mylog(['Error' => Helper::traceError($th)]);
			DB::rollBack();

			$comments = json_decode($mpos360McRequest->comment, true);
			$comments['push_record_err'] = $th->getMessage();

			$paramUpdate = [
				'status' => Mpos360Enum::MPOS360_MC_REQUEST_STT_CAP_NHAT_LOI,
				'comment' => json_encode($comments)
			];

			if ($mpos360McRequest->record_push_count <= 3) {
				$paramUpdate = [
					'status' => Mpos360Enum::MPOS360_MC_REQUEST_STT_CHUA_GUI,
					'comment' => '{}'
				];
			}

			$capNhatVeChuaXuLy = Mpos360MerchantRequest::query()
				->where('id', $mpos360McRequest->id)
				->where('status', Mpos360Enum::MPOS360_MC_REQUEST_STT_DANG_GUI)
				->update($paramUpdate);

			if (!$capNhatVeChuaXuLy) {
				mylog(['Loi cap nhat ve chua xu ly' => $capNhatVeChuaXuLy]);
			}

			throw $th;
		}
	} // End method

	public function danhDauLaCapNhatLoi(Mpos360MerchantRequest $mpos360McRequest, string $message = '', $dataRequest = []): Mpos360MerchantRequest
	{
		$mpos360McRequest->status = Mpos360Enum::MPOS360_MC_REQUEST_STT_CAP_NHAT_LOI;
		$mpos360McRequest->comment = json_encode(['error' => $message]);

		// Nếu push <= 3 lần thì vẫn cho push tiếp
		if ($mpos360McRequest->record_push_count <= 3) {
			$mpos360McRequest->status = Mpos360Enum::MPOS360_MC_REQUEST_STT_CHUA_GUI;
			$mpos360McRequest->comment = '{}';
		}

		if (!empty($dataRequest)) {
			$mpos360McRequest->data_request = json_encode($dataRequest);
		}

		$r = $mpos360McRequest->save();

		if (!$r) {
			throw new BusinessException('Lỗi không cập nhật được về trạng thái cuối');
		}

		return $mpos360McRequest;
	}
} // End class
