<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransactionInstallmentListAction;

use App\Lib\Helper;
use Illuminate\Support\Arr;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionInstallmentListRequest;
use App\Modules\Merchant\Actions\Transaction\subAction\Mpos360TransactionDefineConfigSubAction;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransactionListNormalAction\SubAction\GetTransactionAsListMposSubAction;
use Carbon\Carbon;

class Mpos360TransactionInstallmentListAction
{
	public function run(Mpos360TransactionInstallmentListRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();

		$params = [
			'typeTransaction' => 'STATISTIC_INSTALLMENT_TRANSACTION',
			'merchantFk' => $deviceSession->getMerchantId(),
			'startDate' => $request->getStartDate(),
			'endDate' => $request->getEndDate(),
			'tokenLogin' => $deviceSession->getMposToken(),
			'pageIndex' =>  $request->json('data.start', '1'),
			'pageSize' =>  $request->json('data.limit', '100'),
			'statusTransaction'=> $request->getTranStatus(),
			'paymentMethod'=> $request->getPaymentMethod(),
			'installmentSendStatus'=>  $request->getTranStatusInstallment(),
		];
		$listTransaction = app(Mpos360TransactionDefineConfigSubAction::class)->requestTransList($params);
		$data = [];
		if ($listTransaction) {
			if (isset($listTransaction['data']['installmentTransactionStatisticList'])) {
				$data = $this->__convertDataReturn($listTransaction['data']['installmentTransactionStatisticList']);
				$params['rangeTime'] = collect($listTransaction['data']['installmentTransactionStatisticList'])->map(function ($item) {
					return Carbon::createFromFormat('d-m-Y H:i:s', $item['createdDate'])->format('d-m-Y');
				})->unique()->implode(',');
			}
		}

		$params['email'] = $deviceSession->getMerchantEmail();
		return [
			'rows' => 0,
			'data' => $data,
			'other_data' => [
				'status' => $this->__getStatusDefault(),
				'status_installment'=> $this->__getStatusInstallMent(),
				'filter' => $this->__getFilterDefault(),
				'countSumFilter' => Arr::only($params, [
					'typeTransaction',
					'merchantFk',
					'statusTransaction',
					'paymentMethod',
					'installmentSendStatus',
					'rangeTime',
					'email'
				])
			]
		];
	}

	private function __getStatusDefault()
	{
		$statusDefault = (new Mpos360TransactionDefineConfigSubAction())->getStatusTrans(); 
		// Thực hiện hoán đổi mã màu
		foreach ($statusDefault as &$item) {
			$bgColorTmp = $item['bg_color'];
			$item['bg_color'] = $item['text_color'];
			$item['text_color'] = $bgColorTmp;
		}

		return $statusDefault;
	}
	private function __getFilterDefault()
	{
		$data = $this->__getStatusInstallMent();
		$transaction_status_installment[] = ['value' => 'ALL', 'label' => trans_choice_fallback('trans.all','Tất cả')];
		foreach ($data as $key => $value) {
			$transaction_status_installment[] = [
				'value' => $value['value'],
				'label' => $value['label']
			];
		}
		$data = $this->__getStatusDefault();
		$statusArr[] = ['value' => 'ALL', 'label' => trans_choice_fallback('trans.all','Tất cả')];
		foreach ($data as $key => $value) {
			$statusArr[] = [
				'value' => $value['value'],
				'label' => $value['label']
			];
		}
		return  [
			[
				'key' => 'transaction_time',
				'name' => trans_choice_fallback('trans.title.transactionTime','Thời gian giao dịch'),
				'list' => [
					['value' => 'TODAY', 'label' =>trans_choice_fallback('trans.title.TODAY','Tháng này')],
					['value' => 'THIS_MONTH', 'label' => trans_choice_fallback('trans.title.THIS_MONTH','Tháng này')],
					['value' => 'LAST_MONTH', 'label' => trans_choice_fallback('trans.title.LAST_MONTH','Tháng trước')],
				]
			],

			[
				'key' => 'transaction_method',
				'name' => trans_choice_fallback('trans.title.paymentMethod','Hình thức giao dịch'),
				'list' => $this->__getTransMethodName(),
			],
			[
				'key' => 'transaction_status',
				'name' => trans_choice_fallback('trans.title.status','Trạng thái'),
				'list' => $statusArr
			],
			[
				'key' => 'transaction_status_installment',
				'name' => trans_choice_fallback('trans.installment.status','Trạng thái trả góp'),
				'list' => $transaction_status_installment
			],
		];
	}
	protected function __getStatusInstallMent()
	{
		return (new Mpos360TransactionDefineConfigSubAction())->getStatusInstallment(); 
	}
	
	private function __convertDataReturn($data)
	{
		if ($data) {
			$dataReturn = [];
			$mapData = $this->__getTransMethodName();
			foreach ($mapData as $key1 => $value1) {
				$transMethodNameArr[$value1['value']] = $value1['label'];
			}
			foreach ($data as $key => $value) {
				$timestamp = strtotime($value['createdDate']);
				$timeD = date('d-m-Y', $timestamp);
				if($timeD == date('d-m-Y')) {
					$timeD = __('trans.title.Hôm nay');
				}
				$timeKey = date('Ymd', $timestamp);
				$transaction_method = $this->__getTransMethod($value);
				$transaction_method_name = isset($transMethodNameArr[$transaction_method])?$transMethodNameArr[$transaction_method]:$transaction_method;
				$cardType = $this->__getCardType($value);
				$dataArr[$timeD][] = [
					'icon' => cumtomAsset('images/transaction/transaction-list-normal/CARDS_WIPE.png'),
					'transaction_id' => $value['txid'],
					'transaction_method' => $transaction_method, // thẻ
					'transaction_method_name' => $transaction_method_name,
					'card_number' => ucfirst(strtolower($cardType)) . ': **** ' . substr($value['pan'], -4),
					'time_created' => date('H:i',$timestamp),
					'amount' => Helper::numberFormat($value['amountTransaction']) . ' VND',
					'status' => $value['status'],
					'status_installment'=> $value['installmentSendStatus'],
					'note' => '',
					'other_data' => [
						'note' => ['text_color' => '', 'bg_color' => '']
					],
				];
			}
			foreach ($dataArr as $key => $value) {
				$dataReturn[] = [
					'date' => $key,
					'total_transaction' => 0,
					'list_transaction' => $value,
				];
			}
			return $dataReturn;
		}
		return [];
	}


	private function __getCardType($value)
	{
		if (isset($value['issuerCode'])) {
			return (new Mpos360TransactionDefineConfigSubAction())->getTypeCard($value['issuerCode']); 
		}
		return '';
	}
	private function __getTransMethod($value)
	{
		if ($value) {
			return (new Mpos360TransactionDefineConfigSubAction())->getTransInstallmentMethodCode($value); 
		}
		return '';
	}
	private function __getTransMethodName()
	{
		return (new Mpos360TransactionDefineConfigSubAction())->getMethodInstallMent();
	}
}
