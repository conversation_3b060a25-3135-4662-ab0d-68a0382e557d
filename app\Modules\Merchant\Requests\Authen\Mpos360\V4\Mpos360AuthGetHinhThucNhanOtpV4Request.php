<?php

namespace App\Modules\Merchant\Requests\Authen\Mpos360\V4;

use App\Modules\Merchant\Requests\MerchantRequest;
use Illuminate\Foundation\Http\FormRequest;

class Mpos360AuthGetHinhThucNhanOtpV4Request extends FormRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.username' => ['required', 'string'],
			'data.hasLogined' => ['required', 'string', 'in:YES,NO']
		];
	}

	public function messages()
	{
		return [
			'data.username.required' => 'Email hoặc SĐT đã đăng ký với mPOS là bắt buộc',
			'data.email.string' => 'Email hoặc SĐT đã đăng ký với mPOS phải là chuỗi ký tự',
			'data.hasLogined.required' => 'Trường đánh dấu login là bắt buộc',
			'data.hasLogined.in' => 'Trường đánh dấu phải thuộc một trong các giá trị: YES, NO',
		];
	}

	public function hasLogined(): bool {
		return $this->json('data.hasLogined') == 'YES';
	}
} // End class
