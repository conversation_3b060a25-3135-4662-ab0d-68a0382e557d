<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoDetailV3Action;

use Exception;
use Carbon\Carbon;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360ChungThuc;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoDetailV3Request;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListAction\SubAction\ConfigYeuCauThayDoiSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoDetailAction\SubAction\GetTrangThaiDuyetYcSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoDetailAction\SubAction\XuLyNoiTiepYeuDoiThongTinSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoDetailAction\SubAction\GenGroupMnpDetailForMobileV3SubAction;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoDetailV3Action\SubAction\GetTrangThaiYcForMobileSubAction;

class Mpos360RequestChangeInfoDetailV3Action
{
	public bool $isDuyet = true;

	public function run(Mpos360RequestChangeInfoDetailV3Request $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();

		$mpos360McRequest = Mpos360MerchantRequest::query()
		->with(['mpos360McSupplements', 'mpos360McSupplementNew'])
		->firstWhere([
			'id' => $request->json('data.id'),
			'merchant_id' => $deviceSession->getMerchantId()
		]);

		if (!$mpos360McRequest) {
			throw new BusinessException('Lỗi: không tìm thấy yc đổi thông tin');
		}

		mylog(['BanGhiYcChiTiet' => $mpos360McRequest]);

		$trangThaiXacThuc = $this->getTrangThaiXacThuc($mpos360McRequest);

		$trangThaiYeuCau = app(GetTrangThaiYcForMobileSubAction::class)->run($mpos360McRequest);

		$orderCode = !empty($mpos360McRequest->mpos360_code) ? $mpos360McRequest->mpos360_code : $mpos360McRequest->id;
		$list = [
			[
				'label' => vmsg('Mã yêu cầu'),
				'value' => $orderCode,
				'other_data' => (object) [],
			],

			[
				'label' => vmsg('Ngày tạo'),
				'value' => Carbon::createFromTimestamp($mpos360McRequest->time_created)->format('H:i d/m/Y'),
				'other_data' => (object) [],
			],

			[
				'label' => vmsg('Trạng thái yêu cầu'),
				'value' => $trangThaiYeuCau['name'],
				'other_data' => $trangThaiYeuCau['other_data']
			],
		];

		if (!$mpos360McRequest->isHetHanVaChuaTaoYc() && !$mpos360McRequest->isMcTuHuyYc()) {
			$list[] = [
				'label' => vmsg('Trạng thái xác thực'),
				'value' => $trangThaiXacThuc['name'],
				'other_data' => $trangThaiXacThuc['other_data']
			];
		}

		$trangThaiKyPhuLuc = $this->getTrangThaiKyPhuLuc($mpos360McRequest);
		if (!empty($trangThaiKyPhuLuc) && !empty($mpos360McRequest->mynextpay_id)) {
			$list[] = [
				'label' => vmsg('Trạng thái ký phục lục'),
				'value' => $trangThaiKyPhuLuc['name'],
				'other_data' => $trangThaiKyPhuLuc['other_data']
			];
		}

		$comments = json_decode($mpos360McRequest->comment, true);

		if (!empty($comments) && is_array($comments)) {
			$error = 'Lỗi: ';
			foreach ($comments as $cmt) {
				$error .= (string) $cmt . '.';
			}

			$list[] = [
				'label' => 'Ghi chú',
				'value' => $error,
				'other_data' => [
					'text_color' => '#da2128'
				]
			];
		}

		if ($mpos360McRequest->isYcPhaiBoSungThongTin()) {
			$list[] = [
				'label' => 'Lý do cần bổ sung thông tin',
				'value' => $mpos360McRequest->mpos360McSupplementNew->supplement_reason,
				'other_data' => [
					'text_color' => '#da2128',
					'font_style' => 'italic'
				]
			];
		}

		// Đầu app không hỗ trợ được bổ sung trên app, yêu cầu làm luồng ngoài
		if ($mpos360McRequest->isCanBoSungThongTinNhungAppKhongHoTro()) {
			$list[] = [
				'label' => 'Cảnh báo',
				'value' => $mpos360McRequest->getMessageBoSungThongTinNhungAppKhongHoTro(),
				'other_data' => [
					'text_color' => '#da2128',
					'font_style' => 'italic'
				]
			];
		}

		
		$thongTinChung = [
			'key' => 'common_info',
			'name' => $mpos360McRequest->getTypeNameV3(),
			'list' => $list,
			'other_data' => (object) [
				'font_size' => '20',
				'action' => (object) [],
			]
		];

		$returnData = [
			'warning' => [],

			'data' => [
				$thongTinChung,
			],

			'other_data' => (object) [],
		];

		$groupThayDoi = app(GenGroupMnpDetailForMobileV3SubAction::class)->run($mpos360McRequest);

		foreach ($groupThayDoi as $group) {
			$returnData['data'][] = $group;
		}

		$can = [];

		if (
			$mpos360McRequest->status < Mpos360Enum::MPOS360_MC_REQUEST_STT_DA_GUI_SANG_MNP
			&& !$mpos360McRequest->isHetHanVaChuaTaoYc()
		) {
			$can[] = [
				'can_code' => 'CAN_CANCEL_REQUEST',
				'target' => (object) [
					'screen' => '',
					'params' => (object) []
				]
			];
		}
		

		if (
			($mpos360McRequest->isChuaXacThuc() || $mpos360McRequest->isDaXacThucNhungChuaLamBuoc3())
			&& $mpos360McRequest->time_expired > now()->timestamp
		) {
			$can[] = [
				'can_code' => 'CAN_CONTINUE_MAKE_REQUEST',
				'target' => app(XuLyNoiTiepYeuDoiThongTinSubAction::class)->run($mpos360McRequest)
			];
		}

		if ($mpos360McRequest->isYcDuocHoTroBoSungThongTin()) {
			$mpos360ChungThuc = Mpos360ChungThuc::getChungThucCCCD($mpos360McRequest->merchant_id);
			
			$can[] = [
				'can_code' => 'CAN_BO_SUNG_THONG_TIN',
				'target' => (object) [
					'screen' => 'document_attach_screen',
					'params' => (object) [
						'requestID' => $mpos360McRequest->id,
						'qtsID' => optional($mpos360ChungThuc)->qts_request_id ?? '',
						'isBoSungThongTin' => 'YES'
					]
				]
			];
		}

		$returnData['can'] = $can;
		return $returnData;
	}

	public function getTrangThaiXacThuc(Mpos360MerchantRequest $rq) {
		$returnData = [
			'name' => vmsg('Không xác định'),
			'other_data' => (object) [
				'text_color' => '#ffffff',
				'bg_color' => '#000000',
				'display_type' => 'pills'
			],
		];

		if ($rq->isKhongXacThuc()) {
			$returnData = [
				'name' => vmsg('Không xác thực'),
				'other_data' => (object) [
					'text_color' => '#ffffff',
					'bg_color' => '#000000',
					'display_type' => 'pills'
				],
			];
		}

		if ($rq->isChuaXacThuc()) {
			$returnData = [
				'name' => vmsg('Chưa xác thực'),
				'other_data' => (object) [
					'text_color' => '#ffffff',
					'bg_color' => '#ee9c00',
					'display_type' => 'pills'
				],
			];
		}

		if ($rq->isDaXacThucNhungChuaLamBuoc3()) {
			$returnData = [
				'name' => vmsg('Cần xác minh bước 3'),
				'other_data' => (object) [
					'text_color' => '#ffffff',
					'bg_color' => '#018bf4',
					'display_type' => 'pills'
				],
			];
		}

		if ($rq->isDaLamBuoc3()) {
			$returnData['name'] = vmsg('Đã xác thực');
			$returnData['other_data'] = (object) [
				'text_color' => '#ffffff',
				'bg_color' => '#3bb54a',
				'display_type' => 'pills'
			];
		}

		return $returnData;
	}


	public function getTrangThaiKyPhuLuc(Mpos360MerchantRequest $mpos360McRequest) {
		switch ($mpos360McRequest->status_sign) {
			case 0: 
				return [];
				break;

			case Mpos360Enum::MPOS360_MC_SIGN_STT_CHUA_KY: 
				return [
					'name' => vmsg('Chưa ký'),
					'other_data' => [ 'text_color' => '#ffffff', 'bg_color' => '#e99323', 'display_type' => 'pills' ]
				];
				break;

			case Mpos360Enum::MPOS360_MC_SIGN_STT_DANG_KY: 
				return [
					'name' => vmsg('Đang gửi ký'),
					'other_data' => [ 'text_color' => '#ffffff', 'bg_color' => '#018bf4', 'display_type' => 'pills' ]
				];
				break;

			case Mpos360Enum::MPOS360_MC_SIGN_STT_DA_KY: 
				return [
					'name' => vmsg('Đã ký'),
					'other_data' => [ 'text_color' => '#ffffff', 'bg_color' => '#3bb54a', 'display_type' => 'pills' ]
				];
				break;

			case Mpos360Enum::MPOS360_MC_SIGN_STT_KY_LOI: 
				return [
					'name' => vmsg('Gửi ký lỗi'),
					'other_data' => [ 'text_color' => '#ffffff', 'bg_color' => '#da2128', 'display_type' => 'pills' ]
				];
				break;

			default:
				return [
					'name' => vmsg('Không xác định'),
					'other_data' => [ 'text_color' => '#ffffff', 'bg_color' => '#000000', 'display_type' => 'pills' ]
				];
				break;
		}
	}
} // End class
