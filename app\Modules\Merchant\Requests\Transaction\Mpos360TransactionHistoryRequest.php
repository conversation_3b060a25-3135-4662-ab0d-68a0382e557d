<?php

namespace App\Modules\Merchant\Requests\Transaction;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360TransactionHistoryRequest extends MerchantRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data.date' => ['required', 'date_format:d-m-Y'],
    ];
  }
  public function getStartDate()
  {
    $date = $this->json('data.date', '');
    if ($date) {
      return $date;
    }
    return date('d-m-Y');
  }
  public function getEndDate()
  {
    $date = $this->json('data.date', '');
    if ($date) {
      return $date;
    }
    return date('d-m-Y');
  }
}
