<?php

namespace App\Lib;

use Illuminate\Support\Str;
use App\Modules\Merchant\Model\Setting;

class Mpos360UrlHelper
{
	public static function convertToHttps($url) {
			// Kiểm tra nếu URL bắt đầu bằng "http://"
			if (strpos($url, 'http://') === 0) {
					// Thay thế "http://" bằng "https://"
					return str_replace('http://', 'https://', $url);
			}
			// Trả về URL gốc nếu không phải là "http://"
			return $url;
	}


	public static function buildRouteSecurity(string $routeName, array $params=[]) {
		$paramSign = $params;
		$fulParamEncrypt = $params;

		$paramSign['signed'] = encrypt(json_encode($fulParamEncrypt));
		$url = route($routeName, $paramSign);

		return self::convertToHttps($url);
	}

	/**
	 * @param $deviceTingBoxItem = [▼
			"serialNumber" => "TEST0110T184"
			"status" => "ACTIVE"
			"mobileUserId" => "utgj756886"
		]
	 */
	public static function buildTingBoxDetailUrl(string $routeName, $deviceTingBoxItem=[]) {
		return route($routeName, [
			'tingBoxSerial' => $deviceTingBoxItem['serialNumber'],
			'tingBoxStatus' => $deviceTingBoxItem['status'] == 'ACTIVE' ? 'Đã kích hoạt' : 'Chưa kích hoạt',
			'tingBoxBgColor' => $deviceTingBoxItem['status'] == 'ACTIVE' ? '73ae4a' : 'fdb62f',
		]);
	}
	
	/**
	 * @param $deviceTingBoxItem = [▼
			"serialNumber" => "TEST0110T184"
			"status" => "ACTIVE"
			"mobileUserId" => "utgj756886"
		]
	 */
	public static function getTingBoxThumbnail($tingBoxSerialNumber='') {
		if (Str::startsWith($tingBoxSerialNumber, 'TBS')) {
			return 'https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/mpos360/app/67f88be04465eb435f2ea50cTBS_Icon.png';
		}

		if (Str::startsWith($tingBoxSerialNumber, 'TB01')) {
			return 'https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/mpos360/app/67f88c7b4465eb435f2ea53fTB01_Icon.png';
		}

		if (Str::startsWith($tingBoxSerialNumber, 'TB02')) {
			return 'https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/mpos360/app/67f88c7b4465eb435f2ea540TB02_Icon.png';
		}

		return 'https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/mpos360/app/67f88c7b4465eb435f2ea53fTB01_Icon.png';
	}
} // End class