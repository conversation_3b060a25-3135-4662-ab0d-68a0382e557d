<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\SubAction;

use App\Exceptions\BusinessException;
use Exception;
use Illuminate\Support\Str;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoAdditionalAttachmentRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestCronPushRecordAction\SubAction\GetCccdInfoByQtsRequestIdSubAction;

class ValidateProfileDinhKemSA
{
	public $action;

	public function __construct(GetCccdInfoByQtsRequestIdSubAction $action)
	{
		$this->action = $action;	
	}
	
	public function run(
		Mpos360MerchantRequest $mpos360McRequest,
		Mpos360RequestChangeInfoAdditionalAttachmentRequest $request
	) {
		$additionalProfile = $request->json('data.additional_profiles', []);
		$additionalProfileCollect = collect($additionalProfile);

		$this->__validateChungTuDiKem($mpos360McRequest, $request);

		if ($mpos360McRequest->isYcDoiTknhCaNhanMaHkdUyQuyen() || $mpos360McRequest->isYcDoiTknhCaNhanMaDoanhNghiepUyQuyen()) {
			// Vai trò/Vị trí của người ủy quyền tại ngân hàng
			$positionAuthBank = $additionalProfileCollect->where('profileKey', 'positionAuthBank')->first();
			if (empty($positionAuthBank['value'])) {
				throw new BusinessException(vmsg('Thiếu thông tin: Vai trò/Vị trí của người được ủy quyền'));
			}

			if (!empty($positionAuthBank['value'])) {
				if (strlen($positionAuthBank['value']) < 3) {
					throw new BusinessException(vmsg('Vai trò/Vị trí của người được ủy quyền có độ dài tối thiểu là 3 ký tự'));
				}

				if (strlen($positionAuthBank['value']) > 255) {
					throw new BusinessException(vmsg('Vai trò/Vị trí của người được ủy quyền phải có độ dài tối đa là 255 ký tự'));
				}
			}

			// Mối quan hệ giữa hai bên
			$bankMutualRelation = $additionalProfileCollect->where('profileKey', 'bankMutualRelation')->first();
			if (empty($bankMutualRelation['value'])) {
				throw new BusinessException(vmsg('Thiếu thông tin: Mối quan hệ giữa hai bên (bankMutualRelation)'));
			}

			if (!empty($bankMutualRelation['value'])) {
				if (strlen($bankMutualRelation['value']) < 3) {
					throw new BusinessException(vmsg('Mối quan hệ giữa hai bên phải có độ dài tối thiểu là 3 ký tự'));
				}

				if (strlen($bankMutualRelation['value']) > 255) {
					throw new BusinessException(vmsg('Mối quan hệ giữa hai bên phải có độ dài tối đa là 255 ký tự'));
				}
			}

			// Bắt lỗi thiếu thông tin qts_request_id
			$qtsRequestId = $request->json('data.qts_request_id', '');
			if (empty($qtsRequestId)) {
				throw new BusinessException(vmsg('Thiếu thông tin: Mã qts sau khi ekyc'));
			}

			// Gọi sang hệ thống ekyc để lấy được tên Cá nhân đc ủy quyền
			$isMatchingNguoiThuHuong = $this->__matchingHoTenCaNhanDuocUyQuyen($mpos360McRequest, $qtsRequestId);
			if (!$isMatchingNguoiThuHuong) {
				throw new BusinessException(vmsg('Lỗi: Không khớp tên thông tin người thụ hưởng'));
			}

			return $additionalProfileCollect->filter(function ($item) {
				return in_array(
					$item['profileKey'], 
					['positionAuthBank', 'bankMutualRelation']
				);
			})->values()->all();
		}

		// Yêu cầu đổi người đại diện mới sẽ có các quy tắc riênng sau
		if ($mpos360McRequest->isYeuCauDoiNguoiDaiDienMoi()) {
			// Chức vụ của người đại diện
			$representPosition = $additionalProfileCollect->where('profileKey', 'representPosition')->first();
			if (empty($representPosition['value'])) {
				throw new BusinessException(vmsg('Thiếu thông tin: Vai trò/Chức vụ của người đại diện (representPosition)'));
			}

			if (!empty($representPosition['value'])) {
				if (strlen($representPosition['value']) < 3) {
					throw new BusinessException(vmsg('Vai trò/Chức vụ của người đại diện phải có độ dài tối thiểu là 3 ký tự'));
				}

				if (strlen($representPosition['value']) > 255) {
					throw new BusinessException(vmsg('Vai trò/Chức vụ của người đại diện phải có độ dài tối đa là 255 ký tự'));
				}
			}

			// Mối quan hệ giữa 2 bên của ng đại diện
			$representMutualRelation = $additionalProfileCollect->where('profileKey', 'representMutualRelation')->first();
			if (empty($representMutualRelation['value'])) {
				throw new BusinessException(vmsg('Thiếu thông tin: Mối quan hệ giữa 2 bên (representMutualRelation)'));
			}

			if (!empty($representMutualRelation['value'])) {
				if (strlen($representMutualRelation['value']) < 3) {
					throw new BusinessException(vmsg('Mối quan hệ giữa hai bên phải có độ dài tối thiểu là 3 ký tự'));
				}

				if (strlen($representMutualRelation['value']) > 255) {
					throw new BusinessException(vmsg('Mối quan hệ giữa hai bên phải có độ dài tối đa là 255 ký tự'));
				}
			}

			return $additionalProfileCollect->filter(function ($item) {
				return in_array(
					$item['profileKey'], 
					['representPosition', 'representMutualRelation']
				);
			})->values()->all();
		}
	} // End method

	private function __matchingHoTenCaNhanDuocUyQuyen(Mpos360MerchantRequest $mpos360McRequest, string $qtsRequestId): bool {
		$detailQtsResultDto = $this->action->run($qtsRequestId);
		$fullNameFromKYC = Str::of($detailQtsResultDto->fullName)->slug(' ')->upper()->trim()->__toString();
		
		mylog([
			'TenNguoiThuHuong' => $mpos360McRequest->getTenNguoiThuHuongV3(),
			'FullNameFromKyc' => $fullNameFromKYC
		]);

		return $mpos360McRequest->getTenNguoiThuHuongV3() == $fullNameFromKYC;
	}

	private function __validateChungTuDiKem(Mpos360MerchantRequest $mpos360McRequest, $request) {
		$mpos360McRequest->load('mpos360McSupplementNew');

		/**
		 * Mới tạo thì mới cần phải required, còn bổ sung thì ko cần
		 * MC cố tình up sai hoặc ko up thì bắt bổ sung lại
		 */
		if (!$mpos360McRequest->mpos360McSupplementNew) {
			if (
				$mpos360McRequest->isYcDoiTknhCaNhanMaHkdUyQuyen()
				|| $mpos360McRequest->isYcDoiTknhCaNhanMaDoanhNghiepUyQuyen()
			) {
				$cccdHaiMatCuaCaNhanDuocUyQuyen = $request->json('data.attachments.id_documents.cccd_2mat_sau_cua_ca_nhan_duoc_uy_quyen');
				
				if (empty($cccdHaiMatCuaCaNhanDuocUyQuyen) || count($cccdHaiMatCuaCaNhanDuocUyQuyen) < 2) {
					throw new BusinessException('Bạn cần cung cấp đủ CCCD 2 mặt của cá nhân được ủy quyền');
				}
			}
	
			if ($mpos360McRequest->isYcDoiTknhCaNhanMaDoanhNghiepUyQuyen()) {
				$giayUyQuyen = $request->json('data.attachments.id_documents.anh_giay_uy_quyen');
				if (empty($giayUyQuyen)) {
					throw new BusinessException('Bạn cần cung cấp giấy ủy quyền có dấu và chữ ký của công ty');
				}
			}

			if (
				$mpos360McRequest->isYcDoiNguoiDaiDienMoiDoanhNghiepV3()
				|| $mpos360McRequest->isYcDoiNguoiDaiDienMoiHKDV3()
			) {
				$cccdHaiMatNguoiDaiDienMoi =  $request->json('data.attachments.id_documents.cccd_hai_mat_cua_nguoi_dai_dien_moi');
				if (empty($cccdHaiMatNguoiDaiDienMoi) || count($cccdHaiMatNguoiDaiDienMoi) < 2) {
					throw new BusinessException('Bạn cần cung cấp đủ CCCD 2 mặt của người đại diện mới');
				}
			}

			if ($mpos360McRequest->isYcDoiNguoiDaiDienMoiDoanhNghiepV3()) {
				$giayUyQuyen = $request->json('data.attachments.id_documents.anh_giay_uy_quyen');
				if (empty($giayUyQuyen)) {
					throw new BusinessException('Bạn cần cung cấp giấy ủy quyền có dấu và chữ ký của công ty');
				}
			}
		} // endif check luồng tạo
	}
} // End class