<?php
namespace App\Lib\partner;

class QRParser {
  public function parseQRCode(string $qrText): array {
    $result = [
      'version' => '',           // tag 00
      'initiation_method' => '', // tag 01
      'merchant_info' => [],     // tag 38
      'currency' => '',          // tag 53
      'country' => '',          // tag 58
      'additional_data' => '',   // tag 62
      'crc' => ''               // tag 63
    ];
    
    $position = 0;
    while ($position < strlen($qrText)) {
      $tag = substr($qrText, $position, 2);
      $position += 2;

      $length = (int)substr($qrText, $position, 2);
      $position += 2;

      $value = substr($qrText, $position, $length);
      $position += $length;

      switch ($tag) {
        case '00':
          $result['version'] = $value;
          break;
        case '01':
          $result['initiation_method'] = $value;
          break;
        case '38':
          $result['merchant_info'] = $this->parseTag38($value);
          break;
        case '53':
          $result['currency'] = $this->parseCurrency($value);
          break;
        case '58':
          $result['country'] = $value;
          break;
        case '62':
          $result['additional_data'] = $this->parseAdditionalData($value);
          break;
        case '63':
          $result['crc'] = $value;
          break;
      }
    }

    return $result;
  }

  private function parseTag38(string $value): array {
    $parsed = [
      'consumer_id' => '',
      'service_code' => '',
      'account_number' => '',
      'merchant_id' => ''
    ];
    
    $pos = 0;
    while ($pos < strlen($value)) {
      $subTag = substr($value, $pos, 2);
      $pos += 2;
      
      $subLength = (int)substr($value, $pos, 2);
      $pos += 2;
      
      $subValue = substr($value, $pos, $subLength);
      $pos += $subLength;

      switch ($subTag) {
        case '00':
          $parsed['consumer_id'] = $subValue;
          break;
        case '01':
          $parsed['service_code'] = $subValue;
          // Trích xuất số tài khoản từ service code
          $parsed['account_number'] = substr($subValue, 14);
          break;
        case '02':
          $parsed['merchant_id'] = $subValue;
          break;
      }
    }

    return $parsed;
  }

  private function parseCurrency(string $value): string {
    $currencyMap = [
      '704' => 'VND'
    ];
    return $currencyMap[$value] ?? $value;
  }

  private function parseAdditionalData(string $value): array {
    $parsed = [
      'bill_number' => '',
      'mobile_number' => '',
      'reference_label' => '',
      'purpose_of_transaction' => ''
    ];

    $pos = 0;
    while ($pos < strlen($value)) {
      $subTag = substr($value, $pos, 2);
      $pos += 2;

      if ($pos >= strlen($value)) break;

      $subLength = (int)substr($value, $pos, 2);
      $pos += 2;

      if ($pos >= strlen($value)) break;

      $subValue = substr($value, $pos, $subLength);
      $pos += $subLength;

      switch ($subTag) {
        case '01':
          $parsed['bill_number'] = $subValue;
          break;
        case '02':
          $parsed['mobile_number'] = $subValue;
          break;
        case '05':
          $parsed['reference_label'] = $subValue;
          break;
        case '08':
          $parsed['purpose_of_transaction'] = $subValue;
          break;
      }
    }

    return $parsed;
  }
}