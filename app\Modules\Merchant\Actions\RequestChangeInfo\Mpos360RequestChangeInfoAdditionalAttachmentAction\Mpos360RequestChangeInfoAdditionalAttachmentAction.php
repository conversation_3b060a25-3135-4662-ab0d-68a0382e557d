<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction;

use Exception;
use Illuminate\Support\Arr;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessAction\SubAction\MappingDinhKemSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\SubAction\GetPhuongThucQuetB3SubAction;

class Mpos360RequestChangeInfoAdditionalAttachmentAction
{
	public function run(Mpos360RequestChangeInfoAdditionalAttachmentRequest $request)
	{
		$mpos360MerchantRequest = Mpos360MerchantRequest::query()->find($request->json('data.id'));

		if (!$mpos360MerchantRequest) {
			throw new BusinessException('Không tìm được bản ghi yêu cầu thay đổi');
		}

		$attachments = $request->json('data.attachments', []);
		
		$profilesImgUrl = app(MappingDinhKemSubAction::class)->run($attachments);

		$dataRequest = json_decode($mpos360MerchantRequest->data_request, true);

		foreach ($request->json('data.additional_profiles') as $profile) {
			$profileKey = $profile['profileKey'];
			$profileValue = $profile['value'];

			$dataRequest[0]['profiles'][$profileKey] = $profileValue;
		}

		foreach ($profilesImgUrl as $profileKey => $profileValue) {
			$dataRequest[0]['profiles'][$profileKey] = $profileValue;
		}
		
		if (empty($dataRequest[0]['scan_method'])) {
			$dataRequest[0]['scan_method'] = (object)[];
		}


		$mpos360MerchantRequest->data_request = json_encode($dataRequest);
		$mpos360MerchantRequest->status_verify = Mpos360Enum::MPOS360_MC_VERIFY_STT_DA_XAC_THUC;
		$savedResult = $mpos360MerchantRequest->save();

		if (!$savedResult) {
			throw new BusinessException('Lỗi không thể bổ sung thông tin');
		}

		// Liệt kê ra các phương thức chứng thực => đề xuất xác thực tương ứng
		$scanStep3 = app(GetPhuongThucQuetB3SubAction::class)->run($mpos360MerchantRequest->merchant_id);

		return [
			'id' => $mpos360MerchantRequest->id,
			'msg' => 'Bổ sung tài liệu đính kèm thành công',
			'status' => 'SUCCESS',
			'can' => Mpos360Enum::MPOS360_CAN_GOTO_STEP3,
			'scan_method' => $scanStep3
		];
	}
}
