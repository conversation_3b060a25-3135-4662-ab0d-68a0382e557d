<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoValidateEmailAndMobileAction;

use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\GetMNPAccessTokenSubAction;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoValidateEmailAndMobileRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction\MnpGetMerchantProfileSubAction;

class Mpos360RequestChangeInfoValidateEmailAndMobileAction
{
	private bool $__isChangeEmail  = false;
	private bool $__isChangeMobile = false;

	private bool $__isValidEmail   = true;
	private bool $__isValidMobile  = true;

	public function run(Mpos360RequestChangeInfoValidateEmailAndMobileRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		$merchantId = $deviceSession->getMerchantId();

		$deviceSessionWithToken = app(GetMNPAccessTokenSubAction::class)->run($deviceSession);

		$mnpMerchantDetail = app(MnpGetMerchantProfileSubAction::class)->run(
			$merchantId,
			$deviceSessionWithToken->mnp_token
		);

		$currentEmail = $mnpMerchantDetail['data']['authoriserEmail'];
		$currentMobile = $mnpMerchantDetail['data']['authoriserContactNumber'];

		if ($request->json('data.authoriserEmail') != $currentEmail) {
			$this->__isChangeEmail = true;
		}

		if ($request->json('data.authoriserContactNumber') != $currentMobile) {
			$this->__isChangeMobile = true;
		}

		// Thì mới cần gọi api
		if ($request->hasThayTheTkDangNhapMpos()) {

		}

		$returnData = [
			'authoriserEmail' => [
				'is_change' => $this->__isChangeEmail ? 'YES' : 'NO',
				'is_valid' => $this->__isValidEmail ? 'YES' : 'NO',
			],

			'authoriserContactNumber' => [
				'is_change' => $this->__isChangeMobile ? 'YES' : 'NO',
				'is_valid' => $this->__isValidMobile ? 'YES' : 'NO',
			],

			'is_change_email_or_mobile' => ($this->__isChangeEmail && $this->__isChangeMobile) ? 'YES' : 'NO',
			'replace_mpos_account_field' => $request->getThayTheTkDangNhapMpos(),
			'msg' => __('dttv3.Thành công'),
			'status' => 'SUCCESS'
		];

		if (!$this->__isValidEmail) {
			$returnData['msg'] = __('dttv3.*Email đã tồn tại, vui lòng nhập email khác');
			$returnData['status'] = 'ERROR';
		}

		if (!$this->__isValidMobile) {
			$returnData['msg'] = __('dttv3.*SĐT đã tồn tại, vui lòng nhập SĐT khác');
			$returnData['status'] = 'ERROR';
		}

		return $returnData;
	}
} // End class
