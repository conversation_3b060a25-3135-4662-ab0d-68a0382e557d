<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransactionInstallmentDetailAction\SubAction;
// {"transactionType":"H\u00e0ng h\u00f3aTr?
//     g\u00f3p","paymentMethod":"Th\u1ebb","txid":"20240822094316601","authCode":"094322","createdDate":"04-09-2024
//     00:00:00","settleDate":"22-08-2024
//     09:43:20","voildDate":"","status":104,"amount":6000000,"customerPaymentFee":125000,"totalDiscount":0,"description":"INSTALLMENT-STANDARDCHARTERED
//     Installment: 3 Months\/STANDARDCHARTERED Tra gop: 3
//     Thang-","transactionFee":125000,"receivedAmount":5996700,"withdrawStatus":"HAS_BALANCE","campainName":"","promotionCode":"","discountValue":"","cardholderName":"DUNG\/DOAN
//     THI
//     ","issuerCode":"MASTER_LOCAL","pan":"*************5095","applicationLabel":"Mastercard","batch":"108","tid":"********","mid":"***************","rrn":"************","muid":"sp6qwert","installmentSendStatus":"APPROVED_MAIL","customerInstallmentFee":125000,"installmentFee":125000,"bankName":"StandardChartered","period":3}
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionInstallmentDetailRequest;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionInstallmentListRequest;
use App\Lib\partner\MPOS;
use App\Lib\Helper;
use App\Modules\Merchant\Actions\Transaction\subAction\Mpos360TransactionDefineConfigSubAction;

class Mpos360TransactionInstallmentDetailSubAction
{
    public $_type = 'DETAIL_INSTALLMENT_TRANSACTION';
    public function returnData($listTransaction = [])
    {
        $data = $this->__defaultReturn();
        if ($listTransaction) {
            if (isset($listTransaction['data']['mc360DetailTransaction'])) {
                $dataDetail = $listTransaction['data']['mc360DetailTransaction'];
                $data['data'][] = $this->__commonInfo($dataDetail); //0
                $data['data'][] = $this->__orderInfo($dataDetail); //0
                // if (isset($dataDetail['discountValue']) && $dataDetail['discountValue']) {
                    $data['data'][] = $this->__promotionInfo($dataDetail); //3
                // }
                if (isset($dataDetail['cardholderName'])) {
                    $data['data'][] = $this->__customerInfo($dataDetail); //4
                }
                if (isset($dataDetail['withdrawStatus'])) {
                    $data['data'][] = $this->__paymentInfo($dataDetail); //2
                }
                $data['data'][] = $this->__otherInfo($dataDetail); //5
            }
        }
        return $data;
    }
    private function __defaultReturn()
    {
        return [
            'warning' => [],
            'data' => [],
            'other_data' => (object)[],
        ];
    }
    
    private function __commonInfo($detail)
    {
        $mapData = (new Mpos360TransactionDefineConfigSubAction())->getStatusInstallment();
        $installmentSendStatus = '';
        $installmentSendStatusOther = (object)[];
        $installmentSendStatusText = '';
        if (isset($detail['installmentSendStatus'])) {
            $installmentSendStatus = $detail['installmentSendStatus'];
            foreach ($mapData as $key => $value) {
                if ($value['value'] == $detail['installmentSendStatus']) {
                    $installmentSendStatusText = $value['label'];
                    $installmentSendStatusOther = (object)$value;
                    break;
                }
            }
        }

        $mapData =(new Mpos360TransactionDefineConfigSubAction())->getStatusTrans();
        $statusText = '';
        $statusOther = (object)[];
        if (isset($detail['status'])) {
            foreach ($mapData as $key => $value) {
                if ($value['value'] == $detail['status']) {
                    $statusText = $value['label'];
                    $statusOther = (object)$value;
                    break;
                }
            }
        }
        $data  = [
            'key' => 'common_info',
            'name' => trans_choice_fallback('trans.title.common_info','Thông tin chung'),
            'list' => [],
        ];
        $period = '';
        if ($detail['period']) {
            $period = $detail['bankName'] . '/' . $detail['period'] . ' '.trans_choice_fallback('trans.month','tháng');
        }

        $paymentMethod = '';
        $mapData = (new Mpos360TransactionDefineConfigSubAction())->getMethodInstallMent();
        foreach ($mapData as $key1 => $value1) {
            $transMethodNameArr[$value1['value']] = $value1['label'];
        }
        $transaction_method = $this->__getTransMethod($detail);
        $paymentMethod = isset($transMethodNameArr[$transaction_method]) ? $transMethodNameArr[$transaction_method] : $transaction_method;
        $data['list'] = [
            [
                'key' => 'transType',
                'label' => trans_choice_fallback('trans.title.transType','Loại GD'),
                'value' => trans_choice_fallback('trans.installment.payment','Thanh toán trả góp'),
                'other_data' => (object) [],
            ],
            [
                'key' => 'paymentMethod',
                'label' => trans_choice_fallback('trans.title.paymentMethod','Hình thức GD'),
                'value' => $paymentMethod,
                'other_data' => (object) [],
            ],
            [
                'key' => 'period',
                'label' => trans_choice_fallback('trans.title.bank_period','Ngân hàng/Kỳ hạn'),
                'value' => $period,
                'other_data' => (object) [],
            ],
            [
                'key' => 'txid',
                'label' => trans_choice_fallback('trans.title.txid'),
                'value' => $detail['txid'],
                'other_data' => (object) [],
            ],
            [
                'key' => 'authCode',
                'label' =>trans_choice_fallback('trans.title.authCode'),
                'value' => $detail['authCode'],
                'other_data' => (object) [],
            ],
            [
                'key' => 'createdDate',
                'label' => trans_choice_fallback('trans.title.createdDate'),
                'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'createdDate', 'datetime'),
                'other_data' => (object) [],
            ],
            [
                'key' => 'settleDate',
                'label' => trans_choice_fallback('trans.title.settleDate'),
                'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'settleDate', 'datetime'),
                'other_data' => (object) [],
            ],
            [
                'key' => 'voildDate',
                'label' => trans_choice_fallback('trans.title.voildDate'),
                'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'voildDate', 'datetime'),
                'other_data' => (object) [],
            ],
            [
                'key' => 'status',
                'label' => trans_choice_fallback('trans.title.status'),
                'value' => $statusText,
                // 'other_data' => $statusOther,
                'other_data' => (object) [],
            ],
            [
                'key' => 'installmentSendStatus',
                'label' => trans_choice_fallback('trans.instalment.status'),
                'value' => $installmentSendStatusText,
                'other_data' => $installmentSendStatusOther,
            ],
        ];
        
        return $data;
    }
    private function __orderInfo($detail)
    {
        $discountValue =  (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'discountValue', 'amount');
        if ($discountValue) {
            $discountValue = '-' . $discountValue;
        }
        $detail['totalPaymentAmount2'] = (int)$detail['amount']  + (int)$detail['customerPaymentFee'] + (int)$detail['customerInstallmentFee'] - (int)$detail['discountValue'];
        $data  = [
            'key' => 'order_info',
            'name' => trans_choice_fallback('trans.title.order_info'),
            'list' => [],
        ];
        $data['list'] = [
            [
                'key' => 'amount_order',
                'label' => trans_choice_fallback('trans.title.amount_order'),
                'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'amount', 'amount'),
                'other_data' => (object) [],
            ],
            [
                'key' => 'discountValue',
                'label' => trans_choice_fallback('trans.title.discountValue'),
                'value' => $discountValue,
                'other_data' => (object) [
                    // 'font_weight' => 'bold'
                ],
            ],
            [
                'key' => 'customerPaymentFee',
                'label' => trans_choice_fallback('trans.title.customer_fee'),
                'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'customerPaymentFee', 'amount'),
                'other_data' => (object) [],
            ],
            [
                'key' => 'customerInstallmentFee',
                'label' => trans_choice_fallback('trans.title.customer_installment_fee'),
                'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'customerInstallmentFee', 'amount'),
                'other_data' => (object) [],
            ],
            [
                'key' => 'totalPaymentAmount2',
                'label' => trans_choice_fallback('trans.title.total_payment'),
                'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'totalPaymentAmount2', 'amount'),
                'other_data' => (object) [
                    'font_weight' => 'bold',
                ],
            ]

        ];
        if($detail['description']) {
            $data['list'][] = [
                'label' => trans_choice_fallback('trans.title.description'),
                'value' => $detail['description'],
                'other_data' => ['text_color' => '#404041', 'bg_color' => '#ebebeb','display'=> 'block']
            ];
        }
        return $data;
    }
    private function __getTransMethod($value)
    {
        if ($value) {
            return (new Mpos360TransactionDefineConfigSubAction())->getTransInstallmentMethodCode($value);
        }
        return '';
    }
    
    private function __paymentInfo($detail)
    {

        $data  = [
            'key' => 'payment_info',
            'name' => 'Thông tin thanh toán',
            'list' => [],
        ];
        $mapData = (new Mpos360TransactionDefineConfigSubAction())->getWithdrawStatusTrans2();
        $withdrawStatus= '';
        $withdrawStatusText = '';
        $withdrawStatusOther = (object)[];
        foreach ($mapData as $key => $value) {
            $withDrawstatusArr[$value['value']] = $value;
        }
        if (!isset($detail['withdrawStatus']) || $detail['withdrawStatus'] !== 'HAS_BALANCE') {
            $withdrawStatus=  'CHUA_THANH_TOAN';
            $detail['withdrawStatus'] = 'CHUA_THANH_TOAN';
        }else{
            $withdrawStatus =  $detail['withdrawStatus'];
        }
        $withdrawStatusText =  $withDrawstatusArr[$withdrawStatus]['label'];
        $withdrawStatusOther = (object)$withDrawstatusArr[$withdrawStatus];

        $data['list'] = [
			[
				'key' => 'amount',
				'label' => trans_choice_fallback('trans.title.amount_payment'),
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'amount', 'amount'),
				'other_data' => (object) [],
			],
			[
				'key' => 'customerInstallmentFee',
				'label' => trans_choice_fallback('trans.title.merchant_installment_fee'),
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'installmentSendStatus', 'amount'),
				'other_data' => (object) [],
			],
            [
				'key' => 'installmentFee',
				'label' => trans_choice_fallback('trans.title.merchant_trans_fee'),
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'installmentFee', 'amount'),
				'other_data' => (object) [],
			],
			[
				'key' => 'withdrawStatus',
				'label' => trans_choice_fallback('trans.title.statusInfo'),
				'value' => $withdrawStatusText,
				'other_data' => $withdrawStatusOther,
			]
		];
        return $data;
    }
    private function __promotionInfo($detail)
    {
        $data  = [
            'key' => 'promotions',
            'name' => trans_choice_fallback('trans.title.promotions'),
            'list' => [
                [
                    'key' => 'campainName',
                    'label' => trans_choice_fallback('trans.title.campainName'),
                    'value' => $detail['campainName'],
                    'other_data' => (object) [
                        // 'display_type' => 'webview',
                        // 'url' => 'https://mpos.vn'
                    ],
                ],
                [
                    'key' => 'promotionCode',
                    'label' => trans_choice_fallback('trans.title.promotionCode'),
                    'value' => $detail['promotionCode'],
                    'other_data' => (object) [],
                ],
                [
                    'key' => 'discountValue',
                    'label' => trans_choice_fallback('trans.title.discountValue'),
                    'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'discountValue', 'amount'),
                    'other_data' => (object) [
                        'font_weight' => 'bold'
                    ],
                ]
            ],
        ];
        return $data;
    }
    private function __customerInfo($detail)
    {
        $cardType = '';
        if (isset($detail['issuerCode'])) {
            $cardType = (new Mpos360TransactionDefineConfigSubAction())->getTypeCard($detail['issuerCode']);
        }
        $pan = $cardType . ': ' . $detail['pan'];
        $data  = [
            'key' => 'customer',
            'name' => trans_choice_fallback('trans.title.customer'),
            'list' => [
                [
                    'key' => 'cardholderName',
                    'label' =>trans_choice_fallback('trans.title.cardholderName'),
                    'value' => $detail['cardholderName'],
                    'other_data' => (object) [],
                ],
                [
                    'key' => 'pan',
                    'label' => trans_choice_fallback('trans.title.pan'),
                    'value' =>  $pan,
                    'other_data' => (object) [],
                ]
            ],
        ];
        return $data;
    }
    private function __otherInfo($detail)
    {
        $data  = [
            'key' => 'other',
            'name' => trans_choice_fallback('trans.title.other'),
            'list' =>[
                [
                    'key' => 'batch',
                    'label' =>trans_choice_fallback('trans.title.batch'),
                    'value' => $detail['batch'],
                    'other_data' => (object) [],
                ],
                [
                    'key' => 'tid',
                    'label' => trans_choice_fallback('trans.title.TID'),
                    'value' => $detail['tid'],
                    'other_data' => (object) [],
                ],
                [
                    'key' => 'mid',
                    'label' => trans_choice_fallback('trans.title.MID'),
                    'value' => $detail['mid'],
                    'other_data' => (object) [],
                ],
                // [
                //     'key' => 'Mã cấp phép',
                //     'label' => 'Mã cấp phép',
                //     'value' => '',
                //     'other_data' => (object) [],
                // ],
                [
                    'key' => 'rrn',
                    'label' => trans_choice_fallback('trans.title.rrn'),
                    'value' => $detail['rrn'],
                    'other_data' => (object) [
                        'font_weight' => 'bold'
                    ],
                ],
                [
                    'key' => 'muid',
                    'label' => trans_choice_fallback('trans.title.muid'),
                    'value' => $detail['muid'],
                    'other_data' => (object) [],
                ]
            ],
        ];
        return $data;
    }
}
