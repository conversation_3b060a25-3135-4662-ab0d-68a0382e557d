<?php

namespace App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360DetailDiemBanAction;

use App\Lib\Helper;
use App\Lib\Logs;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use App\Lib\partner\SoundBox;
use App\Lib\MnpOnboardNewMcHelper;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use App\Modules\TingBoxVAMC\Requests\DiemBan\Mpos360DetailDiemBanRequest;
use App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360DetailDiemBanAction\SubAction\HandleDiemBanDetailSubAction;
use App\Modules\TingBoxVAMC\Models\BankVAMC;

class Mpos360DetailDiemBanAction
{
	public array $listMuIds = [];

	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;
		
	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run(Mpos360DetailDiemBanRequest $request)
	{
		Logs::writeInfo("detai diem ban param", $request->json('data'));
		$detailMc = $this->mnpOnboardNewMcHelper->detailMcV2([
			'mposMcId' => $request->json('data.merchantId')
		]);
		
		$listBankCanLinking = BankVAMC::query()->whereIn('isDisplay', [BankVAMC::DISPLAY_PROD, BankVAMC::DISPLAY_SAP_RA_MAT])
																					 ->orderBy('sort', 'DESC')
																					 ->get(['bankCode', 'image', 'bankName', 'shortName']);

		$r = app(HandleDiemBanDetailSubAction::class)->run(
			$detailMc,
			$request->json('data.merchantId'),
			$request->json('data.muId'),
			$listBankCanLinking
		);

		$holderName = $detailMc['data']['passportObject']['customerName'] ?? $detailMc['data']['holderName'] ?? '';


		$r['detail']['bankMerchantInfo'] = [
			'bankAccountName' => Str::of($holderName)->slug(' ')->title()->trim()->__toString(),
			'bankAccountNo' => '',
			'cccdNo' => $detailMc['data']['passportObject']['passport'],
			'mobileNo' => Helper::isUserNameEqualEmail($detailMc['data']['username']) ? '' : $detailMc['data']['username'],
			'mobileUserId' => $request->json('data.muId'),
			'merchantId' => $request->json('data.merchantId')
		];

		$getMcTingBoxDetail = (new SoundBox())->getMcTingBox([
			"mcId" => $request->json('data.merchantId'), // mcId
			"muName" => $request->json('data.muId'), // muId / muName
			"partnerCode" => Helper::getPartnerCode($request->json('data.merchantId')), 
		]);

		if ($getMcTingBoxDetail['data']['currentMode'] == 'VANP') {
			if (!empty($r['detail']['listBanking']['inter'])) {
				$r['detail']['listBanking']['inter'][0]['is_default'] = 'YES';
			}
		}


		if (!empty($getMcTingBoxDetail['data']['mcQr'])) {
			foreach ($getMcTingBoxDetail['data']['mcQr'] as $qr) {
				if ($qr['integratedMethod'] == 'VAMC') {
					foreach ($r['detail']['listBanking']['direct'] as &$b) {
						if ($b['partner_request_id'] == $qr['vaNextPayNumber'] && isset($qr['state']) && $qr['state'] == 'DEFAULT') {
							$b['is_default'] = 'YES';
						}
					}
				}
			}
			
		}

		$r['detail']['receiveType'] = 'QUA_VI_MO';
		$r['detail']['receiveAuto'] = [];

		if ($getMcTingBoxDetail['data']['currentMode'] == 'VAMC') {
			$currentVATienDoVe = collect($getMcTingBoxDetail['data']['mcQr'])->first(function ($it) {
				return $it['status'] == 'ACTIVE' && isset($it['state']) && $it['state'] == 'DEFAULT';
			}); 
	
			
			if (!empty($currentVATienDoVe)) {
				$auto = Arr::only($currentVATienDoVe, ['bankAccountNumber', 'mcBankAccountHolderName', 'mcBankAccountNumber', 'bankCode']);
				
				if (empty($auto['mcBankAccountNumber'])) {
					$auto['mcBankAccountNumber'] = $auto['bankAccountNumber'];
				}
				
				$r['detail']['receiveType'] = 'NHAN_TU_DONG';
				$r['detail']['receiveAuto'][] = $auto;
			}
		}
		
		if (!empty($getMcTingBoxDetail['data']['mcQrDefault']['qrAccountName'])) {
			$r['detail']['shopInfo']['qrDisplayName'] = $getMcTingBoxDetail['data']['mcQrDefault']['qrAccountName'];
		}
		
		$r['detail']['listBankCanLinking'] = $listBankCanLinking->map(function ($it) {
			return $it->only(['bankCode', 'image']);
		})->values()->toArray();

		$r['detail']['receiveVimo'] = [];

		if ($r['detail']['receiveType'] == 'QUA_VI_MO') {
			$r['detail']['receiveVimo'][] = [
				'bankCode' => Helper::getBankCode($detailMc['data']['bankName']),
				'bankAccountNumber' =>  $detailMc['data']['accountNo'],
				'mcBankAccountHolderName' => $detailMc['data']['holderName'],
				'mcBankAccountNumber' => $detailMc['data']['accountNo'],
			];
		}

		if ($getMcTingBoxDetail['data']['currentMode'] == 'VANP' && !empty($r['detail']['listBanking']['inter'])) {
			$r['detail']['listBanking']['inter'][0]['is_default'] = 'YES';
			
			foreach ($r['detail']['listBanking']['direct'] as &$b) {
				$b['is_default'] = 'NO';
			}
		}

		/**
		 * Kiểm tra nếu có thiết bị mà chưa có liên kết ngân hàng nào thì ẩn nút Thêm tingbox kia đi
		 */
		$r['detail']['isHiddenAddTingBox'] = 'NO';
		if (empty($r['detail']['listBanking']['direct']) && empty($r['detail']['listBanking']['inter']) && !empty($r['detail']['listTingBoxDevice'])) {
			$r['detail']['isHiddenAddTingBox'] = 'YES';
		}

		if (!empty($r['detail']['listBanking']['direct'])) {
			$r['detail']['listBanking']['direct'] = collect($r['detail']['listBanking']['direct'])
																								->sortBy(function ($item) {
																									return $item['is_default'] === 'YES' ? 0 : 1;
																								})
																								->values()
																								->all();

			foreach ($r['detail']['listBanking']['direct'] as $it) {
				foreach ($r['detail']['receiveAuto'] as &$at) {
					if ($at['bankCode'] == 'VCB' && $at['bankCode'] == $it['bank_code']) {
						$at['bankAccountNumber'] = $it['bankMobile'];
						$at['mcBankAccountHolderName'] = '';
						$at['mcBankAccountNumber'] = '';
					}
				}
			}
		}

		return $r;
	}
}
