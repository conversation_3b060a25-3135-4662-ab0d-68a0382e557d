<?php

namespace App\Modules\Merchant\Actions\ChungThuc\Mpos360ChungThucCheckAction;

use Exception;
use App\Lib\partner\MNP;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360ChungThuc;
use App\Modules\Merchant\Requests\ChungThuc\Mpos360ChungThucCheckRequest;

class Mpos360ChungThucCheckAction
{
	public MNP $mnp;

	public function __construct(MNP $mnp)
	{
		$this->mnp = $mnp;
	}

	public function run(Mpos360ChungThucCheckRequest $request)
	{
		$returnData = [
			'status' => '0',
			'msg' => 'Bạn phải chứng thực CCCD trước khi thực hiện gửi yêu cầu thay đổi thông tin',
			'can' => 'CAN_PHAI_CHUNG_THUC_CCCD',
		];

		$deviceSession = $request->getCurrentDeviceSession();
		$merchantId = $deviceSession->getMerchantId();

		$mpos360ChungThuc = Mpos360ChungThuc::getChungThucCCCD($merchantId);

		if ($mpos360ChungThuc) {
			$returnData = [
				'status' => '1',
				'msg' => 'Bạn có thể tạo yêu cầu đổi thông tin',
				'can' => 'CAN_CO_THE_TAO_YC_DOI_THONG_TIN',
			];
		}

		return $returnData;
	}
}
