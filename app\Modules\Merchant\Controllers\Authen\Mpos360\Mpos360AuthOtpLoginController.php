<?php

namespace App\Modules\Merchant\Controllers\Authen\Mpos360;

use App\Lib\Helper;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360AuthGetOtpLoginRequest;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360AuthVerifyOtpLoginRequest;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthGetOtpLoginAction\Mpos360AuthGetOtpLoginAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthVerifyOtpLoginAction\Mpos360AuthVerifyOtpLoginAction;

class Mpos360AuthOtpLoginController extends Controller
{
	public function Mpos360AuthGetOtpLogin(Mpos360AuthGetOtpLoginRequest $request)
	{
		try {
			$result = app(Mpos360AuthGetOtpLoginAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360AuthVerifyOtpLogin(Mpos360AuthVerifyOtpLoginRequest $request)
	{
		try {
			$result = app(Mpos360AuthVerifyOtpLoginAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
} // End class
