<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransactionNormalDetailAction\SubAction;

use App\Lib\Helper;

use App\Modules\Merchant\Actions\Transaction\subAction\Mpos360TransactionDefineConfigSubAction;

class Mpos360TransactionNormalDetailForMacqSubAction
{
	public $_type = 'DETAIL_TRANSACTION';
	public function __convertDataReturn($listTransaction = [])
	{
		$data = $this->__defaultReturn();
		
		if ($listTransaction) {
			if (isset($listTransaction['data']['mc360DetailTransaction'])) {
				$dataDetail = $listTransaction['data']['mc360DetailTransaction'];
				$data['data'][] = $this->__commonInfo($dataDetail); //0
				$data['data'][] = $this->__orderInfo($dataDetail); //1

				if (isset($dataDetail['cardholderName'])) {
					$data['data'][] = $this->__customerInfo($dataDetail); //4
				}

				$data['data'][] = $this->__otherInfo($dataDetail); //5
			}
		}

		return $data;
	}

	private function __orderInfo($detail)
	{
		$discountValue =  (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'discountValue', 'amount');
		if ($discountValue) {
			$discountValue = '-' . $discountValue;
		}
		$detail['totalPaymentAmount2'] = (int)$detail['amount'] + (int)$detail['customerPaymentFee']  - (int)$detail['discountValue'];
		$data  = [
			'key' => 'order_info',
			'name' => trans_choice_fallback('trans.title.order_info', 'Thông tin đơn hàng'),
			'list' => [],
		];
		$data['list'] = [
			[
				'key' => 'amount_order',
				'label' => trans_choice_fallback('trans.title.amount_order',  'Số tiền đơn hàng'),
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'amount', 'amount'),
				'other_data' => (object) [],
			],
			
			[
				'key' => 'totalPaymentAmount2',
				'label' => trans_choice_fallback('trans.title.totalPaymentAmount2', 'Tổng thanh toán'),
				'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'totalPaymentAmount2', 'amount'),
				'other_data' => (object) [
					'font_weight' => 'bold',
				],
			],
		];

		if (!empty($detail['description'])) {
			$data['list'][] = [
				'key' => 'MoTa',
				'label' => '',
				'value' => $detail['description'],
				'other_data' => (object) [
					"display_type" => "block"
				],
			];
		}
		
		return $data;
	}

	private function __getTransMethodCode($value)
	{
		if ($value) {
			return (new Mpos360TransactionDefineConfigSubAction())->getPaymentMethodCode($value);
		}
		return '';
	}

	private function __getTransMethodName()
	{
		return (new Mpos360TransactionDefineConfigSubAction())->getPaymentMethod();
	}

	private function __commonInfo($detail)
	{
		$mapData = (new Mpos360TransactionDefineConfigSubAction())->getStatusTrans();
		$statusText = '';
		$statusOther = (object)[];

		if (isset($detail['status'])) {
			foreach ($mapData as $key => $value) {
				if ($value['value'] == $detail['status']) {
					$statusText = $value['label'];
					$statusOther = (object)$value;
					break;
				}
			}
		}

		$statusText = 'Thành công';
		$statusOther = ['value' => '100', 'label' => trans_choice_fallback('trans.status.100', 'Thành công'), 'text_color' => '#ffffff', 'bg_color' => '#3BB54A', 'display_type' => 'pills'];

		$data  = [
			'key' => 'common_info',
			'name' => trans_choice_fallback('trans.title.common_info', 'Thông tin chung'),
			'list' => [],
		];


		$paymentMethod = '';
		$mapData = (new Mpos360TransactionDefineConfigSubAction())->getPaymentMethod();
		foreach ($mapData as $key1 => $value1) {
			$transMethodNameArr[$value1['value']] = $value1['label'];
		}
		$transaction_method = $this->__getTransMethod($detail);
		$paymentMethod = isset($transMethodNameArr[$transaction_method]) ? $transMethodNameArr[$transaction_method] : $transaction_method;

		$tranMode = request()->get('vaMode');
		$data['list'] = [
			[
				'key' => 'transType',
				'label' => 'Loại GD',
				'value' => 'VietQR',
				'other_data' => (object) [],
			],
			[
				'key' => 'paymentMethod',
				'label' => 'Hình thức',
				'value' => $tranMode == 'VAMC' ? 'Trực tiếp' : 'Qua Vimô',
				'other_data' => (object) [],
			],
			[
				'key' => 'txid',
				'label' => 'Mã giao dịch',
				'value' => $detail['txid'],
				'other_data' => (object) [],
			],

			[
				'key' => 'createdDate',
				'label' => 'Thời gian',
				'value' => $detail['createdDate'],
				'other_data' => (object) [],
			],

			[
				'key' => 'status',
				'label' => 'Trạng thái',
				'value' => $statusText,
				'other_data' => $statusOther,
			],
		];

		
		return $data;
	}
	private function __getTransMethod($value)
	{
		if ($value) {
			return (new Mpos360TransactionDefineConfigSubAction())->getPaymentMethodCode($value);
		}
		return '';
	}

	private function __promotionInfo($detail)
	{
		if (!isset($detail['promotionCode']) || empty($detail['promotionCode'])) {
			return [];
		}

		$data  = [
			'key' => 'promotions',
			'name' => trans_choice_fallback('trans.title.promotions', 'CT khuyến mại'),
			'list' => [
				[
					'key' => 'campainName',
					'label' => trans_choice_fallback('trans.title.campainName', 'Chương trình'),
					'value' => $detail['campainName'],
					'other_data' => (object) [
						// 'display_type' => 'webview',
						// 'url' => 'https://mpos.vn'
						// update 06.11.2024: mpos không hỗ trợ đc website CTKM, off thành text đen
					],
				],
				[
					'key' => 'promotionCode',
					'label' => trans_choice_fallback('trans.title.promotionCode', 'Mã khuyến mại'),
					'value' => $detail['promotionCode'],
					'other_data' => (object) [],
				],
				[
					'key' => 'discountValue',
					'label' => trans_choice_fallback('trans.title.discountValue', 'Số tiền KM'),
					'value' => (new Mpos360TransactionDefineConfigSubAction())->__convertData($detail, 'discountValue', 'amount'),
					'other_data' => (object) [
						'font_weight' => 'bold'
					],
				]
			],
		];
		return $data;
	}
	private function __customerInfo($detail)
	{
		$cardType = '';
		if (isset($detail['issuerCode'])) {
			$cardType = (new Mpos360TransactionDefineConfigSubAction())->getTypeCard($detail['issuerCode']);
		}
		$pan = $cardType . ': ' . $detail['pan'];
		$data  = [
			'key' => 'customer',
			'name' => trans_choice_fallback('trans.title.customer', 'Khách hàng'),
			'list' => [
				[
					'key' => 'cardholderName',
					'label' => trans_choice_fallback('trans.title.cardholderName', 'Chủ thẻ'),
					'value' => $detail['cardholderName'],
					'other_data' => (object) [],
				],
				[
					'key' => 'pan',
					'label' => trans_choice_fallback('trans.title.pan', 'Thẻ/TKNH/Ví điện tử'),
					'value' =>  $pan,
					'other_data' => (object) [],
				]
			],
		];
		return $data;
	}
	private function __otherInfo($detail)
	{
		$data  = [
			'key' => 'other',
			'name' => trans_choice_fallback('trans.title.other', 'Khác'),
			'list' => [
				[
					'key' => 'rrn',
					'label' => trans_choice_fallback('trans.title.rrn', 'Số tham chiếu'),
					'value' => $detail['rrn'],
					'other_data' => (object) [
						'font_weight' => 'bold'
					],
				],
				[
					'key' => 'muid',
					'label' => trans_choice_fallback('trans.title.muid', 'Mobile user'),
					'value' => $detail['muid'],
					'other_data' => (object) [],
				]
			],
		];
		return $data;
	}



	private function __convertTime($time)
	{
		try {
			if ($time) {
				$strtotime = strtotime($time);
				return date('H:i d-m-Y', $strtotime);
			}
			return '';
		} catch (\Throwable $th) {
			return $time;
		}
	}
	private function __convertAmount($amount)
	{
		try {
			return Helper::numberFormat($amount) . ' VND';
		} catch (\Throwable $th) {
			return $amount;
		}
	}
	private function __defaultReturn()
	{
		return [
			'warning' => [],
			'data' => [],
			'other_data' => (object)[],
		];
	}

	private function __getStatusDefault()
	{
		return (new Mpos360TransactionDefineConfigSubAction())->getStatusTrans();
	}
	private function __getCardType($value)
	{
		if (isset($value['issuerCode'])) {
			return (new Mpos360TransactionDefineConfigSubAction())->getTypeCard($value['issuerCode']);
		}
		return '';
	}
}
