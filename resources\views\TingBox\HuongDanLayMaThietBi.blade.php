<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, maximum-scale=1">
  <title>Quét mã Serial thiết bị Tingbox</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
    }
    .tabs {
      display: flex;
    margin-bottom: 20px;
    cursor: pointer;
    justify-content: space-between;
    }
    .tab {
      padding: 10px ;
      border-bottom: 3px solid transparent;
    }
    .tab[onclick]{background: transparent;}
    .tab.active {
      color: #73AE4A;
      font-weight: bold;
      border-bottom: 3px solid #73AE4A;
    }
    .content {
      display: none;
    }
    .content.active {
      display: block;
    }
    .content-head{
        text-align: center;
    }
    .content-head p{
        color: #808890;
        font-size: 16px;
        font-weight: 600;
        text-align: center;
    }
    .device-image {
      max-width: 250px;
      border-radius: 8px;
    }
    .steps {
      margin-top: 15px;
    }
    .steps b {
      color: #73AE4A;
    }
    .phone {
      color: #007BFF;
      font-weight: bold;
    }
  </style>
</head>
<body>

  <div class="tabs">
    <div class="tab active" onclick="showTab(0)">Tingbox 01</div>
    <div class="tab" onclick="showTab(1)">Tingbox 02</div>
    <div class="tab" onclick="showTab(2)">TingboxS</div>
  </div>

  <div class="content active">
    <div class="content-head">
        <img src="https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/mpos360/app/67f9c9144465eb435f2ebc26TB01.png" alt="Tingbox 01" class="device-image" style="object-position: 0 0; ">
        <p>Quét mã Serial thiết bị</p>
    </div>
    
    <div class="steps">
      <p><b>Bước 1:</b> Tìm mã QR Serial in sẵn ở mặt sau loa Tingbox</p>
      <p><b>Bước 2:</b> Hướng camera điện thoại vào mã QR</p>
      <p><b>Bước 3:</b> Sau khi quét thành công, nhấn “Gán thiết bị”</p>
      <p>Trường hợp mã QR bị mờ hoặc khó quét, vui lòng liên hệ bộ phận CSKH qua số <span class="phone">1900.63.64.88</span> để được trợ giúp.</p>
    </div>
  </div>

  <div class="content">
    <div class="content-head">
        <img src="https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/mpos360/app/683f0d75f280ac4f7d4888f8img-tingbox2.png" alt="Tingbox 02" class="device-image" style="object-position: 0 0; ">
        <p>Quét mã Serial thiết bị</p>
    </div>
    <div class="steps">
      <p><b>Bước 1:</b> Tìm mã QR Serial in sẵn ở mặt dưới loa Tingbox</p>
      <p><b>Bước 2:</b> Hướng camera điện thoại vào mã QR</p>
      <p><b>Bước 3:</b> Sau khi quét thành công, nhấn “Gán thiết bị”</p>
      <p>Trường hợp mã QR bị mờ hoặc khó quét, vui lòng liên hệ bộ phận CSKH qua số <span class="phone">1900.63.64.88</span> để được trợ giúp.</p>
    </div>
  </div>

  <div class="content">
    <div class="content-head">
        <img src="https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/mpos360/app/67f9c9144465eb435f2ebc29TBS.png" alt="TingboxS" class="device-image" style="object-position: 0 0;">
        <p>Quét mã Serial thiết bị</p>
    </div>
    <div class="steps">
      <p><b>Bước 1:</b> Tìm mã QR Serial in sẵn ở mặt dưới loa Tingbox</p>
      <p><b>Bước 2:</b> Hướng camera điện thoại vào mã QR</p>
      <p><b>Bước 3:</b> Sau khi quét thành công, nhấn “Gán thiết bị”</p>
      <p>Trường hợp mã QR bị mờ hoặc khó quét, vui lòng liên hệ bộ phận CSKH qua số <span class="phone">1900.63.64.88</span> để được trợ giúp.</p>
    </div>
  </div>

  <script>
    function showTab(index) {
      var tabs = document.querySelectorAll(".tab");
      var contents = document.querySelectorAll(".content");
      tabs.forEach((tab, i) => {
        tab.classList.toggle("active", i === index);
        contents[i].classList.toggle("active", i === index);
      });
    }
  </script>

</body>
</html>
