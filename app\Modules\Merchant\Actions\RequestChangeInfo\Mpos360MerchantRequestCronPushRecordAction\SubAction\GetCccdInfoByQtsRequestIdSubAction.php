<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestCronPushRecordAction\SubAction;

use App\Exceptions\BusinessException;
use Exception;
use App\Lib\partner\EKYC;
use App\Modules\Merchant\DTOs\RequestChangeInfo\DetailQtsResultDto;

class GetCccdInfoByQtsRequestIdSubAction
{
	public EKYC $eKYC;

	public function __construct(EKYC $eKYC)
	{
		$this->eKYC = $eKYC;
	}

	public function run(string $qtsRequestId = '')
	{
		$result = $this->eKYC->getLogDetail($qtsRequestId);

		if (empty($result['status']) || empty($result['data'])) {
			throw new BusinessException('Lỗi VMMC: Không truy vấn được thông tin đã thực hiện eKYC');
		}

		$detailQtsResultDto = new DetailQtsResultDto($result);
		return $detailQtsResultDto;
	}
} // End class