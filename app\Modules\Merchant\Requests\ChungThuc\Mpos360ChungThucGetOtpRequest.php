<?php

namespace App\Modules\Merchant\Requests\ChungThuc;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360ChungThucGetOtpRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.service_code' => ['required', 'string', 'max:50'],
			'data.obj_value' => [
				'required',
				'string',
			],
			'data.reference_id' => [
				'required',
				'string'
			]
		];
	}

	public function messages()
	{
		return [
			'data.service_code.required' => vmsg('ChungThucGetOtpServiceCodeLaBatBuoc'),
			'data.service_code.string' => vmsg('ChungThucGetOtpServiceCodePhaiLaChuoiKyTu'),
			'data.service_code.max' => vmsg('ChungThucGetOtpServiceCodePhaiDuoi50KyTu'),

			'data.obj_value.required' => vmsg('ChungThucGetOtpGiaTriLaBatBuoc'),
			'data.obj_value.string' => vmsg('ChungThucGetOtpGiaTriPhaiLaKieuChuoi'),

			'data.reference_id.required' => vmsg('ChungThucGetOtpIdThamChieuLaBatBuoc'),
			'data.reference_id.string' => vmsg('ChungThucGetOtpIdThamChieuLaKieuChuoi'),
		];
	}
} // End class
