<?php

namespace App\Modules\Merchant\DTOs\Notification\Mpos360GetGroupWithUnreadItemsDto;

use Illuminate\Support\Collection;

class DataDto
{
	public string $code;
	public int $count;
	public Collection $groups;
	public string $name = '';

	public function __construct(string $code, int $count, array $groups)
	{
		$this->code = $code;
		$this->count = $count;
		$this->groups = $this->buildGroups($groups);
		
		if ($this->code == 'ALL') {
			$this->name = 'Tất cả';
		}
	}

	public function buildGroups(array $groups): Collection
	{
		$groupCollection = Collection::make($groups)->map(function ($item) {
			return new GroupItemDto(
				$item['code'],
				$item['count']
			);
		});

		return $groupCollection;
	}
}
