<?php
namespace App\Modules\TingBoxVAMC\Actions\CloseLinkBankAction\SubAction;

use App\Lib\Logs;
use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Lib\partner\SoundBox;
use App\Lib\TelegramAlertWarning;
use App\Exceptions\BusinessException;
use App\Modules\TingBoxVAMC\Models\PlanEvent;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;

class CloseLinkBankSoundBoxSubAction
{
    public SoundBox $soundBox;

	public int $isNumberSync = 0;

	public function __construct(SoundBox $soundBox)
	{
		$this->soundBox = $soundBox;	
	}

    public function run(Request $request,MerchantShopBank $merchantShopBank)
	{
		$merchantShopBankAssign = null;

        if($request->json('data.merchantShopBankIdAssign')){
            $merchantShopBankAssign = MerchantShopBank::query()->where([
                'id' => $request->json('data.merchantShopBankIdAssign'),
                'merchant_id' => $merchantShopBank->merchant_id,
                'shop_id' => $merchantShopBank->shop_id,
            ])->first();
        }

        while ($this->isNumberSync < 3) {
            $params = [
                "mcId" => $merchantShopBank->merchant_id, 
                "mobileUserName" => $merchantShopBank->shop_id,
                "vaNextPayNumber" => $merchantShopBank->partner_request_id, // Tài khoản VA Nextpay number đang thao tác
                "assignVaNextPayNumber" => $merchantShopBankAssign ? $merchantShopBankAssign->partner_request_id : '', //  Tài khoản VA Nextpay Number được gán
                "integratedMethod" => $merchantShopBank->account_type == 1 ? 'VAMC' : 'VANP', // VAMC, VANP
                "partnerCode" => Helper::getPartnerCode($merchantShopBank->merchant_id), // Nên là: NP
                "status" => 'CANCELED'
            ];

            $sendSoundBox = $this->soundBox->switchingTknhNhanTien($params);
            Logs::writeInfo('cancel-vamc-response', $sendSoundBox);

            if (empty($sendSoundBox['result'])) {
                ++$this->isNumberSync;
                $message = 'Lỗi huỷ VA sang tingbox: ' . $params['vaNextPayNumber'];
                @TelegramAlertWarning::sendMessage($message);
                if($this->isNumberSync == 3){
                    PlanEvent::query()->forceCreate([
                        'merchant_shop_bank_id' => $merchantShopBank->id,
                        'merchant_id' => $merchantShopBank->merchant_id,
                        'action' => 'CANCLESOUNDBOX',
                        'data' => json_encode($request->json('data'), JSON_UNESCAPED_UNICODE),
                        'time_created' => time(),
                        'time_updated' => time(),
                    ]);	
                    throw new BusinessException('Tài khoản ngân hàng không thể huỷ liên kết sang Tingbox.');
                }
            }else {
                return $sendSoundBox;
            }
        }

		return true;
	}
}