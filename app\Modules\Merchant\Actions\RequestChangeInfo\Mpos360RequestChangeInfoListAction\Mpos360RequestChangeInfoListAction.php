<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListAction;

use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListAction\SubAction\ConfigYeuCauThayDoiSubAction;
use App\Modules\Merchant\Enums\Mpos360Enum;
use Carbon\Carbon;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoListRequest;

class Mpos360RequestChangeInfoListAction
{
	public function run(Mpos360RequestChangeInfoListRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		$merchantId = $deviceSession->getMerchantId();

		$listMpos360MerchantRequest = Mpos360MerchantRequest::query()
			->with(['mpos360McSupplements'])
			->where('type', Mpos360Enum::MPOS360_MC_REQUEST_THAY_DOI_THONG_TIN)
			->where('merchant_id', $merchantId)
			->where('status_verify', '!=', Mpos360Enum::MPOS360_MC_VERIFY_STT_KHONG_CO_THONG_TIN_XAC_THUC)
			->limit($request->json('data.limit', 10))
			->offset($request->json('data.start', 0))
			->latest('id')
			->get()
			->groupBy(function (Mpos360MerchantRequest $it) {
				return Carbon::createFromTimestamp($it->time_created)->format('Y-m-d');
			});
		$returnData = [
			'rows' => '0',
			'data' => []
		];

		foreach ($listMpos360MerchantRequest as $date => $listRequest) {
			$record = [];
			$items = [];
			$record['date'] = $date;
			$record['total_transaction'] = $listRequest->count();
		
			foreach ($listRequest as $rq) {
				$getStatusAndOtherDataForStatus = $this->getStatusAndOtherDataForStatus($rq);

				$orderCode = $rq->mpos360_code; 
				if (empty($orderCode)) {
					$orderCode = $rq->id;
				}

				$mang = [
					'id'            => $rq->id,
					'order_code'    => sprintf('%s: %s', vmsg('Mpos360RequestChangeInfoListAction_MaYC'), $orderCode),
					'type'          => $this->getTypeNameAsString($rq),
					'time_created'  => $rq->getTimeCreateAsString(),
					'profile'       => [],
					'status'        => $getStatusAndOtherDataForStatus['status'],
					'status_verify' => $getStatusAndOtherDataForStatus['status_verify'],
					'note'          => $rq->getNoteAsString(),
					'other_data' 	=> $getStatusAndOtherDataForStatus['other_data']
				];

				$supplement = $rq->mpos360McSupplements->where('status', Mpos360Enum::MPOS360_BO_SUNG_HO_SO_MOI_TAO)->first();
				if ($supplement) {
					$mang['note'] = sprintf('Lý do cần bổ sung thông tin: %s', $supplement->supplement_reason);
				}

				if ($rq->isCanBoSungThongTinNhungAppKhongHoTro()) {
					$mang['note'] = sprintf('%s %s--%s%s', $mang['note'], PHP_EOL, PHP_EOL, $rq->getMessageBoSungThongTinNhungAppKhongHoTro());
				}
				$items[] = $mang;
			}
		
			$record['list_transaction'] = $items;
			$returnData['data'][] = $record;
		}
		

		$returnData['other_data'] = [
			'filter' => [
				[
					'key' => 'status',
					'name' => 'Trạng thái yêu cầu',
					'list' => [
						['value' => 'ALL', 'label' => 'Tất cả'],
						['value' => '1', 'label' => 'Nháp'],
						['value' => '2', 'label' => 'Chưa gửi'],
						['value' => '3', 'label' => 'Đang gửi'],
						['value' => '4', 'label' => 'Đã gửi sang MNP',],
						['value' => '5', 'label' => 'MNP đã xử lý',],
						['value' => '6', 'label' => 'MNP từ chối', ],
						['value' => '7', 'label' => 'Cập nhật lỗi',],
						['value' => '8', 'label' => 'MC hủy yêu cầu', ],
						['value' => '9', 'label' => 'Hết hạn',],
					]
				],

				[
					'key' => 'time_created',
					'name' => 'Thời gian tạo yêu cầu',
					'list' => [
						['value' => 'ALL', 'label' => 'Tất cả'],
						['value' => 'TODAY', 'label' => 'Hôm nay'],
						['value' => 'YESTERDAY', 'label' => 'Hôm qua'],
						['value' => 'THIS_WEEK', 'label' => 'Tuần này'],
						['value' => 'THIS_MONTH', 'label' => 'Tháng này',],
					]
				],
			]
		];

		return $returnData;
	}

	public function getTypeNameAsString(Mpos360MerchantRequest $rq): string {
		if ($rq->isDoiThongTinNganHang()) {
			$type = $rq->getTypeName();
			return vmsg('CommonRequestChangeInfo.Type.'.$type);
		}

		$choice = $rq->getChoiceName();
		return vmsg('CommonRequestChangeInfo.Choice.'.$choice);
	}

	public function getStatusAndOtherDataForStatus(Mpos360MerchantRequest $rq): array {
		// hết hạn yc
		if ($rq->isMcTuHuyYc()) {
			$returnData = [
				'status' => 'MC_TU_HUY_YC',
				'status_verify' => 'HET_HAN',
				'other_data' => (object)[
					'status' => [
						'text' => vmsg('ConfigYeuCauThayDoiSubAction_MCTuHuyYeuCau'),
						'text_color' => '#ffffff',
						'bg_color' => '#da2128',
						'font_style' => 'italic' 
					],
					'status_verify' => [
						'text' => vmsg('ConfigYeuCauThayDoiSubAction_MCTuHuyYeuCau'),
						'text_color' => '#da2128',
						'bg_color' => '#ffffff',
					]
				],
			];
			return $returnData;
		}

		if ($rq->isHetHanVaChuaTaoYc()) {
			$returnData = [
				'status' => 'HET_HAN_YC_HAY_TAO_YC_KHAC',
				'status_verify' => 'HET_HAN',
				'other_data' => (object)[
					'status' => [
						'text' => vmsg('Mpos360RequestChangeInfoListAction_HayTaoYcKhac'),
						'text_color' => '#d329a0',
						'bg_color' => '#ffffff',
						'font_style' => 'italic' 
					],
					'status_verify' => [
						'text' => vmsg('Mpos360RequestChangeInfoListAction_HetHan'),
						'text_color' => '#808890',
						'bg_color' => '#ffffff',
					]
				],
			];
			return $returnData;
		}

		if ($rq->isChuaXacThuc()) {
			$returnData = [
				'status' => 'HAY_XAC_THUC_DE_TAO_YC',
				'status_verify' => 'CHUA_XAC_THUC',
				'other_data' => (object)[
					'status' => [
						'text' => vmsg('Mpos360RequestChangeInfoListAction_HayXacThucYeuCau'),
						'text_color' => '#d329a0',
						'bg_color' => '#ffffff',
						'font_style' => 'italic'
					],
					'status_verify' => [
						'text' => vmsg('Mpos360RequestChangeInfoListAction_ChuaXacThuc'),
						'text_color' => '#e99323',
						'bg_color' => '#ffffff',
					]
				]
			];
			return $returnData;
		}

		if ($rq->isDaXacThucNhungChuaLamBuoc3()) { 
			$returnData = [
				'status' => $rq->status,
				'status_verify' => 'DA_XAC_THUC_NHUNG_CHUA_LAM_BUOC_3',
				'other_data' => (object)[
					'status' => [
						'text' => ConfigYeuCauThayDoiSubAction::getStyleAtList($rq)['text'],
						'text_color' => ConfigYeuCauThayDoiSubAction::getStyleAtList($rq)['text_color'],
						'bg_color' => ConfigYeuCauThayDoiSubAction::getStyleAtList($rq)['bg_color'],
						'font_style' => 'italic',
						'display_type' => 'pills',
						'pre_text' => vmsg('Mpos360RequestChangeInfoListAction_YeuCau'),
					],
					'status_verify' => [
						'text' => vmsg('Mpos360RequestChangeInfoListAction_CanXacMinhBuoc3'),
						'text_color' => '#018bf4',
						'bg_color' => '#ffffff',
					]
				]
			];
		}

		if ($rq->isDaLamBuoc3()) { 
			$returnData = [
				'status' => $rq->status,
				'status_verify' => 'DA_XAC_THUC',
				'other_data' => (object)[
					'status' => [
						'text' => ConfigYeuCauThayDoiSubAction::getStyleAtList($rq)['text'],
						'text_color' => ConfigYeuCauThayDoiSubAction::getStyleAtList($rq)['text_color'],
						'bg_color' => ConfigYeuCauThayDoiSubAction::getStyleAtList($rq)['bg_color'],
						'font_style' => 'italic',
						'display_type' => 'pills',
						'pre_text' => vmsg('Mpos360RequestChangeInfoListAction_YeuCau'),
					],
					'status_verify' => [
						'text' => vmsg('Mpos360RequestChangeInfoListAction_DaXacThuc'),
						'text_color' => '#3bb54a',
						'bg_color' => '#ffffff',
					]
				]
			];
		}

		return $returnData;
	}
} // End class
