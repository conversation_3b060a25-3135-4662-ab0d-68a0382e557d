<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransactionNormalDetailAction;

use App\Lib\Helper;
use App\Lib\partner\MposAWS;
use App\Lib\DBConnectionHelper;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionNormalDetailRequest;
use App\Modules\Merchant\Actions\Transaction\subAction\Mpos360TransactionDefineConfigSubAction;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransactionNormalDetailAction\SubAction\Mpos360GiaoDichQrDetailSubAction;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransactionNormalDetailAction\SubAction\Mpos360TransactionNormalDetailSubAction;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransactionNormalDetailAction\SubAction\Mpos360TransactionNormalDetailForMacqSubAction;

class Mpos360TransactionNormalDetailAction
{
	public $_type = 'DETAIL_TRANSACTION';

	public MposAWS $mposAWS;

	public function __construct(MposAWS $mposAWS)
	{
		$this->mposAWS = $mposAWS;	
	}

	public function run(Mpos360TransactionNormalDetailRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		DBConnectionHelper::closeIfExist();

		$emailMc = $deviceSession->getMerchantEmail();

		$params = [
			'typeTransaction' => $this->_type,
			'merchantFk' => $deviceSession->getMerchantId(),
			'id' => $request->json('data.transaction_id', ''),
			'tokenLogin' => $deviceSession->getMposToken()
		];

		$txId = $request->json('data.txid', '');

		if (!empty($txId)) {
			unset($params['id']);
			$params['txid'] = $txId;

			if (!empty($request->get('vaMode')) && $request->get('vaMode') == 'VAMC') {
				$listTransaction = $this->getChiTietGiaoDichQr($deviceSession, $txId);
				return (new Mpos360TransactionNormalDetailForMacqSubAction)->__convertDataReturn($listTransaction);
			}else {
				$listTransaction = app(Mpos360TransactionDefineConfigSubAction::class)->requestTransDetail($params);
			}
		}else {
			if (!empty($request->get('vaMode')) && $request->get('vaMode') == 'VAMC') {
				$listTransaction = $this->getChiTietGiaoDichQr($deviceSession, $request->json('data.transaction_id'));
				return (new Mpos360TransactionNormalDetailForMacqSubAction)->__convertDataReturn($listTransaction);
			}else {
				$listTransaction = app(Mpos360TransactionDefineConfigSubAction::class)->requestTransDetail($params);
			}
		}

		return (new Mpos360GiaoDichQrDetailSubAction())->__convertDataReturn($listTransaction);
	}

	public function getChiTietGiaoDichQr(DeviceSession $deviceSession, $txId) {
		$params = [
			'data' => [
				'serviceName' => 'STATISTIC_TRANSACTION',
				'merchantFk' => $deviceSession->getMerchantId(),
				'id' => $txId,
				'tokenLogin' => $deviceSession->getMposToken(),
				'typeTransaction' => 'DETAIL_TRANSACTION'
			]
		];

		$r = $this->mposAWS->chiTietGiaoDichQr($params);
		return $r;
	}
}
