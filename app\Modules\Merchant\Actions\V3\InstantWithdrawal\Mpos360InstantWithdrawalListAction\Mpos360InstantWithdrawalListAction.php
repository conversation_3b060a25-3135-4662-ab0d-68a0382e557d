<?php

namespace App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalListAction;

use App\Lib\DBConnectionHelper;
use Carbon\Carbon;
use App\Lib\Helper;
use App\Lib\partner\MPOS;
use Illuminate\Support\Arr;
use App\Modules\Merchant\Requests\InstantWithdrawal\V3\Mpos360InstantWithdrawalListRequest;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalListAction\SubAction\MapTrangThaiYcRtnSubAction;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalListAction\SubAction\MapTrangThaiYcRtnDetailSubAction;

class Mpos360InstantWithdrawalListAction
{
	public MPOS $mpos;

	public function __construct(MPOS $mpos)
	{
		$this->mpos = $mpos;
	}

	public function run(Mpos360InstantWithdrawalListRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		$rangeTime = $request->getStartDateEndDate();
		$data = $request->json('data');
		$params = [
			"serviceName" => "GET_DATA_PAGE",
			"merchantFk" => $deviceSession->getMerchantId(),
			"tokenLogin" => $deviceSession->getMposToken(),
			"pageSize" => $request->json('data.limit', 10),
			"pageIndex" => $request->getPageIndex(),
			'startDate' => $rangeTime['startDate'],
			'endDate' => $rangeTime['endDate'],
		];

		DBConnectionHelper::closeIfExist();

		$result = $this->mpos->getListPaymentNow($params);
		$returnData['filter'] = $this->__getDefaultFilter();

		if (empty($result['data']['data'])) {
			$returnData['data'] = [];
		}else {
			$params['rangeTime'] = collect($result['data']['data']['data'])->map(function ($item) {
				return Carbon::createFromTimestampMs($item['createdDate'])->format('d-m-Y');
			})->unique()->implode(',');

			$data = $this->__getData($result['data']['data']);

			$data = collect($data)->map(function ($item) {
				if ($item['date'] == now()->format('d-m-Y')) {
					$item['date'] = __('rtn.Hôm nay');
				}
	
				return $item;
			})->values()->all();
	
			$returnData['data'] = $data;
		}
		
		$params['email'] = $deviceSession->getMerchantEmail();
		$params['typeTransaction'] = 'STATISTIC_VIET_QR';

		$returnData['other_data']['countSumFilter'] = Arr::only($params, [
			'typeTransaction',
			'merchantFk',
			'rangeTime',
			'email'
		]);
		
		return $returnData;
	} // End method

	private function __getDefaultFilter()
	{
		$filter = [
			[
				'key' => 'order_rtn_time',
				'name' => __('rtn.Thời gian'),
				'list' => [
					[
						'value' => 'ALL',
						'label' => __('rtn.Tất cả')
					],
					// [
					// 	'value' => 'TODAY',
					// 	'label' => __('rtn.Hôm nay')
					// ],

					// [
					// 	'value' => 'THIS_MONTH',
					// 	'label' => __('rtn.Tháng này')
					// ],

					// [
					// 	'value' => 'LAST_MONTH',
					// 	'label' => __('rtn.Tháng trước')
					// ],
				]
			],
		];

		$mapTrangThai = app(MapTrangThaiYcRtnSubAction::class)->run();
		$filterTrangThai = [
			'key' => 'order_rtn_status',
			'name' => __('rtn.Trạng thái yc'),
			'list' => [
				[
					'value' => 'ALL',
					'label' => __('rtn.Tất cả')
				],
			]
		];

		$mapTrangThai = collect($mapTrangThai)->values()->toArray();
		foreach ($mapTrangThai as $it) {
			$filterTrangThai['list'][] = [
				'value' => $it['key'],
				'label' => $it['label']
			];
		}

		// $filter[] = $filterTrangThai;

		return $filter;
	}

	

	private function __getData($apiResult)
	{
		$dataGroupByDate = collect($apiResult['data'])->groupBy(function ($item) {
			return Carbon::createFromTimestampMs($item['createdDate'])->format('d-m-Y');
		})
		->all();


		$returnData = [];

		foreach ($dataGroupByDate as $date => $listYc) {
			$itemDate = [
				'date' => $date,
				'total_trans' => $listYc->count(),
				'data' => []
			];

			$list = [];

			foreach ($listYc as $yc) {
				$yc['withdrawStatus'] = $yc['withdraw']['status'] ?? '';
				$mapTrangThaiDetail = app(MapTrangThaiYcRtnDetailSubAction::class)->run($yc);
				$bank_info = (isset($value['withdraw']['bankAccount']['accountNo']) && isset($value['withdraw']['bankAccount']['hoderName'])) ? $value['withdraw']['bankAccount']['accountNo'] . ' - ' . $value['withdraw']['bankAccount']['hoderName'] : '';
				$receivedAmount = isset($yc['withdraw']['receivedAmount']) ? Helper::priceFormat($yc['withdraw']['receivedAmount']) : 0;

				$list[] = [
					'type' => 'VietQR',
					'total_trans_amount' => Helper::priceFormat($yc['trxSumAmount']),
					'order_code' => $yc['id'],
					'banking_info' => $bank_info,
					'time_created' => Carbon::createFromTimestampMs($yc['createdDate'])->format('H:i'),
					'note' => '',
					'status' => $mapTrangThaiDetail['text'],
					'real_received_amount' => $receivedAmount,
					'amount_unit' => 'VND',
					'other_data' => [
						'status' => [
							'text' => $mapTrangThaiDetail['text'],
							'text_color' =>  $mapTrangThaiDetail['text_color'],
							'bg_color' => $mapTrangThaiDetail['bg_color'],
						],
						'note' => [
							'text' => '',
							'text_color' => '#D22E2A',
						],
						'real_received_amount' => [
							'text' => $receivedAmount,
							'text_color' => '#73AE4A',
						],
						'total_trans_amount' => [
							'text' => Helper::numberFormat($yc['trxSumAmount']),
							'text_color' => '#404041',
						],
					]
				];
			}

			$itemDate['data'] = $list;
			$returnData[] = $itemDate;
		}

		return $returnData;
	}
} // End clas
