<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction;

class BuildBottomNavAppCaiTienSubAction
{
	/**
	 * Update 03.02.2025 bottom nav của app tingbox sẽ có 5 icon và fix cứng để tránh call DB
	 */
	public function run()
	{
		return [
			[
				'title' => 'Giao dịch',
				'titleEn' => 'Transaction',
				'icon' => 'https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/test/67a05ec44b6b7120aaaebd38TransactionHistory.png',
				'screen' => [
					'screenName' => 'TransactionHistoryGeneralScreen',
					'params' => []
				],
				'sort' => '1',
				'display' => '1',
				'is_main' => '0'
			],
			[
				'title' => 'Doanh thu',
				'titleEn' => 'Revenue',
				'icon' => 'https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/test/67a05f654b6b7120aaaebd40Report.png',
				'screen' => [
					'screenName' => 'RevenueScreen',
					'params' => []
				],
				'sort' => '1',
				'display' => '1',
				'is_main' => '0'
			],
			[
				'title' => '',
				'titleEn' => '',
				'icon' => 'https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/test/67a060234b6b7120aaaebd4aQrCuaHang.png',
				'screen' => [
					'screenName' => 'ScanQrScreen',
					'params' => []
				],
				'sort' => '3',
				'display' => '1',
				'is_main' => '1'
			],
			[
				'title' => 'Cửa hàng',
				'titleEn' => 'Store',
				'icon' => 'https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/test/67a060804b6b7120aaaebd53store.png',
				'screen' => [
					'screenName' => 'StoreScreen',
					'params' => []
				],
				'sort' => '4',
				'display' => '1',
				'is_main' => '0'
			],
			[
				'title' => 'Tài khoản',
				'titleEn' => 'Account',
				'icon' => 'https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/test/67a060ac4b6b7120aaaebd54Account.png',
				'screen' => [
					'screenName' => 'AccountScreen',
					'params' => []
				],
				'sort' => '5',
				'display' => '1',
				'is_main' => '0'
			],
		];
	}
}
