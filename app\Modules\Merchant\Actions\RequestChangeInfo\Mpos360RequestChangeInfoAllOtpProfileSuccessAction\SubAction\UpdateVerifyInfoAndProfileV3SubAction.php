<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessAction\SubAction;

use Exception;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;

class UpdateVerifyInfoAndProfileV3SubAction
{
	/**
	 * @param $mpos360McRequest 
	 * @param $nguoiDaiDienData [
	 * 		'isTrungCccdChungThuc' => bool,
	 * 		'profileLayTuCccd' => 
	 * ]
	 * 
	 * @param $getRequestVefifyAsArray [Dữ liệu verify nhét vào json]
	 * @param $attachments [Dữ liệu attachments số hóa thành profile ném vào json]
	 */
	public function run(
		Mpos360MerchantRequest $mpos360McRequest,
		array $nguoiDaiDienData,
		array $getRequestVefifyAsArray,
		array $attachments=[]
	) {
		$dataRequest = json_decode($mpos360McRequest->data_request, true);
		$dataRequest[0]['request_vefify'] = $getRequestVefifyAsArray;

		// Không phải đổi người đại diện mới thì mới cần mapp dữ liệu vào
		if (!$mpos360McRequest->isYeuCauDoiNguoiDaiDienMoi()) {
			$profiles = $dataRequest[0]['profiles'];

			foreach ($getRequestVefifyAsArray as $verityItem) {
				if (!empty($verityItem['field'])) {
					$profiles[$verityItem['field']] = $verityItem['value'];
				}
			}

			if (!empty($nguoiDaiDienData['profileLayTuCccd'])) {
				foreach ($nguoiDaiDienData['profileLayTuCccd'] as $key => $value) {
					$profiles[$key] = $value;
				}
			}

			if (!empty($attachments)) {
				$profileImageUrl = app(MappingDinhKemSubAction::class)->run($attachments);
				foreach ($profileImageUrl as $key => $value) {
					$profiles[$key] = $value;
				}
			}

			
			$dataRequest[0]['profiles'] = $profiles;
		}
		

		// Thay doi  thong tin cua ng dai dien hien tai
		if ($mpos360McRequest->isDoiThongTinLienHe() || $mpos360McRequest->isYeuCauDoiCccdMoi()) {
			$dataRequest[0]['profiles']['typeChangeRepresent'] = 'CHANGE_CURRENT_REPRESENT_INFO';
		}

		if ($mpos360McRequest->isYeuCauDoiNguoiDaiDienMoi()) {
			$dataRequest[0]['profiles']['typeChangeRepresent'] = 'CHANGE_NEW_REPRESENT';
		}

		if ($nguoiDaiDienData['isTrungCccdChungThuc']) {
			$dataRequest[0]['scan_method']['QTS'] = [
				'status' => 'DONE',
				'other_data' => [
					'is_matching_facescan' => 1,
					'matching_percent' => 100
				]
			];
		}

		$dataRequest[0]['raw_attachments'] = $attachments;

		$paramUpdate = [
			'data_request' => json_encode($dataRequest),
			'time_updated' => now()->timestamp,
			'status_verify' => Mpos360Enum::MPOS360_MC_VERIFY_STT_DA_XAC_THUC
		];

		if ($nguoiDaiDienData['isTrungCccdChungThuc']) {
			$paramUpdate['status_verify'] = Mpos360Enum::MPOS360_MC_VERIFY_STT_DA_THUC_HIEN_BUOC3;
			$paramUpdate['method_code'] = 'QTS';
		}

		$r = Mpos360MerchantRequest::query()->where('id', $mpos360McRequest->id)->update($paramUpdate);

		if (!$r) {
			mylog(['Loi update thong tin' => $r]);
			throw new BusinessException(__('dttv3.Lỗi update thông tin hồ sơ vào bản ghi'));
		}

		$can = Mpos360Enum::MPOS360_CAN_GOTO_STEP3;

		if ($nguoiDaiDienData['isTrungCccdChungThuc']) {
			$can = Mpos360Enum::MPOS360_CAN_MARK_DONE_REQUEST;
		}

		return $can;
	}
} // End class
