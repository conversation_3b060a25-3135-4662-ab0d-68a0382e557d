<?php

namespace App\Modules\Merchant\Actions\Notification\Mpos360GetGroupWithUnreadItemsAction;

use App\Exceptions\BusinessException;
use App\Lib\partner\MNPNOTIFY;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\DTOs\Notification\Mpos360GetGroupWithUnreadItemsDto\DataDto;
use App\Modules\Merchant\Requests\Notification\Mpos360GetGroupWithUnreadItemsRequest;
use App\Modules\Merchant\DTOs\Notification\Mpos360GetGroupWithUnreadItemsDto\Mpos360GetGroupWithUnreadItemsDto;

class Mpos360GetGroupWithUnreadItemsAction
{
	public function run(Mpos360GetGroupWithUnreadItemsRequest $request): Mpos360GetGroupWithUnreadItemsDto
	{
		$deviceSession = $request->getCurrentDeviceSession();

		$mposToken = $deviceSession->getMposToken();
		$mposEmail = $deviceSession->getMerchantEmail();

		$groupCode = $request->json('data.groupCode'); 

		// lấy all group nếu ko có thông tin truyền lên
		if (empty($groupCode)) {
			$groupCode = array_values(Mpos360Enum::ENUM_MNPNOTIFY_GROUP_CODE);
		}


		$mnpNotify = new MNPNOTIFY($mposToken);

		$result = $mnpNotify->getUnreadCount($deviceSession->getMerchantUserName(), $groupCode);
		
		mylog(['Result' => $result]);
		
		if (empty($result['data']['groups'])) {
			throw new BusinessException(vmsg('Lỗi gọi lấy số lượng noti chưa đọc'));
		}

		$mpos360GetGroupWithUnreadItemsDto = new Mpos360GetGroupWithUnreadItemsDto(
			$result['status'],
			new DataDto(
				$result['data']['code'],
				$result['data']['count'],
				$result['data']['groups'],
			),
			$result['message']
		);

		return $mpos360GetGroupWithUnreadItemsDto;
	}
} // End class