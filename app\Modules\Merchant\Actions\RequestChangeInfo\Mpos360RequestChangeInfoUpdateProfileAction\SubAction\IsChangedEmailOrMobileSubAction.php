<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\SubAction;

use Exception;
use App\Lib\partner\MNP;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction\MnpGetMerchantProfileSubAction;

class IsChangedEmailOrMobileSubAction
{
	public MNP $mnp;

	public function __construct(MNP $mnp)
	{
		$this->mnp = $mnp;
	}

	public function run(DeviceSession $deviceSessionWithToken, string $newEmail, string $newMobile): bool
	{
		$merchantId = $deviceSessionWithToken->getMerchantId();

		$mnpMerchantDetail = app(MnpGetMerchantProfileSubAction::class)->run(
			$merchantId,
			$deviceSessionWithToken->mnp_token
		);

		
		if (empty($mnpMerchantDetail['status'])) {
			throw new BusinessException('Lỗi không truy vấn được thông tin hồ sơ hiện tại');
		}

		$emailNguoiDaiDienHienTai = $mnpMerchantDetail['data']['authoriserEmail'];
		$sdtNguoiDaiDienHienTai = $mnpMerchantDetail['data']['authoriserContactNumber'];

		$isChanged = ($newEmail != $emailNguoiDaiDienHienTai || $newMobile != $sdtNguoiDaiDienHienTai);

		mylog([
			'merchantId' => $merchantId,
			'newEmail' => $newEmail,
			'newMobile' => $newMobile,
			'emailNguoiDaiDienHienTai' => $emailNguoiDaiDienHienTai,
			'sdtNguoiDaiDienHienTai' => $sdtNguoiDaiDienHienTai,
			'isChanged' => $isChanged ? 'Co thay doi' : 'Khong thay doi'
		]);

		return $isChanged;
	}
} // End class