<?php

return [
	'profile' => [
		/**
		 * trái: là từ api detail 
		 * phải: phục vụ cho api update
		 */
		'authoriserContactNumber' => 'representMobile',
		'authoriserEmail' => 'representEmail',
		'placeOfIssue' => 'representIssuePlace',
		'passport' => 'representPassport',
		'customerName' => 'representName',
		'authoriserAddress1' => 'representCurrentAddress',
		'authoriserAddressPresent' => 'representPermanentAddress',

		// update 06.09.2024, b<PERSON> sung đẻ thêm ra 1 số trường để phục vụ cho bên mobile nhập liệu
		'representPosition' => 'representPosition',
		'representMutualRelation' => 'representMutualRelation',
		'representBirthday' => 'representBirthday'
	],

	'type' => [
		'CHANGE_BANK_ACCOUN_INFO' => 'Tài khoản ngân hàng',
		'CHANGE_REPRESENT_INFO' => 'Người đại diện',
		'CHANGE_MPOS_ACCOUNT_INFO' => 'Thay thế tài khoản đăng nhập MPOS'
	],

	'choice' => [
		'DOI_THONG_TIN_LIEN_HE' => 'Đổi thông tin liên hệ',
		'DOI_NGUOI_DAI_DIEN_MOI' => 'Đổi người đại diện mới',
		'DOI_CCCD_MOI' => 'Đổi CCCD mới',
	],

	'typeChangeRepresent' => [
		'CHANGE_CURRENT_REPRESENT_INFO' => 'Thay đổi thông tin người đại diện hiện tại',
		'CHANGE_NEW_REPRESENT' => 'Đổi người đại diện mới'
	],

	'profile_wording' => [
		'mposId' => 'Mã định danh của MPOS (mPOS ID).',
		'arrTypeChanges' => 'Danh sách các loại thay đổi: thay đổi thông tin đại diện và thay đổi thông tin tài khoản ngân hàng.',
		'typeChangeRepresent' => 'Loại thay đổi người đại diện:',
		'bankId' => 'ID ngân hàng mới.',
		'bankName' => 'Tên ngân hàng mới',
		'branch' => 'Chi nhánh ngân hàng mới.',
		'holderName' => 'Tên người thụ hưởng mới.',
		'bankCityCode' => 'Mã thành phố ngân hàng mới',
		'bankCityName' => 'Tên thành phố ngân hàng mới.',
		'accountNo' => 'Số tài khoản ngân hàng mới.',
		'bankAccType' => 'Loại tài khoản ngân hàng mới',
		'positionAuthBank' => 'Chức vụ của người ủy quyền tại ngân hàng.',
		'bankMutualRelation' => 'Mối quan hệ giữa chủ tài khoản và người có thẩm quyền tại ngân hàng.',
		'representName' => 'Tên người đại diện mới.',
		'representPassport' => 'CCCD/Hộ chiếu của người đại diện mới.',
		'representIssuePlace' => 'Nơi cấp CCCD/Hộ chiếu của người đại diện mới.',
		'representEmail' => 'Email của người đại diện mới.',
		'representMobile' => 'Số điện thoại di động của người đại diện mới.',
		'representBirthday' => 'Ngày sinh của người đại diện mới.',
		'representCurrentAddress' => 'Địa chỉ hiện tại của người đại diện mới.',
		'representPermanentAddress' => 'Địa chỉ thường trú của người đại diện mới.',
		'representPosition' => 'Chức vụ của người đại diện mới.',
		'representMutualRelation' => 'Mối quan hệ giữa người đại diện và người ủy quyền.',
		'representAuthContractNumber' => 'Số giấy ủy quyền của người đại diện.',
		'passportRepresentFrontUrl' => 'URL của ảnh hộ chiếu mặt trước của người đại diện mới.',
		'passportRepresentBackUrl' => 'URL của ảnh hộ chiếu mặt sau của người đại diện mới.',
		'passportAuthoriserFrontUrl' => 'URL của ảnh hộ chiếu người ủy quyền.  (Mặt trước)',
		'passportAuthoriserBackUrl' => 'URL của ảnh hộ chiếu người ủy quyền. (Mặt sau)',
		'substituteCertUrls' => 'phu URL của giấy chứng từ thay thế',
		'attachedCertUrls' => 'URL của giấy chứng nhận đính kèm',
		'lostEmailUrls' => 'Ảnh chụp hoặc video bạn đang cầm CCCD và phụ lục',
		'lostPassportUrls' => 'Ảnh xác nhận căn cước công dân của bộ công an',
	],

	'bankMnpVimo' => [
		"5fb22b462cc8fd12d0623356" => "09",
		"5fb22b492cc8fd12d0623395" => "02",
		"5fb22b462cc8fd12d0623355" => "07",
		"5fb22b492cc8fd12d0623394" => "",
		"5fb22b462cc8fd12d0623357" => "08",
		"5fb22b472cc8fd12d062335c" => "14",
		"5fb22b492cc8fd12d0623391" => "",
		"5fb22b472cc8fd12d062335b" => "10",
		"5fb22b492cc8fd12d0623390" => "",
		"5fb22b472cc8fd12d062335a" => "19",
		"5fb22b482cc8fd12d0623383" => "",
		"5fb22b472cc8fd12d0623367" => "",
		"5fb22b482cc8fd12d0623382" => "",
		"5fb22b472cc8fd12d0623366" => "83",
		"5fb22b482cc8fd12d0623384" => "",
		"5fb22b482cc8fd12d0623381" => "",
		"5fb22b472cc8fd12d0623369" => "29",
		"5fb22b482cc8fd12d0623380" => "42",
		"5fb22b462cc8fd12d062334e" => "05",
		"5fb22b472cc8fd12d0623371" => "33",
		"5fb22b462cc8fd12d062334d" => "03",
		"5fb22b482cc8fd12d0623379" => "35",
		"5fb22b462cc8fd12d062334f" => "01",
		"5fb22b472cc8fd12d0623375" => "12",
		"5fb22b482cc8fd12d0623376" => "30",
		"5fb22b472cc8fd12d0623374" => "34",
		"5fb22b472cc8fd12d0623373" => "39",
		"5fb22b472cc8fd12d0623372" => "18",
		"5fb22b482cc8fd12d0623377" => "27",
		"5fb22b472cc8fd12d062335e" => "13",
		"5fb22b472cc8fd12d062335d" => "24",
		"5fb22b492cc8fd12d0623389" => "",
		"5fb22b492cc8fd12d0623386" => "41",
		"5fb22b492cc8fd12d0623385" => "37",
		"5fb22b472cc8fd12d062336c" => "17",
		"5fb22b472cc8fd12d062336b" => "51",
		"5fb22b472cc8fd12d062336a" => "28",
		"5fb22b492cc8fd12d062338f" => "",
		"5fb22b492cc8fd12d062338e" => "",
		"5fb22b492cc8fd12d062338d" => "",
		"5fb22b492cc8fd12d062338b" => "",
		"5fb22b492cc8fd12d062338a" => "",
		"5fb22b462cc8fd12d0623350" => "04",
		"5fb22b472cc8fd12d062336f" => "15",
		"5fb22b462cc8fd12d0623354" => "25",
		"5fb22b462cc8fd12d0623353" => "",
		"5fb22b492cc8fd12d0623396" => "26",
		"5fb22b4a2cc8fd12d0623398" => "",
		"5fb22b4a2cc8fd12d0623397" => "",
		"5fb22b482cc8fd12d062337f" => "43",
		"5fb22b472cc8fd12d0623359" => "06",
		"5fb22b472cc8fd12d0623358" => "23",
		"5fb22b472cc8fd12d0623360" => "11",
		"5fb22b472cc8fd12d0623364" => "20",
		"5fb22b472cc8fd12d0623363" => "21",
		"5fb22b472cc8fd12d0623362" => "22",
		"5fb22b472cc8fd12d0623361" => "16",
		"5fb22b482cc8fd12d062337a" => "32",
		"5fb22b482cc8fd12d062337d" => "",
		"5fb22b482cc8fd12d062337c" => "",
	]
];
