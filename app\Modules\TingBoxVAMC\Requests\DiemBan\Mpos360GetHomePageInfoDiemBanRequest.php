<?php

namespace App\Modules\TingBoxVAMC\Requests\DiemBan;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360GetHomePageInfoDiemBanRequest extends MerchantRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.merchantId' => ['required', 'string'],
			'data.muId' => ['present', 'string'],
			'data.username' => ['required', 'string'],
			'data.versionApp' => ['required', 'string'],
			'data.currentDate' => ['required', 'string', 'date_format:d-m-Y']
    ];
  }
}
