<?php

namespace App\Lib\partner;

use App\Exceptions\BusinessException;
use App\Lib\partner\MNP;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Model\Mpos360MerchantRequestSupplement;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;

class MNPExtend extends MNP
{
	public function sendYc(Mpos360MerchantRequest $mpos360McRequest, string $mnpToken) 
	{
		$dataRequest = $mpos360McRequest->getDataRequestV3();
		$data = [
			'mposId' => $mpos360McRequest->merchant_id,
			'code' => $mpos360McRequest->mpos360_code
		];

		foreach ($dataRequest as $item) {
			$data['arrTypeChanges'][] = $item['type'];
			foreach ($item['profiles'] as $mnpProfileKey => $mnpProfileValue) {
				$data[$mnpProfileKey] = $mnpProfileValue;
			}
		}

		// Handle gửi ảnh đúng loại, ko động vào luồng yc
		$rawAttachments = $dataRequest[0]['raw_attachments']['id_documents'] ?? [];

		// cá nhân đc ủy quyền 
		if (!empty($rawAttachments['cccd_2mat_sau_cua_ca_nhan_duoc_uy_quyen'])) {
			$data['passportAuthoriserFrontUrl'] = Arr::first($rawAttachments['cccd_2mat_sau_cua_ca_nhan_duoc_uy_quyen']);
			$data['passportAuthoriserBackUrl'] = Arr::last($rawAttachments['cccd_2mat_sau_cua_ca_nhan_duoc_uy_quyen']);

			foreach ($data['substituteCertUrls'] as $index => $link) {
				if ($link == $data['passportAuthoriserFrontUrl'] || $link == $data['passportAuthoriserBackUrl']) {
					unset($data['substituteCertUrls'][$index]);
				}
			}

			$data['substituteCertUrls'] = array_values($data['substituteCertUrls']);
		}

		// căn cccd người mới thì vào key người ủy quyền
		if (!empty($rawAttachments['cccd_hai_mat_cua_nguoi_dai_dien_moi'])) {
			$data['passportAuthoriserFrontUrl'] = Arr::first($rawAttachments['cccd_hai_mat_cua_nguoi_dai_dien_moi']);
			$data['passportAuthoriserBackUrl'] = Arr::last($rawAttachments['cccd_hai_mat_cua_nguoi_dai_dien_moi']);

			foreach ($data['substituteCertUrls'] as $index => $link) {
				if ($link == $data['passportAuthoriserFrontUrl'] || $link == $data['passportAuthoriserBackUrl']) {
					unset($data['substituteCertUrls'][$index]);
				}
			}

			$data['substituteCertUrls'] = array_values($data['substituteCertUrls']);
		}

		// xử lý ảnh giấy ủy quyền
		if (!empty($rawAttachments['anh_giay_uy_quyen'])) {
			$data['authorizationUrls'] = $rawAttachments['anh_giay_uy_quyen'];

			foreach ($data['substituteCertUrls'] as $index => $link) {
				if (in_array($link, $data['authorizationUrls'])) {
					unset($data['substituteCertUrls'][$index]);
				}
			}

			$data['substituteCertUrls'] = array_values($data['substituteCertUrls']);
		}

		$this->baseUrl = $this->baseUrl2;
		$this->authToken = $mnpToken;

		$result = $this->sendRequest('/create', 'POST', $data);

		mylog([
			'data' => $data,
			'baseUrl' => $this->baseUrl,
			'authToken' => $this->authToken,
			'result' => $result
		]);
		return $this->returnData($result);
	}

	public function validateEmailAndMobile(string $mnpToken, string $email='', string $mobile='') {
		if (empty($email) && empty($mobile)) {
			throw new BusinessException('Email or Mobile must have value to validate');
		}
		
		$params = [];

		if (!empty($email)) {
			$params['email'] = $email;
		}

		if (!empty($mobile)) {
			$params['mobile'] = $mobile;
		}

		$this->baseUrl = $this->baseUrlCustomer;
		$this->authToken = $mnpToken;
		$result = $this->sendRequest('/cnps-cstm-customer-other/validate-account', 'POST', $params);
		
		mylog([
			'data' => $params,
			'baseUrl' => $this->baseUrl,
			'authToken' => $this->authToken,
			'result' => $result
		]);

		return $result;
	}

	public function updateBoSungThongTin(Mpos360MerchantRequestSupplement $mpos360McSupplement, string $mnpToken, Collection $listBoSungTruocDo) 
	{
		$dataRequest = json_decode($mpos360McSupplement->data_request, true);
		$attachments = $dataRequest['attachments']['substituteCertUrls'];

		if ($listBoSungTruocDo->isNotEmpty()) {
			foreach ($listBoSungTruocDo as $supp) {
				$dataRequestSupp = json_decode($supp->data_request, true);
				$suppAttachments = $dataRequestSupp['attachments']['substituteCertUrls'] ?? [];

				if (!empty($suppAttachments)) {
					foreach ($suppAttachments as $atm) {
						$attachments[] = $atm;
					}
				}
			}
		}
		
		$data = [
			'reqId' => $mpos360McSupplement->mpos360McRequest->mynextpay_id,
			'additionalUrls' => $attachments
		];

		$this->baseUrl = $this->baseUrl2;
		$this->authToken = $mnpToken;

		$result = $this->sendRequest('/update-file', 'POST', $data);

		mylog([
			'data' => $data,
			'baseUrl' => $this->baseUrl,
			'authToken' => $this->authToken,
			'result' => $result
		]);

		return $result;
	}
} // End class
