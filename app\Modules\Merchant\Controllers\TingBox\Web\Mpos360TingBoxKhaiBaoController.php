<?php

namespace App\Modules\Merchant\Controllers\TingBox\Web;

use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Filesystem\Cache;
use App\Lib\MnpOnboardNewMcHelper;
use App\Modules\Merchant\Model\CacheAction;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\TingBox\Web\Mpos360TingBoxKhaiBaoListInfoRequest;

class Mpos360TingBoxKhaiBaoController extends Controller
{
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function Mpos360TingBoxKhaiBaoListInfo(Mpos360TingBoxKhaiBaoListInfoRequest $request)
	{
		MnpOnboardNewMcHelper::handleSecurity($request->get('merchantId'), $request->get('signed'));
		
		$params = ['mposMcId' => $request->get('merchantId')];
		$cccdName = '';

		$listStepInfo = @$this->mnpOnboardNewMcHelper->detailMc($params);
		
		$taiKhoanNganHang = $listStepInfo;

		$locationEditParam = [];

		if (!empty($listStepInfo['data']['locations'][0])) {
			$locationEditParam = $listStepInfo['data']['locations'][0];
			$locationEditParam = Arr::only($locationEditParam, ['id', 'areaName', 'areaAddress', 'areaCityCode', 'areaDistrictCode']);
			$locationEditParam['mposMcId'] = $listStepInfo['data']['mposMcId'];
			$locationEditParam['mccId'] = $listStepInfo['data']['locations'][0]['mcc'];
		}
		
		$dataNganhNgheNganHangThanhPho = @$this->mnpOnboardNewMcHelper->getNganhNgheNganHangThanhPho(true);

		$actionAction = @CacheAction::query()->where('step', 2)
						->where('reference_id', $request->get('merchantId'))
						->first();
		if ($actionAction) {
			$otherData = json_decode($actionAction->other_data, true);
			$cccdName = Str::of($otherData['name'])->slug(' ')->upper()->trim()->__toString();
		}

		return view('TingBoxWeb.listInfoKhaiBao', [
			'listStepInfo' => $listStepInfo,
			'taiKhoanNganHang' => $taiKhoanNganHang,
			'request' => $request,
			'locationEditParam' => $locationEditParam,
			'title' => 'Khai báo cửa hàng, thiết bị, TKNH',
            'dataNganhNgheNganHangThanhPho' => $dataNganhNgheNganHangThanhPho,
            'cccdName' => $cccdName,
		]);
	}

	public function Mpos360TingBoxKhaiBaoThietBi(Request $request)
	{
		$params = [
			'serial' => trim(strtoupper($request->input('serial'))),
			'mposMcId' => $request->input('mposMcId'),
			'areaId' => $request->input('areaId'),
		];

		$messageGeneral = sprintf('Lỗi thiết bị TingBox %s không thể kích hoạt do không đáp ứng điều kiện hoặc đã thuộc đơn vị khác. Vui lòng kiểm tra lại mã thiết bị hoặc liên hệ CSKH để được hỗ trợ.', trim(strtoupper($request->input('serial'))));;

		$response = @$this->mnpOnboardNewMcHelper->ganThietBiTingBox($params);
		
		if (!empty($response['result']) && $response['code'] == 1000) {
			
			return response()->json([
				'success' => true,
				'code' => $response['code'],
				'data' => $response['data'],
				'message' => '',
				'swal' => [
					'title' => 'Thành công',
					'html' => view('TingBoxWeb.modal.modalGanLoaThanhCong')->render(),
					'iconHtml' =>  '<svg width="81" height="80" viewBox="0 0 81 80" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M40.5 0C18.46 0 0.5 17.96 0.5 40C0.5 62.04 18.46 80 40.5 80C62.54 80 80.5 62.04 80.5 40C80.5 17.96 62.54 0 40.5 0ZM59.62 30.8L36.94 53.48C36.38 54.04 35.62 54.36 34.82 54.36C34.02 54.36 33.26 54.04 32.7 53.48L21.38 42.16C20.22 41 20.22 39.08 21.38 37.92C22.54 36.76 24.46 36.76 25.62 37.92L34.82 47.12L55.38 26.56C56.54 25.4 58.46 25.4 59.62 26.56C60.78 27.72 60.78 29.6 59.62 30.8Z" fill="#73AE4A"/>
</svg>
'
				]
			]);
		}

		if(!$response['result'] || $response['code'] != 1000){
			$message = isset($response['message']) && !empty($response['message']) ? $response['message'] : $messageGeneral;
			$message = sprintf('MNP Err: %s (Code: %s)', $message, $response['code']);
			return response()->json([
				'success' => false,
				'code' => $response['code'],
				'data' => $response['data'],
				'message' => $message,
				'swal' => [
						'title' => 'Thất bại',
						'html' => view('TingBoxWeb.modal.modalGanLoaThatBai', ['tingBoxSerial' => $params['serial'], 'response' => $response])->render(),
						'iconHtml' => '<svg width="81" height="80" viewBox="0 0 81 80" fill="none" xmlns="http://www.w3.org/2000/svg">
	<path d="M40.5 80C62.5914 80 80.5 62.0914 80.5 40C80.5 17.9086 62.5914 0 40.5 0C18.4086 0 0.5 17.9086 0.5 40C0.5 62.0914 18.4086 80 40.5 80Z" fill="#FDB62F"/>
	<path d="M40.4998 48C39.0513 47.9999 37.6589 47.4393 36.6145 46.4357C35.57 45.432 34.9544 44.063 34.8967 42.6156L34.1576 23.9531C34.128 23.1988 34.251 22.4462 34.5192 21.7405C34.7874 21.0348 35.1953 20.3905 35.7185 19.8463C36.2418 19.3021 36.8695 18.8691 37.5641 18.5733C38.2587 18.2775 39.0058 18.125 39.7608 18.125H41.2389C41.9938 18.125 42.741 18.2775 43.4356 18.5733C44.1302 18.8691 44.7579 19.3021 45.2811 19.8463C45.8043 20.3905 46.2123 21.0348 46.4805 21.7405C46.7487 22.4462 46.8717 23.1988 46.842 23.9531L46.103 42.6156C46.0452 44.063 45.4297 45.432 44.3852 46.4357C43.3407 47.4393 41.9484 47.9999 40.4998 48Z" fill="white"/>
	<path d="M46.0982 56.2781C46.0982 53.1862 43.5917 50.6797 40.4998 50.6797C37.4079 50.6797 34.9014 53.1862 34.9014 56.2781C34.9014 59.3701 37.4079 61.8766 40.4998 61.8766C43.5917 61.8766 46.0982 59.3701 46.0982 56.2781Z" fill="white"/>
	</svg>
	',
						'icon' => 'warning'
					]
			]);
		}

		return response()->json([
			'success' => false,
			'code' => $response['code'],
			'data' => $response['data'],
			'message' => $messageGeneral,
			'swal' => [
					'title' => 'Thất bại',
					'html' => view('TingBoxWeb.modal.modalGanLoaThatBai', ['tingBoxSerial' => $params['serial'], 'response' => $response])->render(),
					'iconHtml' => '<svg width="81" height="80" viewBox="0 0 81 80" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M40.5 80C62.5914 80 80.5 62.0914 80.5 40C80.5 17.9086 62.5914 0 40.5 0C18.4086 0 0.5 17.9086 0.5 40C0.5 62.0914 18.4086 80 40.5 80Z" fill="#FDB62F"/>
<path d="M40.4998 48C39.0513 47.9999 37.6589 47.4393 36.6145 46.4357C35.57 45.432 34.9544 44.063 34.8967 42.6156L34.1576 23.9531C34.128 23.1988 34.251 22.4462 34.5192 21.7405C34.7874 21.0348 35.1953 20.3905 35.7185 19.8463C36.2418 19.3021 36.8695 18.8691 37.5641 18.5733C38.2587 18.2775 39.0058 18.125 39.7608 18.125H41.2389C41.9938 18.125 42.741 18.2775 43.4356 18.5733C44.1302 18.8691 44.7579 19.3021 45.2811 19.8463C45.8043 20.3905 46.2123 21.0348 46.4805 21.7405C46.7487 22.4462 46.8717 23.1988 46.842 23.9531L46.103 42.6156C46.0452 44.063 45.4297 45.432 44.3852 46.4357C43.3407 47.4393 41.9484 47.9999 40.4998 48Z" fill="white"/>
<path d="M46.0982 56.2781C46.0982 53.1862 43.5917 50.6797 40.4998 50.6797C37.4079 50.6797 34.9014 53.1862 34.9014 56.2781C34.9014 59.3701 37.4079 61.8766 40.4998 61.8766C43.5917 61.8766 46.0982 59.3701 46.0982 56.2781Z" fill="white"/>
</svg>
',
					'icon' => 'warning'
				]
		]);
	}

	public function Mpos360TingBoxGetFormSuaCuaHang(Request $request)
	{
		$formParams = $request->input('formParams');
		$paramEditCuaHang = json_decode($formParams, true);

		$listNganHangThanhPho = @$this->mnpOnboardNewMcHelper->getNganhNgheNganHangThanhPho(true);
								
		$listQuanHuyen = @$this->mnpOnboardNewMcHelper->getQuanHuyenTuMaTinhThanh($paramEditCuaHang['areaCityCode'], true);
		
		$html = view('TingBoxWeb.modal.modalSuaThongTinCuaHang', [
			'listNganHangThanhPho' => $listNganHangThanhPho,
			'paramEditCuaHang' => $paramEditCuaHang,
			'listQuanHuyen' => $listQuanHuyen
		])->render();

		return response()->json([
			'success' => true,
			'code' => 100,
			'data' => [],
			'message' => $html
		]);
	}

	public function Mpos360TingBoxUpdateCuaHang(Request $request)
	{
		$params = $request->all();
		$r = @$this->mnpOnboardNewMcHelper->updateThongTinCuaHang($params);

		if (!empty($r['data']) && !empty($r['result'])) {
			return response()->json([
				'success' => true,
				'code' => $r['code'],
				'data' => $r['data'],
				'message' => 'Cập nhật thông tin cửa hàng thành công'
			]);
		}

		return response()->json([
			'success' => false,
			'code' => $r['code'],
			'data' => $r['data'],
			'message' => sprintf('Cập nhật thông tin cửa hàng thất bại (Code: %s)', $r['code'] ?? '-1')
		]);
	}

	public function Mpos360iTngBoxOnChangeTinhThanh(Request $request)
	{
		$cityId = $request->input('cityId');
		$listQuanHuyen = @$this->mnpOnboardNewMcHelper->getQuanHuyenTuMaTinhThanh($cityId, true);
		$html = view('TingBoxWeb.inc.selectOptionQuanHuyen', ['listQuanHuyen' => $listQuanHuyen])->render();
		
		return response()->json([
			'success' => true,
			'code' => 1000,
			'data' => [],
			'message' => $html
		]);
	}

	public function Mpos360iTngBoxOnCheckTknh(Request $request)
	{
		$listBank = @$this->mnpOnboardNewMcHelper->getNganhNgheNganHangThanhPho(true);
		$bankItem = collect($listBank['banks'])->where('bankId', $request->input('bank_id'))->first();

		$paramCheckStk = [
			'bank_account_holder' => trim($request->input('bank_account_holder')),
			'bank_account' => trim($request->input('bank_account')),
			'bank_id' => $bankItem['bankVimoId'],
			'bank_name' => $request->input('bank_name')
		];

		$actionAction = @CacheAction::query()->where('step', 2)
																				->where('reference_id', $request->input('mposMcId'))
																				->first();
		if (!$actionAction) {
			return response()->json([
				'success' => false,
				'code' => 1000,
				'data' => [],
				'message' => 'Không tìm được thông tin CCCD đã cung cấp trước đó'
			]);
		}

		$otherData = json_decode($actionAction->other_data, true);
		$cccdName = Str::of($otherData['name'])->slug(' ')->upper()->trim()->__toString();
		
		if (!Str::contains($paramCheckStk['bank_account_holder'], $cccdName )) {
			return response()->json([
				'success' => false,
				'code' => 1000,
				'data' => [],
				'message' => 'Tên người thụ hưởng phải trùng khớp với tên trên CCCD bạn đã khai báo'
			]);
		}

		try {
			$checkTknhResult = @$this->mnpOnboardNewMcHelper->checkThongTinBanking($paramCheckStk);
		}catch(\Throwable $th) {
			$checkTknhResult = [];
		}
		

		$paramUpdateNganHang = [
			'bankId' => $bankItem['bankId'], // id bank của mnp (request truyên)
			'mposMcId' => $request->input('mposMcId'),
			'accountNo' => $paramCheckStk['bank_account'],
			'holderName' => $paramCheckStk['bank_account_holder'],
			'bankVerify' => $checkTknhResult['status'] == 'STK_HOP_LE' ? 'true' : false,
		];

		$html = view('TingBoxWeb.modal.modalCheckTknhResult', [
			'params' => $paramCheckStk,
			'checkTknhResult' => $checkTknhResult,
			'paramUpdateNganHang' => $paramUpdateNganHang
		])->render();

		
		return response()->json([
			'success' => true,
			'code' => 1000,
			'data' => [],
			'message' => $html
		]);
	}

	public function Mpos360iTngBoxOnUpdateTknh(Request $request)
	{
		$param = $request->all();
		
		if ($param['bankVerify'] === 'true') {
			$param['bankVerify'] = true;
		}else {
			$param['bankVerify'] = false;
		}

		$listBank = @$this->mnpOnboardNewMcHelper->getNganhNgheNganHangThanhPho(true);
		$bankItem = collect($listBank['banks'])->where('bankId', $param['bankId'])->first();

		$paramCheckStk = [
			'bank_account_holder' => $param['holderName'],
			'bank_account' => $param['accountNo'],
			'bank_id' => $bankItem['bankVimoId'],
		];

		try {
			$checkTknhResult = @$this->mnpOnboardNewMcHelper->checkThongTinBanking($paramCheckStk);
		}catch(\Throwable $th) {
			$checkTknhResult = [];
		}

		mylog(['action' => 'CHECK_STK_BUOC_CUOI']);

		if (empty( $checkTknhResult['status'] ) || $checkTknhResult['status'] != 'STK_HOP_LE') {
			$msg = 'Hệ thống không thể kiểm tra được STK ngân hàng của bạn. Vui lòng liên hệ chuyên viên hỗ trợ để được xử lý';

			return response()->json([
				'success' => false,
				'code' => -1,
				'data' => [],
				'message' => $msg,
				'swal' => [
					'icon' => 'error',
					'title' => 'Thất bại',
					'html' => $msg
				]
			]);
		}

		$param['bankVerify'] = true;
		$r = @$this->mnpOnboardNewMcHelper->updateThongTinBanking($param);
		
		$message = sprintf('%s (Code: %s)', $r['message'] ?? 'Đã có lỗi xảy ra, vui lòng liên hệ chuyên viên hỗ trợ để xử lý', $r['code'] ?? '-1');
		$success = false;

		if (!empty($r['result']) && $r['code'] == 1000) {
			$success = true;
			$message = 'Tài khoản ngân hàng đã được liên kết thành công. mPOS sẽ sử dụng để chuyển tiền từ các giao dịch hàng ngày vào tài khoản này.';
		}

		
		return response()->json([
			'success' => $success,
			'code' => $r['code'],
			'data' => [],
			'message' => $message,
			'swal' => [
				'icon' => $success ? 'success' : 'error',
				'title' => $success ? 'Thành công' : 'Thất bại',
				'html' => $message
			]
		]);
	}
} // End class
