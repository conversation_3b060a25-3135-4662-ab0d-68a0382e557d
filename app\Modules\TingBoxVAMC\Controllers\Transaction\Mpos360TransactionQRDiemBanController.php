<?php

namespace App\Modules\TingBoxVAMC\Controllers\Transaction;

use App\Lib\Helper;
use App\Modules\WebBackend\Controllers\Controller;
use App\Modules\TingBoxVAMC\Requests\Transaction\Mpos360GetDsDiemBanMuidRequest;
use App\Modules\TingBoxVAMC\Requests\Transaction\Mpos360CountSumDoanhThuByDiemBanRequest;
use App\Modules\TingBoxVAMC\Requests\Transaction\Mpos360TransactionQRListByDiemBanRequest;
use App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360GetDsDiemBanMuidAction\Mpos360GetDsDiemBanMuidAction;
use App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360GetDsDiemBanMuidAction\Mpos360GetDsDiemBanMuidTingBoxMcAction;
use App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360CountSumDoanhThuByDiemBanAction\Mpos360CountSumDoanhThuByDiemBanAction;
use App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360TransactionQRListByDiemBanAction\Mpos360TransactionQRListByDiemBanAction;

class Mpos360TransactionQRDiemBanController extends Controller
{
	public function Mpos360TransactionQRListByDiemBan(Mpos360TransactionQRListByDiemBanRequest $request)
	{
		try {
			$result = app(Mpos360TransactionQRListByDiemBanAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function Mpos360CountSumDoanhThuByDiemBan(Mpos360CountSumDoanhThuByDiemBanRequest $request)
	{
		try {
			$result = app(Mpos360CountSumDoanhThuByDiemBanAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function Mpos360GetDsDiemBanMuid(Mpos360GetDsDiemBanMuidRequest $request)
	{
		try {
			// $result = app(Mpos360GetDsDiemBanMuidAction::class)->run($request);
			$result = app(Mpos360GetDsDiemBanMuidTingBoxMcAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
