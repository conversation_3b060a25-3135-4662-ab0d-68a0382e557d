<?php

namespace App\Modules\Merchant\Actions\ServiceProgramRegister\Mpos360ServiceProgramRegisterFinishAction;

use App\Exceptions\BusinessException;
use Exception;
use App\Lib\partner\MNP;
use App\Modules\Merchant\Model\Mpos360PhuongThucBuoc3;
use App\Modules\Merchant\Enums\ServiceProgramRegisterEnum;
use App\Modules\Merchant\Model\Mpos360MerchantRequestServiceProgramRegister;
use App\Modules\Merchant\Requests\ServiceProgramRegister\Mpos360ServiceProgramRegisterFinishRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction\MnpGetMerchantProfileSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestCronPushRecordAction\SubAction\GetCccdInfoByQtsRequestIdSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\GetMNPAccessTokenSubAction;
use App\Modules\Merchant\Model\Setting;

class Mpos360ServiceProgramRegisterFinishAction
{
	public GetCccdInfoByQtsRequestIdSubAction $action;

	public function __construct(GetCccdInfoByQtsRequestIdSubAction $action)
	{
		$this->action = $action;	
	}

	public function run(Mpos360ServiceProgramRegisterFinishRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		$merchantId = $deviceSession->getMerchantId();

		$yeuCauDangKyDichVu = Mpos360MerchantRequestServiceProgramRegister::query()->firstWhere([
			'merchant_id' => $merchantId,
			'id' => $request->json('data.request_id')
		]);

		if (!$yeuCauDangKyDichVu) {
			throw new BusinessException('Không tìm thấy yêu cầu đăng ký dịch vụ');
		}

		$dataRequest = $yeuCauDangKyDichVu->getDataRequest();

		if ($yeuCauDangKyDichVu->isLoaiMcHKD()) {
			if (empty($dataRequest['signatureUrl'])) {
				throw new BusinessException('Yêu cầu của bạn bị thiếu chữ ký, từ chối xử lý');
			}
		}

		$qtsRequestId = $request->json('data.qts_request_id', '');
		
		if (!empty($qtsRequestId) && $yeuCauDangKyDichVu->isLoaiMcHKD()) {
			$dataRequest['qts_request_id'] = $qtsRequestId;	
			$detailQtsResult = $this->action->run($qtsRequestId);
			
			$isMatching = $yeuCauDangKyDichVu->isMatchingCccd($detailQtsResult->currentCardId, $detailQtsResult->oldCardId);
			
			$settingPassKyc = Setting::query()->firstWhere(['key' => 'LIST_MC_CONFIG_PASSED_KYC_DANG_KY_DICH_VU']);
			$listMcPassedKyc = optional($settingPassKyc)->value ? json_decode($settingPassKyc->value, true) : [];

			if (!$isMatching && !in_array($merchantId, $listMcPassedKyc)) {
				throw new BusinessException('Lỗi: Thông tin eKYC và số CCCD của chủ merchant không giống nhau');
			}
		}
		
		$yeuCauDangKyDichVu->status = ServiceProgramRegisterEnum::SPR_STT_TAO_XONG_YC;
		$yeuCauDangKyDichVu->time_updated = now()->timestamp;
		$yeuCauDangKyDichVu->data_request = json_encode($dataRequest);
		$r = $yeuCauDangKyDichVu->save();

		if (!$r) {
			throw new BusinessException('Lỗi không hoàn thành được yêu cầu đăng ký dịch vụ');
		}

		return [
			'request_id' => $yeuCauDangKyDichVu->id,
			'status' => 'SUCCESS',
			'msg' => [
				'title' => 'Gửi đăng ký thành công',
				'content' => sprintf('%s%s%s',
					'Cảm ơn Quý đơn vị đã tin tưởng và lựa chọn dịch vụ của NextPay. Chúng tôi xin xác nhận đã nhận được yêu cầu đăng ký của Quý đơn vị. Trong vòng 2 ngày làm việc, đội ngũ của NextPay sẽ xử lý và phản hồi lại để hoàn tất quá trình đăng ký.',
					PHP_EOL,
					'Chúng tôi cam kết sẽ mang đến những dịch vụ tốt nhất để đáp ứng nhu cầu và mong đợi của Quý đơn vị.'
				)
			]
		]; 
	}
} // End class