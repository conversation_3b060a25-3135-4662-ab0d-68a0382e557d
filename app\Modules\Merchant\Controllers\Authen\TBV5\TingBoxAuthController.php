<?php

namespace App\Modules\Merchant\Controllers\Authen\TBV5;

use App\Lib\Helper;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\Authen\TBV5\Mpos360LoginTingBoxV5Request;
use App\Modules\Merchant\Actions\Authen\TingBoxAuth\Mpos360LoginTingBoxV5Action;

class TingBoxAuthController extends Controller
{
	public function Mpos360LoginTingBoxV5(Mpos360LoginTingBoxV5Request $request)
	{
		try {
			$deviceSession = app(Mpos360LoginTingBoxV5Action::class)->run($request);
			$result = $deviceSession->toArray();
			return $this->successResponse($result, $request, 200);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
} // End class
