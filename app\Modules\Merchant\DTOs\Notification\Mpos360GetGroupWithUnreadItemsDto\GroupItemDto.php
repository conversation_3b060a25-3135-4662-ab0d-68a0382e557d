<?php

namespace App\Modules\Merchant\DTOs\Notification\Mpos360GetGroupWithUnreadItemsDto;

use App\Modules\Merchant\Enums\Mpos360Enum;

class GroupItemDto
{
	public string $code;
	public int $count;
	public string $name = '';

	public function __construct(string $code, int $count)
	{
		$this->code = $code;
		$this->count = $count;
	}

	public function setName(): self
	{
		$this->name = Mpos360Enum::ENUM_MNPNOTIFY_GROUP_CODE_NAME[$this->code];
		return $this;
	}
}
