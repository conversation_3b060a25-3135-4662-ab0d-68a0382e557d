<?php

namespace App\Http\Middleware;

use Closure;
use App\Traits\ApiResponser;
use App\Exceptions\BusinessException;
use App\Lib\DeviceSessionManualHelper;
use App\Modules\Merchant\Model\PartnerConfig;

class ValidateHashUploadMiddleware extends ValidateHashMiddleware
{
	use ApiResponser;

	public function getApiSecretFromPublic(string $apiKey, string $currentPath=''): string
	{
		$apiSecret = env('MPOS360_MOBILE_SECRET_KEY');
		return $apiSecret;
	}

	public function handle($request, Closure $next, $fieldHash)
	{
		if ( 
			env('APP_ENV') === 'local' && env('APP_DEBUG') == true 
																 && empty(env('APP_DEBUG_CHECK_SUM')) 
		) {
			return $next($request);
		}
		
		throw_if(
			empty($request->input('lang')),
			new BusinessException('Ngôn ngữ là bắt buộc', 422)
		);

		throw_if(
			!in_array($request->input('lang'), ['vi', 'en']),
			new BusinessException('<PERSON>ệ thống chỉ hỗ trợ ngôn ngữ: vi và en', 422)
		);

		throw_if(
			empty($request->input('time_request')),
			new BusinessException('TimeRequest là bắt buộc', 422)
		);

		$apiKey = $request->input('api_key', '');

		if (empty($apiKey)) {
			throw new BusinessException('API-KEY là bắt buộc', 422);
		}

		if (strlen($apiKey) > env('MPOS360_DO_DAI_KEY_PHIEN_CU', 36)) {
			// ăn vào logic mới
			$dv = DeviceSessionManualHelper::makeInstance($apiKey);
			return $next($request);
		}

		$apiSecret = $this->getApiSecretFromPublic($apiKey);

		$fieldExplode = explode('|', $fieldHash);

		$combineString = '';

		foreach ($fieldExplode as $key) {
			$combineString .= sprintf('%s:%s+', $key, $request->input('data.' . $key));
		}

		$combineChecksum = [
			$request->method(),
			$request->path(),
			$request->input('time_request'),
			$request->input('lang'),
			$combineString,
			$apiKey
		];

		$stringBeforeHash = implode('|', $combineChecksum);

		$checksum = hash_hmac('sha512', $stringBeforeHash, $apiSecret);

		$clientCheckSum = $request->input('checksum');

		if (empty($clientCheckSum) || empty($checksum)) {
			throw new BusinessException('Checksum là bắt buộc', 422);
		}

		if ($clientCheckSum == $checksum) {
			return $next($request);
		}

		$message = sprintf('Checksum không hợp lệ');
		throw new BusinessException($message, 412);
	}
} // End class
