<?php

namespace App\Modules\Merchant\Actions\TingBox\Mpos360GetCauHinhByMerchantAction\SubAction;

use App\Modules\Merchant\Model\DeviceSession;

class EmptyCauHinhMcSubAction
{
	/**
	 * $tingting = [
			'merchantId' => $merchantId,
			'listMobileUser' => [],
			'clientId' => $clientId,
			'topic' => sprintf('/APP360/%s/data', $clientId),
			'mqAuthen' => [
				'userName' => env('SOUNDBOX_MQ_USERNAME'),
				'password' => env('SOUNDBOX_MQ_PASSWORD'),
				'mqtt' => env('SOUNDBOX_MQ_BROKERS')
			]
		];
	 */
	public function run($tingting, DeviceSession $deviceSession)
	{
		$returnData = [
			'tingbox' => [
				'currentMode' => 'VANP',
				'qrDefault' => [
					'storeName' => $deviceSession->getMerchantName(),
					'mobileUserName' => 'MOBILE_USER_DEFAULT',
					'qrDefault' => 'Chua kich hoat',
					'qrTypeDefault' => 'VIETQR',
					'qrIdDefault' => 'VA_STATIC00000000',
					'qrBankDefault' => '',
					'accountNumberDefault' => '',
					'accountNameDefault' => $deviceSession->getMerchantName(),
					'status' => 'ACTIVE',
					'address' => '',
					'muId' => 'MOBILE_USER_DEFAULT',
					'bank' => [
						'holderName' => $deviceSession->getMerchantName(),
						'accountNo' => 'TKNH đã đăng ký',
						'bankCode' => 'VIMO',
						'integratedMethod' => 'VANP',
					],
					'step_action' => [
						['step' => '1', 'status' => '2', 'step_name' => 'Khai báo tài khoản ngân hàng'],
						['step' => '2', 'status' => '2', 'step_name' => 'Khai báo cửa hàng kinh doanh'],
						['step' => '3', 'status' => '2', 'step_name' => 'Gán loa Tingbox'],
					]
				]
			],
			'tingting' => [
				'merchantId' => $tingting['merchantId'],
				'listMobileUser' => [
					[
						'storeName' => $deviceSession->getMerchantName(),
						'mobileUserName' => 'MOBILE_USER_DEFAULT',
						'qrDefault' => 'Chua kich hoat',
						'qrTypeDefault' => 'VIETQR',
						'qrIdDefault' => 'VA_STATIC00000000',
						'qrBankDefault' => '',
						'accountNumberDefault' => '',
						'accountNameDefault' => $deviceSession->getMerchantName(),
						'status' => 'ACTIVE',
						'address' => '',
						'muId' => 'MOBILE_USER_DEFAULT',
						'bank' => [
							'holderName' => $deviceSession->getMerchantName(),
							'accountNo' => 'TKNH đã đăng ký',
							'bankCode' => 'VIMO',
							'integratedMethod' => 'VANP',
						],
						'step_action' => [
							['step' => '1', 'status' => '2', 'step_name' => 'Khai báo tài khoản ngân hàng'],
							['step' => '2', 'status' => '2', 'step_name' => 'Khai báo cửa hàng kinh doanh'],
							['step' => '3', 'status' => '2', 'step_name' => 'Gán loa Tingbox'],
						]
					]
				],
				'clientId' => $tingting['clientId'],
				'topic' => $tingting['topic'],
				'mqAuthen' => $tingting['mqAuthen'],
				'totalAmountCurrent' => $tingting['totalAmountCurrent'] ?? '--',
				'transactionCount' => $tingting['transactionCount'] ?? '--',
			]
		];

		return $returnData;
	}
}
