<?php

namespace App\Traits;

use App\User;
use App\Lib\Logs;
use App\Utils\Result;
use Illuminate\Support\Str;

/**
 * <AUTHOR> <<EMAIL>>
 */
trait ApiResponser
{
	public $cashObjectProperties = [];

	/**
	 * @param array $data
	 * @param $request
	 * @param integer $code
	 * @param string $message
	 *
	 * @return string
	 */
	public function successResponse($data = [], $request, $code = Result::REQUEST_SUCCESS, $message = null)
	{
		$checkArray = false;
		
		if (is_array($data) || is_object($data)) {
			if (is_object($data)) {
				$data = (array)$data;
			}
			$checkArray = $this->replace_null_with_empty_string($data);
		}

		$apiRequestUUID = (string) Str::random(8);
		$response = [
			'api_request_id' => request()->json('api_request_id', $apiRequestUUID),
			'success' => true,
			'checksum' => '',
			'result_code' => $code,
			'message' => $message ?: Result::$resultMessage[$code],
			'data' => ($checkArray) ?: (object) [],
		];

		return response()->json($response);
	}

	/**
	 * @param integer $code
	 * @param string $message
	 * @param array $errors
	 *
	 * @return string
	 */
	public function errorResponse($code = Result::ERROR, $message = null, $errors = [])
	{
		$resultMessages = Result::$resultMessage;

		if (!is_null($errors)) {
			// (new Logs())->writeFileLog($errors);
		} else {
			if (!array_key_exists($code, $resultMessages)) {
				// (new Logs())->writeFileLog("Error: " . $message);
			} else {
				// (new Logs())->writeFileLog("Error: " . $resultMessages[$code]);
			}
		}

		// if (!array_key_exists($code, $resultMessages)) {
		//     $code = Result::REQUEST_FALSE;
		// }

		$apiRequestUUID = (string) Str::random(8);
		$response = [
			'api_request_id' => request()->json('api_request_id', $apiRequestUUID),
			'success' => false,
			'checksum' => '',
			'result_code' => $code,
			'message' => !empty($message) ? $message : 'Đã có lỗi xảy ra, bạn vui lòng đợi kỹ thuật hỗ trợ xử lý.',
		];

		if (empty($errors)) {
			$errors['errors'] = [$message ?? [$message] ?? 'Error'];
		}

		$response['data'] = $errors;
		return response()->json($response);
	}

	public  function replace_null_with_empty_string($array)
	{
		
		foreach ($array as $key => $value) {
			if (is_object($value)) {
				$value = $this->castObject($value);
			} elseif (is_array($value)) {
				$array[$key] = $this->replace_null_with_empty_string($value);
			} else {
				$array[$key]  = (string) $value;
			}
		}

		return $array;
	}

	public function castObject($object)
	{
		foreach ($object as $k => $val) {
			if (is_object($val)) {
				$object->$k = $this->castObject($val);
			}

			if (is_array($val)) {
				$object->$k = $this->replace_null_with_empty_string($val);
			}

			if (!is_object($val) && !is_array($val)) {
				$object->$k = (string) $val;
			}
		}

		return $object;
	}
} // End class
