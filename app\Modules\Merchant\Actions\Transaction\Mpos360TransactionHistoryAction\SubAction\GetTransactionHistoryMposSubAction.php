<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransactionHistoryAction\SubAction;

use App\Exceptions\BusinessException;
use App\Lib\partner\MPOS;
use App\Modules\Merchant\DTOs\Transaction\Mpos360TransactionHistoryDto\Mpos360TransactionHistoryDto;
use App\Modules\Merchant\Model\DeviceSession;
use Carbon\Carbon;

/**
 * array:4 [
  "status" => true
  "data" => array:9 [
    "totalAmountTransaction" => 222430816
    "totalAmountSettlement" => 70338000
    "totalAmountNotSettlement" => 152092816
    "countTransaction" => 31
    "countSettlement" => 18
    "countNotSettlement" => 13
    "countQuickWithdraw" => 0
    "countWithdraw" => 0
    "error" => array:3 [
      "code" => 1000
      "message" => "DO_SERVICE_SUCCESS"
      "messageEn" => ""
    ]
  ]
  "status_code_partner" => 1000
  "message" => "DO_SERVICE_SUCCESS"
]
 */

class GetTransactionHistoryMposSubAction
{
	public function run(DeviceSession $deviceSession, Carbon $date): Mpos360TransactionHistoryDto
	{
		$params = [
			'merchantFk' => $deviceSession->getMerchantId(),
			'startDate' => $date->format('d-m-Y'),
			'tokenLogin' => $deviceSession->getMposToken(),
		];

		$transactionHistory = (new MPOS())->countTransSumByTime($params);
		mylog([
			'params' => $params,
			'transactionHistory' => $transactionHistory,
		]);

		if (empty($transactionHistory['data'])) {
			throw new BusinessException('Lỗi không lấy được thông tin lịch sử gd');
		}

		return new Mpos360TransactionHistoryDto(
			$transactionHistory['data']['totalAmountTransaction'],
			$transactionHistory['data']['totalAmountSettlement'],
			$transactionHistory['data']['totalAmountNotSettlement'],
			$transactionHistory['data']['countTransaction'],
			$transactionHistory['data']['countSettlement'],
			$transactionHistory['data']['countNotSettlement'],
			$transactionHistory['data']['countQuickWithdraw'],
			$transactionHistory['data']['countWithdraw'],
			$transactionHistory['data']['countTransactionInstallment'],
		);
	}
}
