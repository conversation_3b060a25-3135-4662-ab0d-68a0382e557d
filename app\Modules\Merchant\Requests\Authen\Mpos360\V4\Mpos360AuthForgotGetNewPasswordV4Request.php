<?php

namespace App\Modules\Merchant\Requests\Authen\Mpos360\V4;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360AuthForgotGetNewPasswordV4Request extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.username' => ['required', 'string'],
			'data.otp_id' => ['required', 'string'],
			'data.password' => ['required', 'string', 'min:6', 'max:36', 'confirmed', 'not_in:123456'],
		];
	}

	public function messages() {
		return [
			'data.otp_id.required' => 'OtpId là bắt buộc',
			'data.otp_id.string' => 'OtpId phải là kiểu chuỗi ký tự',
			'data.password.required' => 'Mật khẩu là bắt buộc',
			'data.password.string' => 'Mật khẩu là dạng chuỗi ký tự',
			'data.password.min' => '<PERSON><PERSON>t khẩu phải có độ dài tối thiểu là 6 ký tự',
			'data.password.max' => 'Mật khẩu phải có độ dài tối đa là 36 ký tự',
			'data.password.confirmed' => 'Xác nhận mật khẩu phải trùng khớp với mật khẩu mới',
			'data.password.not_in' => 'Mật khẩu mới phải khác 123456',
		];
	}
} // End class
