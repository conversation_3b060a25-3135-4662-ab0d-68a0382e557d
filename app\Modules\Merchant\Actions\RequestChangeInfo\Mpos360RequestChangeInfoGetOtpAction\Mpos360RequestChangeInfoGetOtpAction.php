<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoGetOtpAction;

use Exception;
use App\Lib\OtpHelper;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\SendSmsOtpMNPSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\SendMailOtpMNPSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\GetMNPAccessTokenSubAction;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoGetOtpRequest;
use App\Modules\Merchant\Actions\ChungThuc\Mpos360ChungThucGetOtpAction\SubAction\SendEmailWithTemplateSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoGetOtpAction\SubAction\CreateOtpSubAction;

class Mpos360RequestChangeInfoGetOtpAction
{
	public function run(Mpos360RequestChangeInfoGetOtpRequest $request)
	{
		$id = $request->json('data.request_id');
		$merchantRequest = Mpos360MerchantRequest::query()->find($id);

		if (!$merchantRequest) {
			throw new BusinessException(__('dttv3.Lỗi: không tìm thấy bản ghi yc thay đổi thông tin'));
		}

		$deviceSession = $request->getCurrentDeviceSession();
		$deviceSessionWithToken = app(GetMNPAccessTokenSubAction::class)->run($deviceSession);
		
		$mpos360CodeOtp = app(CreateOtpSubAction::class)->run(
			$merchantRequest,
			$deviceSession,
			$request
		);
		
		if ($request->isGetEmailOtp()) {
			$sendOtpResult = app(SendEmailWithTemplateSubAction::class)->run(
				$mpos360CodeOtp,
				$deviceSessionWithToken,
				sprintf('[%s] - Mã OTP thay đổi email người đại diện từ ứng dụng %s', $mpos360CodeOtp->otp, __('setting.appName')),
				sprintf('Thay đổi email người đại diện của bạn'),
				'ThayDoiEmailNguoiDaiDien'
			);

			return [
				'otp_id' => $mpos360CodeOtp->id,
				'msg' => __('dttv3.Mã xác thực OTP đã được gửi đến email. Vui lòng kiểm tra và xác thực', ['email' => $mpos360CodeOtp->obj_value]),
				'countdown_time_get_new_otp' => OtpHelper::getSoGiayCountdown()
			];
		}

		if ($request->isGetMobileOtp()) {
			$sendOtpResult = app(SendSmsOtpMNPSubAction::class)->run($mpos360CodeOtp, $deviceSessionWithToken);

			return [
				'otp_id' => $mpos360CodeOtp->id,
				'msg' => __('dttv3.Mã xác thực OTP đã được gửi đến SĐT. Vui lòng kiểm tra và nhập vào ô dưới đây', ['mobile' => $mpos360CodeOtp->obj_value]),
				'countdown_time_get_new_otp' => OtpHelper::getSoGiayCountdown()
			];
		}
	} // End method
} // End class
