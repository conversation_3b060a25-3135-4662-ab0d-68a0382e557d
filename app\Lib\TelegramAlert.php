<?php

namespace App\Lib;

use Exception;
use Throwable;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Http;

define('ACCOUTING_TOPIC1', env('ACCOUTING_TOPIC_TELE', 4));              // topic hạch toán

class TelegramAlert
{
	const IS_SEND_MESSAGE = true;


	protected static $_url = 'https://api.telegram.org/bot';
	protected static $_token = '**********************************************';

	public function checkConstants()
	{
		return [
			'ACCOUTING_TOPIC' => ACCOUTING_TOPIC,
			'IS_SEND_MESSAGE' => self::IS_SEND_MESSAGE
		];
	}

	public static function sendMessage($text, $type = 'Request', $topicId = 2)
	{
		$group = '-1002210309234';

		if (!self::IS_SEND_MESSAGE) {
			return true;
		}

		if (!is_string($text)) {
			$detail = "\n Message: " . $text->getMessage();
			$detail .= "\n Line:" . $text->getLine();
			$detail .= "\n File: " . $text->getFile();
		} else {
			$detail = "\n {$type}: <pre>" . $text . "</pre>";
		}

		$text .= "\n" . sprintf('URL: /%s', request()->path());
		$text .= "\n" . sprintf('ENV: %s', config('app.env'));

		try {
			return self::pushNoty(['chat_id' => $group, 'text' => $text, 'message_thread_id' => $topicId]);
		} catch (Exception $e) {
			return false;
		}
	}

	// public static function sendAccouting($text, $type = 'Request')
	// {
	// 	return self::sendMessage($text, $type, ACCOUTING_TOPIC);
	// }

	public static function pushNoty(array $params = [], string $botToken = '')
	{
		// try {
		// 	if (!empty($botToken)) {
		// 		$uri = self::$_url . $botToken . '/sendMessage?parse_mode=html';
		// 	} else {
		// 		$uri = self::$_url . self::$_token . '/sendMessage?parse_mode=html';
		// 	}

		// 	$option['verify'] = false;
		// 	$option['form_params'] = $params;
		// 	$option['http_errors'] = false;
		// 	$client = new Client();
		// 	$response = $client->request("POST", $uri, $option);

		// 	return json_decode($response->getBody(), true);
		// } catch (\Throwable $e) {
		// 	mylog(['TeleError' => $e->getTrace()]);
		// }
		$url = 'https://discord.com/api/webhooks/1375643950018330734/PVP9X0P197TaP79SRVkowoECFA8Tm6_k1VX3btlhLJVZEORRHK1Kt5-vQOYgzgE_Vgh8';
		
		try {
			return Http::timeout(5)
			->retry(1, 500)
			->withHeaders([
    		'Content-Type' => 'application/json',
			])->post($url, [
				'content' => sprintf('[%s] --- %s', now()->toDateTimeString(), $params['text'] ?? 'unknow messsage')
			]);
		} catch (\Throwable $th) {
			//throw $th;
		}
	}
} // End class