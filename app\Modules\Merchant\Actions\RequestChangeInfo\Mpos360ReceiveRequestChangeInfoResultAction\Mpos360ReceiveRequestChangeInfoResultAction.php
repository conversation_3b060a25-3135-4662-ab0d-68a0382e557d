<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360ReceiveRequestChangeInfoResultAction;

use App\Exceptions\BusinessException;
use Illuminate\Support\Facades\Cache;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Model\Mpos360MerchantRequestSupplement;
use App\Modules\Merchant\Requests\RequestChangeInfo\ReceiveRequestChangeInfoResultRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360ReceiveRequestChangeInfoResultAction\SubAction\UpdateChungThucNeuYeuCauCoKycSubAction;

class Mpos360ReceiveRequestChangeInfoResultAction
{
	public function run(ReceiveRequestChangeInfoResultRequest $request)
	{
		$mnpId = trim($request->json('data.mynextpay_id'));
		$mpos360McRequest = Mpos360MerchantRequest::query()->firstWhere(['mynextpay_id' => $mnpId]);

		if (!$mpos360McRequest) {
			throw new BusinessException('Lỗi không tìm thấy yêu cầu');
		}

		if ($request->isBoSungThongTin()) {
			$mpos360McRequestSupplement = Mpos360MerchantRequestSupplement::query()->firstOrCreate([
				'status' => 1,
				'merchant_request_id' => $mpos360McRequest->id
			], [
				'supplement_reason' => strip_tags($request->json('data.supplement_docs_reason', '')),
				'merchant_id' => $mpos360McRequest->merchant_id,
				'data_request' => '{}',
				'time_created' => now()->timestamp
			]);

			if ($mpos360McRequestSupplement) {
				return [
					'success' => '1',
					'data' => $mpos360McRequest->mynextpay_id,
					'msg' => 'Ghi nhận bổ sung thông tin thành công'
				];
			}

			throw new BusinessException('Lỗi không ghi nhận được bổ sung thông tin');
		}

		if ($mpos360McRequest->isTrangThaiCuoiV3()) {
			$msg = sprintf('YC `%s`đã ở trạng thái cuối, không xử lý', $mpos360McRequest->mynextpay_id);
			throw new BusinessException($msg);
		}

		$status = $request->json('data.status');

		$mpos360McRequest->status = Mpos360Enum::MPOS360_MC_REQUEST_STT_DA_TU_CHOI;
		
		if ($status == 'APPROVED') {
			$mpos360McRequest->status = Mpos360Enum::MPOS360_MC_REQUEST_STT_DA_XU_LY;
			$mpos360McRequest->time_approved = now()->timestamp;
		}else {
			$mpos360McRequest->time_denied = now()->timestamp;
		}

		// Dù là từ chối hay duyệt cuối cùng thì các yêu cầu bổ sung dang dở nên đưa về trạng thái cuối cùng
		$suppUpdate = Mpos360MerchantRequestSupplement::query()
			->where('merchant_request_id', $mpos360McRequest->id)
			->where('merchant_id', $mpos360McRequest->merchant_id)
			->whereIn('status', [
				Mpos360Enum::MPOS360_BO_SUNG_HO_SO_MOI_TAO, 
				Mpos360Enum::MPOS360_BO_SUNG_HO_SO_DA_BO_SUNG_THONG_TIN,
				Mpos360Enum::MPOS360_BO_SUNG_HO_SO_DANG_GUI_MNP
			])
			->update([
			'status' => Mpos360Enum::MPOS360_BO_SUNG_HO_SO_KHONG_CAN_XU_LY,
			'time_updated' => now()->timestamp
			]);			

		$mpos360McRequest->data_feedback = $request->json('data.data_feedback');
		$mpos360McRequest->time_updated = now()->timestamp;
		$r = $mpos360McRequest->save();

		if (!$r) {
			throw new BusinessException('Lỗi không cập nhật được bản ghi');
		}

		if ($mpos360McRequest->status = Mpos360Enum::MPOS360_MC_REQUEST_STT_DA_XU_LY) {
			app(UpdateChungThucNeuYeuCauCoKycSubAction::class)->run($mpos360McRequest);
			$merchantId = $mpos360McRequest->merchant_id;
			Cache::forget('mnp_profile_list_only__' . $merchantId);
			Cache::forget('mnp_profile_list_only_vi_' . $merchantId);
			Cache::forget('mnp_profile_list_only_en_' . $merchantId);
		}

		return [
			'success' => '1',
			'data' => $mpos360McRequest->mynextpay_id,
			'msg' => 'Xử lý bản ghi thành công'
		];
	}
} // End clas
