<?php

namespace App\Modules\Merchant\Requests\Authen\Mpos360;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360ChangePasswordRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.email' => ['required', 'string', 'email', 'max:255'],
      'data.current_password' => ['required', 'string'],
      'data.password' => ['required', 'string', 'min:6', 'max:36', 'confirmed', 'not_in:123456', 'different:data.current_password'],
		];
	}

	public function messages() {
		return [
			'data.email.required' => vmsg('DoiMatKhauEmailLaBatBuoc'),
			'data.email.string' => vmsg('DoiMatKhauEmailPhaiLaKieuChuoi'),
			'data.email.email' => vmsg('DoiMatKhauEmailPhaiDungDinhDang'),
			'data.email.max' => vmsg('DoiMatKhauEmailPhaiCoDoDaiToiDaLa255KyTu'),
			
			'data.current_password.required' => vmsg('DoiMatKhauMatKhauHienTaiLaBatBuoc'),

			'data.password.required' => vmsg('DoiMatKhauMatKhauMoiLaBatBuoc'),
			'data.password.min' => vmsg('DoiMatKhauMatKhauMoiCoDoDaiToiThieuLa6KyTu'),
			'data.password.max' => vmsg('DoiMatKhauMatKhauMoiCoDoDaiToiDaLa36KyTu'),
			'data.password.confirmed' => vmsg('DoiMatKhauXacNhanMatKhauMoiKhongTrungKhop'),
			'data.password.not_in' => vmsg('DoiMatKhauMatKhauMoiPhaiKhac123456'),
			'data.password.different' => vmsg('DoiMatKhauMatKhauMoiPhaiKhacMatKhauCu'),
		];
	}
} // End class
