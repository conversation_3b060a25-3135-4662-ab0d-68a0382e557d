<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForBankingSA;

use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\DTOs\RequestChangeInfo\CreateMerchantRequestDto;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoUpdateProfileRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\SubAction\CreateMerchantRequestSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\SubAction\MappingVerifyBankingToProfileSubAction;

class TaoYcDoiSTKCuaCaNhanDuocHKDUyQuyenSA
{
	public function run(
		CreateMerchantRequestDto $dto,
		Mpos360RequestChangeInfoUpdateProfileRequest $request
	) {
		// Chuyển đổi dữ liệu "request_vefify" về "profiles"
		$saveData = app(MappingVerifyBankingToProfileSubAction::class)->run(
			$request->json('data.request_vefify', []),
			$dto
		);

		// Tạo 1 bản ghi yc có trạng thái
		$saveData['status_verify'] = Mpos360Enum::MPOS360_MC_VERIFY_STT_CHUA_XAC_THUC;
		$mpos360McRequest = app(CreateMerchantRequestSubAction::class)->run($saveData);
		$savedResult = $mpos360McRequest->save();

		if (!$savedResult) {
			throw new BusinessException(vmsg('Lỗi: không tạo được yc đổi thông tin TKNH'));
		}

		$returnData = [
			'id' => $mpos360McRequest->id,
			'request_change_info_id' => $mpos360McRequest->mynextpay_id,
			'status' => 'SUCCESS',
			'msg' => vmsg('Tạo yêu cầu cầu đổi thông tin thành công'),
			'can' => Mpos360Enum::MPOS360_CAN_EKYC_CA_NHAN_DUOC_UY_QUYEN,
			'verification_profiles' => [],
			'additional_profiles' => $this->__buildDuLieuChungTuDiKem(),
			'list_sign_method' => [],
			'scan_method' => []
		];

		return $returnData;
	}

	private function __buildDuLieuChungTuDiKem()
	{
		$additionalProfiles = collect([])
			->push([
				'profileKey' => 'positionAuthBank',
				'value' => '',
				'type' => 'INPUT',
				'label' => vmsg('Vai trò/Vị trí của người ủy quyền tại ngân hàng'),
				'other_data' => (object) []
			])->push([
				'profileKey' => 'bankMutualRelation',
				'value' => '',
				'type' => 'DROPDOWN',
				'label' => vmsg('Mối quan hệ giữa hai bên'),
				'other_data' => [
					'list' => [
						['key' => 'bo_me_con', 'label' => vmsg('Bố/Mẹ - Con'), 'value' => 'Bố/Mẹ - Con', 'slot' => []],
						['key' => 'anh_chi_em', 'label' => vmsg('Anh - Chị - Em'), 'value' => 'Anh - Chị - Em', 'slot' => []],
						['key' => 'vo_chong', 'label' => vmsg('Vợ - Chồng'), 'value' => 'Vợ - Chồng', 'slot' => []],
						['key' => 'khac', 'label' => vmsg('Khác'), 'value' => 'Khác', 'slot' => []],
					]
				]
			])
			->push([
				'profileKey' => 'identificationDocument',
				'value' => '',
				'label' => vmsg('Chứng từ đính kèm'),
				'other_data' => [
					'list' => [
						[
							'key' => 'anh_giay_uy_quyen',
							'value' => 'anh_giay_uy_quyen',
							'label' => vmsg('Ảnh giấy ủy quyền được công chứng tại cơ quan có thẩm quyền'),
							'slot' => [
								[
									'substituteCertUrls' => 'substituteCertUrls',
									'label' => vmsg('Ảnh 1')
								],
							],
						],

						[
							'key' => 'anh_giay_khai_sinh',
							'value' => 'anh_giay_khai_sinh',
							'label' => vmsg('Ảnh giấy khai sinh'),
							'slot' => [
								[
									'profileKey' => 'substituteCertUrls',
									'label' => vmsg('Ảnh 1')
								],
							]
						],

						[
							'key' => 'anh_giay_dang_ky_ket_hon',
							'value' => 'anh_giay_dang_ky_ket_hon',
							'label' => vmsg('Ảnh giấy đăng ký kết hôn'),
							'slot' => [
								[
									'profileKey' => 'substituteCertUrls',
									'label' => vmsg('Ảnh 1')
								],
							]
						],

						[
							'key' => 'anh_so_ho_khau',
							'value' => 'anh_so_ho_khau',
							'label' => vmsg('Ảnh tất cả các trang Sổ hộ khẩu giấy'),
							'slot' => [
								[
									'profileKey' => 'substituteCertUrls',
									'label' => vmsg('Ảnh 1')
								],
							]
						],

						[
							'key' => 'anh_man_hinhg_app_vneid',
							'value' => 'anh_man_hinhg_app_vneid',
							'label' => vmsg('Video quay màn hình ứng dụng VNeID từ khi đăng nhập đến các trang thể hiện mối quan hệ giữa người ủy quyền và người được ủy quyền'),
							'slot' => [
								[
									'profileKey' => 'substituteCertUrls',
									'label' => vmsg('Ảnh 1')
								],
							]
						],
					]
				]
			])
			->values()
			->toArray();

		return $additionalProfiles;
	}
} // End class