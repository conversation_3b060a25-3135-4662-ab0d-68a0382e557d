<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthVerifyOtpLoginAction;

use Exception;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Setting;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360CodeOtp;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360AuthVerifyOtpLoginRequest;

class Mpos360AuthVerifyOtpLoginAction
{
	public function run(Mpos360AuthVerifyOtpLoginRequest $request): array
	{
		$deviceSession = $request->getCurrentDeviceSession();

		$otp = trim($request->json('data.otp'));
		$otpId = $request->json('data.otp_id');

		$settingFakeOtp = Setting::query()->firstWhere(['key' => 'FAKE_OTP_PASSED', 'value' => '1']);

		$mpos360CodeOtp = Mpos360CodeOtp::query()->where([
			'id' => $otpId,
			'user_id' => $deviceSession->user_id,
			'command_code' => 'MERCHANT_LOGIN'
		])->first();

		$merchantEmail = $deviceSession->getMerchantEmail();

		if (optional($settingFakeOtp)->id || $merchantEmail == '<EMAIL>') {
			goto HAS_PASS_OTP;
		}

		if (!$mpos360CodeOtp) {
			throw new BusinessException(vmsg('Mpos360ChungThucVerifyOtpAction_LoiKhongTimThayBanGhiOtp'));
		}

		if ($mpos360CodeOtp->isFinalStatus()) {
			throw new BusinessException(vmsg('Mpos360ChungThucVerifyOtpAction_LoiOtpDaDuocSuDung'));
		}

		if ($mpos360CodeOtp->isExpiredOtp()) {
			throw new BusinessException(vmsg('Mpos360ChungThucVerifyOtpAction_LoiOtpDaHetHan'));
		}

		if ($mpos360CodeOtp->otp != $otp) {
			throw new BusinessException(vmsg('Mpos360ChungThucVerifyOtpAction_LoiOtpKhongChinhXac'));
		}

		$mpos360CodeOtp->status = Mpos360Enum::MPOS360_OTP_DA_SU_DUNG;
		$mpos360CodeOtp->save();

		HAS_PASS_OTP:
		// Pass otp rồi thì điều hướng can

		return [
			'reference_id' => $mpos360CodeOtp->reference_id,
			'msg' => 'Verify Otp success',
			'can' => Mpos360Enum::CAN_GO_TO_HOME_SCREEN_AND_CHANGE_PASSWORD,
		];
	}
} // End class
