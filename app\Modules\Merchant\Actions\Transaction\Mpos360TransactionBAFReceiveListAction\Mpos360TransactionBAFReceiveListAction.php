<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransactionBAFReceiveListAction;

use Carbon\Carbon;
use App\Lib\Helper;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionBAFReceiveListRequest;
use App\Modules\Merchant\Actions\Transaction\subAction\Mpos360TransactionDefineConfigSubAction;

class Mpos360TransactionBAFReceiveListAction
{
	public function run(Mpos360TransactionBAFReceiveListRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		$params = [
			'typeTransaction' => 'STATISTIC_WITHDRAW',
			'merchantFk' => $deviceSession->getMerchantId(),
			'startDate' => $request->getStartDate(),
			'endDate' => $request->getEndDate(),
			'tokenLogin' => $deviceSession->getMposToken(),
			'pageIndex' =>  $request->json('data.start', '1'),
			'pageSize' =>  $request->json('data.limit', '100'),
			'withdrawStatus'=>  $request->getStatus(),
		];
		$listTransaction = app(Mpos360TransactionDefineConfigSubAction::class)->requestTransList($params);
		$data = [];
		if ($listTransaction) {
			if (isset($listTransaction['data']['withdrawStatisticList'])) {
				$data = $this->__convertDataReturn($listTransaction['data']['withdrawStatisticList']);
				$params['rangeTime'] = collect($listTransaction['data']['withdrawStatisticList'])->map(function ($item) {
					return Carbon::createFromFormat('d-m-Y H:i:s', $item['createdDate'])->format('d-m-Y');
				})->unique()->implode(',');
			}
		}

		$params['email'] = $deviceSession->getMerchantEmail();
		return [
			'rows' => 0,
			'data' => $data,
			'other_data' => [
				'status' => $this->__getStatusDefault(),
				'filter' => $this->__getFilterDefault(),
				'countSumFilter' => Arr::only($params, [
					'typeTransaction',
					'merchantFk',
					'withdrawStatus',
					'rangeTime',
					'email'
				])
			]
		];
	}

	private function __getStatusDefault()
	{
		return (new Mpos360TransactionDefineConfigSubAction())->getWithdrawStatusTrans();
	}
	private function __getFilterDefault()
	{
		
		$data = $this->__getStatusDefault();
		$statusArr[] = ['value' => 'ALL', 'label' => trans_choice_fallback('trans.all')];
		foreach ($data as $key => $value) {
			$statusArr[] = [
				'value' => $value['value'],
				'label' => $value['label']
			];
		}
		return  [
			[
				'key' => 'transaction_time',
				'name' => trans_choice_fallback('trans.title.createdDate'),
				'list' => [
					['value' => 'TODAY', 'label' => trans_choice_fallback('trans.title.TODAY')],
					// ['value' => 'YESTERDAY', 'label' => 'Hôm qua'],
					['value' => 'THIS_MONTH', 'label' => trans_choice_fallback('trans.title.THIS_MONTH')],
					['value' => 'LAST_MONTH', 'label' => trans_choice_fallback('trans.title.LAST_MONTH')],
				]
			],
			[
				'key' => 'transaction_status',
				'name' => trans_choice_fallback('trans.title.statusInfo'),
				'list' => $statusArr
			]
		];
	}
	protected function __getStatusInstallMent()
	{
		return (new Mpos360TransactionDefineConfigSubAction())->getStatusInstallment();
	}
	private function __convertDataReturn($data)
	{
		if ($data) {
			$dataReturn = [];
			$mapData = $this->__getTransMethodName();
			foreach ($mapData as $key1 => $value1) {
				$transMethodNameArr[$value1['value']] = $value1['label'];
			}
			foreach ($data as $key => $value) {
				$timestamp = strtotime($value['createdDate']);
				$timeD = date('d-m-Y', $timestamp);
				$fee = $value['amountTransaction'] - $value['receivedAmount'];
				$dataArr[$timeD][] = [


					'order_code' => $value['withdrawNumber'],
					'transaction_id' => $value['withdrawId'],
					'time_created' => date('H:i', $timestamp),
					'amount' => Helper::numberFormat($value['amountTransaction']) . ' VND',
					'fee' => Helper::numberFormat($fee) . ' VND',
					'receivedAmount'=> Helper::numberFormat($value['receivedAmount']) . ' VND',
					'status' => $value['withdrawStatus'],
					'note' => '',
					'other_data' => (object) [],
				];
			}
			foreach ($dataArr as $key => $value) {
				if ($key == now()->format('d-m-Y')) {
					$key = __('trans.title.Hôm nay');
				}

				$dataReturn[] = [
					'date' => $key,
					'total_transaction' => 0,
					'list_transaction' => $value,
				];
			}

			return $dataReturn;
		}
		return [];
	}


	private function __getCardType($value)
	{
		if (isset($value['issuerCode'])) {
			return (new Mpos360TransactionDefineConfigSubAction())->getTypeCard($value['issuerCode']);
		}
		return '';
	}
	
	private function __getTransMethodName()
	{
		return (new Mpos360TransactionDefineConfigSubAction())->getMethodInstallMent();
	}
}
