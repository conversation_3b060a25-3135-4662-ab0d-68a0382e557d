<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForBankingSA;

use App\Exceptions\BusinessException;
use Exception;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\DTOs\RequestChangeInfo\CreateMerchantRequestDto;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoUpdateProfileRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\SubAction\CreateMerchantRequestSubAction;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\SubAction\KiemTraPhuongThucKyV3SA;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\SubAction\MappingVerifyBankingToProfileSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\SubAction\GetPhuongThucQuetB3Ver2SubAction;

class TaoYcDoiSTKKhacCuaDoanhNghiepSA
{
	public function run(
		CreateMerchantRequestDto $dto,
		Mpos360RequestChangeInfoUpdateProfileRequest $request
	) {
		// Chuyển đổi dữ liệu "request_vefify" về "profiles"
		$saveData = app(MappingVerifyBankingToProfileSubAction::class)->run(
			$request->json('data.request_vefify', []),
			$dto
		);

		// Tạo 1 bản ghi yc có trạng thái
		$saveData['status_verify'] = Mpos360Enum::MPOS360_MC_VERIFY_STT_CHUA_XAC_THUC;
		$mpos360McRequest = app(CreateMerchantRequestSubAction::class)->run($saveData);
		$savedResult = $mpos360McRequest->save();

		if (!$savedResult) {
			throw new BusinessException(__('dttv3.Lỗi: không tạo được yc đổi thông tin TKNH'));
		}

		$kiemTraPhuongThucKy = app(KiemTraPhuongThucKyV3SA::class)->run($mpos360McRequest);

		$returnData = [
			'id' => $mpos360McRequest->id,
			'request_change_info_id' => $mpos360McRequest->mynextpay_id,
			'status' => 'SUCCESS',
			'msg' => __('Tạo yêu cầu đổi thông tin thành công'),
			'can' => $kiemTraPhuongThucKy['can'],
			'verification_profiles' => [],
			'additional_profiles' => [],
			'list_sign_method' => $kiemTraPhuongThucKy['sign_method'],
			'scan_method' => app(GetPhuongThucQuetB3Ver2SubAction::class)->run($mpos360McRequest->merchant_id)
		];

		return $returnData;
	}
} // End class