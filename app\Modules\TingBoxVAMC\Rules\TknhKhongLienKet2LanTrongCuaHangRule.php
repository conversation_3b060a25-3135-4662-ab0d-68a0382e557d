<?php

namespace App\Modules\TingBoxVAMC\Rules;

use App\Modules\TingBoxVAMC\Models\MerchantBank;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\DB;

class TknhKhongLienKet2LanTrongCuaHangRule implements Rule
{
	/**
	 * Validate đường link phía mobile gửi lên phải là link hình ảnh chứ ko phải link website
	 *
	 * @param  string  $attribute
	 * @param  mixed  $value: Link hình ảnh
	 * @return bool
	 */
	public function passes($attribute, $bankAccountNumber): bool
	{
		$merchantId = request()->json('data.merchantId');
		$bankCode = request()->json('data.bankCode');

		$where = [
			'merchant_id' => $merchantId,
			'bank_code' => $bankCode,
			'account_number' => $bankAccountNumber
		];

		$merchantBank = MerchantBank::query()->firstWhere($where);

		if (!$merchantBank) {
			return true;
		}

		$mcShopBank = MerchantShopBank::query()->where('merchant_bank_id', $merchantBank->id)
																					 ->where('merchant_id', $merchantId)
																					 ->where('account_type', MerchantShopBank::LOAI_TK_TRUC_TIEP)
																					 ->where('status_link', MerchantShopBank::STT_LINK_DA_LIEN_KET)
																					 ->where('shop_id', request()->json('data.mobileUserId'))
																					 ->first();

		if (!$mcShopBank) {
			return true;
		}

		return false;
	}

	/**
	 * Get the validation error message.
	 *
	 * @return string
	 */
	public function message()
	{
		return 'Bạn đã liên kết tài khoản ngân hàng trong cửa hàng hiện tại rồi. Hãy sử dụng một tài khoản khác.';
	}
}
