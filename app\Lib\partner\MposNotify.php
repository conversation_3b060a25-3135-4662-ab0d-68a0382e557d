<?php

namespace App\Lib\partner;

use App\Lib\Helper;
use App\Lib\TelegramAlertWarning;

class MposNotify extends MPOS
{
	private function callCurl($method, $endpoint = '', $data = [])
	{
		$key = sprintf('ApiMpos-%s-%s', $endpoint, mt_rand(1, 9999999999));
		$log[$key]['request']['data'] = $data;
		$log[$key]['method'] = $method;

		try {
			$curl = curl_init();
			$url = $this->baseUrl;
			if ($endpoint) {
				$url = $this->baseUrl . $endpoint;
			}

			$log[$key]['url'] = $url;

			curl_setopt_array($curl, array(
				CURLOPT_URL => $url,
				CURLOPT_RETURNTRANSFER => true,
				CURLOPT_ENCODING => '',
				CURLOPT_MAXREDIRS => 10,
				CURLOPT_TIMEOUT => 0,
				CURLOPT_FOLLOWLOCATION => true,
				CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
				CURLOPT_CUSTOMREQUEST => $method,
				CURLOPT_POSTFIELDS => json_encode($data),
				CURLOPT_HTTPHEADER => array(
					'Content-Type: application/json'
				),
			));

			$response = curl_exec($curl);
			$data = json_decode($response, true);
			curl_close($curl);
			$log[$key]['response'] = $data;
			
			return $data;
		} catch (\Throwable $e) {
			TelegramAlertWarning::sendMessage(Helper::traceError($e));
			return [
				'error' => [
					'code' => 'ERROR NOTFOUD',
					'message' => 'Lỗi không xác định',
				]
			];
		}
	}

	public function getListTopicNotify($merchantId)
	{
		$path = '/topic/follow?merchantId=' . $merchantId;
		$response = $this->callCurl('GET', $path, []);
		return $response;
	}
} // End class
