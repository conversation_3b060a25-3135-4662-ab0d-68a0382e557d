<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoCheckEmailAndMobileAction;

use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\SubAction\IsChangedEmailOrMobileSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\GetMNPAccessTokenSubAction;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoCheckEmailAndMobileRequest;

/**
 * kiểm tra email và sđt đã là giá trị mới hay chưa
 * Nếu cả 2 đều là giá trị cũ -> C<PERSON> thể gọi api đánh successs để goto bước 3
 * 
 * Chốt phase 1: không làm otp cho phần đổi người đại diện nên nếu có nhập khác thì vẫn cứ là goto bước 3
 */
class Mpos360RequestChangeInfoCheckEmailAndMobileAction
{
	public function run(Mpos360RequestChangeInfoCheckEmailAndMobileRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		$deviceSessionWithToken = app(GetMNPAccessTokenSubAction::class)->run($deviceSession);

		$isChanged = app(IsChangedEmailOrMobileSubAction::class)->run(
			$deviceSessionWithToken,
			$request->json('data.new_email'),
			$request->json('data.new_mobile'),
		);

		// Có thay đổi thông tin email+sđt mới => chuyển hướng sang màn hình otp
		if ($isChanged) {
		}

		return [
			'email' => $request->json('data.new_email'),
			'mobile' => $request->json('data.new_mobile'),
			'can' => 'MPOS360_CAN_MARK_ALL_OTP_PROFILE_SUCCESS'
		];
	}
} // End class
