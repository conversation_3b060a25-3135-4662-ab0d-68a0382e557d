<?php

return [
/*--------------------------- Validate Hash ---------------------*/
	/**
	 * @param ValidateHashMiddleware::class
	 */
	'ValidateHashMiddleware_PartnerKhongTonTai' => 'Partner không tồn tại',
	'ValidateHashMiddleware_PartnerDaBiKhoa' => 'Partner đã bị khóa',
	'ValidateHashMiddleware_BanKhongCoQuyenTruyCapApiChucNangNay' => 'Bạn không có quyền truy cập tính năng này',
	'ValidateHashMiddleware_UserApiNayKhongTheDecryptDuocThongTin' => 'Hệ thống không giải mã được thông tin',
	'ValidateHashMiddleware_PhienLamViecKhongTonTai' => 'Phiên làm việc không tồn tại',
	'ValidateHashMiddleware_PhienLamViecDaHetHan' => 'Phiên làm việc đã hết. Vui lòng thoát ứng dụng và đăng nhập lại.',
	'ValidateHashMiddleware_NgonNguLaBatBuoc' => 'Ngôn ngữ là bắt buộc',
	'ValidateHashMiddleware_HeThongChiHoTroNgonNguViVaEn' => 'Hệ thống chỉ hỗ trơ: Tiếng Việt và Tiếng Anh',
	'ValidateHashMiddleware_TimeRequestLaBatBuoc' => 'Thời gian là bắt buộc',
	'ValidateHashMiddleware_ApiKeyLaBatBuoc' => 'Error!',
	'ValidateHashMiddleware_LoiApiHashField' => 'Error Hash Field',
	'ValidateHashMiddleware_CheckSumLaBatBuoc' => 'Pair code is required',
	'ValidateHashMiddleware_CheckSumLaKhongHopLe' => 'Pair code is not valid',
/*--------------------------- Đăng ký ---------------------*/
	/**
	 * @param Mpos360AuthRegisterRequest::class
	 */
	'ThamSoGetLinkRegisterLaBatBuoc' => 'Tham số get link đăng ký là bắt buộc',
	'ThamSoGetLinkRegisterPhaiLaKieuSo' => 'Tham số get link đăng ký phải là kiểu số',


	'KhongTaoDuocBanGhiXacThucTaiKhoan' => 'Không tạo được bản ghi xác thực tài khoản',
	'KhongLayDuocThongTinXacThucTaiKhoan' => 'Không lấy được thông tin xác thực tài khoản',
	'XacThucTaiKhoanBuoc1KhongChinhXac' => 'Xác thực tài khoản bước 1 không chính xác',
	'OtpChuaHetThoiGianKhongLayDuocMaMoi' => 'Otp chưa hết thời gian không lấy được mã mới',
/*--------------------------- /End đăng ký ---------------------*/

/*--------------------------- Đăng nhập ---------------------*/
	/**
	 * @param Mpos360AuthLoginRequest::class
	 */
	'EmailDangNhapLaBatBuoc' => 'Email đăng nhập là bắt buộc',
	'EmailDangNhapPhaiLaKieuChuoi' => 'Email đăng nhập phải là kiểu chuỗi ký tự',
	'EmailDangNhapPhaiLa1Email' => 'Email không đúng định dạng',
	'EmailDangNhapKhongDuocVuotQua255KyTu' => 'Email đăng nhập phải có độ dài dưới 255 ký tự',
	
	'MatKhauDangNhapLaBatBuoc' => 'Mật khẩu đăng nhập là bắt buộc',
	'MatKhauDangNhapPhaiLaKieuChuoi' => 'Mật khẩu đăng nhập phải là kiểu chuỗi',
	'MatKhauDangNhapKhongDuocVuotQua36KyTu' => 'Mật khẩu đăng nhập không được vượt quá 36 ký tự',

	'HeDieuHanhThietBiLaBatBuoc' => 'Hệ điều hành thiết bị là bắt buộc',
	'HeDieuHanhThietBiPhaiLaKieuChuoi' => 'Hệ điều hành thiết bị phải là kiểu chuỗi',
	'HeDieuHanhThietBiPhaiLaAndroidHoacIos' => 'Hệ điều hành thiết bị phải là: ANDROID hoặc IOS',

	'TokenThietBiLaBatBuoc' => 'Token thiết bị là bắt buộc',
	'TokenThietBiPhaiLaKieuChuoi' => 'Token thiết bị phải là kiểu chuỗi',
	'ToKenThietBiKhongVuotQua255KyTu' => 'Token thiết bị không được vượt quá 255 ký tự',

	/**
	 * @param Mpos360AuthLoginAction::class
	 */
	'DangNhapLoiKhongTimThayThongTinPartner' => 'Lỗi: không tìm thấy thông tin partner',

	/**
	 * @param CreateMpos360UserSubAction::class
	 */
	'DangNhapLoiKhongTaoDuocUser' => 'Lỗi: không tạo được user',

	/**
	 * @param GetDeviceIdByOsAndTokenAction::class
	 */
	'DangNhapLoiKhongLayDuocThongTinThietBi' => 'Lỗi: không lấy được thông tin thiết bị',

	/**
	 * @param LoginMerchantViaMposSubAction::class,
	 */
	'LoginMerchantViaMposSubAction_KhongCoThongTinTraVeTuDoiTac' => 'Không có thông tin trả về từ đối tác',
	'LoginMerchantViaMposSubAction_MatKhauKhongChinhXac' => 'Mật khẩu không chính xác',
	'LoginMerchantViaMposSubAction_LoiApiDangNhapDoiTac' => 'Lỗi api đăng nhập đối tác',
/*--------------------------- /End đăng nhập ---------------------*/

/*--------------------------- Xác minh thiết bị ---------------------*/
	/**
	 * @param Mpos360AuthVerifyMposDeviceRequest::class
	 */
	'XacMinhThietBiMaThietBiLaBatBuoc' => 'Mã thiết bị là bắt buộc',
	'XacMinhThietBiMaThietBiPhaiLaKieuChuoi' => 'Mã thiết bị phải là kiểu chuỗi',
	'XacMinhThietBiMaThietBiCoDoDaiToiDaLa255KyTu' => 'Mã thiết bị phải có độ dài dưới 255 ký tự',
	'XacMinhThietBiMaThietBiCoDoDaiToiDaLa10KyTu' => 'Mã thiết bị phải có độ dài tối thiểu là 10 ký tự số',

	/**
	 * @param Mpos360AuthVerifyMposDeviceAction::class
	 */
	'XacMinhThietBiXacThucThanhCong' => 'Xác minh thiết bị thành công',
	'XacMinhThietBiThongTinKhongChinhXac' => 'Lỗi: thông tin không chính xác',
/*--------------------------- /End xác minh thiết bị ---------------------*/

/*--------------------------- Đổi mật khẩu ---------------------*/
	/**
	 * @param Mpos360ChangePasswordRequest::class
	 */
	'DoiMatKhauEmailLaBatBuoc' => 'Email là bắt buộc',
	'DoiMatKhauEmailPhaiLaKieuChuoi' => 'Email phải là kiểu chuỗi ký tự',
	'DoiMatKhauEmailPhaiDungDinhDang' => 'Email phải đúng định dạng',
	'DoiMatKhauEmailPhaiCoDoDaiToiDaLa255KyTu' => 'Email phải có độ dài dưới 255 ký tự',

	'DoiMatKhauMatKhauHienTaiLaBatBuoc' => 'Mật khẩu hiện tại là bắt buộc',

	'DoiMatKhauMatKhauMoiLaBatBuoc' => 'Mật khẩu mới là bắt buộc',
	'DoiMatKhauMatKhauMoiCoDoDaiToiThieuLa4KyTu' => 'Mật khẩu mới tối thiểu phải có độ dài 4 ký tự',
	'DoiMatKhauMatKhauMoiCoDoDaiToiThieuLa6KyTu' => 'Mật khẩu mới tối thiểu phải có độ dài 6 ký tự',
	'DoiMatKhauMatKhauMoiCoDoDaiToiDaLa36KyTu' => 'Mật khẩu mới phải có độ dài tối đa dưới 36 ký tự',
	'DoiMatKhauXacNhanMatKhauMoiKhongTrungKhop' => 'Mật khẩu mới không trùng khớp',
	'DoiMatKhauMatKhauMoiPhaiKhac123456' => 'Mật khẩu mới phải khác 123456',
	'DoiMatKhauMatKhauMoiPhaiKhacMatKhauCu' => 'Mật khẩu mới phải khác mật khẩu cũ',

	/**
	 * @param Mpos360ChangePasswordAction::class
	 */
	'DoiMatKhauLoiDoiTac' => 'Lỗi đối tác: ',
	'DoiMatKhauMessageResultSuccess' => 'Đổi mật khẩu thành công',
/*--------------------------- /End Đổi mật khẩu ---------------------*/

/*--------------------------- Chứng thực ---------------------*/
	/*-------màn hình danh sách trường chứng thực-------*/
		/**
		 * @param Mpos360ChungThucGetFieldInfoRequest::class
		 */
		'ChungThucEmailMerchantLaBatBuoc' => 'VALUE_LANG',
		'ChungThucEmailMerchantPhaiLaKieuChuoi' => 'VALUE_LANG',
		'ChungThucEmailMerchantPhaiLaEmailDungDinhDang' => 'VALUE_LANG',

		/**
		 * @param GetMNPAccessTokenSubAction::class
		 */
		'LoiLuuThongTinTokenMnp' => 'Lỗi lưu thông tin token mnp',
		'LoiGoiDoiTacMnp' => 'Lỗi gọi đối tác MNP',
		
		/**
		 * @param Mpos360ChungThucGetFieldInfoAction::class
		 */
		'BanCanPhaiChungThucCccdLanDauTienDeCoTheTaoYeuCauThayDoi' => 'Bạn cần phải chứng thực CCCD lần đầu tiên để có thể tạo yêu cầu thay đổi',
		'DeDamBaoAnToanChoTaiKhoanQuyKhachVuiLongXacThucCacThongTinDuoiDay' => 'Để đảm bảo an toàn cho tài khoản, Qúy khách vui lòng chứng thực các thông tin dưới đây',
		'QuyKhachVuiLongChoTrongGiayLatDeHeThongXuLyChungThucThongTin' => 'Qúy khách vui lòng chờ trong giây lát để hệ thống xử lý chứng thực thông tin',
	/*-------Lấy mã otp chứng thực-------*/
		/**
		 * @param Mpos360ChungThucGetOtpRequest::class
		 */
		'ChungThucGetOtpServiceCodeLaBatBuoc' => 'Mã dịch vụ là bắt buộc',
		'ChungThucGetOtpServiceCodePhaiLaChuoiKyTu' => 'Mã dịch vụ là chuỗi ký tự',
		'ChungThucGetOtpServiceCodePhaiDuoi50KyTu' => 'Mã dịch vụ phải dưới 50 ký tự',
		
		'ChungThucGetOtpGiaTriLaBatBuoc' => 'Otp là bắt buộc',
		'ChungThucGetOtpGiaTriPhaiLaKieuChuoi' => 'Otp phải là kiểu chuỗi',

		'ChungThucGetOtpIdThamChieuLaBatBuoc' => 'Mã tham chiếu là bắt buộc',
		'ChungThucGetOtpIdThamChieuLaKieuChuoi' => 'Mã tham chiếu phải là kiểu chuỗi',

		/**
		 * @param Mpos360ChungThucGetOtpAction::class
		 */
		'ChungThucGetOtpLoiKhongTimThayBanGhiChungThucLienQuan' => 'Lỗi: không tìm thấy bản ghi chứng thực liên quan',
		'ChungThucGetOtpLoiKhongTaoDuocBanGhiOtp' => 'Lỗi: không tạo được otp',

		/**
		 * @param SendSmsOtpMNPSubAction::class
		 */
		'LoiGuiOtpQuaSMS' => 'Lỗi gửi otp: ',

		/**
		 * @param SendMailOtpMNPSubAction::class
		 */
		'LoiGuiOtpQuaEmail' => 'Lỗi gửi otp: ',
	/*-------gửi lại mã otp chứng thực-------*/
		/**
		 * @param Mpos360ChungThucResendOtpRequest::class
		 */
		'ChungThucResendOtpOtpIdLaBatBuoc' => 'OtpId là bắt buộc',
		'ChungThucResendOtpOtpPhaiLaKieuChuoi' => 'OtpId phải là kiểu chuỗi',
		'ChungThucResendOtpOtpPhaiCoDoDaiDuoi10KyTu' => 'OtpId phải dưới 10 ký tự',

		/**
		 * @param Mpos360ChungThucResendOtpAction::class
		 */
		'ChungThucResendOtpKhongTimThayBanGhiOtp' => 'Lỗi: không tìm thấy bản ghi otp',
		'ChungThucResendOtpOtpDaDuocSuDung' => 'Lỗi: otp đã được sử dụng',
		'ChungThucResendOtpLoiKhongDungThongTinUser' => 'Lỗi: sai thông tin chủ sở hữu otp',

		'ChungThucResendOtpLoiKhongTaoDuocOtp' => 'Lỗi: không tạo được otp',

		'ChungThucResendOtpGuiOtpThanhCongQuaSMS' => 'Đã gửi OTP đến SĐT :obj_value của bạn. Vui lòng kiểm tra trong hộp thư đến',
		'ChungThucResendOtpGuiOtpThanhCongQuaEmail' => 'Đã gửi OTP đến Email :obj_value của bạn. Vui lòng kiểm tra trong hộp thư đến, quảng cáo, spam',
		'ChungThucResendOtpLoiGuiOtpKhongXacDinh' => 'Không xác định được lỗi gửi OTP',

	/*-------verify otp chứng thực-------*/
		/**
		 * @param Mpos360ChungThucVerifyOtpRequest::class
		 */
		'ChungThucVerifyOtpOtpIdLaBatBuoc' => 'OtpId là bắt buộc',
		'ChungThucVerifyOtpOtpIdPhaiLaKieuChuoi' => 'OtpId phải là kiểu chuỗi',
		'ChungThucVerifyOtpOtpIdToiDaLa20KyTu' => 'OtpId phải có độ dài tối đa dưới 20 ký tự',

		'ChungThucVerifyOtpOtpLaBatBuoc' => 'Otp là bắt buộc',
		'ChungThucVerifyOtpOtpPhaiLaKieuChuoi' => 'Otp phải là kiểu chuỗi',
		'ChungThucVerifyOtpOtpCoDoDaiKhongQua10KyTu' => 'Otp có độ dài không quá 10 ký tự',

		'ChungThucVerifyOtpMaThamChieuLaBatBuoc' => 'Id tham chiếu là bắt buộc',
		'ChungThucVerifyOtpMaThamChieuPhaiLaKieuChuoi' => 'Id tham chiếu phải là kiểu chuỗi',

		/**
		 * @param Mpos360ChungThucVerifyOtpAction::class
		 */
		'Mpos360ChungThucVerifyOtpAction_LoiKhongTimThayBanGhiOtp' => 'Lỗi: không tìm thấy bản ghi otp',
		'Mpos360ChungThucVerifyOtpAction_LoiOtpDaDuocSuDung' => 'Lỗi: otp đã được sử dụng',
		'Mpos360ChungThucVerifyOtpAction_LoiOtpDaHetHan' => 'Lỗi: otp đã hết hạn',
		'Mpos360ChungThucVerifyOtpAction_LoiOtpKhongChinhXac' => 'Lỗi: otp không chính xác',
		'Mpos360ChungThucVerifyOtpAction_LoiKhongTimThayTruongChungThucLienQuan' => 'Lỗi: không tìm thấy trường chứng thực liên quan',
		'Mpos360ChungThucVerifyOtpAction_LoiKhongDanhDauDuocOtpLaDaSuDung' => 'Lỗi: không đánh dấu được otp là đã sử dụng',
		'Mpos360ChungThucVerifyOtpAction_LoiKhongDanhDauDuocLaDaChungThuc' => 'Lỗi: không đánh dấu được là đã chứng thực',
		'Mpos360ChungThucVerifyOtpAction_LoiLuuThongTinChungThucDoiTac' => 'Lỗi: lưu thông tin chứng thực thành công sang đối tác',
		'Mpos360ChungThucVerifyOtpAction_ChungThucThanhCong' => 'Chứng thực ":obj_value" qua kênh ":service_code" thành công',
		'Mpos360ChungThucVerifyOtpAction_LoiOtpKhongChinhXacVaHetHan' => 'The OTP code is incorrect or expired. Please double-check the code in the message or request a new one.',
/*--------------------------- /.End chứng thực ---------------------*/

/*--------------------------- /Giao dịch ---------------------*/
	'TLNA_TrangThaiGiaoDich' => 'Trạng thái giao dịch',
	/*-------lịch sử giao dịch-------*/
	/**
	 * @param Mpos360TransactionHistoryAction::class
	 */
	'Mpos360TransactionHistoryAction_TongTienGiaoDich' => 'Tổng tiền giao dịch',
	'Mpos360TransactionHistoryAction_ChuaKetToan' => 'Chưa kết toán',
	'Mpos360TransactionHistoryAction_DaKetToan' => 'Đã kết toán',
	'Mpos360TransactionHistoryAction_ThanhToanThuong' => 'Thanh toán thường',
	'Mpos360TransactionHistoryAction_GiaoDichQr' => 'Giao dịch QR',
	'Mpos360TransactionHistoryAction_ThanhToanTraGop' => 'Trả góp',
	'Mpos360TransactionHistoryAction_YeuCauRutTienNhanh' => 'YC rút tiền nhanh',
	'Mpos360TransactionHistoryAction_YeuCauNhanTienNhanhVietQr' => 'YC nhận tiền ngay VietQR',
	'Mpos360TransactionHistoryAction_NhanTienVeTKNH' => 'Nhận tiền về TKNH',
	'Mpos360TransactionHistoryAction_GD' => 'GD',
/*--------------------------- /. End Giao dịch ---------------------*/

/*--------------------------- /YC đổi thông tin ---------------------*/
	'CommonRequestChangeInfo' => [
		'Type' => [
			'CHANGE_BANK_ACCOUN_INFO' => 'Tài khoản ngân hàng',
			'CHANGE_REPRESENT_INFO' => 'Người đại diện ký HĐ',
		],
		
		'Choice' => [
			'DOI_THONG_TIN_LIEN_HE' => 'Đổi thông tin liên hệ',
			'DOI_NGUOI_DAI_DIEN_MOI' => 'Đổi người đại diện mới',
			'DOI_CCCD_MOI' => 'Đổi CCCD mới',
		]
	],

	/*-------Danh sách yêu cầu-------*/
	/**
	 * @param Mpos360RequestChangeInfoListAction::class
	 */
	'Mpos360RequestChangeInfoListAction_HayTaoYcKhac' => 'Hãy tạo yêu cầu khác!',
	'Mpos360RequestChangeInfoListAction_HetHan' => 'Hết hạn',
	'Mpos360RequestChangeInfoListAction_HayXacThucYeuCau' => 'Hãy xác thực yêu cầu',
	'Mpos360RequestChangeInfoListAction_ChuaXacThuc' => 'Chưa xác thực',
	'Mpos360RequestChangeInfoListAction_YeuCau' => 'Yêu cầu: ',
	'Mpos360RequestChangeInfoListAction_DaXacThuc' => 'Đã xác thực',
	'Mpos360RequestChangeInfoListAction_CanXacMinhBuoc3' => 'Cần làm xác thực thay đổi',

	'Mpos360RequestChangeInfoListAction_MaYC' => 'Mã YC',

	/**
	 * @param ConfigYeuCauThayDoiSubAction::class
	 */
	'ConfigYeuCauThayDoiSubAction_Nhap' => 'Cần hoàn thành',
	'ConfigYeuCauThayDoiSubAction_ChuaGui' => 'Chưa gửi',
	'ConfigYeuCauThayDoiSubAction_DangGui' => 'Đang gửi',
	'ConfigYeuCauThayDoiSubAction_DaGuiSangMnp' => 'Đã gửi sang MNP',
	'ConfigYeuCauThayDoiSubAction_MnpDaXuLy' => 'MNP đã xử lý',
	'ConfigYeuCauThayDoiSubAction_MnpTuChoi' => 'MNP từ chối',
	'ConfigYeuCauThayDoiSubAction_CapNhatLoi' => 'Cập nhật lỗi',
	'ConfigYeuCauThayDoiSubAction_MCTuHuyYeuCau' => 'MC hủy yêu cầu',
	'ConfigYeuCauThayDoiSubAction_HetHan' => 'Hết hạn',
/*--------------------------- /.End YC đổi thông tin ---------------------*/

/*--------------------------- /.Chứng thực ---------------------*/
	/**
	 * @param Mpos360ChungThuc::class
	 */
	'Mpos360ChungThuc_ChuaChungThuc' => 'Chưa chứng thực',
	'Mpos360ChungThuc_ChoChungThuc' => 'Đang chứng thực',
	'Mpos360ChungThuc_DaHetHan' => 'Đã hết hạn',
	'Mpos360ChungThuc_DaChungThuc' => 'Đã chứng thực',
/*--------------------------- /.End chứng thực YC đổi thông tin ---------------------*/

/*--------------------------- /.Yêu cầu xóa tài khoản ---------------------*/
	/**
	 * @param Mpos360AuthRemoveAccountRequest::class
	 */
	'Mpos360AuthRemoveAccountRequest_EmailLaBatBuoc' => 'Email là bắt buộc',
	'Mpos360AuthRemoveAccountRequest_EmailPhaiLaKieuChuoi' => 'Email phải là kiểu chuỗi',
	'Mpos360AuthRemoveAccountRequest_EmailPhaiDungDinhDang' => 'Email phải đúng định dạng',
	'Mpos360AuthRemoveAccountRequest_EmailKhongDuocVuotQua255KyTu' => 'Email phải có độ dài dưới 255 ký tự',

	'Mpos360AuthRemoveAccountRequest_LyDoXoaTaiKhoanLaBatBuoc' => 'Lý do xóa tài khoản là bắt buộc',
	'Mpos360AuthRemoveAccountRequest_LyDoXoaTaiKhoanPhaiLaKieuChuoi' => 'Lý do xóa tài khoản phải là kiểu chuỗi',
	'Mpos360AuthRemoveAccountRequest_LyDoXoaTaiKhoanPhaiPhaiCoDoDaiDuoi300KyTu' => 'Lý do xóa tài khoản phải có độ dài dưới 300 ký tự',

	'Mpos360AuthRemoveAccountRequest_MatKhauLaBatBuoc' => 'Mật khẩu là bắt buộc',
	'Mpos360AuthRemoveAccountRequest_MatKhauPhaiLaKieuChuoi' => 'Mật khẩu phải là kiểu chuỗi',
	/**
	 * @param Mpos360AuthRemoveAccountAction::class
	 */
	'Mpos360AuthRemoveAccountAction_LoiMatKhauKhongChinhXac' => 'Lỗi mật khẩu không đúng',
	'Mpos360AuthRemoveAccountAction_LoiKhongTaoDuocYeuCauXoaTaiKhoan' => 'Lỗi không tạo được yêu cầu xóa tài khoản',
	'Mpos360AuthRemoveAccountAction_LoiCapNhatThoiGianHetHanPhien' => 'Lỗi cập nhật thời gian hết hạn',
	'Mpos360AuthRemoveAccountAction_XoaTaiKhoanThanhCong' => 'mPOS đã tiếp nhận yêu cầu xóa tài khoản {email} thành công. Cám ơn Quý khách đã tin tưởng và sử dụng mPOS trong thời gian vừa qua. Mọi ý kiến đóng góp xin liên hệ bộ phận chăm sóc khách hàng của mPOS.

Nếu bạn muốn giữ lại tài khoản thì vui lòng đăng nhập vào hệ thống trước ngày :timeline',
/*--------------------------- /.End yêu cầu xóa tài khoản ---------------------*/

/*-----------------------Dữ liệu hồ sơ (chỉ có ở bản tiếng anh do là data của MNP)------------------*/
	'Chi nhánh' => 'Chi nhánh',
	'Ngân hàng' => 'Ngân hàng',
	'Số TKNH' => 'Số TKNH',
	'Loại tài khoản ngân hàng' => 'Loại tài khoản ngân hàng',
	'Tên chủ tài khoản' => 'Tên chủ tài khoản',
	'Tỉnh/Thành phố' => 'Tỉnh/Thành phố',

	'Số điện thoại người đại diện' => 'Số điện thoại người đại diện',
	'Nơi cấp' => 'Nơi cấp',
	'Số CMND/CCCD/Hộ chiếu' => 'Số CMND/CCCD/Hộ chiếu',
	'Địa chỉ thường trú' => 'Địa chỉ thường trú',
	'Địa chỉ hiện tại' => 'Địa chỉ hiện tại',
	'Email người đại diện' => 'Email người đại diện',
	'Tên người đại diện' => 'Tên người đại diện',

	// group code name
	'TKNH nhận tiền' => 'TKNH nhận tiền',
	'Người đại diện ký HĐ' => 'Người đại diện ký HĐ',
	'Thông tin tài khoản Tingbox.vn' => 'Thông tin tài khoản Tingbox.vn',

	'Đổi người đại diện mới' => 'Đổi người đại diện mới',
	'Đổi CCCD mới' => 'Đổi CCCD mới',
	'Đổi thông tin liên hệ' => 'Đổi thông tin liên hệ',

	'Thành phố' => 'Thành phố',
	'Tên Merchant' => 'Tên Merchant',
	'Email đăng nhập' => 'Email đăng nhập',
	'Tên viết tắt' => 'Tên viết tắt',
	'Địa chỉ' => 'Địa chỉ',
	'Số điện thoại' => 'Số điện thoại',
	'Quận/Huyện' => 'Quận/Huyện',
/*---------------------------Dữ liệu hồ sơ---------------------*/

/*---------------------------Home---------------------*/
	/**
	 * @param GetMenuForAppSubAction::class,
	 */
	'GetMenuForAppSubAction_BaoCaoGiaoDich' => 'Báo cáo giao dịch',
	'GetMenuForAppSubAction_LichSuGiaoDich' => 'Lịch sử giao dịch',
	'GetMenuForAppSubAction_ThanhToanThuong' => 'Thanh toán thường',
	'GetMenuForAppSubAction_ThanhToanTraGop' => 'Trả góp',
	'GetMenuForAppSubAction_ThongTinTaiKhoan' => 'Thông tin tài khoản',
	'GetMenuForAppSubAction_YcDoiThongTin' => 'Yêu cầu đổi thông tin',
	'GetMenuForAppSubAction_HopThu' => 'Hộp thư',
	'GetMenuForAppSubAction_HopDongDichVu' => 'Hợp đồng dịch vụ',
	'GetMenuForAppSubAction_ThietBiThanhToan' => 'Thiết bị thanh toán',
	'GetMenuForAppSubAction_NhanTienVeTKNH' => 'Nhận tiền về TKNH',
	'GetMenuForAppSubAction_YcNhanTienNhanh' => 'Yêu cầu nhận tiền nhanh',
	'GetMenuForAppSubAction_NhanTienTuVietQr' => 'Nhận tiền nhanh VietQr',
	'GetMenuForAppSubAction_DangKyDichVu' => 'Đăng ký dịch vụ',
/*---------------------------/End Home---------------------*/

/*---------------------------/List phương thức---------------------*/
	/**
	 * @param GetPhuongThucQuetB3Ver2SubAction
	 */
	'GetPhuongThucQuetB3Ver2SubAction_SDK' => 'Chứng thực CCCD người đại diện',
	'GetPhuongThucQuetB3Ver2SubAction_QTS' => 'Xác thực khuôn mặt',
	'GetPhuongThucQuetB3Ver2SubAction_ZALO' => 'Qua Zalo',
	'GetPhuongThucQuetB3Ver2SubAction_SMS' => 'Qua SMS',
	'GetPhuongThucQuetB3Ver2SubAction_EMAIL' => 'Qua Email',
/*---------------------------/End List phương thức---------------------*/
	/**
	 * @param Mpos360RequestChangeInfoUpdateProfileAction::class
	 */
	'Bạn cần làm chứng thực CCCD trước khi tạo yêu cầu đổi thông tin' => 'Bạn cần làm chứng thực CCCD trước khi tạo yêu cầu đổi thông tin',

	/**
	 * @param AddingAdditionalProfileSubAction::class
	 */
	'CCCD/GP lái xe/Hộ khẩu/Hộ chiếu' => 'CCCD/GP lái xe/Hộ khẩu/Hộ chiếu',
	'Chức vụ của người ủy quyền tại ngân hàng' => 'Chức vụ của người ủy quyền tại ngân hàng',
	'Mối quan hệ giữa chủ tài khoản và người có thẩm quyền tại ngân hàng' => 'Mối quan hệ giữa chủ tài khoản và người có thẩm quyền tại ngân hàng',
	'CCCD' => 'CCCD',
	'Ảnh chụp CCCD mặt trước' => 'Ảnh chụp CCCD mặt trước',
	'Ảnh chụp CCCD mặt sau' => 'Ảnh chụp CCCD mặt sau',
	'Ảnh chụp CCCD mặt sau' => 'Ảnh chụp CCCD mặt sau',
	'GP lái xe' => 'GP lái xe',
	'GPLX mặt trước' => 'GPLX mặt trước',
	'GPLX mặt sau' => 'GPLX mặt sau',
	'Hộ chiếu' => 'Hộ chiếu',
	'Ảnh 1' => 'Ảnh 1',
	'Ảnh 2' => 'Ảnh 2',
	'Ảnh 3' => 'Ảnh 3',
	'Ảnh 4' => 'Ảnh 4',
	'Hộ khẩu' => 'Hộ khẩu',
/*---------------------------Luồng đổi thông tin V3---------------------*/
	/**
	 * @param BuildChoiceOptionSubAction::class
	 */
	'Đổi số TKNH khác của hộ kinh doanh' => 'Đổi số TKNH khác của hộ kinh doanh',
	'Cùng người đại diện pháp luật trên ĐKKD' => 'Cùng người đại diện pháp luật trên ĐKKD',
	'Đổi số TKNH cá nhân được ủy quyền' => 'Đổi số TKNH cá nhân được ủy quyền',
	'Cung cấp giấy tờ của người mới & giấy ủy quyền' => 'Cung cấp giấy tờ của người mới & giấy ủy quyền',
	'Đổi số TKNH khác của doanh nghiệp' => 'Đổi số TKNH khác của doanh nghiệp',
	'Đổi người đại diện khác' => 'Đổi người đại diện khác',
	'Cung cấp CCCD của người đại diện mới & giấy ủy quyền' => 'Cung cấp CCCD của người đại diện mới & giấy ủy quyền',
	'Đổi/Cập nhật CCCD mới' => 'Đổi/Cập nhật CCCD mới',
	'Của người đại diện hiện tại, khi làm mới hoặc cấp lại giấy tờ' => 'Của người đại diện hiện tại, khi làm mới hoặc cấp lại giấy tờ',
	'Đổi thông tin liên hệ' => 'Đổi thông tin liên hệ',
	'Thông tin người đại diện hiện tại' => 'Thông tin người đại diện hiện tại',

	/**
	 * @param Mpos360MerchantSignatureCreateRequest::class
	 */
	'Mpos360MerchantSignatureCreateRequest_EmailLaBatBuoc' => 'Email là bắt buộc',
	'Mpos360MerchantSignatureCreateRequest_EmailPhaiLaKieuChuoi' => 'Email phải là kiểu chuỗi',
	'Mpos360MerchantSignatureCreateRequest_EmailKhongDungDinhDang' => 'Email không đúng định dạng',
	'Mpos360MerchantSignatureCreateRequest_ChuKyLaBatBuoc' => 'Chữ ký là bắt buộc',
	'Mpos360MerchantSignatureCreateRequest_ChuKyPhaiLaMotChuoiKyTu' => 'Chữ ký phải là một chuỗi ký tự',
	'Mpos360MerchantSignatureCreateRequest_ChuKyPhaiLaMotUrl' => 'Chữ ký phải là một url',
	'ImageUrlRule_ChuKyPhaiLaMotHinhAnh' => 'Url chữ ký phải là một hình ảnh',
	'Mpos360MerchantSignatureCreateRequest_TrangThaiChuKyLaBatBuoc' => 'Trạng thái chữ ký là bắt buộc',
	'Mpos360MerchantSignatureCreateRequest_TrangThaiChuKyKhongHopLe' => 'Trạng thái chữ ký phải là: ĐANG SỬ DỤNG hoặc KHÔNG SỬ DỤNG',

	/**
	 * @param Mpos360RequestChangeInfoMarkAsDoneAction::class
	 */
	'Mpos360RequestChangeInfoMarkAsDoneAction_ChungToiDaTiepNhanYeuCauCuaBan' => 'Chúng tôi đã tiếp nhận yêu cầu cập nhật thông tin của Quý đơn vị. Hệ thống MPOS sẽ tiến hành kiểm tra và phản hồi trong vòng 02 ngày làm việc. Trân trọng cảm ơn Quý đơn vị đã tin tưởng và sử dụng dịch vụ của chúng tôi.',

	/**
	 * @param Mpos360RequestChangeInfoAdditionalAttachmentRequest
	 */
	'IdYeuCauLaBatBuoc' => 'Id yêu cầu là bắt buộc',
	'IdYeuCauPhaiLaDangSo' => 'Id yêu cầu phải là dạng số',
	'IdYeuCauPhaiLaSoTuNhien' => 'Id yêu cầu phải là số tự nhiên',
	'IdYeuCauPhaiCoGiaTriThapNhatLa1' => 'Id yêu cầu phải có giá trị thấp nhất là: 1',
	'EmailMcLaBatBuoc' => 'Email MC là bắt buộc',
	'EmailMcPhaiLaKieuChuoi' => 'Email MC phải là kiểu chuỗi',
	'EmailMcKhongDungDinhDang' => 'Email MC không đúng định dạng',
	'PhaiCoThamSoQtsRequestId' => 'Phải có tham số qts',
	'QtsRequestPhaiLaDangChuoi' => 'Tham số qts phải là dạng chuỗi',
	'HoSoBoSungLaBatBuoc' => 'Hồ sơ bổ sung là bắt buộc',
	'HoSoBoSungPhaiLaKieuMang' => 'Hồ sơ bổ sung phải là kiểu mảng',
	'MaHoSoLaBatBuoc' => 'Mã hồ sơ là bắt buộc',
	'MaHoSoPhaiLaKieuChuoi' => 'Mã hồ sơ phải là kiểu chuỗi',
	'GiaTriHoSoLaBatBuoc' => 'Giá trị hồ sơ là bắt buộc',
	'GiaTriHoSoLaKieuChuoi' => 'Giá trị hồ sơ phải là kiểu chuỗi',
	'FileDinhKemLaBatBuoc' => 'File đính kèm là bắt buộc',
	'FileDinhKemPhaiLaKieuMang' => 'File đính kèm phải là kiểu mảng',
	'ChungTuThayTheLaBatBuoc' => 'Chứng từ thay thế là bắt buộc',
	'ChungTuThayThePhaiLaDangMang' => 'Chứng từ thay thế phải là kiểu mảng',
	'PhaiCoThamSoChungNhanDiKem' => 'Phải có tham số chứng nhận đi kèm',
	'ChungNhanDiKemPhaiLaDangMang' => 'Chứng nhận đi kèm phải là kiểu mảng',
	'ChungNhanDiKemPhaiLaDangChuoi' => 'Item chứng nhận đi kèm phải là kiểu chuỗi',
	'ChungNhanDiLemPhaiLaUrl' => 'Item chứng nhận đi kèn phải là 1 url',

	/**
	 * @param ValidateProfileDinhKemSA::class
	 */
	'Thiếu thông tin: Vai trò/Vị trí của người ủy quyền tại ngân hàng (positionAuthBank)' => 'Thiếu thông tin: Vai trò/Vị trí của người ủy quyền tại ngân hàng (positionAuthBank)',
	'Thiếu thông tin: Vai trò/Vị trí của người được ủy quyền' => 'Thiếu thông tin: Vai trò/Vị trí của người được ủy quyền',
	'Vai trò/Vị trí của người được ủy quyền có độ dài tối thiểu là 3 ký tự' => 'Vai trò/Vị trí của người được ủy quyền có độ dài tối thiểu là 3 ký tự',
	'Vai trò/Vị trí của người được ủy quyền phải có độ dài tối đa là 255 ký tự' => 'Vai trò/Vị trí của người được ủy quyền phải có độ dài tối đa là 255 ký tự',
	'Vai trò/Vị trí của người ủy quyền tại ngân hàng phải có độ dài tối thiểu là 3 ký tự' => 'Vai trò/Vị trí của người ủy quyền tại ngân hàng phải có độ dài tối thiểu là 3 ký tự',
	'Vai trò/Vị trí của người ủy quyền tại ngân hàng phải có độ dài tối đa là 255 ký tự' => 'Vai trò/Vị trí của người ủy quyền tại ngân hàng phải có độ dài tối đa là 255 ký tự',
	'Thiếu thông tin: Mối quan hệ giữa hai bên (bankMutualRelation)' => 'Thiếu thông tin: Mối quan hệ giữa hai bên (bankMutualRelation)',
	'Mối quan hệ giữa hai bên phải có độ dài tối thiểu là 3 ký tự' => 'Mối quan hệ giữa hai bên phải có độ dài tối thiểu là 3 ký tự',
	'Mối quan hệ giữa hai bên phải có độ dài tối đa là 255 ký tự' => 'Mối quan hệ giữa hai bên phải có độ dài tối đa là 255 ký tự',
	'Thiếu thông tin: Mã qts sau khi ekyc' => 'Thiếu thông tin: Mã qts sau khi ekyc',
	'Lỗi: Không khớp tên thông tin người thụ hưởng' => 'Lỗi: Không khớp tên thông tin người thụ hưởng',
	'Thiếu thông tin: Vai trò/Chức vụ của người đại diện (representPosition)' => 'Thiếu thông tin: Vai trò/Chức vụ của người đại diện (representPosition)',
	'Vai trò/Chức vụ của người đại diện phải có độ dài tối thiểu là 3 ký tự' => 'Vai trò/Chức vụ của người đại diện phải có độ dài tối thiểu là 3 ký tự',
	'Vai trò/Chức vụ của người đại diện phải có độ dài tối đa là 255 ký tự' => 'Vai trò/Chức vụ của người đại diện phải có độ dài tối đa là 255 ký tự',
	'Thiếu thông tin: Mối quan hệ giữa 2 bên (representMutualRelation)' => 'Thiếu thông tin: Mối quan hệ giữa 2 bên (representMutualRelation)',
	'Mối quan hệ giữa hai bên phải có độ dài tối thiểu là 3 ký tự' => 'Mối quan hệ giữa hai bên phải có độ dài tối thiểu là 3 ký tự',
	'Mối quan hệ giữa hai bên phải có độ dài tối đa là 255 ký tự' => 'Mối quan hệ giữa hai bên phải có độ dài tối đa là 255 ký tự',
	'Không tìm được bản ghi yêu cầu thay đổi' => 'Không tìm được bản ghi yêu cầu thay đổi',
	'Bổ sung tài liệu đính kèm thành công' => 'Bổ sung tài liệu đính kèm thành công',

	/**
	 * @param TaoYcDoiSTKCuaCaNhanDuocDoanhNghiepUyQuyenSA::class
	 */
	'Lỗi: không tạo được yc đổi thông tin TKNH' => 'Lỗi: không tạo được yc đổi thông tin TKNH',
	'Tạo yêu cầu cầu đổi thông tin thành công' => 'Tạo yêu cầu cầu đổi thông tin thành công',
	'Vai trò/Vị trí của người ủy quyền tại ngân hàng' => 'Vai trò/Vị trí của người ủy quyền tại ngân hàng',
	'Vai trò/Vị trí của người được ủy quyền' => 'Vai trò/Vị trí của người được ủy quyền',
	'Mối quan hệ giữa hai bên' => 'Mối quan hệ giữa hai bên',
	'Bố/Mẹ - Con' => 'Bố/Mẹ - Con',
	'Anh - Chị - Em' => 'Anh - Chị - Em',
	'Vợ - Chồng' => 'Vợ - Chồng',
	'Khác' => 'Khác',
	'Chứng từ đính kèm' => 'Chứng từ đính kèm',
	'Ảnh Giấy ủy quyền có dấu và chữ ký của công ty' => 'Ảnh Giấy ủy quyền có dấu và chữ ký của công ty',
	'Ảnh 1' => 'Ảnh 1',

	/**
	 * @param TaoYcDoiSTKCuaCaNhanDuocHKDUyQuyenSA::class
	 */
	'Lỗi: không tạo được yc đổi thông tin TKNH' => 'Lỗi: không tạo được yc đổi thông tin TKNH',
	'Tạo yêu cầu cầu đổi thông tin thành công' => 'Tạo yêu cầu cầu đổi thông tin thành công',
	'Vai trò/Vị trí của người ủy quyền tại ngân hàng' => 'Vai trò/Vị trí của người ủy quyền tại ngân hàng',
	'Mối quan hệ giữa hai bên' => 'Mối quan hệ giữa hai bên',
	'Ảnh giấy ủy quyền được công chứng tại cơ quan có thẩm quyền' => 'Ảnh giấy ủy quyền được công chứng tại cơ quan có thẩm quyền',
	'Ảnh giấy khai sinh' => 'Ảnh giấy khai sinh',
	'Ảnh giấy đăng ký kết hôn' => 'Ảnh giấy đăng ký kết hôn',
	'Ảnh tất cả các trang Sổ hộ khẩu giấy' => 'Ảnh tất cả các trang Sổ hộ khẩu giấy',
	'Video quay màn hình ứng dụng VNeID từ khi đăng nhập đến các trang thể hiện mối quan hệ giữa người ủy quyền và người được ủy quyền' => 'Video quay màn hình ứng dụng VNeID từ khi đăng nhập đến các trang thể hiện mối quan hệ giữa người ủy quyền và người được ủy quyền',

	/**
	 * @param Mpos360RequestChangeInfoDetailV3Action::class
	 */
	'Id yêu cầu' => 'Id yêu cầu',
	'Mã yêu cầu' => 'Mã yêu cầu',
	'Ngày tạo' => 'Ngày tạo',
	'Trạng thái yêu cầu' => 'Trạng thái yêu cầu',
	'Trạng thái xác thực' => 'Trạng thái xác thực',
	'Trạng thái ký phục lục' => 'Trạng thái ký phục lục',
	'Thông tin chung' => 'Thông tin chung',

	'Đổi thông tin TKNH' => 'Đổi thông tin TKNH',
	'Thay đổi thông tin TKNH' => 'Thay đổi thông tin TKNH',
	'Kiểu thay đổi' => 'Kiểu thay đổi',
	'Id NH' => 'Id NH',
	'Chi nhánh' => 'Chi nhánh',
	'Tên NH' => 'Tên NH',
	'Số tài khoản' => 'Số tài khoản',
	'Người thụ hưởng' => 'Người thụ hưởng',
	'Loại TKNH' => 'Loại TKNH',
	'Mã tỉnh thành' => 'Mã tỉnh thành',
	'Tên tỉnh thành' => 'Tên tỉnh thành',
	'Chức vụ/Vị trí' => 'Chức vụ/Vị trí',
	'Mối quan hệ' => 'Mối quan hệ',
	'Chứng từ bổ sung' => 'Chứng từ bổ sung',

	'Trạng thái duyệt' => 'Trạng thái duyệt',
	'Chờ duyệt' => 'Chờ duyệt',
	'Đã duyệt' => 'Đã duyệt',
	'Bị từ chối' => 'Bị từ chối',
	'Lý do bị từ chối' => 'Lý do bị từ chối',
	'Cập nhật lỗi' => 'Cập nhật lỗi',
	'Không duyệt' => 'Không duyệt',
	'Lý do' => 'Lý do',
	'Cần xác thực thay đổi thông tin (selfie)' => 'Cần xác thực thay đổi thông tin (selfie)',
	'Thông tin khác' => 'Thông tin khác',

	'Không xác định' => 'Không xác định',
	'Không xác thực' => 'Không xác thực',
	'Chưa xác thực' => 'Chưa xác thực',
	'Cần xác minh bước 3' => 'Cần xác thực thay đổi',
	'Đã xác thực' => 'Đã xác thực',
	'Không xác định' => 'Không xác định',
	'Hết hạn' => 'Hết hạn',
	'MC hủy YC' => 'MC hủy YC',
	
	'Tên người đại diện' => 'Tên người đại diện',
	'Ngày sinh' => 'Ngày sinh',
	'Số CCCD' => 'Số CCCD',
	'Nơi cấp' => 'Nơi cấp',
	'Loại thay đổi' => 'Loại thay đổi',
	'Địa chỉ hiện tại' => 'Địa chỉ hiện tại',
	'Địa chỉ thường chú' => 'Địa chỉ thường trú',
	'Chức vụ/Vị trí' => 'Chức vụ/Vị trí',
	'Mối quan hệ giữa 2 bên' => 'Mối quan hệ giữa 2 bên',
	'Chứng từ bổ sung' => 'Chứng từ bổ sung',
	'Email người đại diện' => 'Email người đại diện',
	'SĐT người đại diện' => 'SĐT người đại diện',

	'Đổi người đại diện mới' => 'Đổi người đại diện mới',
	'Đổi thông tin liên hệ' => 'Đổi thông tin liên hệ',
	'Đổi CCCD mới' => 'Đổi CCCD mới',

	'Chữ ký xác nhận' => 'Chữ ký xác nhận',

	'Chưa ký' => 'Chưa ký',
	'Đang gửi ký' => 'Đang gửi ký',
	'Đã ký' => 'Đã ký',
	'Gửi ký lỗi' => 'Gửi ký lỗi',
	'Không xác định' => 'Không xác định',

	'Chức vụ của người đại diện mới' => 'Chức vụ của người đại diện mới',
	'Giám đốc' => 'Giám đốc',
	'Phó giám đốc' => 'Phó giám đốc',
	'Trưởng phòng' => 'Trưởng phòng',
	'Khác' => 'Khác',

	/**
	 * @param Mpos360RequestChangeInfoCheckProfileBankingAction::class
	 */
	'Lỗi không kiểm tra được thông tin ngân hàng' => 'Lỗi không kiểm tra được thông tin ngân hàng',
	'Số tài khoản hợp lệ' => 'Số tài khoản hợp lệ',
	'Để tránh sai sót khi nhận tiền. Vui lòng kiểm tra lại thông tin trước khi tiếp tục' => 'Để tránh sai sót khi nhận tiền. Vui lòng kiểm tra lại thông tin trước khi tiếp tục',
	'Số tài khoản chưc được xác minh bởi ngân hàng. Vui lòng kiểm tra lại thông tin trước khi tiếp tục!' => 'Số tài khoản chưc được xác minh bởi ngân hàng. Vui lòng kiểm tra lại thông tin trước khi tiếp tục!',
	'Số tài khoản hợp lệ' => 'Số tài khoản hợp lệ',
	'Số tài khoản không chính xác. Vui lòng kiểm tra lại' => 'Số tài khoản không chính xác. Vui lòng kiểm tra lại',
	'Số tài khoản chưc được xác minh bởi ngân hàng. Vui lòng kiểm tra lại thông tin trước khi tiếp tục!' => 'Số tài khoản chưc được xác minh bởi ngân hàng. Vui lòng kiểm tra lại thông tin trước khi tiếp tục!',
	'Số tài khoản không hợp lệ' => 'Số tài khoản không hợp lệ',
	'Sai STK hoặc người thụ hưởng' => 'Sai STK hoặc người thụ hưởng',

	/**
	 * @param Mpos360RequestChangeInfoSaveChangesAction::class
	 */
	'YC không tồn tại' => 'YC không tồn tại',
	'Lỗi: YC không phải loại đổi người đại diện mới' => 'Lỗi: YC không phải loại đổi người đại diện mới',
	'Lỗi không cập nhật được thông tin CCCD vào yêu cầu' => 'Lỗi không cập nhật được thông tin CCCD vào yêu cầu',
/*---------------------------/End luồng đổi thông tin V3---------------------*/

	/**
	 * @param Mpos360GetGroupWithUnreadItemsAction::class
	 */
	'Lỗi gọi lấy số lượng noti chưa đọc' => 'Mpos Err: Lỗi gọi lấy số lượng noti chưa đọc',
]; // end file