<?php

namespace App\Modules\Merchant\Actions\Notification\Mpos360ListTopicAction;

use App\Exceptions\BusinessException;
use Illuminate\Http\Request;
use App\Lib\partner\MposNotify;

class Mpos360ListTopicAction
{
	public MposNotify $mpos;

	public function __construct(MposNotify $mpos)
	{
		$this->mpos = $mpos;
	}

	public function run(Request $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();

		$merchantId = $deviceSession->getMerchantId();

		$listTopic = $this->mpos->getListTopicNotify($merchantId);

		if (!isset($listTopic['lstNotiTopic'])) {
			throw new BusinessException('Lỗi không lấy được danh sách topic');
		}
		
		return $listTopic;
	}
} // End class