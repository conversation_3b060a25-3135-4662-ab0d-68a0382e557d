<?php

namespace App\Modules\TingBoxVAMC\Requests\MerchantBank;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class ConfirmOtpBankRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
			'data.username' => ['present', 'string'],
      'data.merchantBankId' => ['required', 'integer'],
      'data.partnerRequestId' => ['required', 'string'],
      'data.requestId' => ['required', 'string'],
      'data.merchantId' => ['required', 'string'],
      'data.otp' => ['required', 'string'],
      'data.mobileUserId' => ['required', 'string'],
      'data.bankCode' => ['required', 'string'],
      'data.merchantShopBankIdAssign' => ['present', 'integer'],
      'data.action' => ['required', 'string', Rule::in(['CANCEL','LINK','REOPEN'])],
    ];
  }

  public function messages()
	{
		return [
			'data.merchantBankId.required' => 'Mã Id bank là bắt buộc',
			'data.merchantBankId.integer' => 'Mã Id bank phải là kiểu số',
			'data.partnerRequestId.required' => 'Mã tham chiếu đối tác là bắt buộc',
			'data.partnerRequestId.string' => 'Mã tham chiếu đối tác phải là kiểu chuỗi',
			'data.requestId.required' => 'Mã gửi đối tác là bắt buộc',
			'data.requestId.string' => 'Mã gửi đối tác phải là kiểu chuỗi',
			'data.merchantId.required' => 'Id merchant là bắt buộc',
			'data.merchantId.string' => 'Id merchant phải là kiểu chuỗi',
			'data.otp.required' => 'Mã OTP là bắt buộc',
			'data.otp.string' => 'Mã OTP phải là kiểu chuỗi',
      'data.mobileUserId.required' => 'Mã định danh mobile là bắt buộc',
			'data.mobileUserId.string' => 'Mã định danh mobile là kiểu chuỗi',
      'data.bankCode.required' => 'Mã ngân hàng là bắt buộc',
			'data.bankCode.string' => 'Mã ngân hàng là kiểu chuỗi',
			'data.merchantShopBankIdAssign.required' => 'Mã ngân hàng chuyển giao là bắt buộc',
			'data.merchantShopBankIdAssign.integer' => 'Mã ngân hàng chuyển giao là kiểu số',
      'data.action.required' => 'Hành động là bắt buộc',
			'data.action.string' => 'Hành động là kiểu chuỗi',
			'data.action.in' => 'Hành động không hợp lệ',
		];
	}
}
