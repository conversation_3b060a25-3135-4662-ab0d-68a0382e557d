<?php

/**
 * Trong version này có 1 số điều chỉnh sau
 * 1-<PERSON><PERSON>n hình Home thêm hướng dẫn sử dụng (VIẾT THÊM vào api cũ cũng được, ko ảnh hưởng)
 * 2-Lịch sử giao dịch cần rút gọn menu nhận tiền nhanh (1 màn hình 2 tabs, mỗi tab là 1 loại nhận tiền khác nhau)
 * 
 * Link figma chi tiết: 
 * https://www.figma.com/design/WA46KiUfjD53iyWNSAO3Hv/mPOS360_App?node-id=4192-9865&node-type=section&t=WYet7jEtb4V9a1x6-0
 * 
 * Link comment quan trọng: 
 * https://www.figma.com/design/WA46KiUfjD53iyWNSAO3Hv?node-id=4192-9865#1005849603
 */
use Illuminate\Support\Facades\Route;
use App\Http\Middleware\MakeSureThatRequestIsJsonMiddleware;
use App\Modules\Merchant\Controllers\Notification\Mpos360NotificationController;
use App\Modules\Merchant\Controllers\Transaction\V3P1\Mpos360TransactionController;


Route::group([
	'middleware' => [MakeSureThatRequestIsJsonMiddleware::class]
], function () {
	Route::any('v3.1/Mpos360TransactionHistory', [
		'uses' => Mpos360TransactionController::class . '@Mpos360TransactionHistory',
		'as' => 'Mpos360TransactionHistoryAction'
	])->middleware('validateHash:date');

	Route::any('v3.2/Mpos360TransactionHistory', [
		'uses' => Mpos360TransactionController::class . '@Mpos360TransactionHistoryForHome',
		'as' => 'Mpos360TransactionHistoryForHomeAction'
	])->middleware('checkSumForAnyMobile:merchantId');
});



