<?php

namespace App\Modules\TingBoxVAMC\Actions\SendOtpSwitchModeBankAction;

use App\Lib\Logs;
use App\Lib\Helper;
use App\Lib\partner\SoundBox;
use App\Lib\MnpOnboardNewMcHelper;
use App\Exceptions\BusinessException;
use App\Modules\TingBoxVAMC\Models\ActionDelays;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\SendOtpSwitchModeBankRequest;
use App\Modules\TingBoxVAMC\Actions\SendOtpSwitchModeBankAction\SubAction\CreateOtpDangKySubAction;
use App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360SendOtpAction\SubAction\SendOtpSubAction;
use App\Modules\TingBoxVAMC\Actions\SendOtpSwitchModeBankAction\SubAction\KiemTraDieuKienChuyenModeSubAction;

class SendOtpSwitchModeBankAction
{

	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run(SendOtpSwitchModeBankRequest $request)
	{
		Logs::writeInfo("SendOtpSwitchModeBankAction", $request->json('data'));
		
		$detailMc = [];

		if ($request->json('data.otpType') == 'CHUYEN_MODE') {
			$actionDelay = ActionDelays::query()->where('merchant_id', $request->json('data.merchantId'))
											->where('shop_id', $request->json('data.mobileUserId'))
											->where('action_type','send_otp_switch_mode')
											->where('scheduled_at','<',time())
											->where('executed_at','>',time())
											->where('is_active',1)->first();
											
			if ($actionDelay) {
				$totalMinutes = ($actionDelay->executed_at - time()) / 60;
				$msg = sprintf('Tính năng chuyển đổi hình thức nhận tiền hiện chưa hỗ trợ thay đổi liên tục. Vui lòng quay lại sau %s phút.', ceil($totalMinutes));
				throw new BusinessException($msg, 5009);
			}
		}
	
		/**
		 * Kiểm tra thông tin MC trường modeUsed để xem có thực hiện chặn chuyển mode hay không?
		 * App cũ không truyền merchantShopBankId
		 */
		$merchantShopBankId = $request->json('data.merchantShopBankId');
		if (!empty($merchantShopBankId)) {
			$msgError = 'Tài khoản của bạn hiện chưa được hỗ trợ chuyển đổi hình thức nhận tiền này! Vui lòng liên hệ CSKH theo số Hotline: 1900.63.64.88 để biết thêm chi tiết!';
			
			$mcTingBox = (new SoundBox())->getMcTingBox([
				"mcId" => $request->json('data.merchantId'),
				"muName" => $request->json('data.mobileUserId'),
				"partnerCode" => Helper::getPartnerCode($request->json('data.merchantId')),
			]);

			$config = $mcTingBox['data']['partnerConfigBankRes'];

			//$currentPartnerCode = Helper::getPartnerCode($request->json('data.merchantId'));

	
			if (empty($mcTingBox['data'])) {
				throw new BusinessException('Lỗi không tìm thấy thông tin merchant Tingbox');
			}

			if ($request->isChuyenModeVAMC()) {
				$mcShopBank = MerchantShopBank::query()->with('merchantBank')
																							 ->find($request->json('data.merchantShopBankId'));
				if (!$mcShopBank) {
					throw new BusinessException('Không tìm thấy thông tin liên kết ngân hàng', 5009);
				}

				app(KiemTraDieuKienChuyenModeSubAction::class)->run($mcShopBank);

				$configBank = collect($config)->whereIn('vaMode', ['VAMC', 'ALL'])
																			 ->pluck('bankCode')
																			 ->toArray();
				if (empty($configBank)) {
					throw new BusinessException($msgError, 5009);
				}

				if (in_array($mcShopBank->merchantBank->bank_code, $configBank) || in_array('ALL', $configBank)) {
					// pass
				}else {
					throw new BusinessException($msgError, 5009);
				}
			}

			if ($request->isChuyenModeVANP()) {
				$firstConfig = collect($config)->whereIn('vaMode', ['VANP', 'ALL'])
																			 ->first();
				if (empty($firstConfig)) {
					throw new BusinessException($msgError, 5009);
				}
			}

			if ($request->isSetMacDinh()) {
				$mcShopBank = MerchantShopBank::query()->with('merchantBank')
																							 ->find($request->json('data.merchantShopBankId'));
				if (!$mcShopBank) {
					throw new BusinessException('Không tìm thấy thông tin liên kết ngân hàng', 5009);
				}

				$configBank = collect($config)->whereIn('vaMode', ['VAMC', 'ALL'])
																			 ->pluck('bankCode')
																			 ->toArray();
				if (empty($configBank)) {
					throw new BusinessException($msgError, 5009);
				}

				if (in_array($mcShopBank->merchantBank->bank_code, $configBank) || in_array('ALL', $configBank)) {
					// pass
				}else {
					throw new BusinessException($msgError, 5009);
				}
			}
		}
		

		if(Helper::isUserNameEqualEmail($request->json('data.username'))){
			$detailMc = $this->mnpOnboardNewMcHelper->detailMcV2([
				'mposMcId' => $request->json('data.merchantId')
			]);

			if (empty($detailMc['result']) || $detailMc['code'] != 1000) {
				throw new BusinessException(
					'Lỗi không tìm thấy thông tin của tài khoản',
					$detailMc['code']
				);
			}
			$p = $request->all();
			$p['data']['username'] = isset($detailMc['data']) && isset($detailMc['data']['mobile']) ? $detailMc['data']['mobile'] : '';
			$request->merge($p);
		}

		if ($request->isTaoMoiOtpDangKy()) {
			$mpos360CodeOtp = app(CreateOtpDangKySubAction::class)->run($request, $request->json('data.otp_id'));
		}

		if ($request->isGuiLaiOtpDangKy()) {
			$mpos360CodeOtp = app(CreateOtpDangKySubAction::class)->run($request, $request->json('data.otp_id'));
		}

		$sendOtpResult = app(SendOtpSubAction::class)->run($request, $mpos360CodeOtp);

		return [
			'otp_id' => $mpos360CodeOtp->id,
			'channel' => $request->json('data.channel'),
			'username' => $request->json('data.username'),
			'countdown_time_get_new_otp' => $mpos360CodeOtp->time_out - time(),
			'other_data' => (object) []
		];
	}
}
