<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360ChangePasswordAction;

use Exception;
use App\Lib\partner\MPOS;
use App\Lib\PasswordHandler;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Setting;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360ChangePasswordRequest;

class Mpos360ChangePasswordAction
{  
	public MPOS $mpos;

	public function __construct(MPOS $mpos)
	{
		$this->mpos = $mpos;
	}

  public function run(Mpos360ChangePasswordRequest $request)
  {
		$deviceSession = $request->getCurrentDeviceSession();
		$newPassword = $request->json('data.password');
		
		$passwordRuleSetting = Setting::getPasswordRules();
		$passwordHandler = new PasswordHandler($passwordRuleSetting);
		$isSimplePassword = $passwordHandler->isSimplePassword($newPassword);

		if ($isSimplePassword) {
			throw new BusinessException('Mật khẩu của bạn quá đơn giản, hãy sử dụng khẩu khác');
		}

		$param = [
			'currentPass' => $request->json('data.current_password'),
			'newPass' => $newPassword,
			'confirmPass' => $newPassword,
			'merchantFk' => $deviceSession->getMerchantId(),
			'tokenLogin' => $deviceSession->getMposToken()
		];

		
		$doiMatKhau = $this->mpos->changePassword($param);
		
		$code = $doiMatKhau['data']['error']['code'] ?? [];

		if (empty($code) || $code != 1000) {
			throw new BusinessException('Mpos Err: ' . $doiMatKhau['message']);
		}

		mylog([
			'param doi mk' => $param,
			'doiMatKhau' => $doiMatKhau
		]);


    $returnData = [
			'email' => $request->json('data.email'),
			'is_changed_pwd' => 'SUCCESS',
			'can' => Mpos360Enum::CAN_GO_TO_LOGIN_SCREEN,
			'msg' => vmsg('DoiMatKhauMessageResultSuccess')
		];

		return $returnData;
  }
} // End class
