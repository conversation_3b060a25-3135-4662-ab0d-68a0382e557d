<?php

namespace App\Modules\Merchant\Actions\TingBox\Mpos360CheckProgressKhaiBaoTingBoxAction;

use App\Exceptions\BusinessException;
use App\Lib\MnpOnboardNewMcHelper;
use App\Modules\Merchant\Enums\TingTingEnum;
use App\Modules\Merchant\Requests\TingBox\Mpos360GetStepKhaiBaoTingBoxRequest;

class Mpos360CheckProgressKhaiBaoTingBoxAction
{
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run(Mpos360GetStepKhaiBaoTingBoxRequest $request)
	{
		$merchantId = $request->json('data.merchantId');

		$detailMc = $this->mnpOnboardNewMcHelper->detailMc(['mposMcId' => $merchantId]);

		// Mã lỗi MC từ xa xưa không onboard qua mpos360
		if (isset($detailMc['code']) && $detailMc['code'] == 1028) {
			$msg = 'Bạn là merchant đã thiết lập thông tin từ trước khi có tính năng này. Để khai báo thêm thiết bị TingBox, vui lòng liên hệ Sales hỗ trợ';
			throw new BusinessException($msg);
		}

		if (empty($detailMc['result']) || $detailMc['code'] != 1000) {
			throw new BusinessException($detailMc['message']);
		}

		// Chưa cung cấp CCCD
		if (empty($detailMc['data']['passport'])) {
			return [
				'can' => TingTingEnum::CAN_GO_TO_KHAI_BAO_CCCD,
				'isAllowHomeScreen' => 'NO'
			];
		}

		// Chưa có cửa hàng -> ko có nút bỏ qua
		if (empty($detailMc['data']['locations'])) {
			return [
				'can' => TingTingEnum::CAN_GO_TO_KHAI_BAO_MAN_HINH_3STEP,
				'isAllowHomeScreen' => 'NO'
			];
		}

		$firstLocation = $detailMc['data']['locations'][0];
		// Chưa có thiết bị tingbox -> không có nút bỏ qua
		if (empty($firstLocation['deviceDTOs'])) {
			return [
				'can' => TingTingEnum::CAN_GO_TO_KHAI_BAO_MAN_HINH_3STEP,
				'isAllowHomeScreen' => 'NO'
			];
		}

		return [
			'can' => TingTingEnum::CAN_GO_TO_KHAI_BAO_MAN_HINH_3STEP,
			'isAllowHomeScreen' => 'YES'
		];
	}
}
