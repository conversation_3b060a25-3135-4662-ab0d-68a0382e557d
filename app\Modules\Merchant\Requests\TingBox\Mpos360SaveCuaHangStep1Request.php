<?php

namespace App\Modules\Merchant\Requests\TingBox;

use Illuminate\Foundation\Http\FormRequest;

class Mpos360SaveCuaHangStep1Request extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data.username' => ['required', 'string'],
      'data.shopName' => ['required', 'string', 'min:3', 'max:255'],
			'data.qrDisplayName' => ['present', 'string', 'min:0', 'max:255'],
      'data.industryId' => ['required', 'string'],
      'data.cityId' => ['required', 'string'],
      'data.cityId' => ['required', 'string'],
      'data.districtId' => ['required', 'string'],
      'data.address' => ['required', 'string', 'min:3', 'regex:/^[a-zA-Z0-9\s\/,\x{00C0}-\x{1EF9}]*$/u', 'max:400'],
			'data.shopId' => ['present', 'string']
    ];
  }

	public function messages()
	{
		return [
			'data.username.required' => 'Username là bắt buộc',
			'data.username.string' => 'Username phải là kiểu chuỗi',

			'data.shopName.required' => 'Tên cửa hàng là bắt buộc',
			'data.shopName.string' => 'Tên cửa hàng phải là kiểu chuỗi',
			'data.shopName.min' => 'Tên cửa hàng phải có độ dài tối thiểu là 3 ký tự',
			'data.shopName.max' => 'Tên cửa hàng phải có độ dài tối đa là 255 ký tự',

			'data.qrDisplayName.required' => 'Tên mã QR cửa hàng là bắt buộc',
			'data.qrDisplayName.string' => 'Tên mã QR cửa hàng phải là kiểu chuỗi',
			'data.qrDisplayName.regex' => 'Tên mã QR cửa hàng phải đảm bảo: ký tự in hoa, ký tự số và khoảng trắng',
			'data.qrDisplayName.min' => 'Tên mã QR cửa hàng phải có độ dài tối thiểu là 3 ký tự',
			'data.qrDisplayName.max' => 'Tên mã QR cửa hàng phải có độ dài tối đa là 255 ký tự',

			'data.industryId.required' => 'Ngành nghề kinh doanh là bắt buộc',
			'data.industryId.string' => 'Ngành nghề kinh doanh phải là kiểu chuỗi',
			
			'data.cityId.required' => 'Tỉnh/Thành phố là bắt buộc',
			'data.cityId.string' => 'Tỉnh/Thành phố phải là kiểu chuỗi',
			
			'data.districtId.required' => 'Quận/huyện là bắt buộc',
			'data.districtId.string' => 'Quận/huyện phải là kiểu chuỗi',

			'data.address.required' => 'Địa chỉ kinh doanh là bắt buộc',
			'data.address.string' => 'Địa chỉ kinh doanh phải là kiểu chuỗi',
			'data.address.min' => 'Địa chỉ kinh doanh phải có độ dài tối thiểu là 3 ký tự',
			'data.address.max' => 'Địa chỉ kinh doanh phải có độ dài tối đa là 400 ký tự',
			'data.address.regex' => 'Địa chỉ kinh doanh chỉ được chứa chữ, số, dấu phẩy và dấu xẹt. Không dùng ký tự như - _ | hoặc dấu chấm',
		];
	}

	protected function passedValidation()
	{
		$params = $this->all();
		$params['data']['shopName'] = cleanXSS($params['data']['shopName']);
		$params['data']['qrDisplayName'] = cleanXSS($params['data']['qrDisplayName']);
		$params['data']['address'] = cleanXSS($params['data']['address']);
		$this->merge($params);
	}

	public function isTaoMoiCuaHang(): bool {
		return empty($this->json('data.shopId'));
	}

	public function isSuaThongTinCuaHang(): bool {
		return !$this->isTaoMoiCuaHang();
	}
} // End clas
