<?php

namespace App\Modules\Merchant\DTOs\RequestChangeInfo;

use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Mpos360ChungThuc;
use Carbon\Carbon;

class DetailQtsResultDto
{
	// qts request id
	public string $requestId;

	// số cccd hiện tại
	public string $currentCardId;

	// số cccd/cmtnd cũ
	public ?string $oldCardId;

	// ngày hết hạn cccd hiện tại
	public Carbon $currentCardDate;

	// result full
	public $result;

	public string $fullName;

	public function __construct(array $result)
	{
		$this->result = $result;

		$this->requestId = '';
		$this->currentCardId = '';
		$this->oldCardId = '';
		$this->currentCardDate = now();
		$this->fullName = '';

		$this->setDataFromResult($result);
	}

	public function setDataFromResult($result)
	{
		$resultAsArray = json_decode($result['data'], true);


		if (empty($resultAsArray)) {
			throw new BusinessException('Err: Qts has return empty value');
		}

		if (!empty($resultAsArray)) {
			$this->requestId = $resultAsArray['requestId'];
			$this->currentCardId = $resultAsArray['newCardId'];
			$this->oldCardId = $resultAsArray['oldCardId'];
			
			if ($resultAsArray['cardDetailInfo']['dateOfExpiry'] == 'Không thời hạn') {
				$this->currentCardDate = now()->addYears(10); // khong thoi han thi add 10 nam
			}else {
				$this->currentCardDate = Carbon::createFromFormat(
					'd/m/Y',
					$resultAsArray['cardDetailInfo']['dateOfExpiry']
				);
			}
			

			$response = json_decode($resultAsArray['response'], true);
			$this->fullName = $response['full_name'];

			mylog([
				'detailQtsResultDto' => [
					'requestId' => $this->requestId,
					'currentCardId' => maskCreditCard($this->currentCardId),
					'oldCardId' => maskCreditCard($this->oldCardId),
					'currentCardDate' => $this->currentCardDate,
					'fullName' => $this->fullName
				]
			]);
		}
	}

	public function isHetHanCccd(): bool
	{
		return now()->gt($this->currentCardDate) && !now()->isSameDay($this->currentCardDate);
	}

	public function isMatchingChungThuc(Mpos360ChungThuc $mpos360ChungThuc)
	{
		$cccdChungThuc = $mpos360ChungThuc->getOriginalCCCD();
		mylog(['CCCD o bang chung thuc la' => maskCreditCard($cccdChungThuc)]);
		return $cccdChungThuc == $this->currentCardId || $cccdChungThuc == $this->oldCardId;
	}

	/**
	 *  'representName',
			'typeChangeRepresent',

			'representPassport',
			'representIssuePlace', // chua ho tro
			'representEmail', // ko su dung
			'representMobile', // khong su dung

			'representBirthday',
			'representCurrentAddress',
			'representPermanentAddress',

			"representPosition", // chuc vu
			"representMutualRelation", // moi quan he

			"representAuthContractNumber", //phu
			"passportRepresentFrontUrl", //phu
			"passportRepresentBackUrl" //phu
	 */
	public function mapQtsDataAsNguoiDaiDienProfile(bool $isYeuCauDoiNguoiDaiDienMoi) {
		$resultAsArray = json_decode($this->result['data'], true);
		
		// nguoi dai dien
		$representName = $resultAsArray['cardHolderName'];

		// typeChangeRepresent
		$typeChangeRepresent = $isYeuCauDoiNguoiDaiDienMoi ? 'CHANGE_NEW_REPRESENT' : 'CHANGE_CURRENT_REPRESENT_INFO';

		// cccd moi
		$representPassport = $resultAsArray['cardID'];

		// Nơi cấp hộ chiếu của người đại diện mới. -> đang không hỗ trợ
		$representIssuePlace = 'Cục cảnh sát quản lý hành chính về trật tự xã hội';

		// Ngày/tháng/năm sinh
		$representBirthday = Carbon::createFromFormat('d/m/Y', $resultAsArray['cardDetailInfo']['dateOfBirth'])->valueOf();

		// dia chi hien tai
		$representCurrentAddress = $resultAsArray['cardDetailInfo']['placeOfOrigin'];

		// dia chi thuong chu
		$representPermanentAddress = $resultAsArray['cardDetailInfo']['placeOfResidence'];

		return [
			'representName'             => $representName,
			'typeChangeRepresent'       => $typeChangeRepresent,
			'representPassport'         => $representPassport,
			'representBirthday'         => $representBirthday,
			'representCurrentAddress'   => $representCurrentAddress,
			'representPermanentAddress' => $representPermanentAddress,
			'representIssuePlace' => $representIssuePlace
		];
	}	

	public function getSafeInfo(): array {
		return [
			'requestId' => $this->requestId,
			'currentCardId' => $this->currentCardId,
			'oldCardId' => $this->oldCardId,
			'currentCardDate' => $this->currentCardDate,
		];
	}
} // End class
