<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoStep3HandlerAction;

use Exception;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360ChungThuc;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoStep3HandlerRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentRequest;

class Mpos360RequestChangeInfoStep3HandlerAction
{
	public function run(Mpos360RequestChangeInfoStep3HandlerRequest $request)
	{
		$scanMethod = $request->json('data.scan_method');
		$id = $request->json('data.id');
		$mpos360MerchantRequest = Mpos360MerchantRequest::query()->find($id);

		if (!$mpos360MerchantRequest) {
			throw new BusinessException('Không tìm được bản ghi yêu cầu thay đổi');
		}

		$deviceSession = $request->getCurrentDeviceSession();
		if ($mpos360MerchantRequest->merchant_id != $deviceSession->getMerchantId()) {
			throw new BusinessException('Lỗi: không khớp thông tin mc');
		}

		// Đánh dấu phương thức quét bước 3
		$dataRequest = json_decode($mpos360MerchantRequest->data_request, true);
		$dataRequest[0]['scan_method'][$scanMethod] = [
			'other_data' => (object) [],
			'status' => 'NEW',
		];

		$mpos360MerchantRequest->data_request = json_encode($dataRequest);
		$mpos360MerchantRequest->method_code = $scanMethod;
		$r = $mpos360MerchantRequest->save();

		if (!$r) {
			throw new BusinessException('Lỗi: không cập nhật được phương thức scan');
		}


		$returnData = [
			'id' => $id,
			'scan_method' => $scanMethod,
			'can' => 'UNKNOW',
			'other_data' => []
		];

		if ($scanMethod == 'QTS') {
			$returnData['can'] = Mpos360Enum::MPOS360_CAN_STEP3_QTS;
			$mpos360ChungThuc = Mpos360ChungThuc::getChungThucCCCD($deviceSession->getMerchantId());
			$returnData['other_data']['qts_request_id'] = $mpos360ChungThuc->qts_request_id;
		}

		if ($scanMethod == 'SMS') {
			// gui otp
			$returnData['can'] = Mpos360Enum::MPOS360_CAN_STEP3_SMS_OTP;
		}

		if ($scanMethod == 'EMAIL') {
			// gui otp
			$returnData['can'] = Mpos360Enum::MPOS360_CAN_STEP3_EMAIL_OTP;
		}

		if ($scanMethod == 'ZALO') {
			// gui otp
			$returnData['can'] = Mpos360Enum::MPOS360_CAN_STEP3_ZALO_OTP;
		}

		if ($scanMethod == 'SDK') {
			$returnData['can'] = Mpos360Enum::MPOS360_CAN_STEP3_SDK_HANDLER;
		}

		$returnData['other_data'] = (object) $returnData['other_data'];
		return $returnData;
	}
} // End class
