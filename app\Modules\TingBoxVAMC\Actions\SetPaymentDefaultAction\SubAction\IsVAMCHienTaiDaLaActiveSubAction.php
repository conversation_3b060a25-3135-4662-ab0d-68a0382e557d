<?php

namespace App\Modules\TingBoxVAMC\Actions\SetPaymentDefaultAction\SubAction;

use App\Modules\TingBoxVAMC\Models\MerchantShopBank;

class IsVAMCHienTaiDaLaActiveSubAction
{
	public function run(MerchantShopBank $mcShopBank, $mcTingBox = []): bool
	{
		if (empty($mcTingBox['data']['mcQrDefault'])) {
			return false;
		}

		$item = $mcTingBox['data']['mcQrDefault'];

		return $item['integratedMethod'] == 'VAMC' 
					 && $item['status'] == 'ACTIVE'
					 && isset($item['state']) && $item['state'] == 'DEFAULT'
					 && $mcShopBank->partner_request_id == $item['vaNextPayNumber'];
	}
}
