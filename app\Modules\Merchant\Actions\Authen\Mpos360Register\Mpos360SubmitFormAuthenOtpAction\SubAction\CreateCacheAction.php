<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360SubmitFormAuthenOtpAction\SubAction;

use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\CacheAction;

class CreateCacheAction
{
	public function run($merchantId): CacheAction {
	
		$cacheAction = CacheAction::query()->forceCreate([
			'step' => 1,
			'reference_id' => $merchantId,
			'time_created' => now()->timestamp,
		]);

		if (!$cacheAction) {
			throw new BusinessException('Lỗi không tạo được bản ghi lưu trữ hành động');
		}


		return $cacheAction;
	}
} // End clas