<?php

namespace App\Modules\Merchant\Requests\TingBox\Web;

use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Model\Mpos360User;
use Illuminate\Foundation\Http\FormRequest;

class Mpos360TingBoxKhaiBaoListInfoRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'merchantId' => ['required', 'string'],
    ];
  }
}
