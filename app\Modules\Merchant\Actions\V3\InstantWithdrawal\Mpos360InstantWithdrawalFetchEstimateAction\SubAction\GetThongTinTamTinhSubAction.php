<?php

namespace App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalFetchEstimateAction\SubAction;

use App\Lib\partner\MPOS;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\DeviceSession;
use Exception;

class GetThongTinTamTinhSubAction
{
	public MPOS $mpos;

	public function __construct(MPOS $mpos)
	{
		$this->mpos = $mpos;
	}

	public function run(DeviceSession $deviceSession)
	{
		$params = [
			'serviceName' => 'PAYMENT_NOW_REQUEST_PREVIEW',
			'tokenLogin' => $deviceSession->getMposToken(),
			'merchantFk' => $deviceSession->getMerchantId()
		];

		$result = $this->mpos->paymentNowPreview($params);

		return $result;
	}
} // End class