<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthVerifyOtpForgotPasswordV4Action;

use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Enums\OtpResCodeEnum;
use App\Modules\Merchant\Model\Mpos360CodeOtp;
use App\Modules\Merchant\Requests\Authen\Mpos360\V4\Mpos360AuthVerifyOtpForgotPasswordV4Request;

class Mpos360AuthVerifyOtpForgotPasswordV4Action
{
	public function run(Mpos360AuthVerifyOtpForgotPasswordV4Request $request)
	{
		$mpos360CodeOtp = Mpos360CodeOtp::query()->firstWhere([
			'command_code' => 'FORGOT_PASSWORD',
			'id' => $request->json('data.otp_id'),
			'otp' => $request->json('data.otp'),
		]);

		$message = 'Mã OTP không chính xác hoặc đã hết thời gian hiệu lực. Xin vui lòng kiểm tra lại mã trong tin nhắn hoặc yêu cầu một mã mới';

		if (!$mpos360CodeOtp) {
			throw new BusinessException($message, OtpResCodeEnum::OTP_ERR_KHONG_TIM_THAY);
		}

		if ($mpos360CodeOtp->isExpiredOtp()) {
			throw new BusinessException($message, OtpResCodeEnum::OTP_ERR_HET_HAN);
		}

		if ($mpos360CodeOtp->isFinalStatus()) {
			throw new BusinessException($message, OtpResCodeEnum::OTP_ERR_DA_DUOC_SU_DUNG);
		}

		$mpos360CodeOtp->status = Mpos360Enum::MPOS360_OTP_DA_SU_DUNG;
		$mpos360CodeOtp->time_updated = now()->timestamp;
		$r = $mpos360CodeOtp->save();

		if (!$r) {
			throw new BusinessException('Lỗi không cập nhật được otp là đã hoàn thành', OtpResCodeEnum::OTP_ERR_LOI_CAP_NHAT);
		};

		return [
			'username' => $request->json('data.username'),
			'status' => 'SUCCESS',
			'msg' => 'Otp hợp lệ, bạn có thể đổi mật khẩu',
			'otp_id' => $mpos360CodeOtp->id,
		];
	}
} // End class
