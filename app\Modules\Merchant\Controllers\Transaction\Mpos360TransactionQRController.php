<?php

namespace App\Modules\Merchant\Controllers\Transaction;

use App\Lib\Helper;

use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionQRListRequest;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransQRListAction\Mpos360TransQRListAction;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransQRDetailAction\Mpos360TransQRDetailAction;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransQRListRequest;
use Illuminate\Http\Request;

class Mpos360TransactionQRController extends Controller
{
    public function Mpos360TransactionQRList(Mpos360TransQRListRequest $request)
    {
        try {
            $result = app(Mpos360TransQRListAction::class)->run($request);
            return $this->successResponse($result, $request);
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getCode(), Helper::traceError($e));
        }
    }

    public function Mpos360TransactionQRDetail(Request $request)
    {
        try {
            $result = app(Mpos360TransQRDetailAction::class)->run($request);
            return $this->successResponse($result, $request);
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getCode(), Helper::traceError($e));
        }
    }
}
