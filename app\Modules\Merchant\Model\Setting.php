<?php

namespace App\Modules\Merchant\Model;

use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;

class Setting extends Model
{
	protected $table = 'setting';
	protected $guarded = [];
	public $timestamps = false;

	protected $dates = [];

	protected $hidden = [];

	public function isThayDoiMajor() {}

	public function isThayDoiMinor() {}

	public function isThayDoiPath() {}

	public function isThayDoiBuildNumber() {}

	public function getCauHinhMobileApp(string $os = 'ANDROID')
	{
		if (!in_array($os, ['ANDROID', 'IOS'])) {
			$os = 'OTHER';
		}

		$value = json_decode($this->value, true);
		$settingViaOs = $value['os'][$os];
		return $settingViaOs;
	}

	public function getPhienBanMoiNhat(string $os = 'ANDROID')
	{
		$settingViaOs = $this->getCauHinhMobileApp($os);
		$latestVersion = $settingViaOs['latest_version'];
		
		$latestVersionExplode = explode('+', $latestVersion);
		$majorWithMinorAndPath = explode('.', Arr::first($latestVersionExplode));
		
		$returnData = [
			'major' => $majorWithMinorAndPath[0],
			'minor' => $majorWithMinorAndPath[1],
			'path' => $majorWithMinorAndPath[2],
			'buildNumber' => Arr::last($latestVersionExplode),
		];
		
		return $returnData;
	}

	public static function getPasswordRules(): array {
		$setting = self::query()->where('key', 'PASSWORD_RULES')->first();
		if (!$setting) {
			return [];
		}

		$value = json_decode($setting->value, true);
		$rules = collect($value)->map(function ($item, $passwordRule) {
			if ($passwordRule == 'allowedSymbols') {
				$item = explode(',', $item);
			}

			return $item;
		})
		->filter(function ($item) {
			return $item != 'NONE';
		})
		->all();

		return $rules;
	}
} // End class
