# Hướng dẫn cài đặt PHPDesigner Theme

## Cách cài đặt nhanh nhất

### Bước 1: Copy theme vào VS Code
```bash
# Tìm thư mục extensions của VS Code
# Windows: %USERPROFILE%\.vscode\extensions\
# macOS: ~/.vscode/extensions/
# Linux: ~/.vscode/extensions/

# Copy toàn bộ thư mục phpdesigner-theme vào đó
```

### Bước 2: Restart VS Code
Đóng và mở lại VS Code để theme được load.

### Bước 3: Kích hoạt theme
1. Nhấn `Ctrl+Shift+P` (Windows/Linux) hoặc `Cmd+Shift+P` (macOS)
2. G<PERSON> "Color Theme" và chọn "Preferences: Color Theme"
3. Chọn "PHPDesigner Default" từ danh sách

## Kiểm tra cài đặt

Sau khi cài đặt, bạn sẽ thấy:
- ✅ Editor có nền trắng
- ✅ Sidebar màu xám nhạt
- ✅ PHP code có syntax highlighting màu sắc giống PHPDesigner
- ✅ Comments màu xanh lá, strings màu tím
- ✅ Keywords màu xanh dương và in đậm

## Nếu gặp lỗi

### Theme không xuất hiện trong danh sách
- Kiểm tra lại đường dẫn thư mục extensions
- Đảm bảo file `package.json` có trong thư mục theme
- Restart VS Code

### Màu sắc không đúng
- Đảm bảo đã chọn đúng theme "PHPDesigner Default"
- Kiểm tra file `phpdesigner-default.json` có đầy đủ nội dung

### Muốn tùy chỉnh màu sắc
Bạn có thể chỉnh sửa file `themes/phpdesigner-default.json` để thay đổi màu sắc theo ý muốn.

## Gỡ cài đặt

Để gỡ theme, chỉ cần xóa thư mục `phpdesigner-theme` khỏi thư mục extensions và restart VS Code.
