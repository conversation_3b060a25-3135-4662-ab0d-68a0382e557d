<?php

namespace App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360GetStepKhaiBaoDiemBanAction;

use App\Lib\Helper;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use App\Lib\Mpos360UrlHelper;
use App\Lib\partner\SoundBox;
use App\Lib\MnpOnboardNewMcHelper;
use App\Exceptions\BusinessException;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use App\Modules\TingBoxVAMC\Requests\DiemBan\Mpos360GetStepKhaiBaoDiemBanRequest;
use App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360ListDiemBanAction\SubAction\LoaiBoMobileUserNullSubAction;
use App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360ListDiemBanAction\SubAction\DongBoTknhTrungGianSubAction\DongBoTknhSubAction;

class Mpos360GetStepKhaiBaoDiemBanAction
{
	const CAN_STAY_HERE = 'CAN_STAY_HERE';
	const CAN_GOTO_LIST_DIEMBAN = 'CAN_GOTO_LIST_DIEMBAN';
	const CAN_GOTO_HOME = 'CAN_GOTO_HOME';

	public array $listMuIds = [];

	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}
	
	public function run(Mpos360GetStepKhaiBaoDiemBanRequest $request)
	{
		$returnData = [
			'can' => self::CAN_STAY_HERE,
			'shopInfo' => (object) [],
			'bankMerchantInfo' => (object) [],
			'title' => '',
			'steps' => [
				[
					'stepNumber' => '1',
					'stepCode' => 'KhaiBaoStkNhanTien',
					'stepAction' => 'YES',
					'name' => 'Khai báo tài khoản nhận tiền',
					'active' => 'YES',
					'values' => [],
					'listBank' => (object) []
				],

				[
					'stepNumber' => '2',
					'stepCode' => 'KhaiBaoCuaHang',
					'stepAction' => 'YES',
					'name' => 'Khai báo cửa hàng',
					'active' => 'NO',
					'values' => [],
					'listBank' => (object) []
				],
				
				[
					'stepNumber' => '3',
					'stepCode' => 'GanLoaTingBox',
					'stepAction' => 'YES',
					'name' => 'Gán loa TingBox',
					'active' => 'NO',
					'values' => [],
					'listBank' => (object) []
				],
			],
			'other_data' => (object) []
		];

		$detailMc = $this->mnpOnboardNewMcHelper->detailMcV2(['mposMcId' => $request->json('data.merchantId')]);
		
		if (empty($detailMc['data'])) {
			throw new BusinessException('Lỗi không tìm thấy thông tin MC');
		}

		$detailMc = app(LoaiBoMobileUserNullSubAction::class)->run($detailMc);
		
		$holderName = $detailMc['data']['passportObject']['customerName'] ?? $detailMc['data']['holderName'] ?? ''; 
		
		$returnData['bankMerchantInfo'] = [
			'bankAccountName' => Str::of($holderName)->slug(' ')->title()->trim()->__toString(),
			'bankAccountNo' => '',
			'cccdNo' => $detailMc['data']['passportObject']['passport'],
			'mobileNo' => Helper::isUserNameEqualEmail($detailMc['data']['username']) ? '' : $detailMc['data']['username'],
			'mobileUserId' => '',
			'merchantId' => $request->json('data.merchantId')
		];

		$listMU = $this->getMobileUserIdFromDetail($detailMc);
		
		if (empty($listMU)) {
			throw new BusinessException('Lỗi không tìm thấy thông tin cửa hàng');
		}

		// Nhiều hơn 1 mobileUser
		if (count($listMU) > 1 && empty($request->json('data.muId'))) {
			$returnData['can'] =  self::CAN_GOTO_LIST_DIEMBAN;
			return $returnData;
		}

		$returnData['bankMerchantInfo']['mobileUserId'] = !empty($request->json('data.muId')) ? $request->json('data.muId') : $listMU[0];

		if (!empty($detailMc['data']['bankId'])) {
			$returnData['steps'] = collect($returnData['steps'])->map(function ($item) use ($detailMc) {
				if ($item['stepNumber'] == '1') {
					$item['icon'] = cumtomAsset('images/tingbox/stepKhaiBao/stepActive.svg');
					$item['active'] = 'YES';
					$item['stepAction'] = 'NO';
					$item['values'] = [
						[
							'title' => $detailMc['data']['accountNo'],
							'desc' => sprintf('%s%s%s', $detailMc['data']['holderName'], PHP_EOL, $detailMc['data']['bankName']),
							'icon' => '',
							'other_data' => (object) []
						]
					];
				}
				return $item;
			})->values()->all();
		}

		if (!empty($detailMc['data']['locations'])) {
			if (count($detailMc['data']['locations']) == 1) {
				$defaultLocation = $detailMc['data']['locations'][0];
			}else {
				$defaultLocation = collect($detailMc['data']['locations'])->first(function ($l) use ($request) {
					$listMu = Arr::pluck($l['deviceDTOs'], 'mobileUserId');
					return in_array($request->json('data.muId'), $listMu);
				});
			}
			
			
			
			if (empty($defaultLocation)) {
				return $returnData;
			}
			
			$returnData['shopInfo'] = [
				'shopId' => $defaultLocation['id'],
				'shopName' => $defaultLocation['areaName'],
				'address' => $defaultLocation['areaAddress'],
				'cityId' => $defaultLocation['areaCityCode'],
				'districtId' => $defaultLocation['areaDistrictCode'],
				'industryId' => $defaultLocation['mcc'],
				'qrDisplayName' => $detailMc['data']['qrDisplayName'] ?? ''
			];

			// Bắt có thiết bị hay không
			$listTingBoxDevice = collect($defaultLocation['deviceDTOs'])->filter(function ($dv) {
				return !empty($dv['serialNumber']);
			})->toArray();

			$returnData['steps'] = collect($returnData['steps'])->map(function ($item) use ($defaultLocation, $listTingBoxDevice) {
				if ($item['stepNumber'] == '2' && !empty($defaultLocation['id'])) {
					$item['active'] = 'YES';
					$item['values'] = [
						[
							'title' => $defaultLocation['areaName'],
							'desc' => sprintf('%s, %s, %s', $defaultLocation['areaAddress'], $defaultLocation['areaDistrict'], $defaultLocation['areaCity']),
							'icon' => '',
							'other_data' => (object) []
						]
					];

					// Kiểm tra xem đã có thiết bị hay chưa? Có rồi thì sẽ không có action Sửa nữa
					if (!empty($listTingBoxDevice)) {
						$item['stepAction'] = 'NO';
					}
				}
				return $item;
			})->values()->all();

			
			
			if (!empty($listTingBoxDevice) && !empty($defaultLocation['id'])) {
				$stepValues = [];

				foreach ($listTingBoxDevice as $dv) {
					$url = Mpos360UrlHelper::buildTingBoxDetailUrl('Mpos360TingBoxDeviceHuongDanSuDungAction', $dv);
					$stepValues[] = [
						'title' => $dv['serialNumber'],
						'desc' => $dv['status'] == 'ACTIVE' ? 'Đã kích hoạt' : 'Đang kích hoạt',
						'icon' => Mpos360UrlHelper::getTingBoxThumbnail($dv['serialNumber']),
						'other_data' => [
							'desc' => [
								'text_color' => $dv['status'] == 'ACTIVE' ? '#73ae4a' : '#fdb62f'
							],
							'title' => [
								'webviewUrl' => Mpos360UrlHelper::convertToHttps($url)
							]
						],
					];
				}

				$returnData['steps'] = collect($returnData['steps'])->map(function ($item) use ($stepValues) {
					if ($item['stepNumber'] == '3') {
						$item['icon'] = cumtomAsset('images/tingbox/stepKhaiBao/stepActive.svg');
						$item['active'] = 'YES';
						$item['values'] = $stepValues;
					}

					return $item;
				})->values()->all();
			}
		}

		$listBank['direct'] = [];
		$listBank['inter'] = [];

		// Đang có địa điểm
		if (!empty($detailMc['data']['locations'])) {
			// Xử lý banking mặc định
			$listMobileUserIds = Arr::pluck($defaultLocation['deviceDTOs'], 'mobileUserId');
			
			$listMerchantShopBank = MerchantShopBank::query()
																							->with(['merchantBank', 'switching'])
																							->whereIn('shop_id', $listMobileUserIds)
																							->get();

			if ($listMerchantShopBank->isNotEmpty()) {
				$listBank['direct'] = $listMerchantShopBank->filter(function (MerchantShopBank $mcShopBank) {
					return $mcShopBank->account_type == MerchantShopBank::LOAI_TK_TRUC_TIEP && $mcShopBank->isDaLienKet() && $mcShopBank->isDongBoSoundBox();
				})->map(function ($mcShopBank) {
					return [
						'merchantShopBankId' => $mcShopBank->id,
						'mobileUserId' => $mcShopBank->shop_id,
						'merchantBankId' => $mcShopBank->merchantBank->id,
						'account_number' => $mcShopBank->merchantBank->account_number, 
						'account_holder' => $mcShopBank->merchantBank->account_holder, 
						'account_qr'  => $mcShopBank->account_qr,
						'account_qr_display' => $mcShopBank->account_qr_display,
						'status' => $mcShopBank->getStatusLienKetForMobile(),
						'bank_branch' => $mcShopBank->merchantBank->bank_branch,
						'bank_code' =>  $mcShopBank->merchantBank->bank_code,
						'bank_id' => $mcShopBank->merchantBank->id,
						'request_id' => $mcShopBank->request_id,
						'partner_request_id' => $mcShopBank->partner_request_id,
						'is_default' => 'NO',
						'bank_icon' =>  $mcShopBank->merchantBank->getBankIconUrl(),
					];
				})->values()
				  ->toArray();			
			}
		}

		
		if (empty($detailMc['data']['locations'])) {
			$bankNameExplode = explode(' - ', $detailMc['data']['bankName']);

		
			$listBank['inter'] = [
				[
					'account_number' => $detailMc['data']['accountNo'], 
					'account_holder' => $detailMc['data']['holderName'],
					'account_qr'  => '',
					'account_qr_display' => '',
					'status' => MerchantShopBank::DA_LIEN_KET,
					'bank_branch' => '',
					'bank_code' =>  trim(Arr::first($bankNameExplode)),
					'bank_id' => '',
					'request_id' => '',
					'partner_request_id' => '',
					'is_default' => 'YES'
				]
			];
		}

		if (!empty($detailMc['data']['bankId'])) {
			$bankName = isset($detailMc['data']) && isset($detailMc['data']['bankName']) ? $detailMc['data']['bankName'] : '';
			$bankCode = explode(' - ',$bankName)[0];

			$listBank['inter'][] = [
				'merchantShopBankId' => '-1',
				'mobileUserId' => $returnData['bankMerchantInfo']['mobileUserId'],
				'merchantBankId' => '-1',
				'account_number' => isset($detailMc['data']) && isset($detailMc['data']['accountNo']) ? $detailMc['data']['accountNo'] : '', 
				'account_holder' => isset($detailMc['data']) && isset($detailMc['data']['holderName']) ? $detailMc['data']['holderName'] : '', 
				'account_qr'  => '',
				'account_qr_display' => isset($detailMc['data']) && isset($detailMc['data']['qrDisplayName']) ? $detailMc['data']['qrDisplayName'] : '',
				'status' => 'DA_LIEN_KET',
				'bank_branch' => '',
				'bank_code' =>  $bankCode,
				'bank_id' => isset($detailMc['data']) && isset($detailMc['data']['bankId']) ? $detailMc['data']['bankId'] : '',
				'request_id' => '',
				'partner_request_id' => '',
				'is_default' => 'NO',
				'bank_icon' => 'https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/test/67b5971c4465eb435f2991c5IconBank.png',
			];								
		}

		$returnData['steps'][0]['listBank'] =  (object) [];
		
		$getMcTingBoxDetail = (new SoundBox())->getMcTingBox([
			"mcId" => $returnData['bankMerchantInfo']['merchantId'], // mcId
			"muName" => $returnData['bankMerchantInfo']['mobileUserId'], // muId / muName
			"partnerCode" => Helper::getPartnerCode($returnData['bankMerchantInfo']['merchantId']), 
		]);

		if (!empty($listBank)) {
			if (!empty($getMcTingBoxDetail['data']['currentMode']) && $getMcTingBoxDetail['data']['currentMode'] == 'VANP' && !empty($listBank['inter'])) {
				$listBank['inter'][0]['is_default'] = 'YES';
			}

			$returnData['steps'][0]['listBank'] = $listBank;
		}

		if (empty($getMcTingBoxDetail['data'])) {
			throw new BusinessException('Không tìm thấy thông tin MC Tingbox');
		}

		if (!empty($getMcTingBoxDetail['data']['mcQr'])) {
			foreach ($getMcTingBoxDetail['data']['mcQr'] as $qr) {
				if ($qr['integratedMethod'] == 'VAMC') {
					foreach ($returnData['steps'] as &$step) {
						if ($step['stepCode'] == 'KhaiBaoStkNhanTien') {
							foreach ($step['listBank']['direct'] as &$b) {
								if ($b['partner_request_id'] == $qr['vaNextPayNumber'] && isset($qr['state']) && $qr['state'] == 'DEFAULT') {
									$b['is_default'] = 'YES';
								}
							}
						}
					}
				}
			}
		}

		return $returnData;
	} // End method

	public function getMobileUserIdFromDetail($detailMc) {
		$listMobileUser = [];
		$listLocation = $detailMc['data']['locations'];

		foreach ($listLocation as $location) {
			if (!empty($location['deviceDTOs'])) {
				foreach ($location['deviceDTOs'] as $mu) {
					$listMobileUser[] = $mu['mobileUserId'];
				}
			}
		}

		$listMobileUser = array_unique($listMobileUser);
		
		return $listMobileUser;
	}

	public function isTonTaiSerialThietBi($detailMc) {
		$listLocation = $detailMc['data']['locations'];

		foreach ($listLocation as $location) {
			if (!empty($location['deviceDTOs'])) {
				foreach ($location['deviceDTOs'] as $mu) {
					if (!empty($mu['serialNumber'])) {
						return true;
					}
				}
			}
		}

		return false;
	}
} // End class
