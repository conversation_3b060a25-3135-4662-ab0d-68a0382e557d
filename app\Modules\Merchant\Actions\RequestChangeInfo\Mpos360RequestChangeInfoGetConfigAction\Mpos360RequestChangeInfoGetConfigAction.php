<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoGetConfigAction;

use App\Lib\Helper;
use Exception;
use App\Lib\partner\MNP;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoGetConfigRequest;

/**
 * array:3 [
  "status" => true
  "data" => array:12 [
    "typeChangeInfo" => array:2 [
      0 => array:2 [
        "code" => "CHANGE_BANK_ACCOUN_INFO"
        "name" => "Thay đổi thông tin tài khoản ngân hàng nhận tiền"
      ]
      1 => array:2 [
        "code" => "CHANGE_REPRESENT_INFO"
        "name" => "Thay đổi thông tin người đại diện kí hợp đồng với bên VIMO"
      ]
    ]
    "documentLost" => array:3 [
      0 => array:2 [
        "code" => "LOST_PASSPORT"
        "name" => "MC không còn giữ CMT/CCCD cũ"
      ]
      1 => array:2 [
        "code" => "LOST_MOBILE"
        "name" => "MC không còn giữ số điện thoại cũ"
      ]
      2 => array:2 [
        "code" => "LOST_EMAIL"
        "name" => "MC không còn giữ email cũ"
      ]
    ]
    "cities" => array:63 [
      "vn-lan" => "Long An"
      "vn-hag" => "Hậu Giang"
      "vn-nan" => "Nghệ An"
      "vn-kha" => "Khánh Hòa"
      "vn-ktm" => "Kon Tum"
      "vn-qti" => "Quảng Trị"
      "vn-stg" => "Sóc Trăng"
      "vn-tvh" => "Trà Vinh"
      "vn-agg" => "An Giang"
      "vn-sla" => "Sơn La"
      "vn-hyn" => "Hưng Yên"
      "vn-tnh" => "Tây Ninh"
      "vn-nbh" => "Ninh Bình"
      "vn-tnn" => "Thái Nguyên"
      "vn-tbh" => "Thái Bình"
      "vn-blu" => "Bạc Liêu"
      "vn-hdg" => "Hải Dương"
      "vn-cbg" => "Cao Bằng"
      "vn-bdh" => "Bình Định"
      "vn-bdg" => "Bình Dương"
      "vn-dlk" => "Đắk Lắk"
      "vn-lsn" => "Lạng Sơn"
      "vn-dtp" => "Đồng Tháp"
      "vn-hpg" => "Hải Phòng"
      "vn-btn" => "Bình Thuận"
      "vn-hth" => "Hà Tĩnh"
      "vn-lci" => "Lào Cai"
      "vn-bpc" => "Bình Phước"
      "vn-tgg" => "Tiền Giang"
      "vn-bte" => "Bến Tre"
      "vn-hcm" => "Hồ Chí Minh"
      "vn-cmu" => "Cà Mau"
      "vn-lcu" => "Lai Châu"
      "vn-hgg" => "Hà Giang"
      "vn-bkn" => "Bắc Kạn"
      "vn-bgg" => "Bắc Giang"
      "vn-ntn" => "Ninh Thuận"
      "vn-tth" => "Thừa Thiên - Huế"
      "vn-vpc" => "Vĩnh Phúc"
      "vn-pto" => "Phú Thọ"
      "vn-qnh" => "Quảng Ninh"
      "vn-qni" => "Quảng Ngãi"
      "vn-tha" => "Thanh Hóa"
      "vn-ldg" => "Lâm Đồng"
      "vn-qnm" => "Quảng Nam"
      "vn-vlg" => "Vĩnh Long"
      "vn-ndh" => "Nam Định"
      "vn-vtu" => "Bà Rịa - Vũng Tàu"
      "vn-qbh" => "Quảng Bình"
      "vn-dbn" => "Điện Biên"
      "vn-hbh" => "Hòa Bình"
      "vn-ybi" => "Yên Bái"
      "vn-hnm" => "Hà Nam"
      "vn-gli" => "Gia Lai"
      "vn-pyn" => "Phú Yên"
      "vn-hni" => "Hà Nội"
      "vn-dnk" => "Đắk Nông"
      "vn-tqg" => "Tuyên Quang"
      "vn-dni" => "Đồng Nai"
      "vn-cto" => "Cần Thơ"
      "vn-kgg" => "Kiên Giang"
      "vn-dng" => "Đà Nẵng"
      "vn-bnh" => "Bắc Ninh"
    ]
    "typeChangeRepresentInfo" => array:2 [
      0 => array:2 [
        "code" => "CHANGE_NEW_REPRESENT"
        "name" => "Thay đổi người đại diện mới"
      ]
      1 => array:2 [
        "code" => "CHANGE_CURRENT_REPRESENT_INFO"
        "name" => "Thay đổi thông tin người đại diện hiện tại"
      ]
    ]
    "mcBankAccType" => array:2 [
      0 => array:2 [
        "code" => "MC_COMPANY"
        "name" => "Công ty"
      ]
      1 => array:2 [
        "code" => "MC_INDIVIDUAL"
        "name" => "Cá nhân"
      ]
    ]
    "banks" => array:60 [
      "5fb22b462cc8fd12d0623356" => "DAB - DONGABANK - NH TMCP ĐÔNG Á"
      "5fb22b492cc8fd12d0623395" => "TCB - TECHCOMBANK - NH TMCP KỸ THƯƠNG VN"
      "5fb22b462cc8fd12d0623355" => "STB - SACOMBANK - NH TMCP SÀI GÒN THƯƠNG TÍN"
      "5fb22b492cc8fd12d0623394" => "CO-COP BANK - NH HỢP TÁC XÃ VN"
      "5fb22b462cc8fd12d0623357" => "EIB - EXIMBANK - NH TMCP XUẤT NHẬP KHẨU"
      "5fb22b472cc8fd12d062335c" => "MB - NH TMCP QUÂN ĐỘI"
      "5fb22b492cc8fd12d0623391" => "TMB - NH TOKYO MITSUBISHI"
      "5fb22b472cc8fd12d062335b" => "VPB - VPBANK - NH TMCP VN THỊNH VƯỢNG"
      "5fb22b492cc8fd12d0623390" => "IBK - NH CÔNG NGHIỆP HÀN QUỐC CN VN"
      "5fb22b472cc8fd12d062335a" => "SGB - SAIGONBANK - NH TMCP SÀI GÒN CÔNG THƯƠNG"
      "5fb22b482cc8fd12d0623383" => "BANGKOK BANK - NH BANGKOK"
      "5fb22b472cc8fd12d0623367" => "PNB - SOUTHERNBANK - NH TMCP PHƯƠNG NAM"
      "5fb22b482cc8fd12d0623382" => "MBB - MAYBANK - NH MAYBANK"
      "5fb22b472cc8fd12d0623366" => "VCCB - VIETCAPITALBANK - NH TMCP BẢN VIỆT"
      "5fb22b482cc8fd12d0623384" => "MIZUHO -  NH MIZUHO"
      "5fb22b482cc8fd12d0623381" => "FCB - FIRST COMMERCIAL BANK"
      "5fb22b472cc8fd12d0623369" => "OCB - NH TMCP PHƯƠNG ĐÔNG"
      "5fb22b482cc8fd12d0623380" => "CTB - CITIBANK - NH CITI"
      "5fb22b462cc8fd12d062334e" => "BIDV - NH ĐẦU TƯ VÀ PHÁT TRIỂN VN"
      "5fb22b472cc8fd12d0623371" => "KLB - KIENLONGBANK - NH TMCP KIÊN LONG"
      "5fb22b462cc8fd12d062334d" => "ICB - VIETINBANK - NH TMCP CÔNG THƯƠNG VN"
      "5fb22b482cc8fd12d0623379" => "VID - VIDPUBLIC - NH PUBLIC BANK VIET NAM"
      "5fb22b462cc8fd12d062334f" => "VCB - VIETCOMBANK - NH TMCP NGOẠI THƯƠNG VN"
      "5fb22b472cc8fd12d0623375" => "TPB - TIENPHONGBANK - NH TMCP TIÊN PHONG"
      "5fb22b482cc8fd12d0623376" => "BVB - BAOVIETBANK - NH TMCP BẢO VIỆT"
      "5fb22b472cc8fd12d0623374" => "LPB - LIENVIETPOSTBANK - NH TMCP BƯU ĐIỆN LIÊN VIỆT"
      "5fb22b472cc8fd12d0623373" => "VB - VIETBANK - NH TMCP VN THƯƠNG TÍN"
      "5fb22b472cc8fd12d0623372" => "VAB - VIETABANK - NH TMCP VIỆT Á"
      "5fb22b482cc8fd12d0623377" => "PVC - PVCOMBANK - NH TMCP ĐẠI CHÚNG VN"
      "5fb22b472cc8fd12d062335e" => "VIB - NH TMCP QUỐC TẾ"
      "5fb22b472cc8fd12d062335d" => "NASB - BACABANK - NH TMCP BẮC Á"
      "5fb22b492cc8fd12d0623389" => "WOO - NH WOORIL"
      "5fb22b492cc8fd12d0623386" => "HSBC - NH HSBC"
      "5fb22b492cc8fd12d0623385" => "SHBVN - NH SHINHAN "
      "5fb22b472cc8fd12d062336c" => "PGB - PGBANK - NH TMCP XĂNG DẦU PETROLIMEX"
      "5fb22b472cc8fd12d062336b" => "VNCB - NH TNHH MTV XÂY DỰNG VN"
      "5fb22b472cc8fd12d062336a" => "SCB - NH TMCP SÀI GÒN"
      "5fb22b492cc8fd12d062338f" => "ICBC - INDUSTRIAL AND COMMER BANK OF CHINA"
      "5fb22b492cc8fd12d062338e" => "NH TAIPEI FUBON COMMERCIAL CN VN"
      "5fb22b492cc8fd12d062338d" => "BIDC - NH ĐẦU TƯ VÀ PHÁT TRIỂN CAMPUCHIA"
      "5fb22b492cc8fd12d062338b" => "LVB - LAOVIETBANK - NH LD LÀO VIỆT"
      "5fb22b492cc8fd12d062338a" => "KEB - NH KOREA EXCHANGE CN HÀ NỘI"
      "5fb22b462cc8fd12d0623350" => "VBA - AGRIBANK - NH NÔNG NGHIỆP VÀ PT NÔNG THÔN VN"
      "5fb22b472cc8fd12d062336f" => "SHB - NH TMCP SÀI GÒN HÀ NỘI"
      "5fb22b462cc8fd12d0623354" => "MSB - MARITIMEBANK - NH TMCP HÀNG HẢI"
      "5fb22b462cc8fd12d0623353" => "VDB - VDBANK -  NH PHÁT TRIỂN VIỆT NAM"
      "5fb22b492cc8fd12d0623396" => "NCB -  NH TMCP QUỐC DÂN"
      "5fb22b4a2cc8fd12d0623398" => "UOB - NH UNITED OVERSEAS"
      "5fb22b4a2cc8fd12d0623397" => "DEUTSCHE - DEUTSCHE BANK"
      "5fb22b482cc8fd12d062337f" => "SC - SC -NH STANDARD CHARTERED"
      "5fb22b472cc8fd12d0623359" => "ACB - NH TMCP Á CHÂU"
      "5fb22b472cc8fd12d0623358" => "NAB - NAMABANK - NH TMCP NAM Á"
      "5fb22b472cc8fd12d0623360" => "SEAB - SEABANK - NH TMCP ĐÔNG NAM Á"
      "5fb22b472cc8fd12d0623364" => "ABB - ABBANK - NH TMCP AN BÌNH"
      "5fb22b472cc8fd12d0623363" => "HDB - HDBANK - NH TMCP PHÁT TRIỂN TP HCM"
      "5fb22b472cc8fd12d0623362" => "GPB - GPBANK - NH TMCP DẦU KHÍ TOÀN CẦU"
      "5fb22b472cc8fd12d0623361" => "OCEANBANK - NH TMCP ĐẠI DƯƠNG"
      "5fb22b482cc8fd12d062337a" => "IVB - INDOVINA - NH TNHH INDOVINA"
      "5fb22b482cc8fd12d062337d" => "ANZ - NH ANZ VIỆT NAM"
      "5fb22b482cc8fd12d062337c" => "VRB - NH LIÊN DOANH VIỆT NGA"
    ]
    "imageExt" => "jpg|jpeg|png|gif|bmp|svg|webp|tiff|heic|heif"
    "signForm" => array:2 [
      0 => array:2 [
        "code" => "E_CONTRACT"
        "name" => "Ký điện tử"
      ]
      1 => array:2 [
        "code" => "PAPER_CONTRACT"
        "name" => "Ký giấy"
      ]
    ]
    "signProcess" => array:3 [
      0 => array:2 [
        "code" => "E_CONTRACT"
        "name" => "Ký điện tử"
      ]
      1 => array:2 [
        "code" => "PAPER_CONTRACT"
        "name" => "Ký phụ lục giấy(Tải file mẫu yêu cầu thay đổi thông tin, điền, đóng dấu và tải lên)"
      ]
      2 => array:2 [
        "code" => "SALE_SUPPORT"
        "name" => "Gửi yêu cầu chuyên viên chăm sóc của mMpos liên hệ, hỗ trợ ký phụ lục"
      ]
    ]
    "stateChangeDetailInfo" => array:8 [
      0 => array:2 [
        "code" => "NEW"
        "name" => "Mới tạo"
      ]
      1 => array:2 [
        "code" => "SIGNING"
        "name" => "Đang ký"
      ]
      2 => array:2 [
        "code" => "SIGNED_WAITING_APPROVED"
        "name" => "Đã ký - Chờ duyệt"
      ]
      3 => array:2 [
        "code" => "ADDITIONAL"
        "name" => "Cần bổ sung"
      ]
      4 => array:2 [
        "code" => "WAITING_SUPPORT"
        "name" => "Chờ hỗ trợ"
      ]
      5 => array:2 [
        "code" => "CANCELED"
        "name" => "Đã hủy"
      ]
      6 => array:2 [
        "code" => "REJECTED"
        "name" => "Bị từ chối"
      ]
      7 => array:2 [
        "code" => "COMPLETE"
        "name" => "Hoàn thành"
      ]
    ]
    "mcc" => []
    "stateChangeOverview" => array:4 [
      0 => array:2 [
        "code" => "WAITING_PROCESS"
        "name" => "Chờ xử lý"
      ]
      1 => array:2 [
        "code" => "COMPLETE"
        "name" => "Hoàn thành"
      ]
      2 => array:2 [
        "code" => "CANCELED"
        "name" => "Đã hủy"
      ]
      3 => array:2 [
        "code" => "REJECTED"
        "name" => "Bị từ chối"
      ]
    ]
  ]
  "message" => "Thành công"
]
 */
class Mpos360RequestChangeInfoGetConfigAction
{
	public MNP $mnp;

	public string $mnpCacheName = 'mnp_config_cache';

	public int $mnpCacheTime = 8*60*60;

	public function __construct(MNP $mnp)
	{
		$this->mnp = $mnp;
	}

	public function run()
	{
		if (Cache::has($this->mnpCacheName)) {
			$returnData = Cache::get(($this->mnpCacheName));
			$returnData = $this->handleSortData($returnData);
			return $returnData;
		}

		$result = $this->mnp->getConfig();
		if (!empty($result['status']) && !empty($result['data'])) {
			// lưu được thì tốt, ko đc thì ko sao cả
			$r = Cache::put($this->mnpCacheName, $result, $this->mnpCacheTime);
		}

		$returnData = $this->handleSortData($result);
		return $returnData;
	}

	public function handleSortData($mnpConfig=[]) {
		// Sort tỉnh thành
		$cities = $mnpConfig['data']['cities'];
		$priority = ['vn-hni', 'vn-hcm', 'vn-dng'];

		$citiesSorted = collect($cities)->sortBy(function ($cityName, $cityCode) use ($priority) {
			if (in_array($cityCode, $priority)) {
        return array_search($cityCode, $priority) - count($priority);
    	}

			// cắt chữ cái đầu tiên của tên tỉnh thành, slug để chuẩn hóa chữ "Đ" 
			$firstChar = Str::of($cityName)->substr(0, 1)->slug('')->trim()->__toString();
			return $firstChar;
		})->all();


		$mnpConfig['data']['cities'] = $citiesSorted;

		// Sort ngân hàng
		$banks = $mnpConfig['data']['banks'];
		$priorityBank = ['VCB', 'ICB', 'BIDV', 'VBA', 'TCB'];
		
		$banksSorted = collect($banks)->sortBy(function ($bankName) use ($priorityBank) {
			$explode = explode('-', $bankName);
			$bankCode = trim(Arr::first($explode));
			
			if (in_array($bankCode, $priorityBank)) {
        return array_search($bankCode, $priorityBank) - count($priorityBank);
    	}

			return $bankName;
		})->all();

		$mnpConfig['data']['banks'] = $banksSorted;
		return $mnpConfig;
	}
} // End class
