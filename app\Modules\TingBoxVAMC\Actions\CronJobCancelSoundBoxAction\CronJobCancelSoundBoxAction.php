<?php

namespace App\Modules\TingBoxVAMC\Actions\CronJobCancelSoundBoxAction;

use App\Lib\partner\SoundBox;
use App\Lib\TelegramAlertWarning;
use App\Exceptions\BusinessException;
use App\Lib\Helper;
use App\Modules\TingBoxVAMC\Models\PlanEvent;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use App\Modules\Merchant\Model\Mpos360Logs\LogRequest;


class CronJobCancelSoundBoxAction
{

	protected SoundBox $soundBox;

	public function __construct(SoundBox $soundBox)
	{
		$this->soundBox = $soundBox;
	}

	public function run()
	{
		for ($i=1; $i<=30; $i++) {
			$r = $this->handle();
			if (isset($r) && $r == 'EMPTY') {
				break;
			}
		}
	}

	public function handle() {
		$dataResponse = [];
		$event = PlanEvent::query()->where([
			'status' => 1,
			'action' => 'CANCLESOUNDBOX'
		])->first();

		if (!$event) {
			return 'EMPTY';
		}

		$event->status = 3; // dang xu ly
		$event->save();

		$merchantShopBank = MerchantShopBank::query()->where([
			'id' => $event->merchant_shop_bank_id
		])->first();

		if ($merchantShopBank) {
			if ($merchantShopBank->isDaHuyLienKet() && $merchantShopBank->isChuaDongBoSoundBox()) {

				$dataEvent = json_decode($event->data, true);
				if (empty($dataEvent) || !is_array($dataEvent)) {
					$event->status = 5; // that bai
					$event->time_updated = time();
					$event->save();
				}

				// shop bank chi dinh la mac dinh
				$merchantShopBankAssign = MerchantShopBank::query()->where([
					'id' => isset($dataEvent['merchantShopBankIdAssign']) && !empty($dataEvent['merchantShopBankIdAssign']) ? $dataEvent['merchantShopBankIdAssign'] : '-1',
					'merchant_id' => $merchantShopBank->merchant_id,
					'shop_id' => $merchantShopBank->shop_id,
				])->first();

				$params = [
					"mcId" => $merchantShopBank->merchant_id,
					"mobileUserName" => $merchantShopBank->shop_id,
					"vaNextPayNumber" => $merchantShopBank->partner_request_id, // Tài khoản VA Nextpay number đang thao tác
					"assignVaNextPayNumber" => $merchantShopBankAssign ? $merchantShopBankAssign->partner_request_id : '', //  Tài khoản VA Nextpay Number được gán
					"integratedMethod" => $merchantShopBank->account_type == 1 ? 'VAMC' : 'VANP', // VAMC, VANP
					"partnerCode" => Helper::getPartnerCode($merchantShopBank->merchant_id), // Nên là: NP
					"status" => 'CANCELED'
				];

				$logRequestTingBox = LogRequest::query()->forceCreate([
					'merchant_id' => $merchantShopBank->merchant_id,
					'partner' => 'tingbox',
					'request' => json_encode($params, JSON_UNESCAPED_UNICODE),
					'created_at' => now()->format('Y-m-d H:i:s'),
					'updated_at' => now()->format('Y-m-d H:i:s'),
				]);


				$sendSoundBox = $this->soundBox->switchingTknhNhanTien($params);
				logger()->channel('stdout')->info('requestCloseLinkBankSoundBox', ['param' => $params]);
				logger()->channel('stdout')->info('reponseCloseLinkBankSoundBox', ['response' => $sendSoundBox]);

				$logRequestTingBox->update(['response' => json_encode($sendSoundBox, JSON_UNESCAPED_UNICODE), 'updated_at' => now()->format('Y-m-d H:i:s')]);

				if (empty($sendSoundBox)) {
					$event->status = 5; // thất bại
					$event->time_updated = time();
					$event->save();
					TelegramAlertWarning::sendMessage(sprintf('Lỗi không gửi huỷ liên kết tài khoản được (MerchantShopBankId: %s,eventId: %s)', $merchantShopBank->id, $event->id));
					throw new BusinessException('Lỗi không gửi được huỷ liên kết QR.');
				};


				$merchantShopBank->is_default = 0;
				$merchantShopBank->status_link = 2;
				$merchantShopBank->time_updated = now()->timestamp;
				$merchantShopBank->save();


				if ($merchantShopBankAssign) {
					$merchantShopBankAssign->is_default = 1;
					$merchantShopBankAssign->time_updated = now()->timestamp;
					$merchantShopBankAssign->save();
				}

				$event->status = 2; // thành công
				$event->time_updated = time();
				$event->save();
			} else {
				$event->status = 4; // khong xu ly
				$event->time_updated = time();
				$event->save();
			}

			return $dataResponse;
		}

		TelegramAlertWarning::sendMessage(sprintf('Lỗi không gửi huỷ liên kết tài khoản được (eventId: %s)', $event->id));
		$event->status = 5;
		$event->time_updated = time();
		$event->save();


		return $dataResponse;
	}
} // End class
