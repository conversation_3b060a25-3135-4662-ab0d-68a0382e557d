<?php

namespace App\Modules\Merchant\Requests\RequestChangeInfo\V2;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360RequestChangeInfoUploadPhuLucKyRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.id' => ['required', 'numeric', 'integer', 'min:1'],
			'data.signature_url' => ['required', 'string', 'url'],
		];
	}
} // End class
