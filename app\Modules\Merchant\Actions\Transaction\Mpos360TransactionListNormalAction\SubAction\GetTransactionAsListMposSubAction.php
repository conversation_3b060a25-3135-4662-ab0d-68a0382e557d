<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransactionListNormalAction\SubAction;

use App\Lib\partner\MPOS;

class GetTransactionAsListMposSubAction
{
	public MPOS $mpos;

	public function __construct(MPOS $mpos)
	{
		$this->mpos = $mpos;
	}

	public function run(array $params)
	{
		$listTransaction = $this->mpos->getTrans($params);
		
		mylog([
			'params' => $params,
			'listTransaction' => $listTransaction
		]);

		return $listTransaction;
	}
} // End class
