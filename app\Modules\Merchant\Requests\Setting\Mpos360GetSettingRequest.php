<?php

namespace App\Modules\Merchant\Requests\Setting;

use Illuminate\Support\Arr;
use Illuminate\Foundation\Http\FormRequest;

class Mpos360GetSettingRequest extends FormRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.os' => ['required', 'string'],
			'data.current_version' => ['required', 'string'], // version app đang nằm trên điện thoại của người dùng
		];
	}
} // End class
