<?php

namespace App\Modules\Merchant\Actions\ChungThuc\Mpos360ChungThucGetOtpAction;

use Exception;
use App\Lib\OtpHelper;
use App\Lib\partner\MNP;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Setting;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360CodeOtp;
use App\Modules\Merchant\Model\Mpos360ChungThuc;
use App\Modules\Merchant\Requests\ChungThuc\Mpos360ChungThucGetOtpRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\SendSmsOtpMNPSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\SendMailOtpMNPSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\GetMNPAccessTokenSubAction;
use App\Modules\Merchant\Actions\ChungThuc\Mpos360ChungThucGetOtpAction\SubAction\SendEmailWithTemplateSubAction;

class Mpos360ChungThucGetOtpAction
{
	public MNP $mnp;

	public function __construct(MNP $mnp)
	{
		$this->mnp = $mnp;
	}

	public function run(Mpos360ChungThucGetOtpRequest $request)
	{
		$chungThucId = $request->json('data.reference_id');
		$serviceCode = $request->json('data.service_code');
		$objectValue = $request->json('data.obj_value');

		$mpos360ChungThuc = Mpos360ChungThuc::query()->find($chungThucId);

		if (!$mpos360ChungThuc) {
			throw new BusinessException('Lỗi: không tìm thấy bản ghi chứng thực liên quan');
		}

		$deviceSession = $request->getCurrentDeviceSession();

		$mpos360CodeOtp = Mpos360CodeOtp::query()->forceCreate([
			'command_code' => 'ATTESTATION',
			'service_code' => $serviceCode,
			'user_id'      => $deviceSession->user_id,
			'obj_value'    => $objectValue,
			'message'      => $this->getNoiDung(),
			'otp'          => generateRandomNumber(6),
			'reference_id' => $chungThucId,
			'status'       => 1,
			'time_out'     => now()->addSeconds(OtpHelper::getSoGiayCountdown())->timestamp,
			'time_created' => now()->timestamp,
			'time_updated' => now()->timestamp,
		]);

		if (!$mpos360CodeOtp) {
			throw new BusinessException('Lỗi: không tạo được bản ghi otp');
		}

		$deviceSessionWithToken = app(GetMNPAccessTokenSubAction::class)->run($deviceSession);

		switch ($serviceCode) {
			case 'SMS': 
				$sendOtpResult = app(SendSmsOtpMNPSubAction::class)->run($mpos360CodeOtp, $deviceSessionWithToken);
				break;

			case 'EMAIL': 
				// $sendOtpResult = app(SendMailOtpMNPSubAction::class)->run($mpos360CodeOtp, $deviceSessionWithToken);
				$sendOtpResult = app(SendEmailWithTemplateSubAction::class)->run(
					$mpos360CodeOtp,
					$deviceSessionWithToken,
					sprintf('[%s] - Mã OTP chứng thực thông tin email từ ứng dụng %s', $mpos360CodeOtp->otp, __('setting.appName')),
					sprintf('Chứng thực thông tin email người đại diện'),
					'ChungThucEmail'
				);
				
				break;

			default:
				throw new BusinessException('Lỗi không xác định được kênh gửi otp');
				break;
		}
		
		$msg = sprintf('Hệ thống đã gửi otp tới "%s" qua kênh "%s". Vui lòng kiểm tra trong hộp thư đến/thư spam/thư rác', $objectValue, $serviceCode);
		
		return [
			'otp_id' => $mpos360CodeOtp->id,
			'msg' => $msg,
			'countdown_time_get_new_otp' => OtpHelper::getSoGiayCountdown()
		];
	}

	public function getNoiDung(): string {
		return base64_encode('Noi dung'); // get noi dung tu template;
	}
} // End class
