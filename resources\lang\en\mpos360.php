<?php

return [
/*--------------------------- Validate Hash ---------------------*/
	/**
	 * @param ValidateHashMiddleware::class
	 */
	'ValidateHashMiddleware_PartnerKhongTonTai' => 'Partner does not exist',
	'ValidateHashMiddleware_PartnerDaBiKhoa' => 'Partner has been locked',
	'ValidateHashMiddleware_BanKhongCoQuyenTruyCapApiChucNangNay' => 'You do not have access to this feature',
	'ValidateHashMiddleware_UserApiNayKhongTheDecryptDuocThongTin' => 'The system cannot decrypt the information',
	'ValidateHashMiddleware_PhienLamViecKhongTonTai' => 'Session does not exist',
	'ValidateHashMiddleware_PhienLamViecDaHetHan' => 'Session has expired. Please exit app and relogin',
	'ValidateHashMiddleware_NgonNguLaBatBuoc' => 'Error lang',
	'ValidateHashMiddleware_HeThongChiHoTroNgonNguViVaEn' => 'Error lang',
	'ValidateHashMiddleware_TimeRequestLaBatBuoc' => 'Time Error',
	'ValidateHashMiddleware_ApiKeyLaBatBuoc' => 'Error',
	'ValidateHashMiddleware_LoiApiHashField' => 'Hash field error',
	'ValidateHashMiddleware_CheckSumLaBatBuoc' => 'Error!',
	'ValidateHashMiddleware_CheckSumLaKhongHopLe' => 'Error',
/*--------------------------- Đăng ký ---------------------*/
	/**
	 * @param Mpos360AuthRegisterRequest::class
	 */
	'ThamSoGetLinkRegisterLaBatBuoc' => 'Get link registration parameter is required',
	'ThamSoGetLinkRegisterPhaiLaKieuSo' => 'Get link registration parameter must be a number',


	'KhongTaoDuocBanGhiXacThucTaiKhoan' => 'Failed to create account authentication record',
	'KhongLayDuocThongTinXacThucTaiKhoan' => 'Failed to obtain account credentials',
	'XacThucTaiKhoanBuoc1KhongChinhXac' => 'Incorrect account verification step 1',
	'OtpChuaHetThoiGianKhongLayDuocMaMoi' => 'OTP has not expired and no new code can be obtained',
	
/*--------------------------- /End đăng ký ---------------------*/

/*--------------------------- Đăng nhập ---------------------*/
	/**
	 * @param Mpos360AuthLoginRequest::class
	 */
	'EmailDangNhapLaBatBuoc' => 'Login email is required',
	'EmailDangNhapPhaiLaKieuChuoi' => 'Login email must be a string',
	'EmailDangNhapPhaiLa1Email' => 'Login email must be a valid email address',
	'EmailDangNhapKhongDuocVuotQua255KyTu' => 'Login email must not exceed 255 characters',

	'MatKhauDangNhapLaBatBuoc' => 'Login password is required',
	'MatKhauDangNhapPhaiLaKieuChuoi' => 'Login password must be a string',
	'MatKhauDangNhapKhongDuocVuotQua36KyTu' => 'Login password must not exceed 36 characters',

	'HeDieuHanhThietBiLaBatBuoc' => 'Device operating system is required',
	'HeDieuHanhThietBiPhaiLaKieuChuoi' => 'Device operating system must be a string',
	'HeDieuHanhThietBiPhaiLaAndroidHoacIos' => 'Device operating system must be: ANDROID or IOS',

	'TokenThietBiLaBatBuoc' => 'Device token is required',
	'TokenThietBiPhaiLaKieuChuoi' => 'Device token must be a string',
	'ToKenThietBiKhongVuotQua255KyTu' => 'Device token must not exceed 255 characters',

	/**
	 * @param Mpos360AuthLoginAction::class
	 */
	'DangNhapLoiKhongTimThayThongTinPartner' => 'Error: partner information not found',

	/**
	 * @param CreateMpos360UserSubAction::class
	 */
	'DangNhapLoiKhongTaoDuocUser' => 'Error: unable to create user',

	/**
	 * @param GetDeviceIdByOsAndTokenAction::class
	 */
	'DangNhapLoiKhongLayDuocThongTinThietBi' => 'Error: unable to retrieve device information',

	/**
	 * @param LoginMerchantViaMposSubAction::class,
	 */
	'LoginMerchantViaMposSubAction_KhongCoThongTinTraVeTuDoiTac' => 'No information returned from the partner',
	'LoginMerchantViaMposSubAction_MatKhauKhongChinhXac' => 'Incorrect password',
	'LoginMerchantViaMposSubAction_LoiApiDangNhapDoiTac' => 'Error with partner login API',
/*--------------------------- /End đăng nhập ---------------------*/

/*--------------------------- Xác minh thiết bị ---------------------*/
	/**
	 * @param Mpos360AuthVerifyMposDeviceRequest::class
	 */
	'XacMinhThietBiMaThietBiLaBatBuoc' => 'Device code is required',
	'XacMinhThietBiMaThietBiPhaiLaKieuChuoi' => 'Device code must be a string',
	'XacMinhThietBiMaThietBiCoDoDaiToiDaLa255KyTu' => 'Device code must be less than 255 characters long',
	'XacMinhThietBiMaThietBiCoDoDaiToiDaLa10KyTu' => 'The device code must be at least 10 digits long',

	/**
	 * @param Mpos360AuthVerifyMposDeviceAction::class
	 */
	'XacMinhThietBiXacThucThanhCong' => 'Device verification successful',
	'XacMinhThietBiThongTinKhongChinhXac' => 'Error: inaccurate information',
/*--------------------------- /End xác minh thiết bị ---------------------*/

/*--------------------------- Đổi mật khẩu ---------------------*/
	/**
	 * @param Mpos360ChangePasswordRequest::class
	 */
	'DoiMatKhauEmailLaBatBuoc' => 'Email is required',
	'DoiMatKhauEmailPhaiLaKieuChuoi' => 'Email must be a string',
	'DoiMatKhauEmailPhaiDungDinhDang' => 'Email must be in the correct format',
	'DoiMatKhauEmailPhaiCoDoDaiToiDaLa255KyTu' => 'Email must be less than 255 characters long',

	'DoiMatKhauMatKhauHienTaiLaBatBuoc' => 'Current password is required',

	'DoiMatKhauMatKhauMoiLaBatBuoc' => 'New password is required',
	'DoiMatKhauMatKhauMoiCoDoDaiToiThieuLa4KyTu' => 'New password must be at least 4 characters long',
	'DoiMatKhauMatKhauMoiCoDoDaiToiThieuLa6KyTu' => 'New password must be at least 6 characters long',
	'DoiMatKhauMatKhauMoiCoDoDaiToiDaLa36KyTu' => 'New password must be less than 36 characters long',
	'DoiMatKhauXacNhanMatKhauMoiKhongTrungKhop' => 'New password does not match',
	'DoiMatKhauMatKhauMoiPhaiKhac123456' => 'New password must be different from 123456',
	'DoiMatKhauMatKhauMoiPhaiKhacMatKhauCu' => 'New password must be different from the old password',


	/**
	 * @param Mpos360ChangePasswordAction::class
	 */
	'DoiMatKhauLoiDoiTac' => 'Partner error: ',
	'DoiMatKhauMessageResultSuccess' => 'Password changed successfully',
/*--------------------------- /End Đổi mật khẩu ---------------------*/
/*--------------------------- Chứng thực ---------------------*/
	/*-------màn hình danh sách trường chứng thực-------*/
		/**
		 * @param Mpos360ChungThucGetFieldInfoRequest::class
		 */
		'ChungThucEmailMerchantLaBatBuoc' => 'Merchant email is required',
		'ChungThucEmailMerchantPhaiLaKieuChuoi' => 'Merchant email must be a string',
		'ChungThucEmailMerchantPhaiLaEmailDungDinhDang' => 'Merchant email must be a valid email format',

		/**
		 * @param GetMNPAccessTokenSubAction::class
		 */
		'LoiLuuThongTinTokenMnp' => 'Error saving MNP token information',
		'LoiGoiDoiTacMnp' => 'Error calling MNP partner',

		/**
		 * @param Mpos360ChungThucGetFieldInfoAction::class
		 */
		'BanCanPhaiChungThucCccdLanDauTienDeCoTheTaoYeuCauThayDoi' => 'You need to verify your CCCD for the first time to create a change request',
		'DeDamBaoAnToanChoTaiKhoanQuyKhachVuiLongXacThucCacThongTinDuoiDay' => 'To ensure the safety of your account, please verify the following information',
		'QuyKhachVuiLongChoTrongGiayLatDeHeThongXuLyChungThucThongTin' => 'Please wait a moment while the system processes the information authentication',
	/*-------Lấy mã otp chứng thực-------*/
		/**
		 * @param Mpos360ChungThucGetOtpRequest::class
		 */
		'ChungThucGetOtpServiceCodeLaBatBuoc' => 'Service code is required',
		'ChungThucGetOtpServiceCodePhaiLaChuoiKyTu' => 'Service code must be a string',
		'ChungThucGetOtpServiceCodePhaiDuoi50KyTu' => 'Service code must be under 50 characters',

		'ChungThucGetOtpGiaTriLaBatBuoc' => 'OTP is required',
		'ChungThucGetOtpGiaTriPhaiLaKieuChuoi' => 'OTP must be a string',

		'ChungThucGetOtpIdThamChieuLaBatBuoc' => 'Reference ID is required',
		'ChungThucGetOtpIdThamChieuLaKieuChuoi' => 'Reference ID must be a string',

		/**
		 * @param Mpos360ChungThucGetOtpAction::class
		 */
		'ChungThucGetOtpLoiKhongTimThayBanGhiChungThucLienQuan' => 'Error: record of related authentication not found',
		'ChungThucGetOtpLoiKhongTaoDuocBanGhiOtp' => 'Error: could not create OTP',

		/**
		 * @param SendSmsOtpMNPSubAction::class
		 */
		'LoiGuiOtpQuaSMS' => 'Error sending OTP via SMS',

		/**
		 * @param SendMailOtpMNPSubAction::class
		 */
		'LoiGuiOtpQuaEmail' => 'Error sending OTP via email',

	/*-------gửi lại mã otp chứng thực-------*/
		/**
		 * @param Mpos360ChungThucResendOtpRequest::class
		 */
		'ChungThucResendOtpOtpIdLaBatBuoc' => 'OtpId is required',
		'ChungThucResendOtpOtpPhaiLaKieuChuoi' => 'OtpId must be a string',
		'ChungThucResendOtpOtpPhaiCoDoDaiDuoi10KyTu' => 'OtpId must be under 10 characters',

		/**
		 * @param Mpos360ChungThucResendOtpAction::class
		 */
		'ChungThucResendOtpKhongTimThayBanGhiOtp' => 'Error: OTP record not found',
		'ChungThucResendOtpOtpDaDuocSuDung' => 'Error: OTP has already been used',
		'ChungThucResendOtpLoiKhongDungThongTinUser' => 'Error: incorrect OTP owner information',

		'ChungThucResendOtpLoiKhongTaoDuocOtp' => 'Error: could not create OTP',

		'ChungThucResendOtpGuiOtpThanhCongQuaSMS' => 'OTP has been sent to your phone number :obj_value. Please check your inbox',
		'ChungThucResendOtpGuiOtpThanhCongQuaEmail' => 'OTP has been sent to your email :obj_value. Please check your inbox, promotions, or spam',
		'ChungThucResendOtpLoiGuiOtpKhongXacDinh' => 'Unspecified error sending OTP',
	/*-------verify otp chứng thực-------*/
		/**
		 * @param Mpos360ChungThucVerifyOtpRequest::class
		 */
		'ChungThucVerifyOtpOtpIdLaBatBuoc' => 'OtpId is required',
		'ChungThucVerifyOtpOtpIdPhaiLaKieuChuoi' => 'OtpId must be a string',
		'ChungThucVerifyOtpOtpIdToiDaLa20KyTu' => 'OtpId must be up to 20 characters long',

		'ChungThucVerifyOtpOtpLaBatBuoc' => 'Otp is required',
		'ChungThucVerifyOtpOtpPhaiLaKieuChuoi' => 'Otp must be a string',
		'ChungThucVerifyOtpOtpCoDoDaiKhongQua10KyTu' => 'Otp must be up to 10 characters long',

		'ChungThucVerifyOtpMaThamChieuLaBatBuoc' => 'Reference Id is required',
		'ChungThucVerifyOtpMaThamChieuPhaiLaKieuChuoi' => 'Reference Id must be a string',

		/**
		 * @param Mpos360ChungThucVerifyOtpAction::class
		 */
		'Mpos360ChungThucVerifyOtpAction_LoiKhongTimThayBanGhiOtp' => 'Error: OTP record not found',
		'Mpos360ChungThucVerifyOtpAction_LoiOtpDaDuocSuDung' => 'Error: OTP has already been used',
		'Mpos360ChungThucVerifyOtpAction_LoiOtpDaHetHan' => 'Error: OTP has expired',
		'Mpos360ChungThucVerifyOtpAction_LoiOtpKhongChinhXac' => 'Error: OTP is incorrect',
		'Mpos360ChungThucVerifyOtpAction_LoiKhongTimThayTruongChungThucLienQuan' => 'Error: related verification field not found',
		'Mpos360ChungThucVerifyOtpAction_LoiKhongDanhDauDuocOtpLaDaSuDung' => 'Error: could not mark OTP as used',
		'Mpos360ChungThucVerifyOtpAction_LoiKhongDanhDauDuocLaDaChungThuc' => 'Error: could not mark as verified',
		'Mpos360ChungThucVerifyOtpAction_LoiLuuThongTinChungThucDoiTac' => 'Error: failed to save verification information to partner',
		'Mpos360ChungThucVerifyOtpAction_ChungThucThanhCong' => 'Verification of ":obj_value" via channel ":service_code" was successful',
		'Mpos360ChungThucVerifyOtpAction_LoiOtpKhongChinhXacVaHetHan' => '',
/*--------------------------- /.End chứng thực ---------------------*/

/*--------------------------- /Giao dịch ---------------------*/
	'TLNA_TrangThaiGiaoDich' => 'Transaction Status',
	/*-------lịch sử giao dịch-------*/
		/**
		 * @param Mpos360TransactionHistoryAction::class
		 */
		'Mpos360TransactionHistoryAction_TongTienGiaoDich' => 'Total transaction amount',
		'Mpos360TransactionHistoryAction_ChuaKetToan' => 'Unsettled',
		'Mpos360TransactionHistoryAction_DaKetToan' => 'Settled',
		'Mpos360TransactionHistoryAction_ThanhToanThuong' => 'Regular payment',
		'Mpos360TransactionHistoryAction_GiaoDichQr' => 'QR Transaction',
		'Mpos360TransactionHistoryAction_ThanhToanTraGop' => 'Installment payment',
		'Mpos360TransactionHistoryAction_YeuCauRutTienNhanh' => 'Request for quick withdrawal',
		'Mpos360TransactionHistoryAction_YeuCauNhanTienNhanhVietQr' => 'VietQR quick withdrawal',
		'Mpos360TransactionHistoryAction_NhanTienVeTKNH' => 'Receive money to bank account',
		'Mpos360TransactionHistoryAction_GD' => 'TR',
/*--------------------------- /. End Giao dịch ---------------------*/

/*--------------------------- /YC đổi thông tin ---------------------*/
	'CommonRequestChangeInfo' => [
		'Type' => [
			'CHANGE_BANK_ACCOUN_INFO' => 'Bank account',
			'CHANGE_REPRESENT_INFO' => 'Authorized representative for signing contracts',
		],
		
		'Choice' => [
			'DOI_THONG_TIN_LIEN_HE' => 'Update contact information',
			'DOI_NGUOI_DAI_DIEN_MOI' => 'Change to a new representative',
			'DOI_CCCD_MOI' => 'Update to a new ID card',
		]
	],

	/*-------Danh sách yêu cầu-------*/
	/**
	 * @param Mpos360RequestChangeInfoListAction::class
	 */
	'Mpos360RequestChangeInfoListAction_HayTaoYcKhac' => 'Please create another request!',
	'Mpos360RequestChangeInfoListAction_HetHan' => 'Expired',
	'Mpos360RequestChangeInfoListAction_HayXacThucYeuCau' => 'Please verify the request',
	'Mpos360RequestChangeInfoListAction_ChuaXacThuc' => 'Not verified',
	'Mpos360RequestChangeInfoListAction_YeuCau' => 'Request: ',
	'Mpos360RequestChangeInfoListAction_DaXacThuc' => 'Verified',
	'Mpos360RequestChangeInfoListAction_CanXacMinhBuoc3' => 'Step 3 verification required',

	'Mpos360RequestChangeInfoListAction_MaYC' => 'RequesId',

	/**
	 * @param ConfigYeuCauThayDoiSubAction::class
	 */
	'ConfigYeuCauThayDoiSubAction_Nhap' => 'Incomplete',
	'ConfigYeuCauThayDoiSubAction_ChuaGui' => 'Not sent',
	'ConfigYeuCauThayDoiSubAction_DangGui' => 'Sending',
	'ConfigYeuCauThayDoiSubAction_DaGuiSangMnp' => 'Sent to MNP',
	'ConfigYeuCauThayDoiSubAction_MnpDaXuLy' => 'MNP processed',
	'ConfigYeuCauThayDoiSubAction_MnpTuChoi' => 'MNP rejected',
	'ConfigYeuCauThayDoiSubAction_CapNhatLoi' => 'Update error',
	'ConfigYeuCauThayDoiSubAction_MCTuHuyYeuCau' => 'MC self-canceled request',
	'ConfigYeuCauThayDoiSubAction_HetHan' => 'Expired',
/*--------------------------- /. End Giao dịch ---------------------*/

/*--------------------------- /.Chứng thực ---------------------*/
	/**
	 * @param Mpos360ChungThuc::class
	 */
	'Mpos360ChungThuc_ChuaChungThuc' => 'Not authenticated',
	'Mpos360ChungThuc_ChoChungThuc' => 'Pending authentication',
	'Mpos360ChungThuc_DaHetHan' => 'Expired',
	'Mpos360ChungThuc_DaChungThuc' => 'Authenticated',
/*--------------------------- /.End chứng thực YC đổi thông tin ---------------------*/

/*--------------------------- /.Yêu cầu xóa tài khoản ---------------------*/
	/**
	 * @param Mpos360AuthRemoveAccountRequest::class
	 */
	'Mpos360AuthRemoveAccountRequest_EmailLaBatBuoc' => 'Email is required',
	'Mpos360AuthRemoveAccountRequest_EmailPhaiLaKieuChuoi' => 'Email must be a string',
	'Mpos360AuthRemoveAccountRequest_EmailPhaiDungDinhDang' => 'Email must be in the correct format',
	'Mpos360AuthRemoveAccountRequest_EmailKhongDuocVuotQua255KyTu' => 'Email must be less than 255 characters',

	'Mpos360AuthRemoveAccountRequest_LyDoXoaTaiKhoanLaBatBuoc' => 'Reason for account deletion is required',
	'Mpos360AuthRemoveAccountRequest_LyDoXoaTaiKhoanPhaiLaKieuChuoi' => 'Reason for account deletion must be a string',
	'Mpos360AuthRemoveAccountRequest_LyDoXoaTaiKhoanPhaiPhaiCoDoDaiDuoi300KyTu' => 'Reason for account deletion must be less than 300 characters',

	'Mpos360AuthRemoveAccountRequest_MatKhauLaBatBuoc' => 'Password is required',
	'Mpos360AuthRemoveAccountRequest_MatKhauPhaiLaKieuChuoi' => 'Password must be a string',

	/**
	 * @param Mpos360AuthRemoveAccountAction::class
	 */
	'Mpos360AuthRemoveAccountAction_LoiMatKhauKhongChinhXac' => 'Error: Your password is wrong',
	'Mpos360AuthRemoveAccountAction_LoiKhongTaoDuocYeuCauXoaTaiKhoan' => 'Error: Could not create account deletion request',
	'Mpos360AuthRemoveAccountAction_LoiCapNhatThoiGianHetHanPhien' => 'Error: Failed to update session expiration time',
	'Mpos360AuthRemoveAccountAction_XoaTaiKhoanThanhCong' => 'mPOS has successfully processed the request to delete the account associated with {email}. Thank you for trusting and using mPOS during this time. For any feedback, please contact mPOS customer service.

If you wish to retain the account, please log in to the system before the date :timeline',
/*--------------------------- /.End yêu cầu xóa tài khoản ---------------------*/

/*-----------------------Dữ liệu hồ sơ (chỉ có ở bản tiếng anh do là data của MNP)------------------*/
	'Chi nhánh' => 'Branch',
	'Ngân hàng' => 'Bank',
	'Số TKNH' => 'Account No',
	'Loại tài khoản ngân hàng' => 'Bank Account Type',
	'Tên chủ tài khoản' => 'Holder name',
	'Tỉnh/Thành phố' => 'City',
	
	'Số điện thoại người đại diện' => 'Representation Mobile',
	'Nơi cấp' => 'Place Of Issue',
	'Số CMND/CCCD/Hộ chiếu' => 'Citizen ID number',
	'Địa chỉ thường trú' => 'Permanent address',
	'Địa chỉ hiện tại' => 'Current address',
	'Email người đại diện' => 'Representation Email',
	'Tên người đại diện' => 'Representation Name',

	// group code name
	'TKNH nhận tiền' => 'Receiving Bank Account',
	'Người đại diện ký HĐ' => 'Representative Signing The Contract',
	'Thông tin tài khoản Tingbox.vn' => 'Tingbox.vn Account Infomation',

	'Đổi người đại diện mới' => 'Change New Representative',
	'Đổi CCCD mới' => 'Update Citizen ID number',
	'Đổi thông tin liên hệ' => 'Change Contact Info',

	'Thành phố' => 'City',
	'Tên Merchant' => 'Merchant Name',
	'Email đăng nhập' => 'Email login',
	'Tên viết tắt' => 'Short name',
	'Địa chỉ' => 'Address',
	'Số điện thoại' => 'Phone',
	'Quận/Huyện' => 'District',
/*---------------------------Dữ liệu hồ sơ---------------------*/
/*---------------------------Home---------------------*/
	/**
	 * @param GetMenuForAppSubAction::class,
	 */
	'GetMenuForAppSubAction_BaoCaoGiaoDich' => 'Transaction Report',
	'GetMenuForAppSubAction_LichSuGiaoDich' => 'Transaction History',
	'GetMenuForAppSubAction_ThanhToanThuong' => 'Regular Payment',
	'GetMenuForAppSubAction_ThanhToanTraGop' => 'Installment Payment',
	'GetMenuForAppSubAction_ThongTinTaiKhoan' => 'Account Info',
	'GetMenuForAppSubAction_YcDoiThongTin' => 'ChangeInfo Request',
	'GetMenuForAppSubAction_HopThu' => 'Mailbox',
	'GetMenuForAppSubAction_HopDongDichVu' => 'Services Contract',
	'GetMenuForAppSubAction_ThietBiThanhToan' => 'Payment Device',
	'GetMenuForAppSubAction_NhanTienVeTKNH' => 'Bank Receipt',
	'GetMenuForAppSubAction_YcNhanTienNhanh' => 'Quick Withdrawal',
	'GetMenuForAppSubAction_NhanTienTuVietQr' => 'VietQR Receipt',
	'GetMenuForAppSubAction_DangKyDichVu' => 'Register Services',
/*---------------------------/End Home---------------------*/

/*---------------------------/List phương thức---------------------*/
	/**
	 * @param GetPhuongThucQuetB3Ver2SubAction
	 */
	'GetPhuongThucQuetB3Ver2SubAction_SDK' => 'Certificate Id Card Representative',
	'GetPhuongThucQuetB3Ver2SubAction_QTS' => 'Selfie your face',
	'GetPhuongThucQuetB3Ver2SubAction_ZALO' => 'Via Zalo',
	'GetPhuongThucQuetB3Ver2SubAction_SMS' => 'Via SMS',
	'GetPhuongThucQuetB3Ver2SubAction_EMAIL' => 'Via Email',
/*---------------------------/End List phương thức---------------------*/
/**
	 * @param Mpos360RequestChangeInfoUpdateProfileAction::class
	 */
	'Bạn cần làm chứng thực CCCD trước khi tạo yêu cầu đổi thông tin' => 'You need to veriry your Id Card first',

	/**
	 * @param AddingAdditionalProfileSubAction::class
	 */
	'CCCD/GP lái xe/Hộ khẩu/Hộ chiếu' => 'National ID/Driving License/Household Registration/Passport',
	'Chức vụ của người ủy quyền tại ngân hàng' => 'Position or Role in bank',
	'Mối quan hệ giữa chủ tài khoản và người có thẩm quyền tại ngân hàng' => 'Relationship between two sides',
	'CCCD' => 'National ID',
	'Ảnh chụp CCCD mặt trước' => 'Photo of the front of National ID',
	'Ảnh chụp CCCD mặt sau' => 'Photo of the back of National ID',
	'GP lái xe' => 'Driving License',
	'GPLX mặt trước' => 'Photo of the front of Driving License',
	'GPLX mặt sau' => 'Photo of the back of Driving License',
	'Hộ chiếu' => 'Passport',
	'Ảnh 1' => 'Photo 1',
	'Ảnh 2' => 'Photo 2',
	'Ảnh 3' => 'Photo 3',
	'Ảnh 4' => 'Photo 4',
	'Hộ khẩu' => 'Household Registration',
/*---------------------------Luồng đổi thông tin V3---------------------*/
	/**
	 * @param BuildChoiceOptionSubAction::classh
	 */
	'Đổi số TKNH khác của hộ kinh doanh' => 'Change to a different business bank account',
	'Cùng người đại diện pháp luật trên ĐKKD' => 'Same legal representative on the business registration certificate',
	'Đổi số TKNH cá nhân được ủy quyền' => 'Change to an authorized individual bank account',
	'Cung cấp giấy tờ của người mới & giấy ủy quyền' => 'Provide documents of the new person & authorization letter',
	'Đổi số TKNH khác của hộ doanh nghiệp' => 'Change to a different business entity bank account',
	'Đổi người đại diện khác' => 'Change to a different legal representative',
	'Cung cấp CCCD của người đại diện mới & giấy ủy quyền' => 'Provide the citizen ID of the new legal representative & authorization letter',
	'Đổi/Cập nhật CCCD mới' => 'Change/Update new citizen ID',
	'Của người đại diện hiện tại, khi làm mới hoặc cấp lại giấy tờ' => 'For the current legal representative, when renewing or reissuing documents',
	'Đổi thông tin liên hệ' => 'Change contact information',
	'Thông tin người đại diện hiện tại' => 'Current legal representative information',

	/**
	 * @param Mpos360MerchantSignatureCreateRequest::class
	 */
	'Mpos360MerchantSignatureCreateRequest_EmailLaBatBuoc' => 'Email is required',
	'Mpos360MerchantSignatureCreateRequest_EmailPhaiLaKieuChuoi' => 'Email must be a string',
	'Mpos360MerchantSignatureCreateRequest_EmailKhongDungDinhDang' => 'Email is wrong format',
	'Mpos360MerchantSignatureCreateRequest_ChuKyLaBatBuoc' => 'Signature is required',
	'Mpos360MerchantSignatureCreateRequest_ChuKyPhaiLaMotChuoiKyTu' => 'Signature must be a string',
	'Mpos360MerchantSignatureCreateRequest_ChuKyPhaiLaMotUrl' => 'Signature must be a url',
	'ImageUrlRule_ChuKyPhaiLaMotHinhAnh' => 'Signature url must be an image',
	'Mpos360MerchantSignatureCreateRequest_TrangThaiChuKyLaBatBuoc' => 'Signature status is required',
	'Mpos360MerchantSignatureCreateRequest_TrangThaiChuKyKhongHopLe' => 'Signature status must one of list: USING OR NOT',

	/**
	 * @param Mpos360RequestChangeInfoMarkAsDoneAction::class
	 */
	'Mpos360RequestChangeInfoMarkAsDoneAction_ChungToiDaTiepNhanYeuCauCuaBan' => 'We have received your request to update information. MPOS will review and respond within 02 business days. Thank you for trusting and using our services',


	/**
	 * @param Mpos360RequestChangeInfoAdditionalAttachmentRequest
	 */
	'IdYeuCauLaBatBuoc' => 'Request ID is required',
	'IdYeuCauPhaiLaDangSo' => 'Request ID must be a number',
	'IdYeuCauPhaiLaSoTuNhien' => 'Request ID must be a natural number',
	'IdYeuCauPhaiCoGiaTriThapNhatLa1' => 'Request ID must have a minimum value of: 1',
	'EmailMcLaBatBuoc' => 'MC Email is required',
	'EmailMcPhaiLaKieuChuoi' => 'MC Email must be a string',
	'EmailMcKhongDungDinhDang' => 'MC Email is in an invalid format',
	'PhaiCoThamSoQtsRequestId' => 'The qts parameter is required',
	'QtsRequestPhaiLaDangChuoi' => 'The qts parameter must be a string',
	'HoSoBoSungLaBatBuoc' => 'Supplementary documents are required',
	'HoSoBoSungPhaiLaKieuMang' => 'Supplementary documents must be an array',
	'MaHoSoLaBatBuoc' => 'Document code is required',
	'MaHoSoPhaiLaKieuChuoi' => 'Document code must be a string',
	'GiaTriHoSoLaBatBuoc' => 'Document value is required',
	'GiaTriHoSoLaKieuChuoi' => 'Document value must be a string',
	'FileDinhKemLaBatBuoc' => 'Attachment is required',
	'FileDinhKemPhaiLaKieuMang' => 'Attachment must be an array',
	'ChungTuThayTheLaBatBuoc' => 'Replacement documents are required',
	'ChungTuThayThePhaiLaDangMang' => 'Replacement documents must be an array',
	'PhaiCoThamSoChungNhanDiKem' => 'The accompanying certification parameter is required',
	'ChungNhanDiKemPhaiLaDangMang' => 'Accompanying certifications must be an array',
	'ChungNhanDiKemPhaiLaDangChuoi' => 'Accompanying certification item must be a string',
	'ChungNhanDiLemPhaiLaUrl' => 'Accompanying certification item must be a URL',

	/**
	 * @param ValidateProfileDinhKemSA::class
	 */
	'Thiếu thông tin: Vai trò/Vị trí của người ủy quyền tại ngân hàng (positionAuthBank)' => 'Missing information: Role/Position of the authorized person at the bank (positionAuthBank)',
	'Thiếu thông tin: Vai trò/Vị trí của người được ủy quyền' => 'Missing information: Role/Position of the authorized person',
	'Vai trò/Vị trí của người được ủy quyền có độ dài tối thiểu là 3 ký tự' => 'Role/Position of the authorized person must be at least 3 characters long',
	'Vai trò/Vị trí của người được ủy quyền phải có độ dài tối đa là 255 ký tự' => 'Role/Position of the authorized person must be at most 255 characters long',
	'Vai trò/Vị trí của người ủy quyền tại ngân hàng phải có độ dài tối thiểu là 3 ký tự' => 'Role/Position of the authorized person at the bank must be at least 3 characters long',
	'Vai trò/Vị trí của người ủy quyền tại ngân hàng phải có độ dài tối đa là 255 ký tự' => 'Role/Position of the authorized person at the bank must be no more than 255 characters long',
	'Thiếu thông tin: Mối quan hệ giữa hai bên (bankMutualRelation)' => 'Missing information: Relationship between the two parties (bankMutualRelation)',
	'Mối quan hệ giữa hai bên phải có độ dài tối thiểu là 3 ký tự' => 'Relationship between the two parties must be at least 3 characters long',
	'Mối quan hệ giữa hai bên phải có độ dài tối đa là 255 ký tự' => 'Relationship between the two parties must be no more than 255 characters long',
	'Thiếu thông tin: Mã qts sau khi ekyc' => 'Missing information: qts code after ekyc',
	'Lỗi: Không khớp tên thông tin người thụ hưởng' => 'Error: Beneficiary name mismatch',
	'Thiếu thông tin: Vai trò/Chức vụ của người đại diện (representPosition)' => 'Missing information: Role/Position of the representative (representPosition)',
	'Vai trò/Chức vụ của người đại diện phải có độ dài tối thiểu là 3 ký tự' => 'Role/Position of the representative must be at least 3 characters long',
	'Vai trò/Chức vụ của người đại diện phải có độ dài tối đa là 255 ký tự' => 'Role/Position of the representative must be no more than 255 characters long',
	'Thiếu thông tin: Mối quan hệ giữa 2 bên (representMutualRelation)' => 'Missing information: Relationship between the two parties (representMutualRelation)',
	'Mối quan hệ giữa hai bên phải có độ dài tối thiểu là 3 ký tự' => 'Relationship between the two parties must be at least 3 characters long',
	'Mối quan hệ giữa hai bên phải có độ dài tối đa là 255 ký tự' => 'Relationship between the two parties must be no more than 255 characters long',
	'Không tìm được bản ghi yêu cầu thay đổi' => 'Can not find request change info',
	'Bổ sung tài liệu đính kèm thành công' => 'Adding attachments successfully',

	/**
	 * @param TaoYcDoiSTKCuaCaNhanDuocDoanhNghiepUyQuyenSA::class
	 */
	'Lỗi: không tạo được yc đổi thông tin TKNH' => 'Error: Can not create request',
	'Tạo yêu cầu cầu đổi thông tin thành công' => 'Create request successfully',
	'Vai trò/Vị trí của người ủy quyền tại ngân hàng' => 'Role/Position of authorized person at the bank',
	'Vai trò/Vị trí của người được ủy quyền' => 'Role/Position of the authorized person',
	'Mối quan hệ giữa hai bên' => 'Relationship between two sides',
	'Bố/Mẹ - Con' => 'Parents - Child',
	'Anh - Chị - Em' => 'Brothers - Sisters',
	'Vợ - Chồng' => 'Husband - Wife',
	'Khác' => 'Other',
	'Chứng từ đính kèm' => 'Attached documents',
	'Ảnh Giấy ủy quyền có dấu và chữ ký của công ty' => "Photo of the Power of Attorney with the company's stamp and signature",
	'Ảnh 1' => 'Photo 1',

	/**
	 * @param TaoYcDoiSTKCuaCaNhanDuocHKDUyQuyenSA::class
	 */
	'Lỗi: không tạo được yc đổi thông tin TKNH' => 'Error: Unable to create a request to change bank account information',
	'Tạo yêu cầu cầu đổi thông tin thành công' => 'Successfully created a request to change information',
	'Vai trò/Vị trí của người ủy quyền tại ngân hàng' => 'Role/Position of the authorized person at the bank',
	'Mối quan hệ giữa hai bên' => 'Relationship between the two parties',
	'Ảnh giấy ủy quyền được công chứng tại cơ quan có thẩm quyền' => 'Photo of the notarized Power of Attorney at an authorized agency',
	'Ảnh giấy khai sinh' => 'Photo of the birth certificate',
	'Ảnh giấy đăng ký kết hôn' => 'Photo of the marriage certificate',
	'Ảnh tất cả các trang Sổ hộ khẩu giấy' => 'Photos of all pages of the paper Household Registration Book',
	'Video quay màn hình ứng dụng VNeID từ khi đăng nhập đến các trang thể hiện mối quan hệ giữa người ủy quyền và người được ủy quyền' => 'Video of the VNeID application (from the Login screen to the Residency Information screen)',

	/**
	 * @param Mpos360RequestChangeInfoDetailV3Action::class
	 */
	'Id yêu cầu' => 'Request Id',
	'Mã yêu cầu' => 'Request code',
	'Ngày tạo' => 'Creation date',
	'Trạng thái yêu cầu' => 'Request status',
	'Trạng thái xác thực' => 'Authentication status',
	'Trạng thái ký phục lục' => 'Appendix signing status',
	'Thông tin chung' => 'General information',

	'Đổi thông tin TKNH' => 'Change bank account information',
	'Thay đổi thông tin TKNH' => 'Bank account information change',
	'Kiểu thay đổi' => 'Change type',
	'Id NH' => 'Bank Id',
	'Chi nhánh' => 'Branch',
	'Tên NH' => 'Bank name',
	'Số tài khoản' => 'Account number',
	'Người thụ hưởng' => 'Beneficiary',
	'Loại TKNH' => 'Bank account type',
	'Mã tỉnh thành' => 'Province code',
	'Tên tỉnh thành' => 'Province name',
	'Chức vụ/Vị trí' => 'Position/Role',
	'Mối quan hệ' => 'Relationship',
	'Chứng từ bổ sung' => 'Supplementary documents',

	'Trạng thái duyệt' => 'Approval status',
	'Chờ duyệt' => 'Pending approval',
	'Đã duyệt' => 'Approved',
	'Bị từ chối' => 'Rejected',
	'Lý do bị từ chối' => 'Reason for rejection',
	'Cập nhật lỗi' => 'Update error',
	'Không duyệt' => 'Not approved',
	'Lý do' => 'Reason',
	'Cần xác thực thay đổi thông tin (selfie)' => 'Verification of information change required (selfie)',
	'Thông tin khác' => 'Other information',

	'Không xác định' => 'Undefined',
	'Không xác thực' => 'Not authenticated',
	'Chưa xác thực' => 'Not yet authenticated',
	'Cần xác minh bước 3' => 'Step 3 verification required',
	'Đã xác thực' => 'Authenticated',
	'Không xác định' => 'Undefined',
	'Hết hạn' => 'Expired',
	'MC hủy YC' => 'MC canceled the request',

	'Tên người đại diện' => 'Representative name',
	'Ngày sinh' => 'Date of birth',
	'Số CCCD' => 'CCCD number',
	'Nơi cấp' => 'Place of issuance',
	'Loại thay đổi' => 'Type of change',
	'Địa chỉ hiện tại' => 'Current address',
	'Địa chỉ thường chú' => 'Permanent address',
	'Chức vụ/Vị trí' => 'Position/Role',
	'Mối quan hệ giữa 2 bên' => 'Relationship between the two parties',
	'Chứng từ bổ sung' => 'Supplementary documents',
	'Email người đại diện' => 'Representative’s email',
	'SĐT người đại diện' => 'Representative’s phone number',

	'Đổi người đại diện mới' => 'Change to a new representative',
	'Đổi thông tin liên hệ' => 'Change contact information',
	'Đổi CCCD mới' => 'Change new Id Card',

	'Chữ ký xác nhận' => 'Confirmation signature',

	'Chưa ký' => 'Not signed',
	'Đang gửi ký' => 'Sending for signature',
	'Đã ký' => 'Signed',
	'Gửi ký lỗi' => 'Error sending for signature',
	'Không xác định' => 'Undefined',

	'Chức vụ của người đại diện mới' => 'Position of the new representative',
	'Giám đốc' => 'Director',
	'Phó giám đốc' => 'Deputy Director',
	'Trưởng phòng' => 'Head of Department',
	'Khác' => 'Other',

	/**
	 * @param Mpos360RequestChangeInfoCheckProfileBankingAction::class
	 */
	'Lỗi không kiểm tra được thông tin ngân hàng' => 'Error: Unable to verify bank information',
	'Số tài khoản hợp lệ' => 'Valid account number',
	'Để tránh sai sót khi nhận tiền. Vui lòng kiểm tra lại thông tin trước khi tiếp tục' => 'To avoid errors when receiving money. Please double-check the information before proceeding',
	'Số tài khoản chưa được xác minh bởi ngân hàng. Vui lòng kiểm tra lại thông tin trước khi tiếp tục!' => 'Account number has not been verified by the bank. Please double-check the information before proceeding!',
	'Số tài khoản không chính xác. Vui lòng kiểm tra lại' => 'Incorrect account number. Please double-check',
	'Số tài khoản không hợp lệ' => 'Invalid account number',
	'Sai STK hoặc người thụ hưởng' => 'Incorrect account number or beneficiary',

	
	
	/**
	 * @param Mpos360RequestChangeInfoSaveChangesAction::class
	 */
	'YC không tồn tại' => 'Request not found',
	'Lỗi: YC không phải loại đổi người đại diện mới' => 'The request is not change new representation',
	'Lỗi không cập nhật được thông tin CCCD vào yêu cầu' => 'Can not update card into to the request',
/*---------------------------/End luồng đổi thông tin V3---------------------*/

	/**
	 * @param Mpos360GetGroupWithUnreadItemsAction::class
	 */
	'Lỗi gọi lấy số lượng noti chưa đọc' => 'Mpos Err: get total unread mailbox item',
]; // end file