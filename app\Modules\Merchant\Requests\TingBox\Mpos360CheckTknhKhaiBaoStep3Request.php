<?php

namespace App\Modules\Merchant\Requests\TingBox;

use Illuminate\Support\Str;
use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360CheckTknhKhaiBaoStep3Request extends MerchantRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data.username' => ['required', 'string'],
			'data.bankAccountHolder' => ['required', 'string'],
			'data.bankAccountNumber' => ['required', 'string'],
			'data.bankId' => ['required', 'string']
    ];
  }

	public function getVmmcBankAccountHolder(): string {
		return Str::of($this->json('data.bankAccountHolder'))->slug(' ')->upper()->trim()->__toString();
	}
}
