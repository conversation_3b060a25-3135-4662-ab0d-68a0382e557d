<?php

namespace App\Modules\Merchant\Actions\Notification\Mpos360GetMyNotificationsAction;

use App\Lib\partner\MNPNOTIFY;
use App\Modules\Merchant\Requests\Notification\Mpos360GetMyNotificationsRequest;

class Mpos360GetMyNotificationsAction
{
	public function run(Mpos360GetMyNotificationsRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();

		$mposToken = $deviceSession->getMposToken();
		$mposEmail = $deviceSession->getMerchantEmail();

		$mnpNotify = new MNPNOTIFY($mposToken);
		
		$params = $request->only([
			'data.groupCode',
			'data.cateCode',
			'data.subCateCode',
			'data.pageNum',
			'data.pageSize',
		])['data'];

		$paramsFiltered = array_filter($params, function($value) {
			return $value !== "" && $value !== null;
		});

		if ($paramsFiltered['groupCode'] == 'ALL') {
			unset($paramsFiltered['groupCode']);
		}
		
		mylog(['Params truy van noti' => $paramsFiltered]);

		$listNotification = $mnpNotify->getNotificationList($deviceSession->getMerchantUserName(), $paramsFiltered);

		mylog(['listNotification Result' => $listNotification]);

		return $listNotification;
	}
}
