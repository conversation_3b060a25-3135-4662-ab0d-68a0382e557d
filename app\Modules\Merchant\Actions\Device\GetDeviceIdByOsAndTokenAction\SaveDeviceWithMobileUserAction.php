<?php

namespace App\Modules\Merchant\Actions\Device\GetDeviceIdByOsAndTokenAction;

use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Device;

class SaveDeviceWithMobileUserAction
{
	/**
	 * Hiện tại do ảnh hưởng của luồng login cũ nên deviceToken này chính là fcmToken, 
	 * sau sẽ có phương án xử lý dứt điểm
	 */
	public function run(string $deviceOS, string $deviceToken, int $mpos360UserId=0, $listMobileUser=[]): Device
	{
		$updateDeviceParam = [
			'time_created' => now()->timestamp,
			'time_updated' => now()->timestamp,
			'user_id' => $mpos360UserId,
		];
		
		if (!empty($listMobileUser)) {
			$updateDeviceParam['has_mobile_user'] = 1;
		}

		$device = Device::query()->updateOrCreate([
			'os' => $deviceOS,
			'token' => $deviceToken,
			'user_id' => $mpos360UserId
		], $updateDeviceParam);

		if (!$device) {
			throw new BusinessException(vmsg('DangNhapLoiKhongLayDuocThongTinThietBi'));
		}

		return $device; 
	}
}
