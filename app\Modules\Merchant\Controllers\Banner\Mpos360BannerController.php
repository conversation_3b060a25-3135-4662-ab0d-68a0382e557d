<?php

namespace App\Modules\Merchant\Controllers\Banner;

use App\Lib\DeviceSessionManualHelper;
use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Actions\Banner\Mpos360GetMyBannersAction\Mpos360GetMyBannersAction;
use App\Modules\Merchant\Model\DeviceSession;

class Mpos360BannerController extends Controller
{
	public function Mpos360GetMyBanners(Request $request)
	{
		$apiKey = $request->json('api_key');

		if (strlen($apiKey) > env('MPOS360_DO_DAI_KEY_PHIEN_CU', 36)) {
			$deviceSession = DeviceSessionManualHelper::makeInstance($apiKey);
		}else {
			$deviceSession = DeviceSession::query()->firstWhere(['api_key' => $apiKey]);
		}
		
		try {
			$result = app(Mpos360GetMyBannersAction::class)->run($deviceSession);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
