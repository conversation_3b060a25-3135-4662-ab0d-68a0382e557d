<?php

namespace App\Modules\Merchant\Actions\MerchantSignature\Mpos360MerchantSignatureListAction;

use App\Modules\Merchant\Model\Mpos360MerchantSignature;
use App\Modules\Merchant\Requests\MerchantSignature\Mpos360MerchantSignatureListRequest;

class Mpos360MerchantSignatureListAction
{
	public function run(Mpos360MerchantSignatureListRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		$merchantId = $deviceSession->getMerchantId();

		$signatures = Mpos360MerchantSignature::query()
			->where([
				'merchant_id' => $merchantId,
				'status' => Mpos360MerchantSignature::CHU_KY_DANG_SU_DUNG
			])->latest('id')
			->limit(10)
			->selectRaw("id as signature_id, signature_url")
			->get();

		return $signatures;
	}
}
