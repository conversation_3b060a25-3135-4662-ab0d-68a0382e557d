<?php
namespace App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360SubmitFormAuthenAccountAction;

use Exception;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\CacheAction;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360Register\Mpos360SubmitFormAuthenAccountStep1ActionRequest;

class Mpos360SubmitFormAuthenAccountStep1Action
{
    public function __construct()
	{
	}

    /**
	 *
	 * @param Mpos360SubmitFormAuthenAccountStep1ActionRequest $request
	 * @return void
	 */
    public function run(Mpos360SubmitFormAuthenAccountStep1ActionRequest $request): array 
    {

		$data = $request->all()['data'];

		// Lưu tạm dữ liệu xác thực CCCD cho xác thực bước 1
		$cacheAction = CacheAction::query()->forceCreate([
			'step' => 2,
			'reference_id' => $data['merchant_id'],
			'time_created' => now()->timestamp,
			'other_data' => json_encode([
				'step1' => $data 
			],JSON_UNESCAPED_UNICODE)
		]);

		if (!$cacheAction) {
			throw new BusinessException(vmsg('KhongTaoDuocBanGhiXacThucTaiKhoan'));
		}
		
        $dataResponse = [
			'merchant_id' => $data['merchant_id'],
			'fullname' => $data['fullname'],
			'cccd' => $data['cccd'],
			'gender' => $data['gender'],
			'address' => $data['address'],
			'birthday' => $data['birthday'],
			'other_data' => []
		];

        return $dataResponse;
    }
}