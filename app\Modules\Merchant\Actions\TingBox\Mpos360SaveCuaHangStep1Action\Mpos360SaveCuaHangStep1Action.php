<?php

namespace App\Modules\Merchant\Actions\TingBox\Mpos360SaveCuaHangStep1Action;

use App\Lib\Helper;
use Illuminate\Support\Str;
use App\Lib\MnpOnboardNewMcHelper;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Requests\TingBox\Mpos360SaveCuaHangStep1Request;

class Mpos360SaveCuaHangStep1Action
{
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run(Mpos360SaveCuaHangStep1Request $request)
	{
		$merchantId = $request->json('data.merchantId');

		$params = [
			'mcc' => $request->json('data.industryId'), // id nganh nghe
			'areaName' => $request->json('data.shopName'), // ten cua hang
			'qrDisplayName' => $request->json('data.qrDisplayName'), // ten viet tat tu 2-17 ky tu
			'areaAddress' => $request->json('data.address'), // Dia chi cua hang
			'mposMcId' => $merchantId, // id mpos mc
			'areaCityCode' => $request->json('data.cityId'), // id tỉnh thành
			'areaDistrictCode' => $request->json('data.districtId'), // id quận huyện
		];

		$detailMc = $this->mnpOnboardNewMcHelper->detailMc(['mposMcId' => $merchantId]);

		$params['qrDisplayName'] = Str::of($detailMc['data']['passportObject']['customerName'])->slug(' ')->upper()->trim()->__toString();
		

		if ($request->isTaoMoiCuaHang()) {
			$r = $this->mnpOnboardNewMcHelper->taoCuaHangMoi($params);
			
			if (!empty($r['result']) && !empty($r['code']) && $r['code'] == Mpos360Enum::API_SUCCESS_CODE) {
				$returnData = [
					'status' => 'SUCCESS',
					'data' => [
						'shopId' => isset($r['data']) && isset($r['data']['areaId']) ? $r['data']['areaId'] : '',
					],
					'msg' => 'Tạo cửa hàng thành công'
				];

				return $returnData;
			}

			$code = $r['code'] ?? '-1';
			$msg = sprintf('Lỗi không tạo được cửa hàng: %s (Code: %s)', $r['message'], $code);
			throw new BusinessException($msg, $code);
		}

		if ($request->isSuaThongTinCuaHang()) {
			
			$shopId = $request->json('data.shopId');
			$currentShop = collect($detailMc['data']['locations'])->where('id', $shopId)->first();
			
			if (empty($currentShop)) {
				throw new BusinessException('Hệ thống không tìm thấy thông tin cửa hàng của bạn');
			}
			
			$isHasSerial = collect($currentShop['deviceDTOs'])->contains(function ($it) {
				return !empty($it['serialNumber']);
			});

			if ($isHasSerial) {
				throw new BusinessException('Bạn đã khai báo thiết bị TingBox. Hệ thống từ chối yêu cầu cập nhật thông tin cửa hàng');
			}

			$params['areaId'] = $request->json('data.shopId');
			$r = $this->mnpOnboardNewMcHelper->updateThongTinCuaHang($params);

			if (!empty($r['result']) && !empty($r['code']) && $r['code'] == Mpos360Enum::API_SUCCESS_CODE) {
				$returnData = [
					'status' => 'SUCCESS',
					'data' => [],
					'msg' => 'Cập nhật thông tin cửa hàng thành công'
				];

				return $returnData;
			}
			
			$code = $r['code'] ?? '-1';
			$msg = $r['message'] ?? 'Lỗi không cập nhật được thông tin cửa hàng';
			throw new BusinessException($msg, $code);
		}
	}
} // End class
