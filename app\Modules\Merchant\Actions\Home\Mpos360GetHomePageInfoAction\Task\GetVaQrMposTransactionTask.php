<?php

namespace App\Modules\Merchant\Actions\Home\Mpos360GetHomePageInfoAction\Task;

use App\Lib\partner\MPOS;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransQRListAction\Mpos360TransQRListSubAction\TransQRGetDataSubAction;

class GetVaQrMposTransactionTask
{
	public MPOS $mpos;

	public function __construct(MPOS $mpos)
	{
		$this->mpos = $mpos;
	}

	public function run(DeviceSession $deviceSession)
	{
		// Param fake
		$params = [
			'typeTransaction' => 'STATISTIC_QR',
			"serviceName" => "STATISTIC_TRANSACTION",
			'merchantFk' => $deviceSession->getMerchantId(),
			'tokenLogin' => $deviceSession->getMposToken(),
			"startDate" => now()->subMonths(3)->startOfDay()->format('d/m/Y H:i:s'),
			"endDate" => now()->endOfDay()->format('d/m/Y H:i:s'),
			"pageSize" => 20,
			"pageIndex" => 0,
		];
		
		// Param that
		// $params = [
		// 	'typeTransaction' => 'STATISTIC_QR',
		// 	"serviceName" => "STATISTIC_TRANSACTION",
		// 	'merchantFk' => $deviceSession->getMerchantId(),
		// 	'tokenLogin' => $deviceSession->getMposToken(),
		// 	"startDate" => now()->startOfDay()->format('d/m/Y H:i:s'),
		// 	"endDate" => now()->endOfDay()->format('d/m/Y H:i:s'),
		// 	"pageSize" => 20,
		// 	"pageIndex" => 0,
		// ];
		try {
			$listTransaction = $this->mpos->getTransQRList($params);
		}catch(\Throwable $th) {
			return [];
		}
		
		return $listTransaction;
	}
}
