<?php

namespace App\Modules\Merchant\Actions\Transaction\V3P1\Mpos360TransactionHistoryForHomeAction;

use App\Lib\Helper;
use Illuminate\Support\Arr;
use App\Lib\partner\MposAWS;
use Illuminate\Http\Request;

class Mpos360TransactionHistoryForHomeAction
{
	public MposAWS $mposAws;

	public function __construct(MposAWS $mposAWS)
	{
		$this->mposAws = $mposAWS;
	}


	public function run(Request $request)
	{
		$returnData = [
			'totalAmountCurrent' => '--',
			'transactionCount' => '--'
		];

		$merchantId = $request->json('data.merchantId');

		$p = [
			'serviceName' => 'DAILY_COUNT_SUM_TRANSACTION',
			'merchantFk' => $merchantId,
			'tokenLogin' => 'abcxyz',
			'rangeTime' => now()->format('d-m-Y'),
			'typeTransaction' => 'STATISTIC_QR'
		];

		$thongKeCountSumQrHomNay = $this->mposAws->countSumTransByDate($p);

		if (!empty($thongKeCountSumQrHomNay['data']['data'])) {
			$transactionCurrent = Arr::first($thongKeCountSumQrHomNay['data']['data'], function ($item) use ($p) {
				return isset($item['formatDate']) && $item['formatDate'] == $p['rangeTime'];
			});

			$returnData['totalAmountCurrent'] = $transactionCurrent && isset($transactionCurrent['totalAmount'])  ? Helper::numberFormat($transactionCurrent['totalAmount']) : '--';
			$returnData['transactionCount'] = $transactionCurrent && isset($transactionCurrent['transactionCount'])  ? $transactionCurrent['transactionCount'] : '--';
		}

		return $returnData;
	}
}
