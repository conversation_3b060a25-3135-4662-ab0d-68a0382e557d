<?php

namespace App\Modules\Merchant\Controllers\Transaction;

use App\Lib\Helper;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionCAHistoryCodeListRequest;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionCAHistoryTransListRequest;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionCAHistoryCodeDetailRequest;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionCAHistoryTransDetailRequest;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransactionCAHistoryCodeListAction\Mpos360TransactionCAHistoryCodeListAction;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransactionCAHistoryTransListAction\Mpos360TransactionCAHistoryTransListAction;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransactionCAHistoryCodeDetailAction\Mpos360TransactionCAHistoryCodeDetailAction;
use App\Modules\Merchant\Actions\Transaction\Mpos360TransactionCAHistoryTransDetailAction\Mpos360TransactionCAHistoryTransDetailAction;

// Xử lý các giao dịch lịch sử tạm ứng, gồm: Mã tạm ứng & Giao dịch
class Mpos360TransactionCAHistoryController extends Controller
{
	public function Mpos360TransactionCAHistoryCodeList(Mpos360TransactionCAHistoryCodeListRequest $request)
	{
		try {
			$result = app(Mpos360TransactionCAHistoryCodeListAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360TransactionCAHistoryCodeDetail(Mpos360TransactionCAHistoryCodeDetailRequest $request)
	{
		try {
			$result = app(Mpos360TransactionCAHistoryCodeDetailAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360TransactionCAHistoryTransList(Mpos360TransactionCAHistoryTransListRequest $request)
	{
		try {
			$result = app(Mpos360TransactionCAHistoryTransListAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360TransactionCAHistoryTransDetail(Mpos360TransactionCAHistoryTransDetailRequest $request)
	{
		try {
			$result = app(Mpos360TransactionCAHistoryTransDetailAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
} // End class
