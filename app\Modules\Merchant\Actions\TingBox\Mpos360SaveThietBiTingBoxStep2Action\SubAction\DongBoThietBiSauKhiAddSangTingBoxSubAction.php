<?php

namespace App\Modules\Merchant\Actions\TingBox\Mpos360SaveThietBiTingBoxStep2Action\SubAction;

use App\Lib\Helper;
use App\Lib\partner\SoundBox;
use App\Lib\TelegramAlertWarning;
use App\Modules\Merchant\Requests\TingBox\Mpos360SaveThietBiTingBoxStep2Request;
use App\Modules\TingBoxVAMC\Models\PlanEvent;
use Illuminate\Http\Request;

class DongBoThietBiSauKhiAddSangTingBoxSubAction
{
	public SoundBox $soundBox;

	public int $tries = 0;

	public bool $isAddThietBiThanhCong = false;

	public function __construct(SoundBox $soundBox)
	{
		$this->soundBox = $soundBox;
	}

	// Mpos360SaveThietBiTingBoxStep2Request
	public function run(Request $request, $mobileUserId, $partnerCode='NP')
	{

		$p = [
			'serial' => $request->json('data.tingboxSerial'),
			'partnerCode' => $partnerCode,
			'mcId' => $request->json('data.merchantId'),
			'mobileUserName' => $mobileUserId,
			'status' => '',
		];

		$r = $this->soundBox->addThietBi($p);

		// Add thiết bị thất bại
		if ( empty($r['result']) ) {
			@TelegramAlertWarning::sendMessage("Add thiet bi sang tingbox that bai. " . $p['serial']);
			
			$planEvent = PlanEvent::forceCreate([
				'merchant_id' => $request->get('data.merchantId'),
				'action' => 'ADD_THIET_BI',
				'status' => PlanEvent::STT_MOI_TAO, // moi tao,
				'data' => json_encode($p),
				'response' => json_encode($r ?? []),
				'time_created' => time(),
				'time_updated' => time()
			]);
		}else {
			$this->isAddThietBiThanhCong = true;
		}

		return [
			'status' => 'SUCCESS', 
			'msg' => 'Thành công',
			'isAddThietBiThanhCong' => $this->isAddThietBiThanhCong
		];
	}
} // End class
