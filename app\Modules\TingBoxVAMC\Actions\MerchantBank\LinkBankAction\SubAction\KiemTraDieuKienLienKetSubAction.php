<?php

namespace App\Modules\TingBoxVAMC\Actions\MerchantBank\LinkBankAction\SubAction;

use Illuminate\Http\Request;
use App\Exceptions\BusinessException;

class KiemTraDieuKienLienKetSubAction
{
	public function run(Request $request, $mcTingBox, $currentPartnerCode='')
	{
		$msg = sprintf('Tài khoản của bạn hiện chưa được hỗ trợ liên kết với ngân hàng %s. Vui lòng liên hệ CSKH theo số Hotline: 1900.63.64.88 để biết thêm chi tiết!', $request->json('data.bankCode'));

		$config = $mcTingBox['data']['partnerConfigBankRes'];
		
		$configBank = collect($config)->whereIn('vaMode', ['VAMC', 'ALL'])
																	->pluck('bankCode')
																	->toArray();
																
		if (empty($configBank)) {
			throw new BusinessException($msg, 5009);
		}

		$bankCode = $request->json('data.bankCode');
		if (in_array($bankCode, $configBank) || in_array('ALL', $configBank)) {
			return true;
		} 

		throw new BusinessException($msg, 5009);
	}
}
