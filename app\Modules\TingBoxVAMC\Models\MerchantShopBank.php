<?php

namespace App\Modules\TingBoxVAMC\Models;

use Illuminate\Database\Eloquent\Model;

class MerchantShopBank extends Model
{
	const LOAI_TK_TRUC_TIEP = 1;
	const LOAI_TK_TRUNG_GIAN = 2;

	const CHUA_LIEN_KET = 'CHUA_LIEN_KET';
	const DA_LIEN_KET = 'DA_LIEN_KET';

	//const STT_LINK_DA_LIEN_KET = 1;

	const STT_DA_DONG_BO_TB = 2;

	const STT_LINK_DA_LIEN_KET = 1;
	const STT_LINK_CHUA_LIEN_KET = 0;
	const STT_LINK_BI_HUY = 2;
	const STT_LINK_BI_XOA_KHI_CHUA_CONFIRM = 3;
	const STT_LINK_DANH_DAU_LA_CHO_HUY = 4;

	protected $connection = 'mpos360_data';

	protected $table      = 'merchant_shop_bank';
	
	protected $guarded    = [];
	
	public $timestamps    = false;

	protected $dates      = [];

	protected $hidden     = [];

	public function merchantBank() {
		return $this->belongsTo(MerchantBank::class, 'merchant_bank_id', 'id');
	}

	public function switching() {
		return $this->belongsTo(MerchantShopBankSwitching::class, 'id', 'merchant_shop_bank_id');
	}

	public function getStatusLienKetForMobile() {
		if ($this->status_link == 0) {
			return 'CHUA_LIEN_KET';
		}

		if ($this->status_link == 1) {
			return 'DA_LIEN_KET';
		}

		if ($this->status_link == 2) {
			return 'HUY_LIEN_KET';
		}

		if ($this->status_link == 3) {
			return 'HUY_LIEN_KET';
		}

		if ($this->status_link == 4) {
			return 'CHO_HUY_LIEN_KET';
		}

		return 'KHONG_XAC_DINH';
	}

	public function isHienThiVCB(): bool {
		return $this->merchantBank->bank_code == 'VCB' && in_array($this->status_link, [0, 1, 4]);
	}

	public function getVaNextPayNumber(): string {
		$dataLinked = json_decode($this->data_linked, true);
		return $dataLinked['data']['vaNextpayNumber'];
	}

	public function isDaHuyLienKet(): bool {
		return $this->status_link == 2;
	}

	public function isDaLienKet(): bool {
		return $this->status_link == 1;
	}

	public function isMacDinh(): bool {
		return $this->is_default == 1;
	}

	public function isKhongMacDinh(): bool {
		return $this->is_default == 0;
	}

	public function isDongBoSoundBox(): bool {
		return $this->is_sync_tingbox == 2;
	}

	public function isChuaDongBoSoundBox(): bool {
		return $this->is_sync_tingbox == 1;
	}

	public function getAccountNumber($mobile) {
		if ($this->merchantBank->bank_code != 'VCB') {
			return $this->merchantBank->account_number;
		}

		// Chua xac thuc thi STK la SDT lien ket bank
		if (empty($this->isVerifyBankAccount)) {
			$bankMobile = $this->merchantBank->bank_mobile;
			if (!empty($bankMobile)) {
				return $bankMobile;
			}
			return $mobile;
		}

		return $this->merchantBank->account_number;
	}

	public function getAccountHolder() {
		if ($this->merchantBank->bank_code != 'VCB') {
			return $this->merchantBank->account_holder;
		}

		// Chua xac thuc thi STK la SDT
		if (empty($this->isVerifyBankAccount)) {
			return '';
		}

		return $this->merchantBank->account_holder;
	}
} // End class
