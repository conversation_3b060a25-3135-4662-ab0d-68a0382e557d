<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction;

use Exception;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360User;
use App\Modules\Merchant\DTOs\Authen\Mpos360Auth\LoginMposSuccessDto;

class CreateMpos360UserSubAction
{
	public function run(LoginMposSuccessDto $loginMposSuccessDto, array $loginResultAsArray=[]): Mpos360User
	{
		$mpos360User = Mpos360User::query()->updateOrCreate([
			'merchant_id' => $loginMposSuccessDto->merchantId,
			'username' => $loginMposSuccessDto->username
		], [
			'merchant_id' => $loginMposSuccessDto->merchantId,
			'username' => $loginMposSuccessDto->username,
			'core_id' => '',
			'data_users' => $loginMposSuccessDto->toJson(),
			'data_merchant' => json_encode($loginResultAsArray), 
			'last_ip' => request()->ip(),
			'status' => Mpos360Enum::MPOS360_USER_STT_ACTIVE,
			'last_time' => now()->timestamp,
			'time_created' => now()->timestamp,
			'time_updated' => now()->timestamp,
		]);

		if (!$mpos360User) {
			throw new BusinessException(vmsg('DangNhapLoiKhongTaoDuocUser'));
		}
		return $mpos360User;
	}
}
