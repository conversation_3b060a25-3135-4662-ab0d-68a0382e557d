<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoAttachSignatureV3Action;

use Exception;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Model\Mpos360MerchantSignature;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAttachSignatureAction\SubAction\GetCanByRequestChangeInfoSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAttachSignatureAction\SubAction\GetCanByRequestChangeInfoV3SubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\SubAction\GetPhuongThucQuetB3Ver2SubAction;

class Mpos360RequestChangeInfoAttachSignatureV3Action
{
	public function run(DeviceSession $deviceSession, int $signatureId, int $mpos360McRequestId)
	{
		$merchantId = $deviceSession->getMerchantId();

		$mpos360McRequest = Mpos360MerchantRequest::query()->firstWhere(['merchant_id' => $merchantId, 'id' => $mpos360McRequestId]);
		if (!$mpos360McRequest) {
			throw new BusinessException('Lỗi: không tìm thấy yêu cầu đổi thông tin');
		}

		$mpos360Signature = Mpos360MerchantSignature::query()->firstWhere(['merchant_id' => $merchantId, 'id' => $signatureId]);
		if (!$mpos360Signature) {
			throw new BusinessException('Lỗi: không tìm thấy bản ghi chữ ký');
		}


		$dataRequest = json_decode($mpos360McRequest->data_request, true);
		$signatureUrl = $mpos360Signature->signature_url;
		$dataRequest[0]['signProcess'] = [
			'code' => 'E_CONTRACT',
			'name' => 'Ký vẽ tay nhưng MNP tính là ký điện tử',
			'signature_url' => $signatureUrl,
			'mpos360_sign_code' => 'KY_VE_TAY'
		];

		$mpos360McRequest->data_request = json_encode($dataRequest);
		$mpos360McRequest->status_sign = Mpos360Enum::MPOS360_MC_SIGN_STT_CHUA_KY;
		$wasUpdatedRequest = $mpos360McRequest->save();

		if (!$wasUpdatedRequest) {
			throw new BusinessException('Lỗi: không cập nhật được chữ ký vào yêu cầu thay đổi');
		}

		$can = app(GetCanByRequestChangeInfoV3SubAction::class)->run($mpos360McRequest);

		$returnData = [
			'id' => $mpos360McRequest->id,
			'msg' => 'Cập nhật chữ ký vào yêu cầu thành công',
			'can' => $can,
			'scan_method' => app(GetPhuongThucQuetB3Ver2SubAction::class)->run($mpos360McRequest->merchant_id)
		];

		return $returnData;
	}
} // End class
