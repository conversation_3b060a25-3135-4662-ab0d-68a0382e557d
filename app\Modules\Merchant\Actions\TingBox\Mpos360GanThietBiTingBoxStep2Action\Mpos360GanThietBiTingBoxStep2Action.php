<?php

namespace App\Modules\Merchant\Actions\TingBox\Mpos360GanThietBiTingBoxStep2Action;

use App\Modules\Merchant\Model\Setting;

class Mpos360GanThietBiTingBoxStep2Action
{
	public function run()
	{
		$setting = Setting::query()->firstWhere(['key' => 'HOST_ASSET_MAIL_TEMPLATE']);
		$settingValue = json_decode($setting->value, true);

		$returnData = [
			'heading' => 'Gán loa TingBox',
			'title' => 'Hướng dẫn lấy mã thiết bị',
			'options' => [
				[
					'action' => 'SCAN',
					'name' => 'quét mã QR Serial trên thiết bị TingBox'
				],

				[
					'action' => 'INPUT',
					'name' => 'Nhập mã Serial thiết bị TingBox'
				]
			],
			'other_data' => [
				'title' => [
					'text_color' => '#018bf4',
					'webviewUrl' => $settingValue['common']['mposHuongDanLayMaThietBiTingBoxUrl'] ?? ''
				]
			]
		];
		
		return $returnData;
	}
}
