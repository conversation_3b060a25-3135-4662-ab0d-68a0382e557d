<?php

namespace App\Http\Middleware;

use Closure;
use Exception;
use App\Lib\Encryption;
use App\Lib\TelegramAlert;
use Illuminate\Support\Str;
use App\Traits\ApiResponser;
use App\Lib\TelegramAlertWarning;
use App\Exceptions\BusinessException;
use App\Lib\DeviceSessionManualHelper;
use App\Exceptions\ValidateAsJsonException;
use App\Lib\Helper;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Model\PartnerConfig;

class ValidateHashMiddleware
{
	use ApiResponser;

	public array $publicRouteAction = [
		'Mpos360AuthRegisterAction',
		'Mpos360AuthLoginAction',
		'Mpos360AuthLoginActionV2',
		'Mpos360ForgotPasswordAction',
		'Mpos360ScreenWordingConfigAction',

		// mnp bắn kết quả
		'Mpos360ReceiveRequestChangeInfoResultAction',

		// cronjob
		'Mpos360MerchantRequestCronPushRecordAction',
		'Mpos360PushChungThucAction',
		'Mpos360MerchantRequestCronPushSignAction',
		'Mpos360MerchantRequestCronPushRecordV3Action',
		'Mpos360PushSerialToTingTingSystemAction',
		'Mpos360CronRemoveOldSessionAction',

		// setting
		'Mpos360GetSettingAction',

		// For WebAdmin BackEnd (luôn dùng key mặc định đã cấp)
		'GetAllSetting_WB',
		'GetDetailSetting_WB',
		'UpdateSetting_WB',
		'GetAllRequestChangeInfo_WB',

		// register account
		'Mpos360GetFormRegisterAction',
		'Mpos360GetFormAuthenAccountAction',

		// Dang ky tai khoan
		'Mpos360SubmitFormRegisterAction',
		'Mpos360RegisterSendOtpAction',
		'Mpos360SubmitFormVerifyOtpAction',
		'Mpos360SubmitFormAuthenAccountStep2Action',
		'Mpos360CheckProgressKhaiBaoTingBoxAction',
		'Mpos360GetStepKhaiBaoTingBoxAction',
		'Mpos360GetFormTingBoxStep1Action',
		'Mpos360GetQuanHuyenTuMaTinhThanhStep1',
		'Mpos360SaveCuaHangStep1Action',
		'Mpos360SaveThietBiTingBoxStep2Action',
		'Mpos360DetailThietBiTingBoxStep2Action',
		'Mpos360GetFormKhaiBaoTknhStep3Action',
		'Mpos360CheckTknhKhaiBaoStep3Action',
		'Mpos360SaveTknhStep3Action'
	];

	public function isPublicRoute($request): bool
	{
		$currentRoute = $request->route()->getName();
		return in_array($currentRoute, $this->publicRouteAction);
	}

	public function getApiSecretFromPublic(string $apiKey, string $currentPath): string
	{
		$apiSecret = null;

		if ($apiKey == '78b38e3b-0b71-42c2-b8ec-519cd7cfcb08') {
			$apiSecret = env('MPOS360_MOBILE_SECRET_KEY');
		}
		elseif ($apiKey == '********-dc61-4502-b093-02183bd76ca6') {
			$apiSecret = env('MPOS360_CRONJOB_SECRET_KEY');
		}
		elseif ($apiKey == '9e73796f-85be-4d35-94d9-a9322f585d9a') {
			$apiSecret = env('MPOS360_MYNEXTPAY_SECRET_KEY');
		}

		if (empty($apiSecret)) {
			mylog(['UseOld' => 'yes']);
			$partnerConfig = PartnerConfig::query()->firstWhere('api_key', $apiKey);
		
			if (!$partnerConfig) {
				throw new BusinessException(vmsg('ValidateHashMiddleware_PartnerKhongTonTai'), 410);
			}

			if ($partnerConfig->isLocked()) {
				throw new BusinessException(vmsg('ValidateHashMiddleware_PartnerDaBiKhoa'), 409);
			}

			if (!$partnerConfig->hasPermission($currentPath)) {
				throw new BusinessException(vmsg('ValidateHashMiddleware_BanKhongCoQuyenTruyCapApiChucNangNay'), 403);
			}

			try {
				$apiSecret = decrypt($partnerConfig->api_secret);
			} catch (\Throwable $th) {
				throw new BusinessException(vmsg('ValidateHashMiddleware_UserApiNayKhongTheDecryptDuocThongTin'), 403);
			}
		}
		
		return $apiSecret;
	}

	public function getApiSecretFromMerchant(string $apiKey, string $currentPath): string
	{
		$deviceSession = DeviceSession::query()->with('partnerConfig')->firstWhere('api_key', $apiKey);

		if (!$deviceSession) {
			throw new BusinessException(vmsg('ValidateHashMiddleware_PhienLamViecKhongTonTai'), 401);
		}

		if ($deviceSession->isExprired() && $currentPath != 'Mpos360AuthLogout') {
			throw new BusinessException(vmsg('ValidateHashMiddleware_PhienLamViecDaHetHan'), 401);
		}

		$partnerConfig = $deviceSession->partnerConfig;

		if (!$partnerConfig->hasPermission($currentPath)) {
			throw new BusinessException(vmsg('ValidateHashMiddleware_BanKhongCoQuyenTruyCapApiChucNangNay'), 403);
		}

		try {
			$apiSecret = decrypt($partnerConfig->api_secret);
		} catch (\Throwable $th) {
			throw new BusinessException(vmsg('ValidateHashMiddleware_UserApiNayKhongTheDecryptDuocThongTin'), 403);
		}

		return $apiSecret;
	}

	public function handle($request, Closure $next, $fieldHash)
	{
		if (Helper::isLocal()) {
			return $next($request);
		}
		
		throw_if(
			empty($request->json('lang')),
			new ValidateAsJsonException(vmsg('ValidateHashMiddleware_NgonNguLaBatBuoc'), 422)
		);

		throw_if(
			!in_array($request->json('lang'), ['vi', 'en']),
			new BusinessException(vmsg('ValidateHashMiddleware_HeThongChiHoTroNgonNguViVaEn'), 422)
		);

		throw_if(
			empty($request->json('time_request')),
			new BusinessException(vmsg('ValidateHashMiddleware_TimeRequestLaBatBuoc'), 422)
		);

		$apiKey = $request->json('api_key');
		
		if (strlen($apiKey) > env('MPOS360_DO_DAI_KEY_PHIEN_CU', 36)) {
			// ăn vào logic mới
			$dv = DeviceSessionManualHelper::makeInstance($apiKey);

			if (empty($dv['time_expired']) || $dv['time_expired'] < now()->timestamp) {
				throw new BusinessException(vmsg('ValidateHashMiddleware_PhienLamViecDaHetHan'), 401);
			}

			if (empty($dv['signed'])) {
				throw new BusinessException(vmsg('ValidateHashMiddleware_PhienLamViecDaHetHan'), 401);
			}


			// Đoạn này lấy secret key của mobile để kiểm tra checksum
			$apiSecret = env('MPOS360_MOBILE_SECRET_KEY');
			$isMatchChecksum = @$this->isMatchChecksumAfterLogin($request, $fieldHash, $apiKey, $apiSecret);
			
			if (!$isMatchChecksum) {
				@TelegramAlertWarning::sendMessage("sai check sum");
				mylog([
					'path' => $request->path(),
					'IS_SAI_CHECKSUM' => 'OK'
				]);
				
				if (Str::contains($request->path(), ['Mpos360TransactionBAFReceiveList', 'Mpos360TransactionQRList'])) {
					return $next($request);
				}

				throw new BusinessException('Mã tham chiếu không hợp lệ', 422); 
			}

			return $next($request);
		}

		// Ăn vào luồng cũ
		if (empty($apiKey)) {
			throw new BusinessException(vmsg('ValidateHashMiddleware_ApiKeyLaBatBuoc'), 422);
		}

		$isPublicRoute = $this->isPublicRoute($request);

		if ($isPublicRoute) {
			$apiSecret = $this->getApiSecretFromPublic($apiKey, $request->path());
		}

		if (!$isPublicRoute) {
			$apiSecret = $this->getApiSecretFromMerchant($apiKey, $request->path());
		}

		$fieldExplode = explode('|', $fieldHash);
		if (empty($fieldExplode)) {
			throw new BusinessException(vmsg('ValidateHashMiddleware_LoiApiHashField'), 422);
		}

		$combineString = '';

		foreach ($fieldExplode as $key) {
			$combineString .= sprintf('%s:%s+', $key, $request->json('data.' . $key));
		}

		$combineChecksum = [
			$request->method(),
			$request->path(),
			$request->json('time_request'),
			$request->json('lang'),
			$combineString,
			$apiKey
		];

		$stringBeforeHash = implode('|', $combineChecksum);

		$checksum = hash_hmac('sha512', $stringBeforeHash, $apiSecret);

		$clientCheckSum = $request->json('checksum');

		if (empty($clientCheckSum) || empty($checksum)) {
			throw new BusinessException(vmsg('ValidateHashMiddleware_CheckSumLaBatBuoc'), 422);
		}

		if ($clientCheckSum == $checksum) {
			return $next($request);
		}

		throw new BusinessException(vmsg('ValidateHashMiddleware_CheckSumLaKhongHopLe'), 412);
	}

	public function isMatchChecksumAfterLogin($request, $fieldHash, $apiKey, $apiSecret) {

		$fieldExplode = explode('|', $fieldHash);

		if (empty($fieldExplode)) {
			throw new BusinessException(vmsg('ValidateHashMiddleware_LoiApiHashField'), 422);
		}

		$combineString = '';

		foreach ($fieldExplode as $key) {
			$combineString .= sprintf('%s:%s+', $key, $request->json('data.' . $key));
		}

		$combineChecksum = [
			$request->method(),
			$request->path(),
			$request->json('time_request'),
			$request->json('lang'),
			$combineString,
			$apiKey
		];

		$stringBeforeHash = implode('|', $combineChecksum);

		$checksum = hash_hmac('sha512', $stringBeforeHash, $apiSecret);

		$clientCheckSum = $request->json('checksum');

		if ($clientCheckSum != $checksum) {
			return false;
		}

		return true;
	}
} // End class
