<?php

namespace App\Modules\WebBackend\Resources;

use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoDetailV3Action\SubAction\GetTrangThaiYcForMobileSubAction;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use Illuminate\Http\Resources\Json\ResourceCollection;

class RequestChangeInfoResource extends ResourceCollection
{
  /**
   * Transform the resource collection into an array.
   *
   * @param  \Illuminate\Http\Request  $request
   * @return array
   */
  public function toArray($request)
  {
    return [
      'data' => $this->collection->map(function (Mpos360MerchantRequest $mpos360McRequest) {
				$statusForApp = app(GetTrangThaiYcForMobileSubAction::class)->run($mpos360McRequest);
				$mpos360McRequest->status_for_app = $statusForApp['name'];
				$dataRequest = json_decode($mpos360McRequest->data_request, true);
				if (!empty($dataRequest[0]['profiles']['representPassport'])) {
					$dataRequest[0]['profiles']['representPassport'] = mask_string($dataRequest[0]['profiles']['representPassport'], '*', 3);
				}

				if (!empty($dataRequest[0]['profiles']['representMobile'])) {
					$dataRequest[0]['profiles']['representMobile'] = mask_string($dataRequest[0]['profiles']['representMobile'], '*', 5);
				}

				$mpos360McRequest->data_request = json_encode($dataRequest);
				return $mpos360McRequest;
			})->toArray(),
      'links' => [
      
      ],
      'meta' => [
        
      ]
    ];
  }
}
