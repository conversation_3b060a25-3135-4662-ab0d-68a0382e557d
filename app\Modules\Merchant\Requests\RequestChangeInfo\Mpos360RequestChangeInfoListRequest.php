<?php

namespace App\Modules\Merchant\Requests\RequestChangeInfo;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360RequestChangeInfoListRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.time_created' => ['present', 'string'],
			'data.status' => ['present', 'string'],
			'data.start' => ['required', 'numeric', 'integer', 'min:0'],
			'data.limit' => ['required', 'numeric', 'integer', 'min:5'],
		];
	}


	public function getFilerTimeCreated() {
		$timeCreate = $this->json('data.time_created');
		
		if ($timeCreate == 'TODAY') {
			return now()->startOfDay()->timestamp;
		}

		if ($timeCreate == 'YESTERDAY') {
			return now()->subDay()->startOfDay()->timestamp;
		}

		if ($timeCreate == 'THIS_WEEK') {
			return now()->startOfWeek()->timestamp;
		}

		if ($timeCreate == 'THIS_WEEK') {
			return now()->startOfMonth()->timestamp;
		}

		return -1;
	}

	public function getStatusConditional() {
		$status = $this->json('data.status');
		
		if ($status == 'ALL' || empty($status)) {
			return [['status', '>', '-1']];
		}

		if ($status == 9) {
			return [['time_expired', '<', now()->timestamp]];
		}

		return [['status', '=', $status]];
	}
} // End class
