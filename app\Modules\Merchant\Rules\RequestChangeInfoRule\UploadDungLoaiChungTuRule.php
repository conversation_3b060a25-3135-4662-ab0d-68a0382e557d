<?php

namespace App\Modules\Merchant\Rules\RequestChangeInfoRule;

use App\Lib\Helper;
use Exception;
use Illuminate\Contracts\Validation\Rule;

class UploadDungLoaiChungTuRule implements Rule
{
	private array $__chungToHopLe = [];

	private string $__errorMessage = '';

	public function __construct()
	{
		$this->__chungToHopLe = Helper::getLoaiChungTuDuocPhepUpload();	
	}
	/**
	 * {
			"data": {
				"id": "880",
				"email": "<EMAIL>",
				"qts_request_id": "",
				"additional_profiles": [
					{
						"profileKey": "representPosition",
						"value": "Nhân viên"
					},
					{
						"profileKey": "representMutualRelation",
						"value": "Nhân viên"
					}
				],
				"attachments": {
					"id_documents": {
						"cccd": [
							"https://mpos.vn",
							"https://nextlend.vn"
						]
					},
					"other_documents": [
						"https://signaturely.com/wp-content/uploads/2020/04/oprah-winfrey-signature-signaturely.png"
					]
				}
			},
			"checksum": "7d1e3dbe18145b966df83778046ae6675fcbf2a24f6fa488e1ec06110566e9dc05c1b530a6cf3cee986114f4fdf575628b349188b52d82ddac38ffef1ab298d7",
			"api_key": "1c2edf8e-4614-4c96-beb3-d074e7a290ec",
			"time_request": 1725201305,
			"lang": "vi"
		}
	 */
	public function passes($attribute, $idDocuments): bool {
		foreach ($this->__chungToHopLe as $chungTu) {
			$listGiayToHopLe[$chungTu['key']] = $chungTu['name'];
		}

		foreach ($idDocuments as $loaiChungTu => $listUrlChungTu) {
			if (empty($listGiayToHopLe[$loaiChungTu])) {
				$msg = sprintf('Hệ thống chỉ chấp nhận các loại chứng từ sau: %s', implode(', ', $listGiayToHopLe));
				$this->__errorMessage = $msg;
				return false;
			}
		}

		return true;
	}

	/**
	 * Get the validation error message.
	 *
	 * @return string
	 */
	public function message()
	{
		return $this->__errorMessage;
	}
}
