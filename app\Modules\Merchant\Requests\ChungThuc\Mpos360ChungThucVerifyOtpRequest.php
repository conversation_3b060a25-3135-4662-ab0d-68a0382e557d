<?php

namespace App\Modules\Merchant\Requests\ChungThuc;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360ChungThucVerifyOtpRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.otp_id' => ['required', 'string', 'max:20'],
			'data.otp' => ['required', 'string', 'max:10'],
			'data.reference_id' => ['required', 'string']
		];
	}

	public function messages()
	{
		return [
			'data.otp_id.required' => vmsg('ChungThucVerifyOtpOtpIdLaBatBuoc'),
			'data.otp_id.string' => vmsg('ChungThucVerifyOtpOtpIdPhaiLaKieuChuoi'),
			'data.otp_id.max' => vmsg('ChungThucVerifyOtpOtpIdToiDaLa20KyTu'),

			'data.otp.required' => vmsg('ChungThucVerifyOtpOtpLaBatBuoc'),
			'data.otp.string' => vmsg('ChungThucVerifyOtpOtpPhaiLaKieuChuoi'),
			'data.otp.max' => vmsg('ChungThucVerifyOtpOtpCoDoDaiKhongQua10KyTu'),

			'data.reference_id.required' => vmsg('ChungThucVerifyOtpMaThamChieuLaBatBuoc'),
			'data.reference_id.string' => vmsg('ChungThucVerifyOtpMaThamChieuPhaiLaKieuChuoi'),
		];
	}
} // End class
