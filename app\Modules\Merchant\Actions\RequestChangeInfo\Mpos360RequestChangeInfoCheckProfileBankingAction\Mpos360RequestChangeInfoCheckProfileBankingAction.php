<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoCheckProfileBankingAction;

use Exception;
use App\Lib\partner\VMMC;
use Illuminate\Support\Str;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoCheckProfileBankingRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoGetConfigAction\Mpos360RequestChangeInfoGetConfigAction;

class Mpos360RequestChangeInfoCheckProfileBankingAction
{
	const STK_KHONG_DUNG = 0;
	const STK_KHONG_XAC_DINH = 1;
	const STK_HOP_LE = 2;

	public VMMC $vmmc;

	public function __construct(VMMC $vmmc)
	{
		$this->vmmc = $vmmc;
	}

	public function run(Mpos360RequestChangeInfoCheckProfileBankingRequest $request)
	{
		$requestVerify = [];

		foreach ($request->json('data') as $profileKey => $profileValue) {
			$requestVerify[] = [
				'field' => $profileKey,
				'value' => $profileValue,
				'status_verify' => '1',
				'date_verify' => now()->timestamp
			];
		}

		$params = $request->getParamCheckBankVmmc();
		
		$mnpConfigCache = app(Mpos360RequestChangeInfoGetConfigAction::class)->run();
		$params['bank_id'] = $mnpConfigCache['data']['bankMnpVimo'][$request->json('data.bankId')];
		
		mylog(['Thong tin TKNH can check' => $params]);

		$checkBankInfoResult = $this->vmmc->checkBankAccountMerchant($params);
		mylog(['Ket qua check' => $checkBankInfoResult]);
		if (empty($checkBankInfoResult['status_code_partner'])) {
			throw new BusinessException('Lỗi không kiểm tra được thông tin ngân hàng');
		}

		$code = $checkBankInfoResult['status_code_partner'];

		$returnData = [
			'status' => self::STK_KHONG_DUNG,
			'msg'    => 'Số tài khoản không đúng',
			'can'    => 'CAN_TIEP_TUC_TAO_YC',
			'request_vefify' => $requestVerify,
		];

		// Stk hợp lệ
		if ($code == '00') {
			if (
				!empty($checkBankInfoResult['data'])
				&& Str::contains($checkBankInfoResult['data']['bank_account_holder'], $params['bank_account_holder'])
			) {
				return [
					'status' => self::STK_HOP_LE,
					'msg'    => 'STK hợp lệ',
					'can'    => 'CAN_TIEP_TUC_TAO_YC',
					'request_vefify' => $requestVerify,
				];
			}

			throw new BusinessException('Hệ thống không xác định được STK của bạn là đúng');

			return $returnData;
		}

		throw new BusinessException('Hệ thống không xác định được STK của bạn là đúng');

		// Lỗi không xác định
		if ($code == '99') {
			return [
				'status' => self::STK_KHONG_XAC_DINH,
				'msg'    => 'STK hợp lệ',
				'can'    => 'CAN_TIEP_TUC_TAO_YC',
				'request_vefify' => $requestVerify,
			];
		}

		// Lỗi thông tin
		return $returnData;
	} 
} // End clas
