<?php

namespace App\Modules\TingBoxVAMC\Actions\SendOtpSwitchModeBankAction\SubAction;

use App\Exceptions\BusinessException;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;

class KiemTraDieuKienChuyenModeSubAction
{
	public function run(MerchantShopBank $mcShopBank)
	{
		$bankCode = $mcShopBank->merchantBank->bank_code;

		$msg = sprintf('Ngân hàng liên kết của bạn hiện chưa hoàn tất các điều kiện cần thiết để nhận tiền trực tiếp.%sVui lòng kiểm tra và hoàn tất liên kết ngân hàng.', PHP_EOL);

		// ACB chưa confirm lần 2
		if ($mcShopBank->merchantBank->bank_code == 'ACB') {
			if (!$mcShopBank->isDaLienKet() || $mcShopBank->notificationRequired != 'NO') {
				throw new BusinessException($msg, 5008);
			}
		}

		// Các bank khác chưa thực sự liên kết
		if (!$mcShopBank->isDaLienKet()) {
			throw new BusinessException($msg, 5008);
		}

		return true;
	}
}
