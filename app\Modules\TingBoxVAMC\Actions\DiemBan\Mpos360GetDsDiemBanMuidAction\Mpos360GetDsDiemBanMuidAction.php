<?php

namespace App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360GetDsDiemBanMuidAction;

use App\Lib\MnpOnboardNewMcHelper;
use App\Modules\TingBoxVAMC\Requests\Transaction\Mpos360GetDsDiemBanMuidRequest;

class Mpos360GetDsDiemBanMuidAction
{
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;	
	}

	public function run(Mpos360GetDsDiemBanMuidRequest $request)
	{
		$detailMc = $this->mnpOnboardNewMcHelper->detailMc(['mposMcId' => $request->json('data.merchantId')]);

		$listDiemBan = [];
		$listDiemBan[] = [ 'muid' => '', 'name' => 'Tất cả cửa hàng' ];

		if (!empty($detailMc['data']['locations'])) {
			foreach ($detailMc['data']['locations'] as $location) {
				if (empty($location['deviceDTOs'])) {
					continue;
				}

				$listMobileUser = collect($location['deviceDTOs'])->unique('mobileUserId')->toArray();

				foreach ($listMobileUser as $index => $mu) {
					$listDiemBan[] = [
						'muid' => $mu['mobileUserId'],
						'name' => sprintf('%s', $location['areaName'])
					];
				}
			}
		}

		$returnData = [
			'merchantId' => $request->json('data.merchantId'),
			'listDiemBan' => $listDiemBan
		];

		return $returnData;
	}
} // End class
