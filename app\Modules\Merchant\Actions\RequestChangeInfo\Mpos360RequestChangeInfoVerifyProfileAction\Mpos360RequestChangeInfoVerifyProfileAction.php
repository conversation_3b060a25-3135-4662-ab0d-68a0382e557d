<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoVerifyProfileAction;

use Exception;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Setting;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360CodeOtp;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoVerifyProfileRequest;

class Mpos360RequestChangeInfoVerifyProfileAction
{
	public bool $isFakeOtp = false;

	public array $otpSuccess = [];

	public function run(Mpos360RequestChangeInfoVerifyProfileRequest $request)
	{
		$id = $request->json('data.request_id');

		$mpos360McRequest = Mpos360MerchantRequest::query()->find($id);

		if (!$mpos360McRequest) {
			throw new BusinessException(__('dttv3.Lỗi: không tìm thấy yêu cầu của bạn'));
		}

		$deviceSession = $request->getCurrentDeviceSession();

		if ($deviceSession->getMerchantId() != $mpos360McRequest->merchant_id) {
			throw new BusinessException(__('dttv3.Lỗi: sai lệch bản ghi yêu cầu'));
		}

		$otp = trim($request->json('data.otp'));
		$otpId = $request->json('data.otp_id');

		$settingFakeOtp = Setting::query()->firstWhere(['key' => 'FAKE_OTP_PASSED', 'value' => '1']);

		if (optional($settingFakeOtp)->id) {
			$this->isFakeOtp = true;
		}

		$mpos360CodeOtp = Mpos360CodeOtp::query()
																		->where('id', $otpId)
																		->where('user_id', $deviceSession->user_id);

		if (!$this->isFakeOtp) {
			$mpos360CodeOtp = $mpos360CodeOtp->where('otp', $otp);
		}

		$mpos360CodeOtp = $mpos360CodeOtp->first();

		if (!$mpos360CodeOtp) {
			throw new BusinessException(__('dttv3.Mã OTP không đúng!'));
		}

		if ($this->isFakeOtp) {
			goto HAS_PASS_OTP;
		}

		if ($mpos360CodeOtp->isExpiredOtp()) {
			throw new BusinessException(__('dttv3.Mã OTP không đúng hoặc đã hết hạn, vui lòng kiểm tra lại trong tin nhắn, hoặc lấy lại mã khác'));
		}

		if ($mpos360CodeOtp->isFinalStatus()) {
			throw new BusinessException(__('dttv3.Lỗi: Otp đã được sử dụng'));
		}

		if ($mpos360CodeOtp->reference_id != $mpos360McRequest->id) {
			throw new BusinessException(__('dttv3.Lỗi: sai thông tin otp của yêu cầu'));
		}
		
		// Đoạn này là đã pass otp
		$mpos360CodeOtp->status = Mpos360Enum::MPOS360_OTP_DA_SU_DUNG;
		$r = $mpos360CodeOtp->save();

		if (!$r) {
			throw new BusinessException(__('dttv3.Lỗi: không cập nhật otp về trạng thái cuối'));
		}

		HAS_PASS_OTP:
		$mpos360McRequest->status_verify = Mpos360Enum::MPOS360_MC_VERIFY_STT_CHUA_XAC_THUC;
		$r = $mpos360McRequest->save();

		if (!$r) {
			throw new BusinessException(__('dttv3.Lỗi: không thể đánh dấu hồ sơ là đã xác thực'));
		}

		$dataRequest = json_decode($mpos360McRequest->data_request, true);
		$this->otpSuccess = $dataRequest[0]['otp_success'] ?? [];
		
		// sms otp -> ghi nhận hồ sơ otp thành công
		$requestVerify = [];
		$requestVerifyFromDB = collect($dataRequest[0]['request_vefify']);

		if ($mpos360CodeOtp->isSmsOtp()) {
			$isContains = $requestVerifyFromDB->contains('field', 'representMobile');

			if ($isContains) {
				$index = $requestVerifyFromDB->search('field', 'representMobile');
				unset($dataRequest[0]['request_vefify'][$index]);
			}

			$dataRequest[0]['request_vefify'][] = [
				'field' => 'representMobile',
				'value' => trim($mpos360CodeOtp->obj_value),
				'date_verify' => now()->timestamp,
				'status_verify' => 1,
			];

			$this->otpSuccess[] = 'representMobile';
		}
		

		if ($mpos360CodeOtp->isEmailOtp()) {
			$isContains = $requestVerifyFromDB->contains('field', 'representEmail');

			if ($isContains) {
				$index = $requestVerifyFromDB->search('field', 'representEmail');
				unset($dataRequest[0]['request_vefify'][$index]);
			}

			$dataRequest[0]['request_vefify'][] = [
				'field' => 'representEmail',
				'value' => trim($mpos360CodeOtp->obj_value),
				'date_verify' => now()->timestamp,
				'status_verify' => 1,
			];

			$this->otpSuccess[] = 'representEmail';
		}

		if (!empty($requestVerify)) {
			$mpos360McRequest->forceFill(['data_request' => $dataRequest, 'time_updated' => now()->timestamp])->update();
		}

		if (!empty($this->otpSuccess)) {
			$mpos360McRequest->refresh();
			$dataRequest = $mpos360McRequest->getDataRequestV3();
			$dataRequest[0]['otp_success'] = $this->otpSuccess;
			$mpos360McRequest->data_request = json_encode($dataRequest);
			$r = $mpos360McRequest->save();

			if (!$r) {
				throw new BusinessException('Lỗi không lưu đánh dấu otp là thành công');
			}
		}
		
		return [
			'id' => $mpos360McRequest->id,
			'status' => '1',
			'msg' => 'Thanh cong'
		];
	} // End methds
} // End class
