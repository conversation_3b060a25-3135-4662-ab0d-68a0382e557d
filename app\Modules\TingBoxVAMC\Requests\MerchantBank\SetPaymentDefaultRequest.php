<?php

namespace App\Modules\TingBoxVAMC\Requests\MerchantBank;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class SetPaymentDefaultRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'data' => ['required', 'array'],
						'data.username' => ['present', 'string'],
            'data.merchantShopBankId' => ['required', 'string'],
            'data.merchantId' => ['required', 'string'],
            'data.mobileUserId' => ['required', 'string'],
        ];
    }

    public function messages()
    {
        return [
            'data.merchantShopBankId.required' => 'Mã Id shop bank là bắt buộc',
            'data.merchantShopBankId.string' => 'Mã Id shop bank phải là kiểu chuỗi',
            'data.merchantId.required' => 'Id merchant là bắt buộc',
            'data.merchantId.string' => 'Id merchant phải là kiểu chuỗi',
            'data.mobileUserId.required' => 'Mã định danh mobile là bắt buộc',
            'data.mobileUserId.string' => 'Mã định danh mobile là kiểu chuỗi',
        ];
    }
}
