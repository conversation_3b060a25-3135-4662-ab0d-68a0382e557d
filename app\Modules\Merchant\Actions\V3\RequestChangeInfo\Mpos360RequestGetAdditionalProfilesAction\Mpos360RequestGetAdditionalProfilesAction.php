<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestGetAdditionalProfilesAction;

use App\Exceptions\BusinessException;
use App\Lib\SettingHelper;
use Illuminate\Http\Request;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForRepresentationSA\SA\AddAdditionalProfileV3SA;

class Mpos360RequestGetAdditionalProfilesAction
{
	public function run(Request $request)
	{
		$id = $request->json('data.id');
		
		if (empty($id)) {
			throw new BusinessException(__('dttv3.Id yêu cầu là bắt buộc'));
		}

		$mpos360McRequest = Mpos360MerchantRequest::query()->with('mpos360McSupplementNew')->find($id);

		if (!$mpos360McRequest) {
			throw new BusinessException(__('dttv3.Lỗi: YC đổi thông tin không tồn tại'));
		}

		$warning = '';

		// Là yc phải bổ sung thông tin
		if ($mpos360McRequest->mpos360McSupplementNew) {
			$warning = $mpos360McRequest->mpos360McSupplementNew->supplement_reason;
		}

		$returnData = [
			'id' => $mpos360McRequest->id,
			'additional_profiles' => app(AddAdditionalProfileV3SA::class)->run($mpos360McRequest),
			'warningText' => $warning,
			'settingVideo' => SettingHelper::getSettingVideo()
		];

		return $returnData;
	}
} // End class
