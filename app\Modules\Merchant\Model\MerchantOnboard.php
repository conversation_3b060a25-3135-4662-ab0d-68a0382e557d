<?php

namespace App\Modules\Merchant\Model;

use Illuminate\Database\Eloquent\Model;

class MerchantOnboard extends Model
{
	/**
	 * Kết nối database
	 *
	 * @var string
	 */
	protected $connection = 'mysql';

	/**
	 * Tên bảng
	 *
	 * @var string
	 */
	protected $table = 'merchant_onboard';

	/**
	 * Các trường có thể gán giá trị
	 *
	 * @var array
	 */
	protected $guarded = [];

	/**
	 * Tắt timestamps mặc định của <PERSON>
	 *
	 * @var bool
	 */
	public $timestamps = false;

	/**
	 * Các trường datetime
	 *
	 * @var array
	 */
	protected $dates = [
		'createdAt',
		'updatedAt'
	];

	/**
	 * Các trường ẩn khi serialize
	 *
	 * @var array
	 */
	protected $hidden = [];

	/**
	 * Primary key
	 *
	 * @var string
	 */
	protected $primaryKey = 'id';

	/**
	 * Tự động tăng ID
	 *
	 * @var bool
	 */
	public $incrementing = true;
}
