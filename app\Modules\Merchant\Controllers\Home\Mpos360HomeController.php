<?php

namespace App\Modules\Merchant\Controllers\Home;

use App\Lib\Helper;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\Home\Mpos360ShowQrCuaHangRequest;
use App\Modules\Merchant\Requests\Home\Mpos360GetHomePageInfoRequest;
use App\Modules\Merchant\Actions\Home\Mpos360ShowQrCuaHangAction\Mpos360ShowQrCuaHangAction;
use App\Modules\Merchant\Actions\Home\Mpos360GetHomePageInfoAction\Mpos360GetHomePageInfoAction;

class Mpos360HomeController extends Controller
{
	public function Mpos360GetHomePageInfo(Mpos360GetHomePageInfoRequest $request)
	{
		try {
			$result = app(Mpos360GetHomePageInfoAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360ShowQrCuaHang(Mpos360ShowQrCuaHangRequest $request)
	{
		try {
			$result = app(Mpos360ShowQrCuaHangAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
} // End class
