<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessAction;

use App\Exceptions\BusinessException;
use Exception;
use App\Lib\Helper;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\SubAction\GetPhuongThucQuetB3SubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessAction\SubAction\XuLyCaseThayDoiCccdSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessAction\SubActionV2\KiemTraPhuongThucKySubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\SubAction\GetPhuongThucQuetB3Ver2SubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessAction\SubAction\UpdateVerifyInfoAndProfileSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAttachSignatureAction\SubAction\GetCanByRequestChangeInfoSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\GetMNPAccessTokenSubAction;

/**
 * [OLD] - Ngữ cảnh: Đổi CCCD mới hoặc đổi luôn người đại diện mới
 * -> QTS bước 2, và kiểm tra có khớp với bước 1 hay không?
 * 			>nếu khớp thì không cần scan bước 3
 * 		  >nếu không khớp thì phải scan bước 3
 * 
 * Lưu ý: ở bước này cũng cần lấy qts_request_id rồi bóc tách
 * để lấy được CCCD mới, SĐT mới, Email mới vào trong danh sách hồ sơ
 * 
 * [Update 23.09.2024] - Khi xử lý xong thì điều hướng can ra màn hình danh sách chữ ký
 */
class Mpos360RequestChangeInfoAllOtpProfileSuccessV2Action
{
	public bool $isQtsKhopChungThuc = false;

	public function run(Mpos360RequestChangeInfoAllOtpProfileSuccessRequest $request)
	{
		$id = $request->json('data.id');
		$mpos360McRequest = Mpos360MerchantRequest::query()->find($id);

		if (!$mpos360McRequest) {
			throw new BusinessException('Lỗi: không tìm thấy thông tin yêu cầu');
		}

		$deviceSession = $request->getCurrentDeviceSession();

		if ($deviceSession->getMerchantId() != $mpos360McRequest->merchant_id) {
			throw new BusinessException('Lỗi: Bạn không phải chủ sở hữu yc này');
		}

		$requestVefify = $request->json('data.request_vefify');

		$nguoiDaiDienData = [
			'isTrungCccdChungThuc' => false,
			'profileLayTuCccd' => []
		];

		// Đổi người đại diện mới 
		if ($mpos360McRequest->isYeuCauDoiNguoiDaiDienMoi()) {
			$isDayDuKey = Helper::isDayDuKey(
				['passport', 'authoriserContactNumber', 'authoriserEmail', 'representPosition', 'representMutualRelation'],
				$request->json('data.request_vefify')
			);

			if (!$isDayDuKey) {
				$msg = 'Lỗi: phải đầy đủ thông tin CCCD, Email, SĐT, Vị trí & Mối quan hệ của người đại diện mới';
				throw new BusinessException($msg);
			}

			$representPosition = collect($requestVefify)->where('profileKey', 'representPosition')->first();
			if (strlen($representPosition['value']) < 3) {
				throw new BusinessException('Vị trí/Chức vụ phải có độ dài tối thiểu 3 ký tự');
			}

			if (strlen($representPosition['value']) > 255) {
				throw new BusinessException('Vị trí/Chức vụ phải có độ dài tối đa 255 ký tự');
			}

			$representMutualRelation = collect($requestVefify)->where('profileKey', 'representMutualRelation')->first();
			if (strlen($representMutualRelation['value']) < 3) {
				throw new BusinessException('Mối quan hệ phải có độ dài tối thiểu 3 ký tự');
			}

			if (strlen($representMutualRelation['value']) > 255) {
				throw new BusinessException('Mối quan hệ phải có độ dài tối đa 255 ký tự');
			}
		}

		if ($mpos360McRequest->isYeuCauDoiCccdMoi() || $mpos360McRequest->isYeuCauDoiNguoiDaiDienMoi()) {
			$nguoiDaiDienData = app(XuLyCaseThayDoiCccdSubAction::class)->run($mpos360McRequest, $requestVefify);

			if ($mpos360McRequest->isYeuCauDoiCccdMoi() && empty($nguoiDaiDienData['isTrungCccdChungThuc'])) {
				$msg = 'Lỗi: bạn đổi chọn CCCD của người hiện tại nhưng hệ thống phát hiện CCCD mới là của 1 người khác. Từ chối xử lý';
				throw new BusinessException($msg);
			}
		}
		
		// Update các hồ sơ đã xác thực vào trong bản ghi yêu cầu
		app(UpdateVerifyInfoAndProfileSubAction::class)->run(
			$mpos360McRequest, 
			$nguoiDaiDienData,
			$request->getRequestVefifyAsArray(),
			$request->json('data.attachments')
		);
		
		$mpos360McRequest->refresh();

		$deviceSessionWithToken = app(GetMNPAccessTokenSubAction::class)->run($deviceSession);
		$kiemTraPhuongThucKy = app(KiemTraPhuongThucKySubAction::class)->run($mpos360McRequest, $deviceSessionWithToken);


		$returnData = [
			'id' => $mpos360McRequest->id,
			'msg' => 'Ghi nhận kết quả thành công',
			'can' => $kiemTraPhuongThucKy['can'],
			'list_sign_method' => $kiemTraPhuongThucKy['sign_method'],
			'scan_method' => app(GetPhuongThucQuetB3Ver2SubAction::class)->run($mpos360McRequest->merchant_id)
		];

		return $returnData;
	}
} // End class
