<?php

namespace App\Lib\partner;

use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlertWarning;

class MNP
{
    private $secretKey;
    public $baseUrl;
    public $baseUrl2;
    public $baseUrlUpload;
    public $baseUrlUploadDocuments;
    public $baseUrlCustomer;
    private $baseUrlInteg;
    public $authToken;
    private $appId;

    public function __construct()
    {
        $this->secretKey = env('API_PARTNER_MNP_SECRET_KEY');
        $this->baseUrl = env('API_PARTNER_MNP_API_URL');
        $this->baseUrl2 = env('API_PARTNER_MNP_API_URL2');
        $this->baseUrlUpload = env('API_PARTNER_MNP_API_UPLOAD');
				$this->baseUrlUploadDocuments = env('API_PARTNER_MNP_API_UPLOAD_DOCUMENTS');
        $this->baseUrlCustomer = env('API_PARTNER_MNP_API_CUSTOMER');
        $this->baseUrlInteg = env('API_PARTNER_MNP_API_INTEG');
    }

    public function sendRequest($endpoint, $method = 'GET', $data = null)
    {
        $url = $this->baseUrl . $endpoint;
        mylog(['url' => $url, 'method' => $method, 'data' => $data]);
        
        $curl = curl_init();

        $headers = [
            // 'app-id: ' . $this->appId,
            'Content-Type: application/json'
        ];
        if ($this->authToken) {
            $headers[] = 'Authorization: Bearer ' . $this->authToken;
        }
        $jsonData = json_encode($data);
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
						CURLOPT_CONNECTTIMEOUT => 30,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_POSTFIELDS => $jsonData,
            CURLOPT_HTTPHEADER => $headers,
        ));

				try {
					$response = curl_exec($curl);
					mylog(['response' => @json_decode($response, true)]);
				}catch(\Throwable $th) {
                    TelegramAlertWarning::sendMessage(Helper::traceError($th));
					mylog(['ExecCurlErr' => $th->getMessage()]);
					return [
            "success" => 500,
            "code" => "ERROR",
            "httpStatus" =>  "ERROR",
            "message" =>  "Lỗi kết nối đối tác mnp",
        	];
				}

        
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
				curl_close($curl);
        if ($httpCode == 200) {
            return json_decode($response, true);
        }
        if ($httpCode > 200) {
            $res = @json_decode($response, true);
            if ($res) {
                return [
                    "success" =>  $httpCode,
                    "code" => "ERROR",
                    "httpStatus" =>  "ERROR",
                    "message" =>  isset($res['error']) ? $res['error'] : "LỖI KHÔNG XÁC ĐỊNH",
                ];
            }
        }
        return [
            "success" =>  $httpCode,
            "code" => "ERROR",
            "httpStatus" =>  "ERROR",
            "message" =>  "LỖI KHÔNG XÁC ĐỊNH",
        ];
    }
    private function createChecksum($str)
    {
        return md5($str . $this->secretKey);
    }

    public function getConfig()
    {
        $data = [];
        $result = $this->sendRequest('/get-cfg', 'GET', $data);
        return $this->returnData($result);
    }
    public function getToken($mcId)
    {
        $this->baseUrl = $this->baseUrl2;

        $data = [
            'checkId' => $mcId,
            'checksum' => $this->createChecksum($mcId), // checksum tạo bằng md5(checkId+secretKey)
        ];
        $result = $this->sendRequest('/get-token', 'POST', $data);
        return $this->returnData($result);
    }
    public function requestUpdateData($mposId, $typeChanges = 'CHANGE_BANK_ACCOUN_INFO', $token, $params)
    {
        $this->baseUrl = $this->baseUrl2;
        $arrTypeChanges[] = $typeChanges;
        $this->authToken = $token;
        $data = [
            'mposId' => $mposId,
            'arrTypeChanges' => $arrTypeChanges,
        ];
        if ($typeChanges === 'CHANGE_BANK_ACCOUN_INFO') {
            $allowedKeys = [
                'bankId',
                'bankName',
                'holderName',
                'accountNo',
                'branch',
                'bankCityCode',
                'bankCityName',
                'bankAccType',
                'positionAuthBank', //phu
                'bankMutualRelation', //phu
                'passportAuthoriserFrontUrl', // phu URL của ảnh hộ chiếu người ủy quyền.  (Mặt trước)
                'passportAuthoriserBackUrl', // phu   URL của ảnh hộ chiếu người ủy quyền. (Mặt sau)
                'substituteCertUrls', // phu URL của giấy chứng từ thay thế
                'attachedCertUrls', // phu URL của giấy chứng nhận đính kèm.
                'lostEmailUrls', // phu Ảnh chụp hoặc video bạn đang cầm CCCD và phụ lục
                'lostPassportUrls', // phu Ảnh xác nhận căn cước công dân của bộ công an
            ];
        } elseif ($typeChanges == 'CHANGE_REPRESENT_INFO') {
            // $data['typeChangeRepresent'] = 'CHANGE_NEW_REPRESENT';
            $allowedKeys = [
                'representName',
                'typeChangeRepresent',
                'representPassport',
                'representIssuePlace',
                'representEmail',
                'representMobile',
                'representBirthday',
                'representCurrentAddress',
                'representPermanentAddress',
                "representPosition",
                "representMutualRelation",
                "representAuthContractNumber", //phu
                "passportRepresentFrontUrl", //phu
                "passportRepresentBackUrl", //phu
                'passportAuthoriserFrontUrl', // phu URL của ảnh hộ chiếu người ủy quyền.  (Mặt trước)
                'passportAuthoriserBackUrl', // phu   URL của ảnh hộ chiếu người ủy quyền. (Mặt sau)
                'substituteCertUrls', // phu URL của giấy chứng từ thay thế
                'attachedCertUrls', // phu URL của giấy chứng nhận đính kèm.
                'lostEmailUrls', // phu Ảnh chụp hoặc video bạn đang cầm CCCD và phụ lục
                'lostPassportUrls', // phu Ảnh xác nhận căn cước công dân của bộ công an
            ];
        } else {
            $result['message'] = 'MNP- Loại thay đổi chưa hỗ trợ';
            return $this->returnData($result);
        }
        // Chỉ merge các key được phép
        foreach ($allowedKeys as $key) {
            if (isset($params[$key])) {
                $data[$key] = $params[$key];
            }
        }


        // Loại bỏ các tham số null
        $data = array_filter($data, function ($value) {
            return $value !== null;
        });

       

        $result = $this->sendRequest('/create', 'POST', $data);

        mylog([
					'url' => '/create',
					'method' => 'POST',
					'data' => $data,
					'result' => $result
				]);
        return $this->returnData($result);
    }

    public function validate($mposId, $typeChanges = 'CHANGE_BANK_ACCOUN_INFO', $token, $params)
    {
        $this->baseUrl = $this->baseUrl2;
        $arrTypeChanges[] = $typeChanges;
        $this->authToken = $token;
        $data = [
            'mposId' => $mposId,
            'arrTypeChanges' => $arrTypeChanges,
        ];
        if ($typeChanges === 'CHANGE_BANK_ACCOUN_INFO') {
            $allowedKeys = [
                'bankId',
                'bankName',
                'holderName',
                'accountNo',
                'branch',
                'bankCityCode',
                'bankCityName',
                'bankAccType',
                'positionAuthBank', //phu
                'bankMutualRelation', //phu
                'passportAuthoriserFrontUrl', // phu URL của ảnh hộ chiếu người ủy quyền.  (Mặt trước)
                'passportAuthoriserBackUrl', // phu   URL của ảnh hộ chiếu người ủy quyền. (Mặt sau)
                'substituteCertUrls', // phu URL của giấy chứng từ thay thế
                'attachedCertUrls', // phu URL của giấy chứng nhận đính kèm.
                'lostEmailUrls', // phu Ảnh chụp hoặc video bạn đang cầm CCCD và phụ lục
                'lostPassportUrls', // phu Ảnh xác nhận căn cước công dân của bộ công an
            ];
        } elseif ($typeChanges == 'CHANGE_REPRESENT_INFO') {
            // $data['typeChangeRepresent'] = 'CHANGE_NEW_REPRESENT';
            $allowedKeys = [
                'representName',
                'typeChangeRepresent',
                'representPassport',
                'representIssuePlace',
                'representEmail',
                'representMobile',
                'representBirthday',
                'representCurrentAddress',
                'representPermanentAddress',
                "representPosition",
                "representMutualRelation",
                "representAuthContractNumber", //phu
                "passportRepresentFrontUrl", //phu
                "passportRepresentBackUrl", //phu
                'passportAuthoriserFrontUrl', // phu URL của ảnh hộ chiếu người ủy quyền.  (Mặt trước)
                'passportAuthoriserBackUrl', // phu   URL của ảnh hộ chiếu người ủy quyền. (Mặt sau)
                'substituteCertUrls', // phu URL của giấy chứng từ thay thế
                'attachedCertUrls', // phu URL của giấy chứng nhận đính kèm.
                'lostEmailUrls', // phu Ảnh chụp hoặc video bạn đang cầm CCCD và phụ lục
                'lostPassportUrls', // phu Ảnh xác nhận căn cước công dân của bộ công an
            ];
        } else {
            $result['message'] = 'MNP- Loại thay đổi chưa hỗ trợ';
            return $this->returnData($result);
        }
        // Chỉ merge các key được phép
        foreach ($allowedKeys as $key) {
            if (isset($params[$key])) {
                $data[$key] = $params[$key];
            }
        }


        // Loại bỏ các tham số null
        $data = array_filter($data, function ($value) {
            return $value !== null;
        });

        mylog([
            'url' => '/validate',
            'method' => 'POST',
            'data' => $data,
        ]);

        $result = $this->sendRequest('/validate', 'POST', $data);

        mylog(['result' => $result]);
        return $this->returnData($result);
    }

		public function sign($params=[], $token)
    {
			$this->baseUrl = $this->baseUrl2;
			$this->authToken = $token;
			$data = $params;
			$result = $this->sendRequest('/sign', 'POST', $data);
			return $this->returnData($result);
    }

    public function customerVerifyInfo($mposId, $token, $info = [])
    {
        $this->baseUrl = $this->baseUrlCustomer;
        $this->authToken = $token;
        $data = [
            'mposId' => $mposId,
            'infos' => $info
        ];
        $result = $this->sendRequest('/cnps-cstm-customer/customer-verify-info', 'POST', $data);
        return $this->returnData($result);
    }
    public function getVerifyInfo($mposId, $token)
    {
        $this->baseUrl = $this->baseUrlCustomer;
        $this->authToken = $token;
        $data = [
            'mposId' => $mposId
        ];
        $result = $this->sendRequest('/cnps-cstm-customer/get-verify-info', 'POST', $data);
        return $this->returnData($result);
    }
    public function getCustomerInfo($mposId, $token)
    {
        $this->baseUrl = $this->baseUrlCustomer;
        $this->authToken = $token;
        $data = [
            'mposId' => $mposId
        ];
        $result = $this->sendRequest('/cnps-cstm-customer/get-by-mpos-id', 'POST', $data);
        return $this->returnData($result);
    }
    public function sentOtp($mobile, $otp, $token, $isZns=false)
    {
        $this->baseUrl = $this->baseUrlInteg;
        $this->authToken = $token;
        $data = [
            'mobile' => $mobile,
            'otp' => $otp,
        ];
				if ($isZns) {
					$data['isZns'] = true;
				}
        $result = $this->sendRequest('/api/otp/send-otp', 'POST', $data);
        return $this->returnData($result);
    }

    public function sentEmail($data, $token)
    {
        /*
data = [
"cc"=>  [ "<EMAIL>" ], //email cc
 "html"=>  "String",
  "subject"=>  "ĐÂY LÀ TIÊU ĐỀ MAIL", // tieu de
  "to"=> ["<EMAIL>"]//email người nhận
    ]

        */
        $this->baseUrl = $this->baseUrlInteg;
        $this->authToken = $token;
        $result = $this->sendRequest('/api/notify-email/send-email', 'POST', $data);
        return $this->returnData($result);
    }

    public function uploadFileToS3($file, $token, $folderName = 'user/mpos360/file', $fileType='IMAGE')
    {
			$key = sprintf('MNPS3-UPLOAD-%s', mt_rand());
			$log[$key]['Request'] = [
				'file' => $file,
				'token' => $token,
				'folderName' => $folderName,
				'fileType' => $fileType,
			];

        try {
            $this->baseUrl = $this->baseUrl2;
            $this->authToken = $token;

						if ($fileType == 'IMAGE') {
							$url = $this->baseUrlUpload;
						}

						if ($fileType == 'FILE') {
							$url = $this->baseUrlUploadDocuments;
						}

						$log[$key]['url'] = $url;

            $curl = curl_init();

            $headers = [
                'Authorization: Bearer ' . $this->authToken,
                'Content-Type: multipart/form-data'
            ];

            $postFields = [
                'folderName' => $folderName,
                'files' => new \CURLFile($file['tmp_name'], $file['type'], $file['name'])
            ];

						$log[$key]['Request']['postField'] = $postFields;

            curl_setopt_array($curl, array(
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 20,
								CURLOPT_CONNECTTIMEOUT => 20,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => $postFields,
                CURLOPT_HTTPHEADER => $headers,
            ));

            $response = curl_exec($curl);
						if (!$response) {
							mylog(['Goi sang mnp bi false' => 'ok']);
						}
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

						$log[$key]['Response'] = $response;
            if ($httpCode == 200) {
                $result = json_decode($response, true);
								$log[$key]['Result'] = $result;

								mylog($log);
                return $this->returnData($result);
            } else {
								mylog($log);
                return [
                    'status' => false,
                    'data' => null,
                    'message' => 'Upload failed. HTTP Code: ' . $httpCode
                ];
            }
        } catch (\Throwable $th) {
            TelegramAlertWarning::sendMessage(Helper::traceError($th));
						curl_close($curl);
						$log[$key]['Error'] = sprintf('File: %s - Line: %s', $th->getFile(), $th->getLine());
						mylog($log);
            $result['message'] = 'UN_ERROR';
            return $this->returnData($result);
        }
    }

    public function returnData($result)
    {
        // Xử lý và trả về dữ liệu theo định dạng mong muốn
        // Ví dụ:
        if (isset($result['code']) && $result['code'] === 1000) {
            return [
                'status' => true,
                'data' => $result['data'] ?? null,
                'message' => $result['message'] ?? 'Success',
								'meta' => $result['meta'] ?? []
            ];
        } else {
            return [
                'status' => false,
                'data' => null,
                'message' => $result['message'] ?? 'An error occurred'
            ];
        }
    }
}
