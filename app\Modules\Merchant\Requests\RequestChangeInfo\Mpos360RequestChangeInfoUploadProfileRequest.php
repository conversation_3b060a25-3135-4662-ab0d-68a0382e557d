<?php

namespace App\Modules\Merchant\Requests\RequestChangeInfo;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360RequestChangeInfoUploadProfileRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.email' => ['present', 'string'],
			'data.attachments' => ['required', 'array', 'max:25'],
			'data.attachments.*' => [
				'required',
				'file',
				'mimetypes:image/jpeg,image/png,image/svg+xml,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,video/mp4,video/x-msvideo,video/x-ms-wmv,video/quicktime,video/mpeg,video/3gpp,video/ogg,video/webm,video/x-flv,video/x-matroska,video/x-m4v',
				'max:25600'
			]
		];
	}

	public function messages() {
		return [
			'data.email.required' => vmsg('Upload_EmailLaBatBuoc'),
			'data.email.string' => vmsg('Upload_EmailPhaiLaKieuChuoi'),
			'data.email.email' => vmsg('Upload_EmailKhongDungDinhDang'),

			'data.attachments.required' => vmsg('Upload_FileDinhKemLaBatBuoc'),
			'data.attachments.array' => vmsg('Upload_FileDinhKemPhaiLaMotDanhSach'),
			'data.attachments.max' => vmsg('Upload_DanhSachFileDinhKemToiDaLa25File'),
			'data.attachments.*.required' => vmsg('Upload_PhanTuFileDinhKemLaBatBuoc'),
			'data.attachments.*.mimetypes' => vmsg('Upload_FileDinhKemChiChapChanMimeType'),
			'data.attachments.*.max' => 'Dung lượng upload tối đa là 25MB',
		];
	}
}
