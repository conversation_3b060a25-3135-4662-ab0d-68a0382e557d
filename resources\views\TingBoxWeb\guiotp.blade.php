<div class="modal-header">
    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"> </button>
</div>
<div class="modal-body">
    <form id="sendotp" autocomplete="off">

        <p class="text-center"><img src="assets/img/maOTP.svg"></p>
        <h3 class="BE-bold f20 text-center mb-3">Nhận mã OTP</h3>
        <p class="f16 text-center">M<PERSON> x<PERSON><PERSON> thực được gửi đến số điện thoại<span class="BE-bold"> {{ $phone }},</span> Vui
            lòng chọn hình thức nhận mã</p>

        <p class="text-center mb-4">
            <button type="button" id="zaloOtp" class="text-center btn-blue w-100 d-block" style="border: none;" onclick="return submitSendOtp('zalo');">Nhận mã qua Zalo</button>
            <input type="hidden" name="" value="zalo">
        </p>
        <p class="text-center">
            <button type="button" id="smsOtp" class="cl4040 f16 w-100" style="border: none;background: #fff;" onclick="return submitSendOtp('sms');">Nhận mã qua SMS</button>
            <input type="hidden" name="" value="sms">
        </p>
    </form>
</div>
<script>
	let isSubmitting = false;

	function submitSendOtp(channelSendOtp) {
			if (isSubmitting) return; // Ngăn gửi nhiều lần
			isSubmitting = true;

			const formData = {
					channel: channelSendOtp,
					data: "{{ $data }}",
					phone: "{{ $phone }}",
			};
			$('#loadingPage').addClass('se-pre-con');

			$.ajax({
					url: "/TBWebMpos360SendOtp",
					type: "POST",
					headers: {
							"X-CSRF-TOKEN": "{{ csrf_token() }}",
					},
					contentType: "application/json",
					data: JSON.stringify(formData),
					success: function(data) {
							if (data.success) {
									$('#sendOtpModal').modal('hide');
									$('#main-content').html(data.html);

							} else {
                                alert(data.message);
							}
							$('#loadingPage').removeClass('se-pre-con');
					},
					error: function(error) {
							
							$('#loadingPage').removeClass('se-pre-con');
                            alert("Có lỗi xảy ra, Hãy thông báo kỹ thuật hỗ trợ xử lý.");
					},

					complete: function () {
						isSubmitting = false;
					}
			});
	}
</script>