<?php

namespace App\Modules\Merchant\Actions\ServiceProgramRegister\Mpos360ServiceProgramRegisterListAction;

use App\Exceptions\BusinessException;
use App\Lib\partner\MNPServiceProgram;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\GetMNPAccessTokenSubAction;
use App\Modules\Merchant\Actions\ServiceProgramRegister\Mpos360ServiceProgramRegisterListAction\SubAction\MappingEnabledAndRegisterSubAction;
use App\Modules\Merchant\Actions\ServiceProgramRegister\Mpos360ServiceProgramRegisterListAction\SubAction\MappingServiceProgramSubAction;
use App\Modules\Merchant\Enums\ServiceProgramRegisterEnum;
use App\Modules\Merchant\Model\Mpos360MerchantRequestServiceProgramRegister;
use App\Modules\Merchant\Requests\ServiceProgramRegister\Mpos360ServiceProgramRegisterListRequest;
use Carbon\Carbon;
use Exception;

class Mpos360ServiceProgramRegisterListAction
{
	public MNPServiceProgram $mnpServiceProgram;

	public function __construct(MNPServiceProgram $mnpServiceProgram)
	{
		$this->mnpServiceProgram = $mnpServiceProgram;
	}

	/**
	 * Làm việc với HuyHQ MNP cần clear rõ các vấn đề sau:
	 * 1. MNP không có bảng quản lý dịch vụ
	 * 2. Lúc lấy danh sách, MNP truy vấn bản ghi cuối cùng của dịch vụ để ra quyết định
	 * 		- Nếu mảng rỗng (MC chưa đăng ký dịch vụ)
	 * 		- Mảng có giá trị:
	 * 				status == CANCELD -> hủy: có thể ký lại
	 * 				status != CANCLED -> chờ
	 */

	public function run(Mpos360ServiceProgramRegisterListRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		$deviceSessionWithToken = app(GetMNPAccessTokenSubAction::class)->run($deviceSession);
		$merchantId = $deviceSessionWithToken->getMerchantId();

		$listServiceProgram = $this->mnpServiceProgram->sendRequest(
			'/api/scon/partner/get-list', 
			'POST', 
			[ 'mposId' => $merchantId ],
			$deviceSessionWithToken->mnp_token
		);

		
		if (empty($listServiceProgram['result'])) {
			$msg = sprintf('[MNP Error] - %s (Code: %s)', $listServiceProgram['message'] ?? 'Unknow', $listServiceProgram['code'] ?? '00');
			throw new BusinessException($msg);
		}


		$mapping = MappingServiceProgramSubAction::run();

		// Không có dữ liệu -> MC chưa ký tá cáig gì cả
		if (empty($listServiceProgram['data'])) {
			$returnData = [
				'rows' => 1,
				'data' => [
					[
						'service_program_id' => 'HOME_PAY_LATER',
						'service_code' => 'HOME_PAY_LATER',
						'service_name' => 'Mua trước trả sau với Home PayLater',
						'desc' => 'Dịch vụ cho phép mua hàng trước, trả tiền sau với Home PayLater',
						'content' => '',
						'service_detail_url' => $mapping['services']['HOME_PAY_LATER']['service_detail_url'],
						'status_enabled' => 'YES',
						'status_register' => 'NO',
						'status_mobile' => '',
						'icon' => cumtomAsset('images/serviceProgramRegister/HomeCredit.png'),
						'other_data' => [
							'status_mobile' => (object) [],
						],
						'time_sent' => '',
						'time_actived' => ''
					],
				]
			];
		}

		if (!empty($listServiceProgram['data'])) {

			$returnData = [
				'rows' => count($listServiceProgram['data']),
				'data' => []
			];

			
			/**
			 * [
					"appendixId" => "6756b36462beec54b4f48dd6"
					"code" => "PLHPL01"
					"contractId" => "64c49ba572b2cf7796e41cb2"
					"codePaper" => "TESTHD05"
					"codeHDDT" => "TESTHD05"
					"dateCreatedAppendixDTO" => 1733735268457
					"isApproveFee" => false
					"isDefaultFee" => true
					"isCreatedWithContract" => false
					"state" => "NEW"
					"signStatus" => "YET_SIGNED"
					"typeAppendix" => "HOME_PAY_LATER"
					"appendixHomePayLater" => array:2 [
						"codeAppend" => "PLHPL01"
						"isDefaultFee" => true
					]
					"reasonRejected" => []
					"reasonRejectedHandover" => []
					"createAppendixBy" => "5f408d0cf378e007e9efcd3b"
					"signType" => "SIGN_ECONTRACT"
				]
			 */
			$list = [];

			$listYcDangKyDichVu = Mpos360MerchantRequestServiceProgramRegister::query()
				->where('merchant_id', $merchantId)
				->whereIn('status', [ServiceProgramRegisterEnum::SPR_STT_TAO_XONG_YC, ServiceProgramRegisterEnum::SPR_STT_DANG_DAY_YC])
				->where('record_push_count', '<', 3)
				->get()
				->keyBy('service_program_id');

			foreach ($listServiceProgram['data'] as $item) {
				$serviceCode = $item['typeAppendix'];
				$statusData = $mapping['status'][$item['state']];
				
				$mobileItem = [
					'service_program_id' => $item['typeAppendix'],
					'service_code' => $item['typeAppendix'],
					'service_name' => $mapping['services'][$serviceCode]['service_name'],
					'desc' => $mapping['services'][$serviceCode]['service_desc'],
					'content' => '',
					'service_detail_url' => $mapping['services'][$serviceCode]['service_detail_url'],
					'status_enabled' => 'YES',
					'status_register' => MappingEnabledAndRegisterSubAction::run($statusData),
					'status_mobile' => '',
					'icon' => $mapping['services'][$serviceCode]['icon'],
					'other_data' => [],
					'time_sent' => sprintf('Gửi: %s', Carbon::createFromTimestampMs($item['dateCreatedAppendixDTO'])->format('H:i, d/m/Y')),
					'time_actived' => ''
				];

				// Nếu chưa ký hoặc phụ lục bị hủy => coi như là chưa ký (đc phép ký lại)
				if ($statusData['status_phuluc']['code'] == 'CHUA_KY' || $statusData['status_phuluc']['code'] == 'HUY_KY') {
					$mobileItem['status_register'] = 'NO';
				}

				if (!empty($item['dateApprovedDTO'])) {
					$mobileItem['time_actived'] = sprintf('Kích hoạt: %s', Carbon::createFromTimestampMs($item['dateApprovedDTO'])->format('H:i, d/m/Y'));
				}

				// đang có yc nhưng chưa đẩy sang mnp
				if ($listYcDangKyDichVu->has($serviceCode)) {
					$mobileItem['status_register'] = 'YES';
					$mobileItem['other_data']['status_mobile'] = [
						'code' => 'DANG_GUI_YEU_CAU',
						'text' => 'Đang gửi yêu cầu',
						'other_data' => (object) []
					];
				}

				if (!$listYcDangKyDichVu->has($serviceCode)) {
					// gửi sang bên mnp rồi thì mới cần show text
					if ($mobileItem['status_register'] == 'YES') {
						$mobileItem['other_data']['status_mobile'] = $statusData['status_mobile'];
					}else {
						$mobileItem['other_data'] = (object) $mobileItem['other_data'];
					}
				}
				

				$list[] = $mobileItem;
			}

			$returnData['data'] = $list;
		}
		return $returnData;
	}
} // End class