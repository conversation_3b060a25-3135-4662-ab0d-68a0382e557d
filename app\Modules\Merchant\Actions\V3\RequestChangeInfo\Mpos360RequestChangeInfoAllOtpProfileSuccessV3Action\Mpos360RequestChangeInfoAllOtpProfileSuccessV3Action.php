<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessV3Action;

use Exception;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoAllOtpProfileSuccessRequest;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\SubAction\KiemTraPhuongThucKyV3SA;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\SubAction\GetPhuongThucQuetB3Ver2SubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessAction\SubActionV3\XuLyCaseThayDoiCccdV3SubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessAction\SubAction\UpdateVerifyInfoAndProfileV3SubAction;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessV3Action\SubAction\GetEmailOrMobileMoiNhatV3SubAction;

class Mpos360RequestChangeInfoAllOtpProfileSuccessV3Action
{
	public bool $isQtsKhopChungThuc = false;

	public function run(Mpos360RequestChangeInfoAllOtpProfileSuccessRequest $request)
	{
		$id = $request->json('data.id');

		$mpos360McRequest = Mpos360MerchantRequest::query()->find($id);

		if (!$mpos360McRequest) {
			throw new BusinessException(__('dttv3.Lỗi: không tìm thấy thông tin yêu cầu'));
		}

		$deviceSession = $request->getCurrentDeviceSession();

		if ($deviceSession->getMerchantId() != $mpos360McRequest->merchant_id) {
			throw new BusinessException(__('dttv3.Lỗi: Bạn không phải chủ sở hữu yc này'));
		}

		$requestVefify = $request->json('data.request_vefify');

		$nguoiDaiDienData = [
			'isTrungCccdChungThuc' => false,
			'profileLayTuCccd' => []
		];

		$attachments = $request->json('data.attachments');


		if ($mpos360McRequest->isYeuCauDoiCccdMoi() || $mpos360McRequest->isYeuCauDoiNguoiDaiDienMoi()) {
			$nguoiDaiDienData = app(XuLyCaseThayDoiCccdV3SubAction::class)->run($mpos360McRequest, $requestVefify);

			if ($mpos360McRequest->isYeuCauDoiCccdMoi() && empty($nguoiDaiDienData['isTrungCccdChungThuc'])) {
				$msg = __('dttv3.Lỗi: CCCD không trùng khớp với thông tin của người đại diện hiện tại. Vui lòng sử dụng đúng CCCD còn hạn sử dụng và là bản mới nhất.');
				throw new BusinessException($msg);
			}
		}

		$requestVefifyAsArray = app(GetEmailOrMobileMoiNhatV3SubAction::class)->run(
			$deviceSession,
			$request->getRequestVefifyAsArray()
		);

		// Update các hồ sơ đã xác thực vào trong bản ghi yêu cầu
		app(UpdateVerifyInfoAndProfileV3SubAction::class)->run(
			$mpos360McRequest,
			$nguoiDaiDienData,
			$requestVefifyAsArray,
			$attachments
		);

		$mpos360McRequest->refresh();

		$kiemTraPhuongThucKy = app(KiemTraPhuongThucKyV3SA::class)->run($mpos360McRequest);

		/**
		 * Nếu là đổi người đại diện mới thì sau khi otp xong  thì điều hướng ra màn hình attachment
		 */
		if ($mpos360McRequest->isYeuCauDoiNguoiDaiDienMoi()) {
			$kiemTraPhuongThucKy['can'] = Mpos360Enum::MPOS360_CAN_SUBMIT_ADDITIONAL_ATTACHMENT;
		}

		$returnData = [
			'id' => $mpos360McRequest->id,
			'msg' => __('dttv3.Ghi nhận kết quả thành công'),
			'can' => $kiemTraPhuongThucKy['can'],
			'list_sign_method' => $kiemTraPhuongThucKy['sign_method'],
			'scan_method' => app(GetPhuongThucQuetB3Ver2SubAction::class)->run($mpos360McRequest->merchant_id),
		];

		return $returnData;
	}
} // End class
