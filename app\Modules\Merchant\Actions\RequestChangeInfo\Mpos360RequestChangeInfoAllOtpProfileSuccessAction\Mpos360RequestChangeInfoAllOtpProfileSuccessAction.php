<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessAction;

use Exception;
use App\Lib\Helper;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360ChungThuc;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessAction\SubAction\MappingDinhKemSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestCronPushRecordAction\SubAction\GetCccdInfoByQtsRequestIdSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\SubAction\GetPhuongThucQuetB3SubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessAction\SubAction\XuLyCaseThayDoiCccdSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessAction\SubAction\UpdateVerifyInfoAndProfileSubAction;

/**
 * Ngữ cảnh: Đổi CCCD mới hoặc đổi luôn người đại diện mới
 * -> QTS bước 2, và kiểm tra có khớp với bước 1 hay không?
 * 			>nếu khớp thì không cần scan bước 3
 * 		  >nếu không khớp thì phải scan bước 3
 * 
 * Lưu ý: ở bước này cũng cần lấy qts_request_id rồi bóc tách
 * để lấy được CCCD mới, SĐT mới, Email mới vào trong danh sách hồ sơ
 */
class Mpos360RequestChangeInfoAllOtpProfileSuccessAction
{
	public bool $isQtsKhopChungThuc = false;

	public function run(Mpos360RequestChangeInfoAllOtpProfileSuccessRequest $request)
	{
		$id = $request->json('data.id');
		$mpos360McRequest = Mpos360MerchantRequest::query()->find($id);

		if (!$mpos360McRequest) {
			throw new BusinessException('Lỗi: không tìm thấy thông tin yêu cầu');
		}

		$deviceSession = $request->getCurrentDeviceSession();
		
		if ($deviceSession->getMerchantId() != $mpos360McRequest->merchant_id) {
			throw new BusinessException('Lỗi: Bạn không phải chủ sở hữu yc này');
		}

		$requestVefify = $request->json('data.request_vefify');
		
		$nguoiDaiDienData = [
			'isTrungCccdChungThuc' => false,
			'profileLayTuCccd' => []
		];

		// Đổi người đại diện mới 
		if ($mpos360McRequest->isYeuCauDoiNguoiDaiDienMoi()) {
			$isDayDuKey = Helper::isDayDuKey(
				['passport', 'authoriserContactNumber', 'authoriserEmail', 'representPosition', 'representMutualRelation'],
				 $request->json('data.request_vefify')
			);

			if (!$isDayDuKey) {
				$msg = 'Lỗi: phải đầy đủ thông tin CCCD, Email, SĐT, Vị trí & Mối quan hệ của người đại diện mới';
				throw new BusinessException($msg);
			}
		}

		if ($mpos360McRequest->isYeuCauDoiCccdMoi() || $mpos360McRequest->isYeuCauDoiNguoiDaiDienMoi()) {
			$nguoiDaiDienData = app(XuLyCaseThayDoiCccdSubAction::class)->run($mpos360McRequest, $requestVefify);

			if ($mpos360McRequest->isYeuCauDoiCccdMoi() && empty($nguoiDaiDienData['isTrungCccdChungThuc'])) {
				$msg = 'Lỗi: bạn đổi chọn CCCD của người hiện tại nhưng hệ thống phát hiện CCCD mới là của 1 người khác. Từ chối xử lý';
				throw new BusinessException($msg);
			}
		} 

		// Đoạn này, cần cập nhật các giá trị verify vào verify,  mapping vào `profiles` để sau còn đẩy sang mnp
		$can = app(UpdateVerifyInfoAndProfileSubAction::class)->run(
			$mpos360McRequest, 
			$nguoiDaiDienData,
			$request->getRequestVefifyAsArray(),
			$request->json('data.attachments')
		);

		$returnData = [
			'id' => $mpos360McRequest->id,
			'msg' => 'Ghi nhận kết quả thành công',
			'can' => $can,
			'scan_method' => app(GetPhuongThucQuetB3SubAction::class)->run($mpos360McRequest->merchant_id),
			'list_sign_method' => []
		];

		return $returnData;
	}
} // End class
