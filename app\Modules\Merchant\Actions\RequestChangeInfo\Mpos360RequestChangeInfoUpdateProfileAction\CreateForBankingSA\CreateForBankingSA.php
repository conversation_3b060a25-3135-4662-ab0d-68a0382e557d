<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForBankingSA;

use Exception;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\DTOs\RequestChangeInfo\CreateMerchantRequestDto;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\SubAction\CreateMerchantRequestSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\SubAction\AddingAdditionalProfileSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\SubAction\MappingVerifyBankingToProfileSubAction;

class CreateForBankingSA
{
	public function run(
		CreateMerchantRequestDto $createMerchantRequestDto,
		Mpos360RequestChangeInfoUpdateProfileRequest $request
	) {
		/**
		 * 1-thay đổi ngân hàng thì gọi api check trước
		 * 2-check xong mới submit tạo yêu cầu
		 * 3-bên mobile cần truyền cả thông tin verify_result lên
		 */
		$createMerchantRequestDto->setRequestVerifyIntoDataRequest(request()->json('data.request_vefify', []));
		// kiểm tra đã có yêu cầu đổi thông tin ngân hàng còn dang dở hay không, còn thì báo lỗi luôn
		// $checkExistRq = app(CheckExistChangeBankingInfoSubAction::class)->run($deviceSession);

		// Đưa thông tin đã verify về làm hồ sơ (thay đổi mã, tên NH)
		$saveData = app(MappingVerifyBankingToProfileSubAction::class)->run(
			request()->json('data.request_vefify', []),
			$createMerchantRequestDto
		);

		// Tạo 1 bản ghi yc có trạng thái
		$saveData['status_verify'] = Mpos360Enum::MPOS360_MC_VERIFY_STT_CHUA_XAC_THUC;
		$merchantRequest = app(CreateMerchantRequestSubAction::class)->run($saveData);
		$savedResult = $merchantRequest->save();

		if (!$savedResult) {
			throw new BusinessException('Lỗi không tạo được bản ghi yêu cầu thay đổi');
		}

		// Điều hướng can
		$can = Mpos360Enum::MPOS360_CAN_SUBMIT_ADDITIONAL_ATTACHMENT;

		$returnData = [
			'id' => $merchantRequest->id,
			'request_change_info_id' => $merchantRequest->mynextpay_id,
			'status' => 'SUCCESS',
			'msg' => 'Tạo yêu cầu đổi thông tin thành công',
			'can' => $can,
			'additional_profiles' => app(AddingAdditionalProfileSubAction::class)->run($request),
			'verification_profiles' => []
		];

		return $returnData;
	}
} // End class