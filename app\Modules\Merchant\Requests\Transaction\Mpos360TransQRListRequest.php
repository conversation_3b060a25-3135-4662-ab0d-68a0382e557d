<?php

namespace App\Modules\Merchant\Requests\Transaction;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360TransQRListRequest extends MerchantRequest
{
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.transaction_method' => ['present', 'string'],
			'data.transaction_status' => ['present', 'string'],
			'data.transaction_time' => ['present', 'string'],
			'data.start' => ['required', 'numeric', 'integer', 'min:0'],
			'data.limit' => ['required', 'numeric', 'integer', 'min:10'],
		];
	}

	protected function prepareForValidation()
	{
		parent::prepareForValidation();
		
		$params = $this->all();
		if (empty($params['data']['start'])) {
			$params['data']['start'] = 0;
		}

		if (empty($params['data']['limit'])) {
			$params['data']['limit'] = 20;
		}
		$this->merge($params);
	}

	public function getStartDateEndDate()
	{
		$time = $this->json('data.transaction_time');
		switch ($time) {
			case 'LAST_MONTH':
				return [
					'startDate' => now()->subMonth()->startOfMonth()->format('d/m/Y H:i:s'),
					'endDate' => now()->format('d/m/Y H:i:s'),
				];

			case 'THIS_MONTH':
				return [
					'startDate' => now()->startOfMonth()->format('d/m/Y H:i:s'),
					'endDate' => now()->format('d/m/Y H:i:s'),
				];

			case 'TODAY':
				return [
					'startDate' => now()->startOfDay()->format('d/m/Y H:i:s'),
					'endDate' => now()->format('d/m/Y H:i:s'),
				];

			case 'YESTERDAY':
				return [
					'startDate' => now()->subDay()->startOfDay()->format('d/m/Y H:i:s'),
					'endDate' => now()->subDay()->endOfDay()->format('d/m/Y H:i:s'),
				];
			
			case 'THIS_WEEK':
				return [
					'startDate' => now()->startOfWeek()->format('d/m/Y H:i:s'),
					'endDate' => now()->format('d/m/Y H:i:s'),
				];

			default:
				return [
					'startDate' => now()->subDays(30)->startOfDay()->format('d/m/Y H:i:s'),
					'endDate' => now()->format('d/m/Y H:i:s'),
				];
		}

		return [];
	}

	public function getPageIndex()
	{
		$start = $this->json('data.start', 0);
		$limit = $this->json('data.limit', 20);

		if ($start == 0) {
			$page = 1;
		} else {
			$page = ($start / $limit) + 1;
		}
		return $page;
	}
}
