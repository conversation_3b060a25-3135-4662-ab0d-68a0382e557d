<?php

namespace App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360Register;

use Illuminate\Foundation\Http\FormRequest;

class Mpos360SubmitFormRegisterRequest extends FormRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.username' => ['required', 'string'],
			'data.password' => ['required', 'string', 'min:6', 'confirmed', 'regex:/^[a-zA-Z0-9\/,.\-_@!*]*$/'], //password_confirmation
			'data.partnerInviteCode' => ['nullable', 'string', 'min:3', 'max:100', 'regex:/^[a-zA-Z0-9]+$/']
		];
	}

	public function messages() {
		return [
			'data.username.required' => 'Username là bắt buộc',
			'data.username.string' => 'Username phải là kiểu chuỗi ký tự',
			'data.password.required' => 'Mật khẩu là bắt buộc',
			'data.password.string' => 'Mật khẩu phải là kiểu chuỗi',
			'data.password.min' => 'Mật khẩu phải ít nhất có 6 ký tự',
			'data.password.confirmed' => 'Xác nhận mật khẩu phải trùng khớp với mật khẩu',
			'data.password.regex' => 'Mật khẩu chỉ được chứa chữ cái, số và các ký tự hợp lệ: "/", ",", ".", "-", "_", "@", "!", "*". Không được chứa khoảng trắng.',
			'data.partnerInviteCode.string' => 'Mã giới thiệu phải là kiểu chuỗi',
			'data.partnerInviteCode.min' => 'Mã giới thiệu phải có độ dài tối thiểu là 3 ký tự',
			'data.partnerInviteCode.max' => 'Mã giới thiệu phải có độ dài tối đa là 100 ký tự',
			'data.partnerInviteCode.regex' => 'Mã giới thiệu chỉ được chứa chữ cái và số',
		];
	}
} // End class
