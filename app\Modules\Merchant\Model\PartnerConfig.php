<?php

namespace App\Modules\Merchant\Model;

use Carbon\Carbon;
use Illuminate\Foundation\Auth\User as Authenticatable;

class PartnerConfig extends Authenticatable
{
	protected $table = 'partner_config';
	protected $guarded = [];

	protected $appends = [];
	public $timestamps = false;

	/* --------------------- ACCESSOR AND MUTATOR --------------------- */
	public function getTimeCreatedAsDateAttribute()
	{
		return Carbon::createFromTimestamp($this->created_at);
	}

	/* --------------------- METHOD --------------------- */
	public function isLocked(): bool
	{
		return !empty($this->is_locked);
	}

	public function hasPermission(string $currentPath = ''): bool
	{
		$clientPermissions = json_decode($this->permission, true);
		return in_array($currentPath, $clientPermissions);
	}
	/* --------------------- QUERY --------------------- */
	public static function getCurrentClient(array $where = [])
	{
		return self::where($where)->first();
	}
} // End class
