<?php 
namespace App\Modules\TingBoxVAMC\Actions\CloseLinkBankAction\SubAction;

use App\Lib\Helper;
use App\Lib\partner\SoundBox;
use App\Lib\TelegramAlertWarning;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;

class GoiHuySangBenTingBoxSubAction {
	public SoundBox $soundBox;

	public int $tries = 0;

	public function __construct(SoundBox $soundBox)
	{
		$this->soundBox = $soundBox;
	}

	public function run(MerchantShopBank $merchantShopBank) {
		$result = false;

		$p = [
			'mcId' => $merchantShopBank->merchant_id,
			'mobileUserName' => $merchantShopBank->shop_id,
			"vaNextPayNumber" => $merchantShopBank->partner_request_id, 
			"integratedMethod" => 'VAMC', 
			"partnerCode"  => Helper::getPartnerCode($merchantShopBank->merchant_id),
		];

		while (!$result && $this->tries < 3) {
			$r = $this->soundBox->cancelVAMC($p);
			if (empty($r['result'])) {
				++$this->tries;
				$msg = sprintf('Lỗi hủy VA %s', $merchantShopBank->partner_request_id);
				TelegramAlertWarning::sendMessage($msg);
			}else {
				return true;
			}
		}

		return false;
	}
}