<?php

namespace App\Modules\Merchant\Requests\Transaction;

use Carbon\Carbon;
use Illuminate\Support\Str;
use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360TransactionCountSumByDateRequest extends MerchantRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
			// 'data.email' => ['required', 'email'],
      // 'data.rangeTime' => ['required', 'string'],
			// 'data.typeTransaction' => ['required', 'string']
    ];
  }

	public function isCountGiaoDichQr(): bool {
		return $this->json('data.typeTransaction') == 'STATISTIC_QR';
	}

	public function getNgayCoSoLieuMacq(): Carbon {
		return Carbon::create(2025, 1, 16)->startOfDay();
	}

	public function getNgayTruyenLenAsDate(): Carbon {
		if ($this->isHomNay()) {
			return now()->copy()->startOfDay();
		}

		return Carbon::createFromFormat('d-m-Y', $this->json('data.rangeTime'))->startOfDay();
	}

	public function isHomNay(): bool {

		$date = $this->json('data.rangeTime');
		$pattern = '/^\d{2}-\d{2}-\d{4}$/';
    $isMatch =  preg_match($pattern, $date);

		// Dang ngay-thang-nam: ko phai hom nay
		if ($isMatch) {
			return false;
		}
		
		return true;
	}

	public function isDuocPhepGetSoLieuMacq($merchantEmail=''): bool {
		$ngayTruyenLen = $this->getNgayTruyenLenAsDate();
		$ngayMacqCoSoLieu = $this->getNgayCoSoLieuMacq();
		mylog([
			'ngayTruyenLen' => $ngayTruyenLen,
			'ngayMacqCoSoLieu' => $ngayMacqCoSoLieu
		]);
		return $ngayTruyenLen->isSameDay($ngayMacqCoSoLieu) || $ngayTruyenLen->gte($ngayMacqCoSoLieu);
	}

	public function isGoiQuaMacq(): bool {
		return $this->json('data.typeTransaction') == 'STATISTIC_QR';
	}

	public function isGoiQuaMposCu(): bool {
		return $this->json('data.typeTransaction') == 'STATISTIC_WITHDRAW'
			|| $this->json('data.typeTransaction') == 'GET_DATA_PAGE'
			|| $this->json('data.typeTransaction') == 'STATISTIC_QUICKWITHDRAW'
			|| $this->json('data.typeTransaction') == 'STATISTIC_VIET_QR';
	}

	public function isYcNtnVietQr(): bool {
		return $this->json('data.typeTransaction') == 'STATISTIC_VIET_QR';
	}
} // End class
