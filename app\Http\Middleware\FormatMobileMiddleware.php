<?php

namespace App\Http\Middleware;

use App\Lib\Utilities;
use Closure;
use Illuminate\Support\Facades\DB;

class FormatMobileMiddleware
{
    public $timeStart;

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */

    public function handle($request, Closure $next)
    {
      $mobile = $request->json('data.mobile');

      if ( !empty($mobile) ) {
        $params = $request->json()->all();
        $params['data']['mobile'] = Utilities::mobileNumberFull($mobile);

        $request->merge($params);
      }

      return $next($request);
    }

   
}
