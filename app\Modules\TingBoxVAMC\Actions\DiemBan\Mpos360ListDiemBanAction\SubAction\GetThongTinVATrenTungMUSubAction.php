<?php

namespace App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360ListDiemBanAction\SubAction;

use App\Exceptions\BusinessException;
use App\Lib\Helper;
use App\Lib\partner\SoundBox;
use Illuminate\Support\Arr;

class GetThongTinVATrenTungMUSubAction
{
	public function run($listDiemBan, $request)
	{
		$mcTingBox = (new SoundBox())->getListMobileUser([
			"mcId" => $request->json('data.merchantId'),
			"partnerCode" => Helper::getPartnerCode($request->json('data.merchantId'))
		]);


		if (empty($mcTingBox['data'])) {
			throw new BusinessException('Không tìm thấy thông tin MC Tingbox');
		}


		foreach ($listDiemBan as &$l) {
			$l['receiveType'] = 'QUA_VI_MO';
			$l['receiveAuto'] = [];

		
			$getInfoBank = Arr::first($mcTingBox['data'], function ($item) use ($l) {
				return $item['mcUserMobile'] == $l['shopInfo']['muId'];
			});

			if(isset($getInfoBank['currentMode']) && $getInfoBank['currentMode'] == 'VAMC'){
				$currentVATienDoVe = Arr::first($getInfoBank['mcQr'], function ($it) {
					return $it['status'] == 'ACTIVE' && isset($it['state']) && $it['state'] == 'DEFAULT';
				});

				if ($currentVATienDoVe && !empty($currentVATienDoVe)) {
					$l['receiveType'] = 'NHAN_TU_DONG';
					$l['receiveAuto'][] = Arr::only($currentVATienDoVe, ['bankAccountNumber', 'mcBankAccountHolderName', 'mcBankAccountNumber', 'bankCode']);
				}
			}
			
		}

		return $listDiemBan;
	}
}
