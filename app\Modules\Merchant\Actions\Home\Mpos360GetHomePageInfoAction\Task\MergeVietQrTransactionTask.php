<?php

namespace App\Modules\Merchant\Actions\Home\Mpos360GetHomePageInfoAction\Task;

use App\Lib\Helper;
use Carbon\Carbon;

class MergeVietQrTransactionTask
{
	public function run($listVaTransaction, $listVaQrMposTransaciton) {
		$listOnlyVa = []; 
		$listOnlyMpos = [];

		// Only VA
		if (!empty($listVaTransaction['data']['data'])) {
			$listTrans = $listVaTransaction['data']['data'];
			
			$listOnlyVa = collect($listTrans)->sortByDesc(function ($item) {
				return $item['va_created_at'];
			})
			->take(5)
			->map(function ($item) {
				return $this->__mapForMobileFromOnlyVA($item);
			})
			->values()
			->all();
		}

		// Only Mpos
		if (!empty($listVaQrMposTransaciton['data']['data'])) {
			$listTrans = $listVaQrMposTransaciton['data']['data'];
			
			$listOnlyMpos = collect($listTrans)->sortByDesc(function ($item) {
				return Carbon::createFromFormat('d/m/Y H:i:s', $item['createdDate'])->timestamp;
			})
			->take(5)
			->map(function ($item) {
				return $this->__mapForMobileFromOnlyMpos($item);
			})
			->values()
			->all();
		}
		
		$listTransMobile = collect(array_merge($listOnlyVa, $listOnlyMpos))->sortByDesc(function ($item) {
			return Carbon::createFromFormat('H:i, d/m/Y', $item['dateCreated'])->timestamp;
		})
		->take(5)
		->values()
		->all();

		return $listTransMobile;
	} // End class

	private function __mapForMobileFromOnlyVA($item) {
		return [
			'transId' => $item['np_trans_id'],
			'icon' => cumtomAsset('images/rtn/VietQr.png'),
			'name' => $item['from_account_name'],
			'dateCreated' => Carbon::createFromTimestamp($item['va_created_at'])->format('H:i, d/m/Y'),
			'amount' => Helper::priceFormat($item['amount'], 'đ'),
			'status' => __('home.Đang đồng bộ'),
			'otherData' => [
				'status' => [
					'text' => __('home.Đang đồng bộ'),
					'text_color' => '#f3a200'
				]
			]
		];
	}

	private function __mapForMobileFromOnlyMpos($item) {
		return [
			'transId' => $item['transactionID'],
			'icon' => cumtomAsset('images/rtn/VietQr.png'),
			'name' => 'Chua co thong tin',
			'dateCreated' => Carbon::createFromFormat('d/m/Y H:i:s', $item['createdDate'])->format('H:i, d/m/Y'),
			'amount' => Helper::priceFormat($item['amount'], 'đ'),
			'status' => __('home.Đã đồng bộ'),
			'otherData' => [
				'status' => [
					'text' => __('home.Đã đồng bộ'),
					'text_color' => '#73ae4a'
				]
			]
		];
	}
} // End class
