body {
	margin: 0;
	font-family: "BeVietnamPro-Regular";
	font-size: 14px;
	font-weight: 400;
	line-height: 1.5;
	color: #404041;
	background: #f1f1f1;
}

@font-face {
	font-family: "BeVietnamPro-Regular";
	src: url("../fonts/BeVietnamPro-Regular.ttf");
}

@font-face {
	font-family: "BeVietnamPro-SemiBold";
	src: url("../fonts/BeVietnamPro-SemiBold.ttf");
}

@font-face {
	font-family: "BeVietnamPro-Italic";
	src: url("../fonts/BeVietnamPro-Italic.ttf");
}

body {
	font-family: "BeVietnamPro-Regular";
	font-size: 14px;
}

b {
	font-weight: normal;
	font-family: "BeVietnamPro-SemiBold" !important;
}

a {
	text-decoration: none;
}

ul {
	padding: 0;
	margin: 0;
}

.BE-bold {
	font-family: "BeVietnamPro-SemiBold" !important;
}

.BE-regular {
	font-family: "BeVietnamPro-Regular" !important;
}

.BE-italic {
	font-family: "BeVietnamPro-Italic" !important;
}

.cl890 {
	color: #808890;
}

.cl4040 {
	color: #404040 !important;
}

.clBF4 {
	color: #008BF4;
}

.clE99 {
	color: #DD9300;
}

.f20 {
	font-size: 20px !important;
}

.f18 {
	font-size: 18px !important;
}

.f16 {
	font-size: 16px !important;
}

.f12 {
	font-size: 12px !important;
}

.cl1DA {
	color: #1DA108 !important;
}

.collapse:not(.show) {
	display: none;
}

.container {
	max-width: 590px;
}

.login-wrap {
	max-width: 590px;
	background: #fff;
	padding: 32px 16px;
	margin: 0 auto;
	border-radius: 12px 12px 0 0;
	height: 100%;
}

.logo {
	text-align: center;
	display: block;
	padding: 30px 0;
}

.logo a {
	display: inline-block;
	width: 150px;
}

.header .container {
	max-width: 648px;
	margin: 0 auto;
	font-size: 20px;
	font-weight: 600;
}

.header h2 {
	font-size: 20px;
	margin-bottom: 20px;
}

.login-wrap h3 {
	font-size: 18px;
	margin-bottom: 16px;

}

.modal.action-sheet .modal-dialog {
	padding: 0;
	margin: 0;
	bottom: 0;
	position: fixed;
	width: 100%;
	max-width: 590px;
	z-index: 12000;
	transform: translate(0, 100%);
	left: 0;
	right: 0;
	margin: 0 auto;
}

.modal.action-sheet.show .modal-dialog {
	transform: translate(0, 0);
}

.modal.action-sheet .modal-header {
	border: none;
}

.modal.action-sheet .modal-header .btn-close,
.modal-header .btn-close {
	margin-right: auto;
	margin-left: 0;
}

.modal-header .btn-close {
	position: absolute;
	left: 4px;
	top: 10px;
	z-index: 9;
	padding: 20px;
}

.modal-header {
	display: block;
	text-align: center;
	border: none;
}

.modal-header h5 {
	font-size: 20px;
	font-weight: 600;
}

.btn-blue {
	background: #73AE4A;
	color: #fff;
	font-size: 20px;
	border-radius: 8px;
	padding: 10px;
	font-family: "BeVietnamPro-SemiBold";
}

.btn-blue.disabled {
	background: #DADADA;
	color: #808890;
	pointer-events: none;
	cursor: default;
}

.btn-gray {
	background: #E2E6F2;
	color: #404040;
	font-size: 16px;
	border-radius: 8px;
	padding: 10px;
	font-family: "BeVietnamPro-SemiBold";
}

a.mpos360-btn {
	font-size: 20px;
	color: #73AE4A;
	text-align: center;
	padding: 9px;
	width: 100%;
	background: rgba(115, 174, 74, 0.1);
	border-radius: 8px;
	text-decoration: none;
	display: block;
}

.ms-qr p {
	font-size: 16px;
	margin-top: 10px;
	padding: 0 10px;
}

.modal.action-sheet .modal-content {
	border-radius: 12px 12px 0 0;
	border: none;
}

.float-right {
	float: right;
}

.w-100 {
	width: 100% !important;
}

.d-block {
	display: block !important;
}

.d-flex {
	display: flex !important;
}

.me-2 {
	margin-right: .5rem !important;
}

.text-center {
	text-align: center;
}

.mt-5 {
	margin-top: 3rem !important;
}

.position-relative {
	position: relative !important;
}

.form-select {
	background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxNiAxNic+PHBhdGggZmlsbD0nbm9uZScgc3Ryb2tlPScjMzQzYTQwJyBzdHJva2UtbGluZWNhcD0ncm91bmQnIHN0cm9rZS1saW5lam9pbj0ncm91bmQnIHN0cm9rZS13aWR0aD0nMicgZD0nbTIgNSA2IDYgNi02Jy8+PC9zdmc+);
	display: block;
	width: 100%;
	padding: .375rem 2.25rem .375rem .75rem;
	font-size: 1rem;
	font-weight: 400;
	line-height: 1.5;
	color: var(--bs-body-color);
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	background-color: #fff;
	background-repeat: no-repeat;
	background-position: right .75rem center;
	background-size: 16px 12px;
	border: 1px solid #dee2e6;
	border-radius: 0.375rem;
}

.login-wrap .accordion-item {
	border: none;
}

.login-wrap .accordion-button {
	font-size: 18px;
	font-family: "BeVietnamPro-SemiBold";
	color: #404041;
	padding: 1rem 0;
}

.login-wrap .form-floating .form-control:focus {
	border: 1px solid #008BF4 !important;
	box-shadow: none;
}

.login-wrap .accordion-button:not(.collapsed) {
	box-shadow: none;
	background: none;
	padding: 0 0 12px
}

.login-wrap .accordion-body {
	padding: 0;
}

.login-wrap .form-control.is-invalid {
	background-image: none;
}

.flexthree {
	display: flex;
	align-items: center;
	justify-content: space-between;
}


.login-wrap .form-control {
	height: 45px;
	border-radius: 0;
	font-size: 16px;
	color: #22313F;
	border-radius: 8px;
	font-family: "BeVietnamPro-SemiBold" !important;
}

.form-floating>.form-control:not(:placeholder-shown)~a.close-text {
	display: block;
	z-index: 9;
}

.form-floating>.form-control:not(:placeholder-shown)~label.ping-pong {
	color: #6FA637;
	font-family: "BeVietnamPro-SemiBold";
}

.mb-3 {
	margin-bottom: 1rem !important;
}

.form-floating {
	position: relative;
}

.form-floating>label {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 2;
	height: 100%;
	padding: 1rem .75rem;
	overflow: hidden;
	text-align: start;
	text-overflow: ellipsis;
	white-space: nowrap;
	pointer-events: none;
	border: var(--bs-border-width) solid transparent;
	transform-origin: 0 0;
	transition: opacity .1s ease-in-out, transform .1s ease-in-out;
    color: #808890;
    font-size: 16px;
}

.form-floating>.form-control-plaintext~label,
.form-floating>.form-control:focus~label,
.form-floating>.form-control:not(:placeholder-shown)~label,
.form-floating>.form-select~label {
	color: rgba(var(--bs-body-color-rgb), .65);
	transform: scale(.85) translateY(-.5rem) translateX(.15rem);
}

.form-floating>.form-control-plaintext:focus,
.form-floating>.form-control-plaintext:not(:placeholder-shown),
.form-floating>.form-control:focus,
.form-floating>.form-control:not(:placeholder-shown) {
	padding-top: 1.625rem;
	padding-bottom: .625rem;
}

.form-floating>.form-control,
.form-floating>.form-control-plaintext,
.form-floating>.form-select {
	height: calc(3.5rem + calc(1px * 2));
	min-height: calc(3.5rem + calc(1px * 2));
	line-height: 1.25;
}

.form-floating>.form-control-plaintext~label,
.form-floating>.form-control:focus~label,
.form-floating>.form-control:not(:placeholder-shown)~label,
.form-floating>.form-select~label {
	color: rgba(33, 37, 41, .65);
	transform: scale(.85) translateY(-.5rem) translateX(.15rem);
}

.form-control.is-invalid,
.was-validated .form-control:invalid {
	border-color: #dc3545;
	padding-right: calc(1.5em + .75rem);
	background-repeat: no-repeat;
	background-position: right calc(.375em + .1875rem) center;
	background-size: calc(.75em + .375rem) calc(.75em + .375rem);
}

.text-danger {
	color: rgba(220, 53, 69) !important;
}

.form-floating>.form-control {
	padding: 1rem .75rem;
}

.form-floating>.form-control,
.form-floating>.form-select {
	height: calc(3.5rem + 2px);
	line-height: 1.25;
}

.form-floating>.form-control,
.form-floating>.form-control-plaintext,
.form-floating>.form-select,
.btn-ship {
	height: calc(3.5rem + calc(1px * 2));
	min-height: calc(3.5rem + calc(1px * 2));
	line-height: 1.25;
}
.btn-ship .d-flex{
	justify-content: space-between;
	padding: 0 10px;
}
.form-control,
.btn-ship {
	display: block;
	width: 100%;
	padding: .375rem .75rem;
	font-size: 1rem;
	font-weight: 400;
	line-height: 1.5;
	color: #212529;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	background-color: #fff;
	background-clip: padding-box;
	border: 1px solid #dee2e6;
	border-radius: 0.375rem;
	transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

footer {
	position: fixed;
	background: #fff;
	bottom: 0;
	width: 100%;
	padding: 20px 0;
}

footer ul {
	display: flex;
	align-items: center;
	list-style: none !important;
	justify-content: center;
}

footer ul li a {
	display: flex;
	flex-direction: column;
	align-items: center;
	color: #808890;
	font-size: 12px;
	border-right: 1px solid rgba(128, 136, 144, 0.2);
	padding: 0 12px;
}

footer ul li:last-child a {
	border: none;
}

.password-addon {
	top: 10px
}

.box-edit {
	display: flex;
	border: 1px solid #1DA108;
	padding: 12px;
	border-radius: 8px;
	justify-content: space-between;
}

.box-edit .info {
	display: flex;
	flex-direction: column;
}

.btn-ship {
	height: 58px !important;
	display: flex;
	align-items: center;
	padding: 6px 0;
	min-height: auto;
}

@media (max-width: 768px) {
	.login-wrap {
		max-width: 100%;
		box-shadow: none;
	}

	.footer-installment {
		width: 100%;
	}
}

@media (max-width: 480px) {
	.war-img img {
		height: 90px;
	}

	.notifi-item .listdetail li {
		display: grid;
		grid-template-columns: 50% 50%;
	}

	.notifi-item .listdetail li span {
		text-align: right;
	}
}