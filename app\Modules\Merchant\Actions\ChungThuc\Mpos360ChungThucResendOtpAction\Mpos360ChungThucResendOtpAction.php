<?php

namespace App\Modules\Merchant\Actions\ChungThuc\Mpos360ChungThucResendOtpAction;

use Exception;
use App\Lib\OtpHelper;
use App\Lib\partner\MNP;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360CodeOtp;
use App\Modules\Merchant\Requests\ChungThuc\Mpos360ChungThucResendOtpRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\SendSmsOtpMNPSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\SendMailOtpMNPSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\GetMNPAccessTokenSubAction;
use App\Modules\Merchant\Actions\ChungThuc\Mpos360ChungThucGetOtpAction\SubAction\SendEmailWithTemplateSubAction;

class Mpos360ChungThucResendOtpAction
{
	public MNP $mnp;

	public function __construct(MNP $mnp)
	{
		$this->mnp = $mnp;
	}

	public function run(Mpos360ChungThucResendOtpRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();

		$otpId = $request->json('data.otp_id');
		$mpos360CodeOtp = Mpos360CodeOtp::query()->find($otpId);

		if (!$mpos360CodeOtp) {
			throw new BusinessException(vmsg('ChungThucResendOtpKhongTimThayBanGhiOtp'));
		}

		if ($mpos360CodeOtp->isFinalStatus()) {
			throw new BusinessException(vmsg('ChungThucResendOtpOtpDaDuocSuDung'));
		}

		if ($deviceSession->user_id != $mpos360CodeOtp->user_id) {
			throw new BusinessException(vmsg('ChungThucResendOtpLoiKhongDungThongTinUser'));
		}

		$otpGenerate = false;

		$otpGenerateIsExist = false;

		while (!$otpGenerate) {
			$otpGenerate = generateRandomNumber();

			$otpGenerateIsExist = Mpos360CodeOtp::query()
				->where('otp', $otpGenerate)
				->where('time_out', '>=', now()->timestamp)
				->first();

			if ($otpGenerateIsExist) {
				$otpGenerate = false;
			}
		}

		$mpos360CodeOtp->otp = $otpGenerate;
		$mpos360CodeOtp->time_out = now()->addSeconds(OtpHelper::getSoGiayCountdown())->timestamp;
		$mpos360CodeOtp->time_updated = now()->timestamp;
		$mpos360CodeOtp->status = Mpos360Enum::MPOS360_OTP_CHUA_SU_DUNG;
		$r = $mpos360CodeOtp->save();

		if (!$r) {
			throw new BusinessException(vmsg('ChungThucResendOtpLoiKhongTaoDuocOtp'));
		}

		$deviceSessionWithToken = app(GetMNPAccessTokenSubAction::class)->run($deviceSession);

		if ($mpos360CodeOtp->service_code == 'SMS') {
			app(SendSmsOtpMNPSubAction::class)->run($mpos360CodeOtp, $deviceSessionWithToken);

			return [
				'otp_id' => $mpos360CodeOtp->id,
				'msg' => vmsg('ChungThucResendOtpGuiOtpThanhCongQuaSMS', ['obj_value' => $mpos360CodeOtp->obj_value]),
				'countdown_time_get_new_otp' => OtpHelper::getSoGiayCountdown()
			];
		}

		if ($mpos360CodeOtp->service_code == 'EMAIL') {
			
			$sendOtpResult = app(SendEmailWithTemplateSubAction::class)->run(
				$mpos360CodeOtp,
				$deviceSessionWithToken,
				sprintf('[%s] - Cấp lại mã OTP chứng thực thông tin email từ ứng dụng %s', $mpos360CodeOtp->otp, __('setting.appName')),
				sprintf('Chứng thực thông tin email người đại diện'),
				'ChungThucEmail'
			);

			// luồng cũ
			// app(SendMailOtpMNPSubAction::class)->run($mpos360CodeOtp, $deviceSessionWithToken);

			return [
				'otp_id' => $mpos360CodeOtp->id,
				'msg' => vmsg('ChungThucResendOtpGuiOtpThanhCongQuaEmail', ['obj_value' => $mpos360CodeOtp->obj_value]),
				'countdown_time_get_new_otp' => OtpHelper::getSoGiayCountdown()
			];
		}


		throw new BusinessException(vmsg('ChungThucResendOtpLoiGuiOtpKhongXacDinh'));
	}
} // End class
