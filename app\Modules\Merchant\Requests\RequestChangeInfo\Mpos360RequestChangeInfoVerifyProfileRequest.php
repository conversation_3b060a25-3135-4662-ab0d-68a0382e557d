<?php

namespace App\Modules\Merchant\Requests\RequestChangeInfo;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360RequestChangeInfoVerifyProfileRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.request_id' => ['required', 'numeric', 'integer', 'min:1'],
			'data.otp' => ['required', 'string'],
			'data.otp_id' => ['required', 'numeric', 'integer'],
		];
	}
} // End class
