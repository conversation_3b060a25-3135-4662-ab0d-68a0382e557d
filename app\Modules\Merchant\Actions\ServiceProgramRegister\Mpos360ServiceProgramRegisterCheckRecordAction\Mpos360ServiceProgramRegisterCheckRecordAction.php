<?php

namespace App\Modules\Merchant\Actions\ServiceProgramRegister\Mpos360ServiceProgramRegisterCheckRecordAction;

use App\Lib\Helper;
use App\Modules\Merchant\Enums\ServiceProgramRegisterEnum;
use App\Modules\Merchant\Model\Mpos360MerchantRequestServiceProgramRegister;

class Mpos360ServiceProgramRegisterCheckRecordAction
{
	public array $returnData = [];

	private array $__exceptIds = [];

	public int $maxPushCount = 3;

	public function run(): array
	{
		for ($i = 1; $i <= 50; $i++) {
			try {
				$result = $this->handle();

				if ($result === 'EMPTY') {
					$this->returnData[] = 'EMPTY';
					break;
				}

				if (optional($result)->id) {
					$this->returnData[] = $result->id;
				}
			} catch (\Throwable $th) {
				mylog(['Loi xu ly chung thuc' => Helper::traceError($th)]);
				continue;
			}
		}

		return $this->returnData;
	}

	public function handle()
	{
		$mpos360RequestServiceProgram = Mpos360MerchantRequestServiceProgramRegister::query()
			->where('status', ServiceProgramRegisterEnum::SPR_STT_DA_DAY_YC)
			->whereRaw('time_last_check', '<=', now()->timestamp - 10*60);

		if (!empty($this->__exceptIds)) {
			$mpos360RequestServiceProgram = $mpos360RequestServiceProgram->whereNotIn('id', $this->__exceptIds);
		}

		$mpos360RequestServiceProgram = $mpos360RequestServiceProgram->first();

		if (!$mpos360RequestServiceProgram) {
			return 'EMPTY';
		}

		$this->__exceptIds[] = $mpos360RequestServiceProgram->id;

		mylog([
			'id' => 'CheckYc_' . $mpos360RequestServiceProgram->id,
			'service' => $mpos360RequestServiceProgram->service_program_id
		]);

		
		// Thực hiện gọi api check bản ghi yêu cầu
		$mpos360RequestServiceProgram->time_last_check = now()->timestamp;
		$mpos360RequestServiceProgram->save();
	} // End method
} // End class