<?php

namespace App\Modules\Merchant\Actions\Notification\Mpos360CreateNotifcationAction;

use Illuminate\Support\Str;
use Illuminate\Support\Facades\Http;

class Mpos360CreateNotifcationAction
{
	public function run()
	{
		$json = '{
			"notification": {
					"title": "MPOS.VN LINKCARD",
					"body": "Giao dịch mã số MPL_MP4192496 số tiền là 10.000.000 VND từ LY THI MY HANH (số thẻ **************1988). <PERSON><PERSON> toán thành công bằng MPOS LINKCARD", 
					"image": "https://api.mpos.vn/assets/logo/favicon-qr-v1/logompos.png" 
			},
			"data": {
					"badge": 0,
					"banner": null,
					"content": "Giao dịch mã số MPL_MP4192496 số tiền là 10.000.000 VND từ LY THI MY HANH (số thẻ **************1988). <PERSON><PERSON> toán thành công bằng MPOS LINKCARD", 
					"referId": "TRXCRKGJTFZU4CX9", 
					"notificationId": null,
					"groupCode": "NEWS",
					"category": "NOTIFICATION",
					"sub_category": "PROVIDE_DOCUMENTS",
					"payload": { 
							"udid": "INSTALLMENT-Sacombank_Installment:_12_Months/Sacombank_Tra_gop:_12_Thang-|1f4bc774-aa8b-4860-b529-b5995848f353",
							"orderCode": "MP4192496",
							"transactionStatus": "APPROVED",
							"amount": ********,
							"authoriserName": "Phí Thị Nga",
							"pan": "**************1988",
							"typeTransaction": "Trả góp",
							"methodPayment": "LINKCARD",
							"issuer": "VISA_LOCAL",
							"timePayment": "*************",
							"cardholderName": "LY THI MY HANH",
							"expireTime": 30,
							"allowClose": 2
					},
					"userInfo": {
							"mcId": "1801",
							"mcEmail": "<EMAIL>",
							"mcName": "Đoàn Thị Dung",
							"userName": "topup",
							"userId": "********"
					},
					"buttons": []
			},
			"tokens": [
					"fUfdBBeGQfSQbWNRYen8D3:APA91bFrE1UQ2fH4Kt7C9XCfEF_TWR-aRL_7oemB8an_6XNPjYag9wiCuNNeLik6xrRkTJOZuk4U0D5znUOxEut-JI0cslQLuoNmOvZ1mFup6NomSPOWjrA"
			],
			"topics": null,
			"notifyConfig": {
					"notifyType": "INDIVIDUAL", 
					"hasPushRealtime": true,
					"runtime": null,
					"isNeedPushFCM": true,
					"appType": null, 
					"channel": "APP360"
			}
	}';

		$notificationData = json_decode($json, true);
		$accessToken = env('API_PARTNER_MNP_NOTIFY_ACCESS_TOKEN_FOR_PUSH');

		$notificationData['data']['notificationId'] = (string) Str::uuid();
		$r = Http::withHeaders([
			'Content-Type' => 'application/json',
			'Authorization' => 'Bearer ' . $accessToken
		])->post('https://dev-noti.mpos.vn/api/notify-manager/create', $notificationData);

		dd($r->json());
	}
} // End class