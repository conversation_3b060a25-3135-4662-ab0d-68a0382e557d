<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoMarkAsDoneAction;

use App\Exceptions\BusinessException;
use Exception;
use Illuminate\Support\Str;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360ChungThuc;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\V3\Mpos360RequestChangeInfoMarkAsDoneRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestCronPushRecordAction\SubAction\GetCccdInfoByQtsRequestIdSubAction;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoMarkAsDoneAction\SubAction\XuLyHoanThanhBoSungThongTinSA;

class Mpos360RequestChangeInfoMarkAsDoneAction
{
	public GetCccdInfoByQtsRequestIdSubAction $action;

	public function __construct(GetCccdInfoByQtsRequestIdSubAction $action)
	{
		$this->action = $action;	
	}

	public function run(Mpos360RequestChangeInfoMarkAsDoneRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();

		$mpos360McRequest = Mpos360MerchantRequest::query()
		->with('mpos360McSupplementNew')
		->where([
			'merchant_id' => $deviceSession->getMerchantId(),
			'id' => $request->json('data.id')
		])
		->first();

		if (!$mpos360McRequest) {
			throw new BusinessException(__('dttv3.Lỗi: không tìm thấy yêu cầu thay đổi'));
		}

		// là bổ sung thông tin 
		if ($mpos360McRequest->mpos360McSupplementNew) {
			return app(XuLyHoanThanhBoSungThongTinSA::class)->run($mpos360McRequest);
		}

		// bắt trạng thái yc sau
		if (!$mpos360McRequest->isRequestChuaDaySangMnp()) {
			throw new BusinessException(__('dttv3.Lỗi: yêu cầu của bạn có trạng thái không hợp lệ'));
		}

		// validate
		switch ($mpos360McRequest->method_code) {
			case 'SDK':
				if (empty($request->json('data.qts_request_id'))) {
					throw new BusinessException(__('dttv3.Thiếu thông tin QtsRequestId'));
				}
				break;

			case 'SMS':
			case 'ZALO':
			case 'OTP':
			case 'EMAIL':
				if (empty($request->json('data.otp_id'))) {
					throw new BusinessException(__('dttv3.Thiếu thông tin OtpId'));
				}
				break;

			case 'QTS':
				if (
					empty($request->json('data.other_data.matching_percent')) || 
					empty($request->json('data.other_data.is_matching_facescan'))
				) {
					throw new BusinessException(__('dttv3.Thiếu thông tin tỷ lệ khớp khuôn mặt'));
				}
				break;

			default:
				
				break;
		}

		$dataRequest = json_decode($mpos360McRequest->data_request, true);
		$methodCode = $mpos360McRequest->method_code ?? 'SDK';

		foreach ($dataRequest as &$it) {
			$it['scan_method'][$methodCode]['status'] = 'DONE';
			$it['scan_method'][$methodCode]['other_data'] = $request->json('data.other_data', []);
		}
		$mpos360McRequest->data_request = json_encode($dataRequest);

		$mpos360McRequest->method_code = $methodCode;
		$mpos360McRequest->status = Mpos360Enum::MPOS360_MC_REQUEST_STT_CHUA_GUI;
		$mpos360McRequest->status_verify = Mpos360Enum::MPOS360_MC_VERIFY_STT_DA_THUC_HIEN_BUOC3;
		$mpos360McRequest->time_updated = now()->timestamp;
		$mpos360McRequest->time_expired = now()->addDays(2)->timestamp;
		
		// Kiểm tra qtsRequestId trước
		$qtsRequestId = $request->json('data.qts_request_id', '');
		if (!empty($qtsRequestId)) {
			$this->capNhatChungThuc($mpos360McRequest, $qtsRequestId);
		}

		$r = $mpos360McRequest->save();

		if (!$r) {
			throw new BusinessException(__('dttv3.Lỗi: không đánh dấu được yc là mới tạo'));
		}

		return [
			'id' => $mpos360McRequest->id,
			'msg' => vmsg('Mpos360RequestChangeInfoMarkAsDoneAction_ChungToiDaTiepNhanYeuCauCuaBan'),
		];
	}

	public function capNhatChungThuc(Mpos360MerchantRequest $mpos360McRequest, string $qtsRequestId)
	{
		$mpos360ChungThuc = Mpos360ChungThuc::query()->firstWhere([
			'merchant_id' => $mpos360McRequest->merchant_id,
			'key_code' => 'CCCD'
		]);

		if (!$mpos360ChungThuc) {
			throw new BusinessException(__('dttv3.Lỗi không tìm thấy bản ghi chứng thực'));
		}

		// Kiểm tra tính tương thích của SDK CCCD mới với chứng thực
		$detailQtsResultDto = $this->action->run($qtsRequestId);
		$isMatching = $detailQtsResultDto->isMatchingChungThuc($mpos360ChungThuc);
		if (!$isMatching) {
			throw new BusinessException(__('dttv3.Lỗi: xác thực người thay đổi không giống với số CMT/CCCD hiện tại của bạn'));
		}

		$mpos360ChungThuc->status = Mpos360Enum::MPOS360_CHUNG_THUC_STT_DANG_XAC_NHAN;
		$mpos360ChungThuc->qts_request_id = $qtsRequestId;
		$mpos360ChungThuc->time_created = now()->timestamp;
		$mpos360ChungThuc->time_updated = now()->timestamp;
		$mpos360ChungThuc->time_expired = $detailQtsResultDto->currentCardDate->timestamp;
		$r = $mpos360ChungThuc->save();

		if (!$r) {
			throw new BusinessException(__('dttv3.Lỗi cập nhật chứng thực'));
		}

		return true;
	}
} // End class
