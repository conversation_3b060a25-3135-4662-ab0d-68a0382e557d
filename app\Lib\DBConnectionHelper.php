<?php

namespace App\Lib;

use Illuminate\Support\Facades\DB;

class DBConnectionHelper
{
	public static function closeIfExist(array $listConnection=[])
	{
		if (DB::connection('mysql')->getPdo()) {
			DB::disconnect('mysql');
		}

		if (DB::connection('mpos360_data')->getPdo()) {
			DB::disconnect('mpos360_data');
		}

		if (DB::connection('mpos360_logs')->getPdo()) {
			DB::disconnect('mpos360_logs');
		}
	}
} // End class