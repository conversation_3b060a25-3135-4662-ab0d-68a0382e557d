<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360SearchWithdrawTransAction;

use App\Lib\partner\MPOS;
use App\Modules\Merchant\Requests\Transaction\Mpos360SearchWithdrawTransRequest;

class Mpos360SearchWithdrawTransAction
{
	public function run(Mpos360SearchWithdrawTransRequest $request) {
		// $params = $request->only([
		// 	'data.dateFrom',
		// 	'data.dateTo',
		// 	'data.status',
		// 	'data.pageIndex',
		// 	'data.pageSize',
		// ])['data'];

		$params['merchantId'] = 1801;
		$params['mobileUserToken'] = $request->getMposUserMobileToken();
		
		mylog(['Param get giao dich' => $params]);
		$searchWithdrawTrans = (new MPOS())->getMonthReport($params);
		mylog(['Result' => $searchWithdrawTrans]);
		
	}
}
