<?php

namespace App\Lib;

use App\Modules\Merchant\Model\Setting;

class OtpHelper
{
	public static $soGiayCountDown = null;

	public static function getSoGiayCountdown(): int
	{
		if (self::$soGiayCountDown) {
			return self::$soGiayCountDown;
		}

		$countDownGetNewOtpSetting = Setting::query()->firstWhere(['key' => 'COUNTDOWN_GET_NEW_OTP']);
		$seconds = optional($countDownGetNewOtpSetting)->value ?? 90;
		self::$soGiayCountDown = intval($seconds);
		return self::$soGiayCountDown;
	}

	public static function getSoPhutVaGiayConLai(int $totalSeconds=0): array {
		$minutes = floor($totalSeconds / 60);
    $seconds = $totalSeconds % 60;

		return [
			'm' => $minutes,
			's' => $seconds
		];
	}

	public static function getWordingSoPhutVaGiayConLai(int $totalSeconds) {
		$r = self::getSoPhutVaGiayConLai($totalSeconds);
		$str = '';
		if ($r['m'] > 0) {
			$str .= $r['m'] . ' phút ';
		}

		if ($r['s'] > 0) {
			$str .= $r['s'] . ' giây';
		}

		return $str;
	}
} // End class