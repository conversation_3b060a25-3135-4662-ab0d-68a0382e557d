<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction;

use App\Modules\Merchant\Model\Device;

class UpdateProfileUserIntoDeviceSubAction
{  
  /**
   * Method run
   *
   * @param $deviceInfo $deviceInfo [explicite description]
   * "device": {
      "os": "IOS",
      "token": "MW4iKvl78B5vFImwXjmMeTRnSUwxwcfqlrrQ6xL7yZkcME51cwZoKfJYe9ptBxDjYS78Xts3N6WVAo3U21ht8uFEMU2UqMB3RjpjrOzMADh1tNmrwaVMofzp2H0Bw40W"
    }
   *
   * @return void
   */
  public function run($deviceInfo = [], $profileUserId)
  {
    $device = Device::where('os', $deviceInfo['os'] ?? '')
                    ->where('token', $deviceInfo['token'] ?? '')
                    ->first();
    if (!$device) {
      return;
    }

    $device->profile_users_id = $profileUserId;
    $device->save();
    return $device;
  }
}
