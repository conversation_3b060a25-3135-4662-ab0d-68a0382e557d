<?php

namespace App\Modules\TingBoxVAMC\Controllers\MerchantBank;

use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\WebBackend\Controllers\Controller;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\LinkBankRequest;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\GetInfoBankRequest;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\CloseLinkBankRequest;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\ConfirmOtpBankRequest;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\ActiveQrNoDeviceRequest;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\Mpos360LinkBankV2Request;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\SetPaymentDefaultRequest;
use App\Modules\TingBoxVAMC\Actions\CloseLinkBankAction\CloseLinkBankAction;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\Mpos360GetInfoBankV2Request;
use App\Modules\TingBoxVAMC\Actions\MerchantBank\LinkBankAction\LinkBankAction;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\SendOtpSwitchModeBankRequest;
use App\Modules\TingBoxVAMC\Actions\MerchantBank\LinkBankAction\LinkBankV2Action;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\VerifyOtpSwitchModeBankRequest;
use App\Modules\TingBoxVAMC\Actions\ActiveQrNoDeviceAction\ActiveQrNoDeviceAction;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\Mpos360CheckVaBankNumberRequest;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\SubmitFormSwitchModeBankRequest;
use App\Modules\TingBoxVAMC\Actions\Mpos360VAMCForVCBAction\Mpos360VAMCForVCBAction;
use App\Modules\TingBoxVAMC\Actions\SetPaymentDefaultAction\SetPaymentDefaultAction;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\Mpos360CheckSwitchModeBankRequest;
use App\Modules\TingBoxVAMC\Actions\MerchantBank\GetInfoBankAction\GetInfoBankAction;
use App\Modules\TingBoxVAMC\Actions\MerchantBank\GetInfoBankV2Action\GetInfoBankV2Action;
use App\Modules\TingBoxVAMC\Actions\MerchantBank\GetInfoBankV3Action\GetInfoBankV3Action;
use App\Modules\TingBoxVAMC\Actions\CheckVaBankNumberAction\Mpos360CheckVaBankNumberAction;
use App\Modules\TingBoxVAMC\Actions\MerchantBank\ConfirmOtpBankAction\ConfirmOtpBankAction;
use App\Modules\TingBoxVAMC\Actions\CronJobCancelSoundBoxAction\CronJobCancelSoundBoxAction;
use App\Modules\TingBoxVAMC\Actions\CronJobSwitchModeBankAction\CronJobSwitchModeBankAction;
use App\Modules\TingBoxVAMC\Actions\CronJobSyncQrSoundBoxAction\CronJobSyncQrSoundBoxAction;
use App\Modules\TingBoxVAMC\Actions\SendOtpSwitchModeBankAction\SendOtpSwitchModeBankAction;
use App\Modules\TingBoxVAMC\Actions\MerchantBank\Mpos360GetInfoMcAction\Mpos360GetInfoMcAction;
use App\Modules\TingBoxVAMC\Actions\VerifyOtpSwitchModeBankAction\VerifyOtpSwitchModeBankAction;
use App\Modules\TingBoxVAMC\Actions\SubmitFormSwitchModeBankAction\SubmitFormSwitchModeBankAction;
use App\Modules\TingBoxVAMC\Actions\CronJobSyncDeviceSoundBoxAction\CronJobSyncDeviceSoundBoxAction;
use App\Modules\TingBoxVAMC\Actions\CronJobUpdateOrCreateBankAction\CronJobUpdateOrCreateBankAction;
use App\Modules\TingBoxVAMC\Actions\Mpos360CheckSwitchModeBankAction\Mpos360CheckSwitchModeBankAction;
use App\Modules\TingBoxVAMC\Actions\VAMC\Mpos360CloseLinkBankFakeAction\Mpos360CloseLinkBankFakeAction;
use App\Modules\TingBoxVAMC\Actions\MerchantBank\Mpos360ConfirmOtpBankFakeAction\Mpos360ConfirmOtpBankFakeAction;

class MerchantBankController extends Controller
{
	public function index(Request $request) {
		echo 'vamc';
		exit;
	}

	public function getInfoBank(GetInfoBankRequest $request) {
		try {
			$result = app(GetInfoBankAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function getInfoBankV3(GetInfoBankRequest $request) {
		try {
			$result = app(GetInfoBankV3Action::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function linkBank(LinkBankRequest $request) {
		try {
			$result = app(LinkBankAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function Mpos360LinkBankV2(Mpos360LinkBankV2Request $request) {
		try {
			
			$result = app(LinkBankV2Action::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function confirmOtpBank(ConfirmOtpBankRequest $request) {
		try {
			$result = app(ConfirmOtpBankAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function Mpos360ConfirmOtpBankFake(ConfirmOtpBankRequest $request) {
		try {
			$result = app(Mpos360ConfirmOtpBankFakeAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function closeLinkBank(CloseLinkBankRequest $request) {
		try {
			$result = app(CloseLinkBankAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function Mpos360CloseLinkBankFake(CloseLinkBankRequest $request) {
		try {
			$result = app(Mpos360CloseLinkBankFakeAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function cronjobSyncQrSoundBox(Request $request) {
		try {
			$result = app(CronJobSyncQrSoundBoxAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function setPaymentDefault(SetPaymentDefaultRequest $request) {
		try {
			$result = app(SetPaymentDefaultAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function cronJobUpdateOrCreateBank(Request $request) {
		try {
			$result = app(CronJobUpdateOrCreateBankAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function cronJobCancelSoundBox(Request $request) {
		try {
			$result = app(CronJobCancelSoundBoxAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function cronJobSyncDeviceSoundBox(Request $request) {
		try {
			$result = app(CronJobSyncDeviceSoundBoxAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function activeQrNoDevice(ActiveQrNoDeviceRequest $request) {
		try {
			$result = app(ActiveQrNoDeviceAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function submitFormSwitchModeBank(SubmitFormSwitchModeBankRequest $request) {
		try {
			$result = app(SubmitFormSwitchModeBankAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function sendOtpSwitchModeBank(SendOtpSwitchModeBankRequest $request) {
		try {
			$result = app(SendOtpSwitchModeBankAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function Mpos360CheckSwitchModeBank(Mpos360CheckSwitchModeBankRequest $request) {
		try {
			$result = app(Mpos360CheckSwitchModeBankAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function verifyOtpSwitchModeBank(VerifyOtpSwitchModeBankRequest $request) {
		try {
			$result = app(VerifyOtpSwitchModeBankAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function cronJobSwitchModeBank(Request $request) {
		try {
			$result = app(CronJobSwitchModeBankAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function getInfoBankV2(Mpos360GetInfoBankV2Request $request)
	{
		try {
			$result = app(GetInfoBankV2Action::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function checkVaBankNumber(Mpos360CheckVaBankNumberRequest $request)
	{
		try {
			$result = app(Mpos360CheckVaBankNumberAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function Mpos360GetInfoMc(Request $request)
	{
		try {
			$result = app(Mpos360GetInfoMcAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function Mpos360VAMCForVCB(Request $request) {
		try {
			$result = app(Mpos360VAMCForVCBAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
