<?php

namespace App\Modules\Merchant\Actions\Home\Mpos360GetHomePageInfoAction\SubAction;

use App\Lib\SettingHelper;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginUserNameV2Action\Mpos360AuthLoginUserNameV2Action;
use App\Modules\Merchant\Model\DeviceSession;
use Illuminate\Database\Eloquent\Collection;

class GetMenuForAppSubAction
{
	public function run(DeviceSession $deviceSession, Collection $listSetting)
	{
		$iconVer = 'ver1.1';


		$settingIconVersion = $listSetting->where('key', 'CONFIG_ICON_HOME_VERSION')->first();

		if (optional($settingIconVersion)->value) {
			$iconVer = $settingIconVersion->value;
		}
		
		$menus = [
			[
				'code' => 'BaoCaoGiaoDich',
				'name' => vmsg('GetMenuForAppSubAction_BaoCaoGiaoDich'),
				'img' => cumtomAsset("images/home/<USER>/BaoCaoGiaoDich.png"),
				'type' => 'deeplink',
				'target' => 'appScreen',
				'value' => [
					'appScreen' => [
						'screenName' => '',
						'params' => (object) [],
					],
					'webviewUrl' => '',
					'browserUrl' => ''
				],
			],

			
			[
				'code' => 'YcDoiThongTin',
				'name' => vmsg('GetMenuForAppSubAction_YcDoiThongTin'),
				'img' => cumtomAsset("images/home/<USER>/YcDoiThongTin.png"),
				'type' => 'deeplink',
				'target' => 'appScreen',
				'value' => [
					'appScreen' => [
						'screenName' => '',
						'params' => (object) [],
					],
					'webviewUrl' => '',
					'browserUrl' => ''
				],
			],

			[
				'code' => 'HopThu',
				'name' => vmsg('GetMenuForAppSubAction_HopThu'),
				'img' => cumtomAsset("images/home/<USER>/HopThu.png"),
				'type' => 'deeplink',
				'target' => 'appScreen',
				'value' => [
					'appScreen' => [
						'screenName' => '',
						'params' => (object) [],
					],
					'webviewUrl' => '',
					'browserUrl' => ''
				],
			],

			[
				'code' => 'HopDongDichVu',
				'name' => vmsg('GetMenuForAppSubAction_HopDongDichVu'),
				'img' => cumtomAsset("images/home/<USER>/HopDongDichVu.png"),
				'type' => 'deeplink',
				'target' => 'appScreen',
				'value' => [
					'appScreen' => [
						'screenName' => '',
						'params' => (object) [],
					],
					'webviewUrl' => '',
					'browserUrl' => ''
				],
			],

			[
				'code' => 'HopDongThietBiThanhToanngDichVu',
				'name' => vmsg('GetMenuForAppSubAction_ThietBiThanhToan'),
				'img' => cumtomAsset("images/home/<USER>/ThietBiThanhToan.png"),
				'type' => 'deeplink',
				'target' => 'appScreen',
				'value' => [
					'appScreen' => [
						'screenName' => '',
						'params' => (object) [],
					],
					'webviewUrl' => '',
					'browserUrl' => ''
				],
			],

			[
				'code' => 'ThanhToanThuong',
				'name' => vmsg('GetMenuForAppSubAction_ThanhToanThuong'),
				'img' => cumtomAsset("images/home/<USER>/ThanhToanThuong.png"),
				'type' => 'deeplink',
				'target' => 'appScreen',
				'value' => [
					'appScreen' => [
						'screenName' => '',
						'params' => (object) [],
					],
					'webviewUrl' => '',
					'browserUrl' => ''
				],
			],

			[
				'code' => 'ThanhToanTraGop',
				'name' => vmsg('GetMenuForAppSubAction_ThanhToanTraGop'),
				'img' => cumtomAsset("images/home/<USER>/ThanhToanTraGop.png"),
				'type' => 'deeplink',
				'target' => 'appScreen',
				'value' => [
					'appScreen' => [
						'screenName' => '',
						'params' => (object) [],
					],
					'webviewUrl' => '',
					'browserUrl' => ''
				],
			],

			[
				'code' => 'NhanTienVeTKNH',
				'name' => vmsg('GetMenuForAppSubAction_NhanTienVeTKNH'),
				'img' => cumtomAsset("images/home/<USER>/NhanTienVeTKNH.png"),
				'type' => 'deeplink',
				'target' => 'appScreen',
				'value' => [
					'appScreen' => [
						'screenName' => '',
						'params' => (object) [],
					],
					'webviewUrl' => '',
					'browserUrl' => ''
				],
			],

			[
				'code' => 'YcNhanTienNhanh',
				'name' => vmsg('GetMenuForAppSubAction_YcNhanTienNhanh'),
				'img' => cumtomAsset("images/home/<USER>/YcNhanTienNhanh.png"),
				'type' => 'deeplink',
				'target' => 'appScreen',
				'value' => [
					'appScreen' => [
						'screenName' => '',
						'params' => (object) [],
					],
					'webviewUrl' => '',
					'browserUrl' => ''
				],
			],

			[
				'code' => 'ThongTinTaiKhoan',
				'name' => vmsg('GetMenuForAppSubAction_ThongTinTaiKhoan'),
				'img' => cumtomAsset("images/home/<USER>/ThongTinTaiKhoan.png"),
				'type' => 'deeplink',
				'target' => 'appScreen',
				'value' => [
					'appScreen' => [
						'screenName' => '',
						'params' => (object) [],
					],
					'webviewUrl' => '',
					'browserUrl' => ''
				],
			],

			[
				'code' => 'NhanTienTuVietQr',
				'name' => vmsg('GetMenuForAppSubAction_NhanTienTuVietQr'),
				'img' => cumtomAsset("images/home/<USER>/NhanTienTuVietQr.png"),
				'type' => 'deeplink',
				'target' => 'appScreen',
				'value' => [
					'appScreen' => [
						'screenName' => '',
						'params' => (object) [],
					],
					'webviewUrl' => '',
					'browserUrl' => ''
				],
			],

			
		];

		$settingBetaTest = $listSetting->where('key', 'LIST_MC_USED_BETA_TEST')->first();
		$hasBetaTestDangKyDichVu = SettingHelper::hasBetaTest(
			$settingBetaTest, 
			'DangKyDichVu', 
			$deviceSession->getMerchantEmail()
		);
		
		if ($hasBetaTestDangKyDichVu) {
			$menus[] = [
				'code' => 'DangKyDichVu',
				'name' => vmsg('GetMenuForAppSubAction_DangKyDichVu'),
				'img' => cumtomAsset("images/home/<USER>/DangKyDichVu.png"),
				'type' => 'deeplink',
				'target' => 'appScreen',
				'value' => [
					'appScreen' => [
						'screenName' => '',
						'params' => (object) [],
					],
					'webviewUrl' => '',
					'browserUrl' => ''
				],
			];
		}

		$settingMenus = $listSetting->where('key', 'LIST_MENU_HOME_IMPROVED')->first();
	
		$listMenu = json_decode(optional($settingMenus)->value ?? '{}', true);
		$listMenu = collect($listMenu)->keyBy('code');

		$menuFinals = [];
		foreach ($menus as $menu) {
			$menu['sort'] = $listMenu[$menu['code']]['sort'] ?? 0;

			if ( !empty($listMenu[$menu['code']]['display']) ) {
				$menuFinals[] = $menu;
			}
		}

		$menuFinals = collect($menuFinals)->sortBy(function ($item) {
			return $item['sort'];
		})->values()->all();

		$merchantEmail = $deviceSession->getMerchantUserName();
		$listMcDuyetApp = app(Mpos360AuthLoginUserNameV2Action::class)->getListUserGoHome();

		if (true) {
			$menuFinals[] = [
				'code' => 'KichHoatLoaTingBox',
				'name' => 'Kích hoạt loa TingBox',
				'img' => cumtomAsset("images/home/<USER>/KichHoatLoaTingBox.png"),
				'type' => 'deeplink',
				'target' => 'appScreen',
				'value' => [
					'appScreen' => [
						'screenName' => '',
						'params' => (object) [],
					],
					'webviewUrl' => '',
					'browserUrl' => ''
				],
				'sort' => '1'
			];

			$menuFinals[] = [
				'code' => 'CaiDatWifiLoaTingBox',
				'name' => 'Cài đặt Wi-Fi loa TingBox',
				'img' => "https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/test/67c6766f4465eb435f2ae8d7CaiDatWifiLoaTingBox.png",
				'type' => 'deeplink',
				'target' => 'appScreen',
				'value' => [
					'appScreen' => [
						'screenName' => '',
						'params' => (object) [],
					],
					'webviewUrl' => '',
					'browserUrl' => ''
				],
				'sort' => '1'
			];
		}

		return $menuFinals;
	}
} // End class