<?php

namespace App\Modules\Merchant\Requests\Transaction;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360SearchWithdrawTransRequest extends MerchantRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data.dateFrom' => ['nullable', 'date_format:Y-m-d'],
      'data.dateTo' => ['nullable', 'date_format:Y-m-d'],
      'data.status' => ['nullable'],
      'data.pageIndex' => ['required', 'numeric', 'min:0'],
      'data.pageSize' => ['required', 'numeric', 'min:5'],
    ];
  }
}
