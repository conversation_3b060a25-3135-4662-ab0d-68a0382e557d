<?php


namespace App\Modules;

use Illuminate\Support\ServiceProvider;

class ModuleServiceProvider extends ServiceProvider
{

	public function register()
	{

		// <PERSON><PERSON> báo configs
		$configFile = [
			'merchant' => __DIR__ . '/Merchant/configs/merchant.php',
		];
		foreach ($configFile as $alias => $path) {
			$this->mergeConfigFrom($path, $alias);
		}
	}

	public function boot()
	{
		// Đăng ký modules theo cấu trúc thư mục
		$directories = array_map('basename', \Illuminate\Support\Facades\File::directories(__DIR__));
		foreach ($directories as $moduleName) {
			$this->registerModule($moduleName);
		}
	}

	// Khai báo đăng ký cho từng modules
	private function registerModule($moduleName)
	{
		$modulePath = __DIR__ . "/$moduleName/";
		// <PERSON>hai báo thành phần ở đây
		
		// Khai báo route
		if (\Illuminate\Support\Facades\File::exists($modulePath . "routes/routes.php")) {
			$this->loadRoutesFrom($modulePath . "routes/routes.php");
		}

		if (\Illuminate\Support\Facades\File::exists($modulePath . "routes/publicRoutes.php")) {
			$this->loadRoutesFrom($modulePath . "routes/publicRoutes.php");
		}

		if (\Illuminate\Support\Facades\File::exists($modulePath . "routes/merchantRoutes.php")) {
			$this->loadRoutesFrom($modulePath . "routes/merchantRoutes.php");
		}

		if (\Illuminate\Support\Facades\File::exists($modulePath . "routes/v2.merchantRoutes.php")) {
			$this->loadRoutesFrom($modulePath . "routes/v2.merchantRoutes.php");
		}

		if (\Illuminate\Support\Facades\File::exists($modulePath . "routes/v3.merchantRoutes.php")) {
			$this->loadRoutesFrom($modulePath . "routes/v3.merchantRoutes.php");
		}

		if (\Illuminate\Support\Facades\File::exists($modulePath . "routes/v3.1.dieuChinhRoutes.php")) {
			$this->loadRoutesFrom($modulePath . "routes/v3.1.dieuChinhRoutes.php");
		}

		if (\Illuminate\Support\Facades\File::exists($modulePath . "routes/v3.rutTienNhanhRoutes.php")) {
			$this->loadRoutesFrom($modulePath . "routes/v3.rutTienNhanhRoutes.php");
		}

		if (\Illuminate\Support\Facades\File::exists($modulePath . "routes/v4.php")) {
			$this->loadRoutesFrom($modulePath . "routes/v4.php");
		}

		if (\Illuminate\Support\Facades\File::exists($modulePath . "routes/serviceProgramRegisterRoutes.php")) {
			$this->loadRoutesFrom($modulePath . "routes/serviceProgramRegisterRoutes.php");
		}

		if (\Illuminate\Support\Facades\File::exists($modulePath . "routes/tingtingRoutes.php")) {
			$this->loadRoutesFrom($modulePath . "routes/tingtingRoutes.php");
		}

		if (\Illuminate\Support\Facades\File::exists($modulePath . "routes/tingtingWebRoutes.php")) {
			$this->loadRoutesFrom($modulePath . "routes/tingtingWebRoutes.php");
		}
		
		if (\Illuminate\Support\Facades\File::exists($modulePath . "routes/cronjobRoutes.php")) {
			$this->loadRoutesFrom($modulePath . "routes/cronjobRoutes.php");
		}

		if (\Illuminate\Support\Facades\File::exists($modulePath . "routes/webBackendRoutes.php")) {
			$this->loadRoutesFrom($modulePath . "routes/webBackendRoutes.php");
		}

		if (\Illuminate\Support\Facades\File::exists($modulePath . "routes/v5.tingBoxRoutes.php")) {
			$this->loadRoutesFrom($modulePath . "routes/v5.tingBoxRoutes.php");
		}

		if (\Illuminate\Support\Facades\File::exists($modulePath . "routes/vamcRoutes.php")) {
			$this->loadRoutesFrom($modulePath . "routes/vamcRoutes.php");
		}

		// Khai báo helpers
		//        if (File::exists($modulePath . "helpers")) {
		//            // Tất cả files có tại thư mục helpers
		//            $helper_dir = File::allFiles($modulePath . "helpers");
		//            // khai báo helpers
		//            foreach ($helper_dir as $key => $value) {
		//                $file = $value->getPathName();
		//                require $file;
		//            }
		//        }
	}
}
