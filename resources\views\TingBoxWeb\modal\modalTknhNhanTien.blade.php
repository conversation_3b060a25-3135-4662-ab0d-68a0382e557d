@inject('mnpOnboardNewMcHelper', \App\Lib\MnpOnboardNewMcHelper)
@php($nganhNgheNganHangThanhPho = $mnpOnboardNewMcHelper->getNganhNgheNganHangThanhPho())

<div class="modal fade action-sheet" id="formTknh" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle"
	aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Tài khoản NH nhận tiền</h5>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<p class="text-center"><PERSON><PERSON> đảm bả<PERSON> t<PERSON>h hợp lệ Họ tên Chủ tài khoản ngân hàng phải khớp với Họ tên
					trên căn
					cước/CCCD đã đăng ký mở tài khoản.</p>
				<div class="notifi-item">
					<div class="form-floating mb-3">
						<input type="text" class="form-control add_bankHolder" id="bank_account_holder" placeholder="" value="{{ $cccdName }}" onkeyup="this.value = processTenChuTaiKhoan(this)" readonly>
						<label for="floatingInputValue">Tên chủ tài khoản</label>
						<div class="error-message text-danger mt-1" id="add-error-bankHolder"></div>
					</div>

					<div class="form-floating mb-3">
						<input type="tel" class="form-control add_bankNumber" id="bank_account" placeholder=""
							value="">
						<label for="floatingInputValue">Số tài khoản</label>
						<div class="error-message text-danger mt-1" id="add-error-bankNumber"></div>
					</div>

					<div class="form-floating mb-3">
						<select class="form-select floatingSelectBank add_bankName h-100 choices" id="bank_id">
							<option value="">Ngân hàng nhận tiền</option>
							@foreach ($nganhNgheNganHangThanhPho['banks'] as $item)
								<option value="{{ $item['bankId'] }}">{{ $item['bankName']}}</option>
							@endforeach
						</select>
						<div class="error-message text-danger mt-1" id="add-error-bankName"></div>
					</div>

					<div class="d-flex mt-5">
						<button type="button" class="btn text-center btn-blue btn-success w-100 d-block d-flex align-items-center justify-content-center" onclick="return onCheckTknh(this)">
							<div class="spinner-border d-none" role="status" id="spinnerCheckTknh">
								<span class="visually-hidden">Loading...</span>
							</div>

							<span id="checkTknhActionName">Tiếp tục</span>
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>


<script>
	$(document).ready(function() {
		var choices = new Choices($('.choices')[0], {
			searchPlaceholderValue: 'Nhập để tìm ngân hàng...',
			position: 'top'
		});
	});
</script>

<script>

	var $add_bankHolder = $('.add_bankHolder');
	var $add_bankNumber = $('.add_bankNumber');
	var $add_bankName = $('.add_bankName');
	var $add_errorBankHolder = $("#add-error-bankHolder");
	var $add_errorBankNumber = $("#add-error-bankNumber");
	var $add_errorBankName = $("#add-error-bankName");

	function validateBankHolder() {
            var add_bankHolder = $add_bankHolder.val().trim();
            if (add_bankHolder === "") {
                $add_errorBankHolder.text("Tên chủ tài khoản không hợp lệ");
                $add_bankHolder.addClass("is-invalid");
            } else {
                $add_errorBankHolder.text("");
                $add_bankHolder.removeClass("is-invalid");
            }
        }
        function validateBankNumber() {
            var add_bankNumber = $add_bankNumber.val().trim();
            if (add_bankNumber === "") {
                $add_errorBankNumber.text("Số tài khoản không hợp lệ");
                $add_bankNumber.addClass("is-invalid");
            } else {
                $add_errorBankNumber.text("");
                $add_bankNumber.removeClass("is-invalid");
            }
        }
        function validateBankName() {
            var add_bankName = $add_bankName.val().trim();
            if (add_bankName === "") {
                $add_errorBankName.text("Tên ngân hàng không hợp lệ");
                $add_bankName.addClass("is-invalid");
            } else {
                $add_errorBankName.text("");
                $add_bankName.removeClass("is-invalid");
            }
        }

	$add_bankHolder.on("input", validateBankHolder);
	$add_bankNumber.on("input", validateBankNumber);
	$add_bankName.on("change", validateBankName);

	function onCheckTknh(element) {
		validateBankHolder();
		validateBankNumber();
		validateBankName();
		if (
				$add_errorBankName.text() === "" &&
				$add_errorBankNumber.text() === "" &&
				$add_errorBankHolder.text() === ""
		) {

		const url = new URL(window.location.href);

		const params = {
			bank_account_holder: $('#bank_account_holder').val(),
			bank_account: $('#bank_account').val(),
			bank_id: $('#bank_id').val(), // mnp bankId
			bank_name: $('#bank_id').find('option:selected').text(),
			mposMcId: url.searchParams.get('merchantId')
		};
			$('#loadingPage').addClass('se-pre-con');
			$.post('/Mpos360iTngBoxOnCheckTknh', params).then(function (res) {
				if (res.success) {
					$('#modalStkResult').remove();
					$('body').append(res.message);
					$('#modalStkResult').modal('show');
					$('#formTknh').modal('hide');
				}else {
					alert(res.message);
				}
			}).always(function () {
				$('#checkTknhActionName').show();
				$('#loadingPage').removeClass('se-pre-con');
			});
		}
	}

	function processTenChuTaiKhoan(input) {
		 // Lấy giá trị hiện tại từ input
		 let value = input.value;

		// 1. Chuyển tất cả thành chữ in hoa
		value = value.toUpperCase();

		// 2. Loại bỏ dấu tiếng Việt
		value = removeVietnameseTones(value);

		// 3. Loại bỏ ký tự số
		value = value.replace(/[0-9]/g, '');

		// Cập nhật lại giá trị của input
		return value;
	}

	function removeVietnameseTones(str) {
		str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/gi, 'a');
		str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/gi, 'e');
		str = str.replace(/ì|í|ị|ỉ|ĩ/gi, 'i');
		str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/gi, 'o');
		str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/gi, 'u');
		str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/gi, 'y');
		str = str.replace(/đ/gi, 'd');
		str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/gi, 'A');
		str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/gi, 'E');
		str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/gi, 'I');
		str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/gi, 'O');
		str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/gi, 'U');
		str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/gi, 'Y');
		str = str.replace(/Đ/gi, 'D');
		str = str.replace(/[\u0300-\u036f]/g, ''); // Loại bỏ các dấu tổ hợp
		str = str.replace(/[^a-zA-Z\s]/g, '');    // Loại bỏ các ký tự đặc biệt
		return str;
	}
</script>