<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction;

use App\Modules\Merchant\Model\FcmToken;

class SaveFcmTokenSubAction
{
	public function run($merchantId = '', string $fcmToken): FcmToken
	{
		$fcmTokenRecord = FcmToken::query()->firstOrCreate([
			'fcm_token' => $fcmToken,
			'merchant_id' => $merchantId
		], [
			'time_created' => now()->timestamp,
			'time_updated' => now()->timestamp
		]);

		return $fcmTokenRecord;
	}
} // End class
