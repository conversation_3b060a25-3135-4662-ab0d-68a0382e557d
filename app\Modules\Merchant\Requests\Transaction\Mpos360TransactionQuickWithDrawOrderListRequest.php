<?php

namespace App\Modules\Merchant\Requests\Transaction;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360TransactionQuickWithDrawOrderListRequest extends MerchantRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data.transaction_time' => ['present', 'string'],
      'data.transaction_status' => ['present', 'string'],
			'data.start' => ['required', 'numeric', 'integer', 'min:0'],
			'data.limit' => ['required', 'numeric', 'integer', 'min:5'],
    ];
  }
  public function getStartDate(): string
	{
		$transactionTime = $this->json('data.transaction_time', 'TODAY');
		$transactionTimeArray = explode(',', $transactionTime);

		if (in_array('THIS_MONTH', $transactionTimeArray)) {
			return now()->startOfMonth()->format('d-m-Y');
		}

		if (in_array('LAST_MONTH', $transactionTimeArray)) {
			return now()->subMonth()->startOfMonth()->format('d-m-Y');
		}

		if (in_array('YESTERDAY', $transactionTimeArray)) {
			return now()->yesterday()->format('d-m-Y');
		}

		if (in_array('ALL', $transactionTimeArray)) {
			return now()->subDays(10)->format('d-m-Y');
		}

		return now()->format('d-m-Y');
	}
	public function getEndDate(): string
	{
		$transactionTime = $this->json('data.transaction_time', 'TODAY');
		$transactionTimeArray = explode(',', $transactionTime);

		if (in_array('THIS_MONTH', $transactionTimeArray)) {
			return now()->endOfMonth()->format('d-m-Y');
		}

		if (in_array('LAST_MONTH', $transactionTimeArray)) {
			return now()->subMonth()->endOfMonth()->format('d-m-Y');
		}

		if (in_array('YESTERDAY', $transactionTimeArray)) {
			return now()->yesterday()->endOfDay()->format('d-m-Y');
		}

		return now()->format('d-m-Y');
	}
	public function getStatus() {
		$transactionStatus = $this->json('data.transaction_status', '');
		if($transactionStatus == "ALL") {
			$transactionStatus = '';
		}
		return $transactionStatus;
	}
}
