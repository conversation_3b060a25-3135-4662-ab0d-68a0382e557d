<?php

namespace App\Modules\TingBoxVAMC\Actions\Mpos360VAMCForVCBAction\SubAction;

use App\Exceptions\BusinessException;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use App\Modules\TingBoxVAMC\Enums\TingBoxVAMCEnum;
use Illuminate\Support\Facades\DB;

class VCBCloseBankSubAction
{
	public function run($request)
	{
		// Lấy data từ vaResponse
		$vaResponse = $request->json('vaResponse');
		if (!isset($vaResponse['data']['vaNextpayNumber'])) {
			throw new BusinessException('Thiếu thông tin vaNextpayNumber.');
		}

		// Tìm bản ghi dựa vào vaNextpayNumber
		$merchantShopBank = MerchantShopBank::query()
			->where('partner_request_id', $vaResponse['data']['vaNextpayNumber'])
			->first();

		if (!$merchantShopBank) {
			throw new BusinessException('Không tìm thấy thông tin ngân hàng.');
		}

		
		try {
			DB::beginTransaction();

			$merchantShopBank->status_link = MerchantShopBank::STT_LINK_BI_HUY; 
			$merchantShopBank->time_updated = now()->timestamp;
			$merchantShopBank->is_sync_tingbox = 2; // Đánh dấu đã đồng bộ
			$merchantShopBank->account_qr = $vaResponse['data']['qrCode'];
			$merchantShopBank->account_number_partner = $vaResponse['data']['vaBankNumber'];
			$merchantShopBank->data_linked = json_encode($request->json(), JSON_UNESCAPED_UNICODE);

			if (!$merchantShopBank->save()) {
				throw new BusinessException('Không thể cập nhật trạng thái liên kết.');
			}

			DB::commit();

			return [
				'msg' => 'Đóng liên kết VA thành công.',
				'can' => TingBoxVAMCEnum::CAN_GO_TO_TINGBOX_VAMC_SUCCESS,
				'merchantShopBankId' => $merchantShopBank->id,
				'status' => $merchantShopBank->status_link
			];
		} catch (\Throwable $th) {
			DB::rollBack();
			throw new BusinessException($th->getMessage());
		}
	}
}
