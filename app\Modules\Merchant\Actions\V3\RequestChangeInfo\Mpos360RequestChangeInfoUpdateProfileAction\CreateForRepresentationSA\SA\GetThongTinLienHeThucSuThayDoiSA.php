<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForRepresentationSA\SA;

use App\Exceptions\BusinessException;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction\MnpGetMerchantProfileSubAction;
use App\Modules\Merchant\Model\DeviceSession;

class GetThongTinLienHeThucSuThayDoiSA
{
	public $isChangeEmail = false;

	public $isChangeMobile = false;

	public function run(DeviceSession $deviceSessionWithToken, $emailProfile=[], $mobileProfile=[])
	{
		$mnpMerchantDetail = app(MnpGetMerchantProfileSubAction::class)->run(
			$deviceSessionWithToken->getMerchantId(),
			$deviceSessionWithToken->mnp_token
		);

		if (empty($mnpMerchantDetail['data'])) {
			throw new BusinessException('Lỗi không lấy được thông tin profile list');
		}

		if (!empty($emailProfile['value'])) {
			if ($mnpMerchantDetail['data']['authoriserEmail']['value'] != $emailProfile['value']) {
				$requestVerifies[] = [
					'field' => 'representEmail',
					'value' => $emailProfile['value'],
					'status_verify' => '1',
					'date_verify' => now()->timestamp
				];

				$this->isChangeEmail = $emailProfile['value'];
			}
		}
		
		if (!empty($mobileProfile['value'])) {
			if ($mnpMerchantDetail['data']['authoriserContactNumber']['value'] != @$mobileProfile['value']) {
				$requestVerifies[] = [
					'field' => 'representMobile',
					'value' => $mobileProfile['value'],
					'status_verify' => '1',
					'date_verify' => now()->timestamp
				];

				$this->isChangeMobile = $mobileProfile['value'];
			}
		}
		

		$thongTinThucSuThayDoi = [];
		if (!empty($requestVerifies)) {
			foreach ($requestVerifies as $f) {
				$thongTinThucSuThayDoi[$f['field']] = $f['value'];
			}
		}

		if (empty($thongTinThucSuThayDoi)) {
			throw new BusinessException('Bạn cần phải thay đổi ít nhất 1 trong 2 thông tin email hoặc sđt liên hệ');
		}

		return [
			'dataThayDoiThongTin' => $thongTinThucSuThayDoi,
			'requestVerifies' => $requestVerifies,
			'isChangeMobile' => $this->isChangeMobile,
			'isChangeEmail' => $this->isChangeEmail,
		];
	}
} // End class