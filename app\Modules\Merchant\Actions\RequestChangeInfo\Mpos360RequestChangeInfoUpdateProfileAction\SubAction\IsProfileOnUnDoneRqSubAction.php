<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\SubAction;

use Exception;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;

class IsProfileOnUnDoneRqSubAction
{
	public function run(int $merchantId, array $verificationProfiles, $mnpGroupCode = '')
	{
		$listRequestUnsend = Mpos360MerchantRequest::query()
																							 ->where('merchant_id', $merchantId)
																							 ->whereIn('status', [
																									Mpos360Enum::MPOS360_MC_REQUEST_STT_NHAP,
																									Mpos360Enum::MPOS360_MC_REQUEST_STT_CHUA_GUI,
																							 ])
																							 ->get();

		mylog([
			'listRequestUnsend' => $listRequestUnsend->pluck('id'),
			'verificationProfiles' => $verificationProfiles,
			'mnpGroupCode' => $mnpGroupCode
		]);

		if ($listRequestUnsend->isEmpty()) {
			return true;
		}

		foreach ($verificationProfiles as $p) {
			foreach ($listRequestUnsend as $mr) {
				$dataRequest = json_decode($mr->data_request, true);

				foreach ($dataRequest as $item) {
					if ($item['type'] == $mnpGroupCode) {
						$profilesKeyWantsChange = collect($item['profile_wants_change'])->pluck('profileKey')->toArray();

						if ( in_array($p['profileKey'], $profilesKeyWantsChange) ) {
							$msg = sprintf('Hồ sơ: "%s" đã được tạo yêu cầu và chưa được xử lý. Từ chối yêu cầu mới', $p['label']);
							throw new BusinessException($msg);
						}
					}
				}
				
			}
		}
	}
} // End class
