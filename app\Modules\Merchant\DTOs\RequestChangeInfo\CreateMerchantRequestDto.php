<?php

namespace App\Modules\Merchant\DTOs\RequestChangeInfo;

class CreateMerchantRequestDto
{
	public string $merchantId;
	public string $userName;
	public string $dataRequest;
	public string $dataFeedBack;
	public string $myNextpayId;

	public int $version;

	public function __construct(
		string $merchantId,
		string $userName,
		string $dataRequest,
		string $dataFeedBack,
		string $myNextpayId = ''
	) {
		$this->merchantId = $merchantId;
		$this->userName = $userName;
		$this->dataRequest = $dataRequest;
		$this->dataFeedBack = $dataFeedBack;
		$this->myNextpayId = $myNextpayId;

		$this->version = 1;
	}

	public function toArray(int $type = 1): array
	{
		return [
			'type' => $type, // thay doi thong tin
			'merchant_id' => $this->merchantId,
			'username' => $this->userName,
			'data_request' => $this->dataRequest,
			'data_feedback' => $this->dataFeedBack,
			'response' => '{}',
			'comment' => '{}',
			'status' => 1,
			'time_created' => now()->timestamp,
			'time_updated' => now()->timestamp,
			'mynextpay_id' => $this->myNextpayId,
			'version' => $this->version
		];
	}

	public function getDataRequestAsArray(): array {
		return json_decode($this->dataRequest, true);
	}

	public function setRequestVerifyIntoDataRequest(array $requestVerify=[]): self {
		$dataRequest = $this->getDataRequestAsArray();
		$dataRequest[0]['request_vefify'] = $requestVerify;
		$this->dataRequest = json_encode($dataRequest);
		return $this;
	}

	public function setProfiles(array $profiles): self {
		$dataRequest = $this->getDataRequestAsArray();
		$dataRequest[0]['profiles'] = $profiles;
		$this->dataRequest = json_encode($dataRequest);
		return $this;
	}
} // End class
