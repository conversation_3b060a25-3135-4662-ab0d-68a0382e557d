<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360SubmitFormRegisterAction;

use App\Exceptions\BusinessException;
use App\Lib\Helper;
use App\Lib\OtpHelper;
use App\Lib\MnpOnboardNewMcHelper;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Enums\TingTingEnum;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360AuthRegisterRequest;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360Register\Mpos360SubmitFormRegisterRequest;

class Mpos360SubmitFormRegisterAction
{
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	/**
	 * Submit form thì chỉ cần gọi otp có bản ghi
	 *
	 * @param Mpos360AuthRegisterRequest $request
	 * @return void
	 */
	public function run(Mpos360SubmitFormRegisterRequest $request): array
	{
		// Gọi api sang mynextoay để kiểm tra MC này đã được đăng ký số điện thoại hay chưa
		$mposMcInfo = $this->mnpOnboardNewMcHelper->checkSdtHopLe(
			$request->json('data.username'),
			$request->json('data.password')
		);

		if (!$mposMcInfo['result'] || $mposMcInfo['code'] != 1000) {
			throw new BusinessException($mposMcInfo['message'], $mposMcInfo['code']);
		}
		
		$returnData = [
			'status' => 'SUCCESS',
			'msg' => '',
			'username' => $request->json('data.username'),
			'channels' => ['ZALO', 'SMS'],
			'merchantId' => $mposMcInfo['data']['mposMcId'] ?? '',
			'can' => TingTingEnum::CAN_DO_VERIFY_OTP
		];

		if (!empty($returnData['merchantId'])) {
			$returnData['can'] = TingTingEnum::CAN_GO_TO_KHAI_BAO_CCCD;

			if (!empty($mposMcInfo['data']['passport'])) {
				$returnData['can'] = TingTingEnum::CAN_GO_TO_KHAI_BAO_MAN_HINH_3STEP;
			}
		}

		return $returnData;
	}
} // End class
