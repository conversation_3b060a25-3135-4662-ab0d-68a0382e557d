<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Support\Str;
use App\Traits\ApiResponser;

class InlineException extends Exception
{
	use ApiResponser;

	public $errors;
	public $errorCode;
	public $message;

	public function __construct($message = '', $errorCode = 422, $errors = [])
	{
		parent::__construct($message, $errorCode);
		$this->errors = $errors;
		$this->errorCode = $errorCode;
		$this->message = $message;
	}

	public function render()
	{
		$formattedErrors = [];

		foreach ($this->errors as $key => $messages) {
			$newKey = Str::after($key, 'data.'); // Loại bỏ "data."
			$formattedErrors[$newKey] = $messages;
		}

		$r = $this->errorResponse($this->errorCode, $this->message, [
			'errors' => $this->__getErrorAsList(),
			'errorsDetail' => $formattedErrors
		]);

		return $r;
	}

	private function __getErrorAsList()
	{
		$returnErrors = [];

		foreach ($this->errors as $errors) {
			foreach ($errors as $err) {
				$returnErrors[] = $err;
			}
		}

		return $returnErrors;
	}
}
