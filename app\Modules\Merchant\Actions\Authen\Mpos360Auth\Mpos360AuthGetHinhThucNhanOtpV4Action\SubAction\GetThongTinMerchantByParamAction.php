<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthGetHinhThucNhanOtpV4Action\SubAction;

use App\Exceptions\BusinessException;
use App\Lib\Helper;
use App\Lib\partner\MPOS;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360User;

class GetThongTinMerchantByParamAction
{
	public MPOS $mpos;

	public function __construct(MPOS $mpos)
	{
		$this->mpos = $mpos;
	}
	
	public function run(string $mposUserName=''): array
	{
		$mposMcInfo = $this->mpos->getMcInfoByUserName([
			'serviceName' => 'MERCHANT_GET_INFO',
			'username' => $mposUserName
		]);

		$code = $mposMcInfo['data']['error']['code'] ?? -1;
		
		if ($code == Mpos360Enum::API_MERCHANT_NOT_FOUND) {
			if (Helper::isUserNameEqualEmail($mposUserName)) {
				$errorMessage = sprintf('Không tìm thấy tài khoản với email %s. Xin kiểm tra lại email hoặc đăng ký mới với Tingbox.vn', $mposUserName);
			}else {
				$errorMessage = sprintf('Không tìm thấy tài khoản với số điện thoại %s. Xin kiểm tra lại số điện thoại hoặc đăng ký mới với Tingbox.vn', $mposUserName);
			}
			
			throw new BusinessException($errorMessage);
		}

		if ($code != Mpos360Enum::API_SUCCESS_CODE) {
			$errorMessage = $mposMcInfo['data']['error']['message'] ?? 'Lỗi không xác định';
			throw new BusinessException(sprintf('MPOS Err: %s (Code: %s)', $errorMessage, $code));
		}

		/**
		 * Đoạn này cần phải call vào api mpos để lấy được thông tin, 
		 * -chưa có api thì fake tạm, sau ghép api thì return đúng theo cái mảng fake
		 */
		
		$mcInfo =  [
			'mcEmail' => $mposMcInfo['data']['data']['emailMerchant'],
			'mcMobile' => $mposMcInfo['data']['data']['mobileNo'],
			'mcId' => $mposMcInfo['data']['data']['merchantId'],
			'zaloId' => $mposMcInfo['data']['data']['mobileNo'],
			'username' => $mposUserName,
			'merchantName' => $mposMcInfo['data']['data']['emailMerchant'],
		];
		
		// Lưu thông tin user vào DB để sau không dính lỗi liên quan đến MC lần đầu dùng app
		$mpos360User = Mpos360User::query()->firstOrCreate([
			'merchant_id' => $mcInfo['mcId'],
			'username' => $mcInfo['username']
		], [
			'username' => $mcInfo['username'],
			'data_users' => json_encode([
				'username' => $mcInfo['username'],
				'merchantId' => $mcInfo['mcId'],
				'merchantName' => $mcInfo['merchantName'] ?? $mcInfo['username'],
				'merchantEmail' => $mcInfo['mcEmail']
			]),
			'time_created' => now()->timestamp,
			'merchant_id' => $mcInfo['mcId']
		]);

		if (!$mpos360User) {
			throw new BusinessException('Không lưu được thông tin user');
		}

		$mcInfo['userId'] = $mpos360User->id;
		return $mcInfo;
	}
}
