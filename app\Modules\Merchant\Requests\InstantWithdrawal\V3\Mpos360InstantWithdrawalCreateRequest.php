<?php

namespace App\Modules\Merchant\Requests\InstantWithdrawal\V3;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360InstantWithdrawalCreateRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.email' => ['required', 'string', 'email'],
		];
	}
}
