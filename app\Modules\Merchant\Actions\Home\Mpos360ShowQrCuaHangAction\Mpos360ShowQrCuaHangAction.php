<?php

namespace App\Modules\Merchant\Actions\Home\Mpos360ShowQrCuaHangAction;

use App\Lib\partner\SoundBox;
use App\Modules\Merchant\Model\Setting;
use App\Modules\Merchant\Requests\Home\Mpos360ShowQrCuaHangRequest;
use Exception;

class Mpos360ShowQrCuaHangAction
{
	public SoundBox $soundBox;

	public function __construct(SoundBox $soundBox)
	{
		$this->soundBox = $soundBox;
	}

	public function run(Mpos360ShowQrCuaHangRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		$listMobileUser = $deviceSession->getListMobileUser();

		$listMobileUser = $this->__selectedFirstMobileUser($listMobileUser);

		$query = ['mcId' => $deviceSession->getMerchantId(), 'muId' => ''];
		// $query = ['mcId' => 50440383, 'muId' => ''];
		
		$queryString = http_build_query($query);

		$r = $this->soundBox->sendRequest('/get-qr-default-4-app?' . $queryString);

		$backgroundImage = cumtomAsset('images/qrCuaHang/tet-background.png');

		$settingImage = Setting::query()->firstWhere([
			'key' => 'BACKGROUND_IMAGE_QR_CUA_HANG'
		]);

		if ($settingImage) {
			$backgroundImage = cumtomAsset($settingImage->value);
		}

		$returnData = [
			'hasQr' => 'YES',
			'backgroundImage' => $backgroundImage,

			'listMobileUser' => $listMobileUser,

			"vietQr" => [
				'qrInfo' => [
					'qrCode' => '',
					'qrType' => '',
					'qrTypeImage' => '',
					'accountNumber' => '',
					'location' => '',
					'mobileUserName' => '',
				],
			],

			"googleApplePay" => (object) [
				// 'qrInfo' => [
				// 	'linkQrUrl' => 'https://images.squarespace-cdn.com/content/v1/5d3f241fa4e0350001fa20d5/*************-AIZAXV2978MGIDQE0GT7/qr-code.png?format=1500w',
				// 	'qrType' => 'GoogleApplePay',
				// 	'qrTypeImage' => cumtomAsset('images/qrCuaHang/GoogleApplePayType.png'),
				// 	'accountNumber' => '9686 6888 0717 1234 567',
				// 	'location' => 'Tokyo Life 18 Tam Trinh'
				// ],
			],

			'description' => 'Khách hàng mở ứng dụng Mobile Banking sử dụng tính năng quét mã QR, hướng camera vào mã QR ở trên và làm theo hướng dẫn trên ứng dụng để hoàn tất giao dịch.',

			'footer' => 'Đặt mua loa {TingBox}',
			'popup' => [
				'title' => '',
				'desc' => ''
			],
			'otherData' => [
				'footer' => [
					'TingBox' => [
						'text' => 'TingBox.vn',
						'webViewUrl' => 'https://tingbox.vn/'
					]
				]
			]
		];

		// Không có thông tin chưa gán thiết bị tingbox hoặc không được kích hoạt dịch vụ
		if (empty($r['data'])) {
			$returnData['hasQr'] = 'NO';
			$returnData['popup']['title'] = 'Dịch vụ chưa được kích hoạt';
			$returnData['popup']['desc'] = 'Bạn vui lòng liên hệ CSKH để được hỗ trợ';
			return $returnData;
		}

		$listItemVietQr = collect($r['data'])->filter(function ($item) {
			return $item['qrTypeDefault'] == 'VIETQR';
		})->values()->all();

		// Không có cho VietQr
		if (empty($listItemVietQr)) {
			$returnData['hasQr'] = 'NO';
			$returnData['popup']['title'] = 'Dịch vụ chưa được kích hoạt';
			$returnData['popup']['desc'] = 'Bạn vui lòng liên hệ CSKH để được hỗ trợ';
			return $returnData;
		}

		if (!empty($listItemVietQr)) {
			// Empty Qr thì dùng item đầu tiên
			if (empty($request->json('data.mobileUserId'))) {
				$returnData['vietQr'] = [
					'qrCode' => $r['data'][0]['qrDefault'],
					'qrType' => $r['data'][0]['qrTypeDefault'],
					'qrTypeImage' => cumtomAsset('images/qrCuaHang/VietQrType.png'),
					'accountNumber' => $r['data']['accountNumberDefault'],
					'location' => '',
					'mobileUserName' => '',
				];

				return $returnData;
			}

			if (!empty($request->json('data.mobileUserId'))) {
				$activeMobileUser = collect($listMobileUser)->where('mobileUserId', $request->json('data.mobileUserId'))->first();
				$activeQrData = collect($r['data'])->where('mobileUserName', $activeMobileUser['mobileUserName'])->first();
				
				if (empty($activeQrData)) {
					$returnData['vietQr'] = [
						'qrCode' => $r['data'][0]['qrDefault'],
						'qrType' => $r['data'][0]['qrTypeDefault'],
						'qrTypeImage' => cumtomAsset('images/qrCuaHang/VietQrType.png'),
						'accountNumber' => $r['data']['accountNumberDefault'],
						'location' => '',
						'mobileUserName' => '',
					];
	
					return $returnData;
				}

				$returnData['vietQr'] = [
					'qrCode' => $activeQrData['qrDefault'],
					'qrType' => $activeQrData['qrTypeDefault'],
					'qrTypeImage' => cumtomAsset('images/qrCuaHang/VietQrType.png'),
					'accountNumber' => $activeQrData['accountNumberDefault'],
					'location' => '',
					'mobileUserName' => $activeMobileUser['mobileUserName']
				];

				return $returnData;
			}
		}
	}

	private function __selectedFirstMobileUser($listMobileUser = [])
	{
		$listMobileUser = collect($listMobileUser)->map(function ($item, $index) {
			return [
				'mobileUserId' => $item['id'],
				'mobileUserName' => $item['username'],
			];
		})
		->values()
		->toArray();

		return $listMobileUser;
	}
} // End class
