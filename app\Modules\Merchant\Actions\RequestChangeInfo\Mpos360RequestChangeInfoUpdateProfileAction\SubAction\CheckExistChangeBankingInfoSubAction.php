<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\SubAction;

use Exception;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;

class CheckExistChangeBankingInfoSubAction
{
	public function run(DeviceSession $deviceSession): bool
	{
		$merchantId = $deviceSession->getMerchantId();

		$listRequestUnsend = Mpos360MerchantRequest::query()
			->where('merchant_id', $merchantId)
			->whereIn('status', [
				Mpos360Enum::MPOS360_MC_REQUEST_STT_NHAP,
				Mpos360Enum::MPOS360_MC_REQUEST_STT_CHUA_GUI,
			])
			->get();

		if ($listRequestUnsend->isEmpty()) {
			return true;
		}

		// <PERSON><PERSON><PERSON> tra hồ sơ có nằm trong các request này hay không
		$isExistChangeBankingInfo = $listRequestUnsend->contains(function (Mpos360MerchantRequest $mr) {
			$dataRequest = json_decode($mr->data_request, true);
			foreach ($dataRequest as $item) {
				if ($item['type'] == 'CHANGE_BANK_ACCOUN_INFO') {
					return true;
				}
			}

			return false;
		});

		if ($isExistChangeBankingInfo) {
			throw new BusinessException('Đã tồn tại yêu cầu thay đổi TKNH nhưng chưa được xử lý đến cùng');
		}

		return true;
	}
} // End class
