<?php

namespace App\Modules\TingBoxVAMC\Requests\MerchantBank;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class VerifyOtpSwitchModeBankRequest extends FormRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.username' => ['required', 'string'],
			'data.otp_id' => ['required', 'string'],
			'data.otp' => ['required', 'string', 'max:6'],
			'data.merchantShopBankId' => ['required', 'numeric', 'integer'],
			'data.merchantId' => ['required', 'string'],
			'data.userMobileId' => ['required', 'string'],
			'data.otpType' => ['required', 'string', 'in:CHUYEN_MODE,SET_MAC_DINH,HUY_LIEN_KET']
		];
	}

	public function messages()
	{
		return [
			'data.otp_id.required' => 'otp_id là bắt buộc',
			'data.otp_id.string' => 'otp_id phải là kiểu chuỗi ký tự',
			'data.otp.required' => 'otp là bắt buộc',
			'data.otp.string' => 'otp phải là kiểu chuỗi',
			'data.otp.max' => 'otp tối đa có 6 ký tự',
			'data.username.required' => 'Username là bắt buộc',
			'data.username.string' => 'Username phải là kiểu chuỗi ký tự',
			'data.merchantShopBankId.required' => 'Mã MC ShopBank là bắt buộc',
			'data.merchantShopBankId.numeric' => 'Mã MC ShopBank phải là kiểu số',
			'data.merchantShopBankId.min' => 'Mã MC ShopBank phải tối thiểu từ 1',
			'data.merchantId.required' => 'Id merchant là bắt buộc',
			'data.merchantId.string' => 'Id merchant phải là kiểu chuỗi',
			'data.userMobileId.required' => 'Mã định danh mobile là bắt buộc',
			'data.userMobileId.string' => 'Mã định danh mobile là kiểu chuỗi',
			'data.otpType.required' => 'Loại otp là bắt buộc',
			'data.otpType.string' => 'Loại otp phải là kiểu chuỗi',
			'data.otpType.in' => 'Otp phải được sử dụng trong các ngữ cảnh: Chuyển mode nhận tiền; Sét mặc định; hoặc Hủy liên kết',
		];
	}
}
