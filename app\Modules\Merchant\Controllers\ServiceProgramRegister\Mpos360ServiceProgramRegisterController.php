<?php

namespace App\Modules\Merchant\Controllers\ServiceProgramRegister;

use App\Lib\Helper;
use Illuminate\Http\Request;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Requests\ServiceProgramRegister\Mpos360ServiceProgramRegisterListRequest;
use App\Modules\Merchant\Requests\ServiceProgramRegister\Mpos360ServiceProgramRegisterCheckRequest;
use App\Modules\Merchant\Requests\ServiceProgramRegister\Mpos360ServiceProgramRegisterCreateRequest;
use App\Modules\Merchant\Requests\ServiceProgramRegister\Mpos360ServiceProgramRegisterFinishRequest;
use App\Modules\Merchant\Requests\ServiceProgramRegister\Mpos360ServiceProgramRegisterViewPdfRequest;
use App\Modules\Merchant\Requests\ServiceProgramRegister\Mpos360ServiceProgramRegisterAttachSignRequest;
use App\Modules\Merchant\Actions\ServiceProgramRegister\Mpos360ServiceProgramRegisterListAction\Mpos360ServiceProgramRegisterListAction;
use App\Modules\Merchant\Actions\ServiceProgramRegister\Mpos360ServiceProgramRegisterCheckAction\Mpos360ServiceProgramRegisterCheckAction;
use App\Modules\Merchant\Actions\ServiceProgramRegister\Mpos360ServiceProgramRegisterCreateAction\Mpos360ServiceProgramRegisterCreateAction;
use App\Modules\Merchant\Actions\ServiceProgramRegister\Mpos360ServiceProgramRegisterFinishAction\Mpos360ServiceProgramRegisterFinishAction;
use App\Modules\Merchant\Actions\ServiceProgramRegister\Mpos360ServiceProgramRegisterViewPdfAction\Mpos360ServiceProgramRegisterViewPdfAction;
use App\Modules\Merchant\Actions\ServiceProgramRegister\Mpos360ServiceProgramRegisterAttachSignAction\Mpos360ServiceProgramRegisterAttachSignAction;
use App\Modules\Merchant\Actions\ServiceProgramRegister\Mpos360ServiceProgramRegisterPushRecordAction\Mpos360ServiceProgramRegisterPushRecordAction;

class Mpos360ServiceProgramRegisterController extends Controller
{
	public function Mpos360ServiceProgramRegisterList(Mpos360ServiceProgramRegisterListRequest $request)
	{
		try {
			$result = app(Mpos360ServiceProgramRegisterListAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360ServiceProgramRegisterCheck(Mpos360ServiceProgramRegisterCheckRequest $request)
	{
		try {
			$result = app(Mpos360ServiceProgramRegisterCheckAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360ServiceProgramRegisterViewPdf(Mpos360ServiceProgramRegisterViewPdfRequest $request)
	{
		try {
			$result = app(Mpos360ServiceProgramRegisterViewPdfAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360ServiceProgramRegisterCreate(Mpos360ServiceProgramRegisterCreateRequest $request)
	{
		try {
			$result = app(Mpos360ServiceProgramRegisterCreateAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
	
	public function Mpos360ServiceProgramRegisterAttachSign(Mpos360ServiceProgramRegisterAttachSignRequest $request)
	{
		try {
			$result = app(Mpos360ServiceProgramRegisterAttachSignAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360ServiceProgramRegisterFinish(Mpos360ServiceProgramRegisterFinishRequest $request)
	{
		try {
			$result = app(Mpos360ServiceProgramRegisterFinishAction::class)->run($request);
			return $this->successResponse($result, $request);
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}

	public function Mpos360ServiceProgramRegisterPushRecord()
	{
		try {
			$result = app(Mpos360ServiceProgramRegisterPushRecordAction::class)->run();
			return $this->successResponse($result, request());
		} catch (\Throwable $e) {
			return $this->errorResponse($e->getCode(), Helper::traceError($e));
		}
	}
} // End class
