<?php

namespace App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360ListDiemBanAction;

use App\Lib\MnpOnboardNewMcHelper;
use App\Exceptions\BusinessException;
use App\Lib\Logs;
use App\Lib\Mpos360UrlHelper;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use App\Modules\TingBoxVAMC\Requests\DiemBan\Mpos360ListDiemBanRequest;
use App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360ListDiemBanAction\SubAction\GetThongTinVATrenTungMUSubAction;
use App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360ListDiemBanAction\SubAction\DongBoTknhTrungGianSubAction\DongBoTknhSubAction;
use App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360ListDiemBanAction\SubAction\LoaiBoMobileUserNullSubAction;

class Mpos360ListDiemBanAction
{
	public MnpOnboardNewMcHelper $mnpOnboardNewMcHelper;
	public array $listMuIds = [];

	public array $fieldMcBankSelect = [
		'account_main',
		'account_number',
		'account_holder',
		'account_qr',
		'account_qr_display',
		'status',
		'bank_branch',
		'bank_code'
	];

	public function __construct(MnpOnboardNewMcHelper $mnpOnboardNewMcHelper)
	{
		$this->mnpOnboardNewMcHelper = $mnpOnboardNewMcHelper;
	}

	public function run(Mpos360ListDiemBanRequest $request)
	{
		Logs::writeInfo("list diem ban param", $request->json('data'));
		
		$detailMc = $this->mnpOnboardNewMcHelper->detailMcV2([
			'mposMcId' => $request->json('data.merchantId')
		]);

		if (empty($detailMc['data'])) {
			throw new BusinessException('Lỗi không có thông tin truy vấn điểm bán');
		}

		app(LoaiBoMobileUserNullSubAction::class)->run($detailMc);
		// app(DongBoTknhSubAction::class)->run($detailMc);

		$listThietBi = [];

		foreach ($detailMc['data']['locations'] as $location) {
			if (!empty($location['deviceDTOs'])) {
				foreach ($location['deviceDTOs'] as $index => $dv) {
					$dv['address'] = $location['areaAddress'];
					$dv['cityId'] = $location['areaCityCode'];
					$dv['districtId'] = $location['areaDistrictCode'];
					$dv['industryId'] = $location['mcc'];
					$dv['locationId'] = $location['id'];

					if (count($location['deviceDTOs']) == 1) {
						$dv['locationId'] = $location['id'];
						$dv['locationName'] = $location['areaName'];
					} else {
						$dv['locationId'] = $location['id'];
						$dv['locationName'] = $location['areaName'];
					}

					$listThietBi[] = $dv;
				}
			}
		}

	
		$listMuGroup = collect($listThietBi)->groupBy('mobileUserId')->toArray();
	

		$listDiemBan = [];
		foreach ($listMuGroup as $muId => $muInfo) {
			$this->listMuIds[] = $muId; 

			$listDiemBan[] = [
				'shopInfo' => [
					'shopId' => $muId,
					'muId' => $muId,
					'shopName' => $muInfo[0]['locationName'],
					'address' => $muInfo[0]['address'],
					'cityId' =>  $muInfo[0]['cityId'],
					'districtId' =>  $muInfo[0]['districtId'],
					'industryId' => $muInfo[0]['industryId'],
					'locationId' => $muInfo[0]['locationId'],
				],

				'listTingBoxDevice' => collect($muInfo)->map(function ($it) {
					return [
						'serialNumber' => $it['serialNumber'],
						'status' => $it['status'],
						'mobileUserId' => $it['mobileUserId'],
						'icon' => Mpos360UrlHelper::getTingBoxThumbnail($it['serialNumber'])
					];
				}),
				'listBanking' => [
					'direct' => [],
					'inter' => []
				]
			];
		}


		$listMerchantShopBank = MerchantShopBank::query()->with(['merchantBank', 'switching'])
																										 ->whereIn('shop_id', $this->listMuIds)
																										 ->get()
																										 ->groupBy('shop_id');

						
		foreach ($listDiemBan as &$item) {
			if ($listMerchantShopBank->has($item['shopInfo']['shopId'])) {
				$item['listBanking']['direct'] = $listMerchantShopBank->get($item['shopInfo']['shopId'])
				->filter(function (MerchantShopBank $mcShopBank) {
					return $mcShopBank->account_type == MerchantShopBank::LOAI_TK_TRUC_TIEP && $mcShopBank->isDaLienKet() && $mcShopBank->isDongBoSoundBox();
				})
				->map(function ($mcShopBank) {
						return [
							'account_number' => $mcShopBank->merchantBank->account_number, 
							'account_holder' => $mcShopBank->merchantBank->account_holder, 
							'account_qr'  => $mcShopBank->account_qr,
							'account_qr_display' => $mcShopBank->account_qr_display,
							'status' => $mcShopBank->getStatusLienKetForMobile(),
							'bank_branch' => $mcShopBank->merchantBank->bank_branch,
							'bank_code' =>  $mcShopBank->merchantBank->bank_code,
							'bank_id' => $mcShopBank->merchantBank->id,
							'request_id' => $mcShopBank->request_id,
							'partner_request_id' => $mcShopBank->partner_request_id,
							'is_default' => $mcShopBank->has('switching') ? 'YES' : 'NO',
							'bank_icon' =>  $mcShopBank->merchantBank->getBankIconUrl(),
						];
					})->values()
					->toArray();

				$bankName = isset($detailMc['data']) && isset($detailMc['data']['bankName']) ? $detailMc['data']['bankName'] : '';
				$bankCode = explode(' - ',$bankName)[0];

				$item['listBanking']['inter'][] = [
					'account_number' => isset($detailMc['data']) && isset($detailMc['data']['accountNo']) ? $detailMc['data']['accountNo'] : '', 
					'account_holder' => isset($detailMc['data']) && isset($detailMc['data']['holderName']) ? $detailMc['data']['holderName'] : '', 
					'account_qr'  => '',
					'account_qr_display' => isset($detailMc['data']) && isset($detailMc['data']['qrDisplayName']) ? $detailMc['data']['qrDisplayName'] : '',
					'status' => 'DA_LIEN_KET',
					'bank_branch' => '',
					'bank_code' =>  $bankCode,
					'bank_id' => isset($detailMc['data']) && isset($detailMc['data']['bankId']) ? $detailMc['data']['bankId'] : '',
					'request_id' => '',
					'partner_request_id' => '',
					'is_default' => 'NO',
					'bank_icon' => 'https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/test/67b5971c4465eb435f2991c5IconBank.png',
				];																											
			}
		}
		
		$listDiemBan = app(GetThongTinVATrenTungMUSubAction::class)->run($listDiemBan, $request);
		
		$returnData = [
			'merchantId' => $request->json('data.merchantId'),
			'username' => $request->json('data.username'),
			'listPos' => $listDiemBan
		];

		return $returnData;
	}
}
