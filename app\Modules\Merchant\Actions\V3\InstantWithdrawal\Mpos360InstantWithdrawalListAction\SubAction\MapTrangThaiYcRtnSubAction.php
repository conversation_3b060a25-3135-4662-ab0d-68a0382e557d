<?php 
namespace App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalListAction\SubAction;

class MapTrangThaiYcRtnSubAction {
	public function run() {
		$returnData = [
			[
				'key' => 'APPROVAL',
				'label' => __('rtn.Đã duyệt'),
				'text_color' => '#ffffff',
				'bg_color' => '#008BF4'
			],

			[
				'key' => 'DENIED',
				'label' => __('rtn.Từ chối'),
				'text_color' => '#ffffff',
				'bg_color' => '#DA2128'
			],

			[
				'key' => 'REJECTED',
				'label' => __('rtn.Từ chối'),
				'text_color' => '#ffffff',
				'bg_color' => '#DA2128'
			],

			[
				'key' => 'HAS_BALANCE',
				'label' => __('rtn.Đã thanh toán'),
				'text_color' => '#ffffff',
				'bg_color' => '#3023E9'
			],

			[
				'key' => 'PENDING',
				'label' => __('rtn.Chờ duyệt'),
				'text_color' => '#ffffff',
				'bg_color' => '#E99323'
			],
		];

		$map = collect($returnData)->keyBy('key')->toArray();
		return $map;
	}
}