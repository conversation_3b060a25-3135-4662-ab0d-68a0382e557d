<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Config;
use App\Http\Middleware\MakeSureThatRequestIsJsonMiddleware;
use App\Modules\Merchant\Controllers\Setting\SettingController;
use App\Modules\Merchant\Controllers\Authen\Mpos360\Mpos360AuthController;

Route::middleware(MakeSureThatRequestIsJsonMiddleware::class)->group(function () {
	Route::any('/Mpos360AuthLogin', [
		'uses' => Mpos360AuthController::class . '@Mpos360AuthLogin',
		'as' => 'Mpos360AuthLoginAction'
	])->middleware('validateHash:email|password|os|token');

	Route::any('/Mpos360AuthLoginUserName', [
		'uses' => Mpos360AuthController::class . '@Mpos360AuthLoginUserName',
		'as' => 'Mpos360AuthLoginAction'
	])->middleware('validateHash:value|password|os|fcmToken|deviceToken');

	Route::any('/Mpos360AuthLoginUserNameV2', [
		'uses' => Mpos360AuthController::class . '@Mpos360AuthLoginUserNameV2',
		'as' => 'Mpos360AuthLoginActionV2'
	])->middleware('checkSumForAnyMobile:value|password|os|fcmToken|deviceToken');

	Route::any('/Mpos360AuthLoginUserNameOnly', [
		'uses' => Mpos360AuthController::class . '@Mpos360AuthLoginUserNameOnly',
		'as' => 'Mpos360AuthLoginActionV2'
	])->middleware('checkSumForAnyMobile:value|password|os|fcmToken|deviceToken');

	Route::any('/Mpos360AuthLoginPartner', [
		'uses' => Mpos360AuthController::class . '@Mpos360AuthLoginPartner',
		'as' => 'Mpos360AuthLoginPartner'
	]);

	// Route::any('/Mpos360AuthRecheckLogin', [
	// 	'uses' => Mpos360AuthController::class . '@Mpos360AuthRecheckLogin',
	// 	'as' => 'Mpos360AuthLoginAction'
	// ])->middleware('validateHash:sessionId|userId|sessionApiKey');
});


Route::post('/Mpos360GetSetting', [
	'uses' => SettingController::class . '@Mpos360GetSetting',
	'as' => 'Mpos360GetSettingAction'
])->middleware('validateHash:os|current_version'); 


Route::get('/tai-khoan/dong-tai-khoan', function () {
	return view('TaiKhoan.DongTaiKhoan');
});

Route::any('/504gw-timeout', function () {
	Config::set('app.debug', false);
	abort(504, '504 Gateway Timeout');
});

Route::fallback(function () {
	return view('empty');
});
