<?php

namespace App\Modules\Merchant\Actions\ChungThuc\Mpos360ChungThucVerifyOtpAction;

use Exception;
use App\Lib\partner\MNP;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Setting;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360CodeOtp;
use App\Modules\Merchant\Model\Mpos360ChungThuc;
use App\Modules\Merchant\Requests\ChungThuc\Mpos360ChungThucVerifyOtpRequest;


class Mpos360ChungThucVerifyOtpAction
{
	public MNP $mnp;

	public function __construct(MNP $mnp)
	{
		$this->mnp = $mnp;
	}

	public function run(Mpos360ChungThucVerifyOtpRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();

		$chungThucId = $request->json('data.reference_id');
		$otp         = trim($request->json('data.otp'));
		$otpId       = $request->json('data.otp_id');

		$settingFakeOtp = Setting::query()->firstWhere(['key' => 'FAKE_OTP_PASSED', 'value' => '1']);

		if (optional($settingFakeOtp)->id) {
			goto HAS_PASS_OTP;
		}
		
		$mpos360CodeOtp = Mpos360CodeOtp::query()->where([
			'id' => $otpId,
			'user_id' => $deviceSession->user_id,
			'command_code' => 'ATTESTATION'
		])->first();

		if (!$mpos360CodeOtp) {
			throw new BusinessException(vmsg('Mpos360ChungThucVerifyOtpAction_LoiKhongTimThayBanGhiOtp'));
		}

		if ($mpos360CodeOtp->isFinalStatus()) {
			throw new BusinessException(vmsg('Mpos360ChungThucVerifyOtpAction_LoiOtpDaDuocSuDung'));
		}

		if ($mpos360CodeOtp->isExpiredOtp()) {
			throw new BusinessException(vmsg('Mpos360ChungThucVerifyOtpAction_LoiOtpDaHetHan'));
		}

		if ($mpos360CodeOtp->otp != $otp) {
			throw new BusinessException(vmsg('Mpos360ChungThucVerifyOtpAction_LoiOtpKhongChinhXac'));
		}

		HAS_PASS_OTP:
		// Đoạn này là đúng otp rồi, giờ phải update là đã xác thực
		$mpos360ChungThuc = Mpos360ChungThuc::query()->find($chungThucId);

		if (!$mpos360ChungThuc) {
			throw new BusinessException(vmsg('Mpos360ChungThucVerifyOtpAction_LoiKhongTimThayTruongChungThucLienQuan'));
		}

		$mpos360CodeOtp->status = Mpos360Enum::MPOS360_OTP_DA_SU_DUNG;
		$mpos360CodeOtp->time_updated = now()->timestamp;
		$r = $mpos360CodeOtp->save();

		if (!$r) {
			throw new BusinessException(vmsg('Mpos360ChungThucVerifyOtpAction_LoiKhongDanhDauDuocOtpLaDaSuDung'));
		}
		
		$mpos360ChungThuc->time_confirmed = now()->timestamp;
		$mpos360ChungThuc->time_expired = 0;
		$mpos360ChungThuc->status = Mpos360Enum::MPOS360_CHUNG_THUC_STT_DA_XAC_NHAN;
		$mpos360ChungThuc->time_updated = now()->timestamp;
		$r = $mpos360ChungThuc->save();

		if (!$r) {
			throw new BusinessException(vmsg('Mpos360ChungThucVerifyOtpAction_LoiKhongDanhDauDuocLaDaChungThuc'));
		}

		$msg = vmsg('Mpos360ChungThucVerifyOtpAction_ChungThucThanhCong', [
			'obj_value' => $mpos360CodeOtp->obj_value,
			'service_code' => $mpos360CodeOtp->service_code,
		]);

		return [
			'reference_id' => $mpos360ChungThuc->id,
			'msg' => $msg
		];
	}
} // End class
