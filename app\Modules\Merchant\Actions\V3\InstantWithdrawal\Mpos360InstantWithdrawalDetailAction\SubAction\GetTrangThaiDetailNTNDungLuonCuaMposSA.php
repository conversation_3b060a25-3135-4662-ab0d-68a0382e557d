<?php

namespace App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalDetailAction\SubAction;

class GetTrangThaiDetailNTNDungLuonCuaMposSA
{
	/**
	 * array:35 [
			"id" => "44469526"
			"status" => "APPROVED"
			"withdrawStatus" => "APPROVED"
			"withdrawFk" => "36185710"
			"withdrawNumber" => "78037"
			"requestType" => "NTN-VietQR"
			"createdDate" => "11-11-2024 13:22:27"
			"approvedDate" => "11-11-2024 13:22:29"
			"paymentDate" => ""
			"trxCount" => 1
			"trxFeeAmount" => 0
			"trxFeeInstallMent" => 0
			"feeTransfer" => 8800
			"paymentNowFee" => 0
			"totalAmountAfterFee" => 0
			"totalAmountBeforeFee" => 0
			"paymentNowTrxDTOS" => array:1 [
				0 => array:5 [
					"created_date" => "2024-11-11 13:22:27"
					"created_by" => "<EMAIL>"
					"transaction_fk" => "44397274"
					"payment_now_request_fk" => 44469526
					"status" => "PENDING"
				]
			]
			"transactionQRList" => array:1 [
				0 => array:5 [
					"transactionType" => "VAQR"
					"amount" => 145600
					"createdDate" => "08/11/2024 15:03:20"
					"pan" => "828866**********"
					"issuerCode" => "VAQR"
				]
			]
			"refundTransList" => []
			"collectTransList" => []
			"totalAmount" => 145600
			"quickFeeAmount" => 0
			"totalRefundAmount" => 0
			"totalCollectAmount" => 0
			"recieveAmount" => 136800
			"recieveAmountString" => "Một trăm ba mươi sáu ngàn tám trăm đồng"
			"bankAccountNo" => "**********"
			"holderName" => "DUNG"
			"bankName" => "NH DAU TU VA PHAT TRIEN VN (BIDV)"
			"bankBranch" => "HÀ NỘI"
			"payoutId" => "********"
			"viewStatus" => "Đã duyệt"
			"payoutApprovedStatus" => "FAIL"
			"payoutVimoStatus" => "PENDING"
			"payoutBankTransferStatus" => "PENDING"
		]
	 */
	public function run($rc)
	{
		
		if ($rc['viewStatus'] == 'Đã duyệt') {
			return [
				'text' => $rc['viewStatus'],
				'text_color' => '#ffffff',
				'bg_color' => '#018bf4'
			];
		}

		if ($rc['viewStatus'] == 'Mới tạo') {
			return [
				'text' => $rc['viewStatus'],
				'text_color' => '#ffffff',
				'bg_color' => '#a3917c'
			];
		}

		if ($rc['viewStatus'] == 'Đã thanh toán') {
			return [
				'text' => $rc['viewStatus'],
				'text_color' => '#ffffff',
				'bg_color' => '#73ae4a'
			];
		}

		if ($rc['viewStatus'] == 'Chi tiền thất bại') {
			return [
				'text' => $rc['viewStatus'],
				'text_color' => '#ffffff',
				'bg_color' => '#da2128'
			];
		}

		if ($rc['viewStatus'] == 'Chờ ngân hàng xử lý') {
			return [
				'text' => $rc['viewStatus'],
				'text_color' => '#ffffff',
				'bg_color' => '#e2b7b7'
			];
		}

	
		return [
			'text' => $rc['viewStatus'],
			'text_color' => '#ffffff',
			'bg_color' => '#000000',
		];
	}
}
