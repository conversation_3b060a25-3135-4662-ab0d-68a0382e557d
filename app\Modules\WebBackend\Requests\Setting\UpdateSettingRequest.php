<?php

namespace App\Modules\WebBackend\Requests\Setting;

use App\Modules\Merchant\Requests\MerchantRequest;
use Illuminate\Validation\Rule;

class UpdateSettingRequest extends MerchantRequest
{
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.id' => ['required', 'string'],
			'data.name' => ['required', 'string', 'max:255'],
			'data.key' => ['required', 'string', 'max:100'],
			'data.value' => ['required', 'string'],
			'data.description' => ['required', 'string', 'max:255']
		];
	}
}
