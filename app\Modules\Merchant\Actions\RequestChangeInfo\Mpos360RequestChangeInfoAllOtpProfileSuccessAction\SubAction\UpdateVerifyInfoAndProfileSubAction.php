<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessAction\SubAction;

use App\Exceptions\BusinessException;
use Exception;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;

class UpdateVerifyInfoAndProfileSubAction
{
	/**
	 * @param $mpos360McRequest 
	 * @param $nguoiDaiDienData [
	 * 		'isTrungCccdChungThuc' => bool,
	 * 		'profileLayTuCccd' => 
	 * ]
	 * 
	 * @param $getRequestVefifyAsArray [Dữ liệu verify nhét vào json]
	 * @param $attachments [Dữ liệu attachments số hóa thành profile ném vào json]
	 */
	public function run(
		Mpos360MerchantRequest $mpos360McRequest,
		array $nguoiDaiDienData,
		array $getRequestVefifyAsArray,
		array $attachments=[]
	) {
		$dataRequest = json_decode($mpos360McRequest->data_request, true);

		$profiles = $dataRequest[0]['profiles'];

		foreach ($getRequestVefifyAsArray as $verityItem) {
			if (!empty($verityItem['field'])) {
				$profiles[$verityItem['field']] = $verityItem['value'];
			}
		}

		if (!empty($nguoiDaiDienData['profileLayTuCccd'])) {
			foreach ($nguoiDaiDienData['profileLayTuCccd'] as $key => $value) {
				$profiles[$key] = $value;
			}
		}

		if (!empty($attachments)) {
			$profileImageUrl = app(MappingDinhKemSubAction::class)->run($attachments);
			foreach ($profileImageUrl as $key => $value) {
				$profiles[$key] = $value;
			}
		}


		$dataRequest[0]['profiles'] = $profiles;
		$dataRequest[0]['request_vefify'] = $getRequestVefifyAsArray;

		if ($nguoiDaiDienData['isTrungCccdChungThuc']) {
			$dataRequest[0]['scan_method']['QTS'] = [
				'status' => 'DONE',
				'other_data' => [
					'is_matching_facescan' => 1,
					'matching_percent' => 100
				]
			];
		}

		$r = Mpos360MerchantRequest::query()
			->where('id', $mpos360McRequest->id)
			->update([
				'data_request' => json_encode($dataRequest),
				'time_updated' => now()->timestamp,
				'status_verify' => Mpos360Enum::MPOS360_MC_VERIFY_STT_DA_XAC_THUC
			]);

		if (!$r) {
			mylog(['Loi update thong tin' => $r]);
			throw new BusinessException('Loi update thong tin');
		}

		$can = Mpos360Enum::MPOS360_CAN_GOTO_STEP3;

		if ($nguoiDaiDienData['isTrungCccdChungThuc']) {
			$can = Mpos360Enum::MPOS360_CAN_MARK_DONE_REQUEST;
		}

		return $can;
	}
} // End class
