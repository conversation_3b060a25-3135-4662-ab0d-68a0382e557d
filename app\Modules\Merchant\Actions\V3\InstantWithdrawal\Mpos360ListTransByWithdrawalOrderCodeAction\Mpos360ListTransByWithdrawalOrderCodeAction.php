<?php

namespace App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360ListTransByWithdrawalOrderCodeAction;

use App\Exceptions\BusinessException;
use Exception;
use App\Lib\partner\MPOS;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Requests\InstantWithdrawal\V3\Mpos360ListTransByWithdrawalOrderCodeRequest;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360ListTransByWithdrawalOrderCodeAction\SubAction\GetGiaoDichTraLaiSubAction;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360ListTransByWithdrawalOrderCodeAction\SubAction\GetGiaoDichThuThemSubAction;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360ListTransByWithdrawalOrderCodeAction\SubAction\GetGiaoDichLucTaoYcSubAction;

// Get danh sách giao dịch Việt QR dựa vào mã rút tiền nhanh
class Mpos360ListTransByWithdrawalOrderCodeAction
{
	public MPOS $mpos;

	public function __construct(MPOS $mpos)
	{
		$this->mpos = $mpos;
	}

	public function run(Mpos360ListTransByWithdrawalOrderCodeRequest $request)
	{
		$orderCode = $request->json('data.order_code');
		$deviceSession = $request->getCurrentDeviceSession();

		$params = [
			'serviceName' => 'PAYMENT_NOW_RQ_GET_DETAIL',
			'requestId' => $orderCode,
			'tokenLogin' => $deviceSession->getMposToken(),
			'merchantFk' => $deviceSession->getMerchantId()
		];

		$detail = $this->mpos->paymentNowGetDetail($params);
		$mposApiCode = $detail['data']['error']['code'] ?? false;
		if (empty($mposApiCode) || $mposApiCode != Mpos360Enum::API_SUCCESS_CODE) {
			throw new BusinessException('Mpos Err: ' . $detail['data']['error']['message'] ?? 'Unknow Err');
		}

		$rc = $detail['data']['data'];
		switch ($request->json('data.type')) {
			case Mpos360Enum::MPOS360_RTN_TYPE_GD_VIETQR_TAO_YC:
			case Mpos360Enum::MPOS360_RTN_TYPE_GD_VIETQR_DUOC_DUYET:
				$data = app(GetGiaoDichLucTaoYcSubAction::class)->run($rc);
				break;

			case Mpos360Enum::MPOS360_RTN_TYPE_GD_TRA_LAI:
				$data = app(GetGiaoDichTraLaiSubAction::class)->run($rc);
				break;

			case Mpos360Enum::MPOS360_RTN_TYPE_GD_THU_THEM:
				$data = app(GetGiaoDichThuThemSubAction::class)->run($rc);
				break;

			default:
				throw new BusinessException('Not found transaction category');
				break;
		}


		$data = collect($data)->map(function ($item) {
			if ($item['date'] == now()->format('Y-m-d')) {
				$item['date'] = __('rtn.Hôm nay');
			}

			if ($item['date'] == now()->subDay()->format('Y-m-d')) {
				$item['date'] = __('rtn.Hôm qua');
			}

			return $item;
		})->values()->all();

		$returnData['data'] = $data;
		$returnData['countSumFilter'] = (object) [];

		return $returnData;
	} // End method
} // End clas
