<?php

namespace App\Modules\TingBoxVAMC\Requests\Transaction;

use Illuminate\Foundation\Http\FormRequest;

class Mpos360CountSumDoanhThuByDiemBanRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array'],
      'data.merchantId' => ['required', 'numeric'],
			'data.vaMode' => ['required', 'string', 'in:VAMC,VANP'],
			'data.rangeTime' => ['required', 'string', 'date_format:d-m-Y'],
			'data.muid' => ['present', 'string'],
			'data.username' => ['present', 'string']
    ];
  }

	public function messages() {
		return [
			'data.merchantId.required' => 'MerchantId là bắt buộc',
			'data.merchantId.numeric' => 'MerchantId phải là kiểu số',
			'data.vaMode.required' => 'Loại giao dịch là bắt buộc',
			'data.vaMode.string' => 'Loại giao dịch phải là kiểu chuỗi',
			'data.vaMode.in' => 'Loại giao dịch phải là kiểu: Trực tiếp hoặc Trung gian',
			'data.rangeTime.required' => 'Thời gian xem doanh thu là bắt buộc',
			'data.rangeTime.string' => 'Thời gian xem doanh thu phải là kiểu chuỗi',
			'data.rangeTime.date_format' => 'Thời gian xem doanh thu phải có định dạng: ngày-tháng-năm',
			'data.muid.present' => 'Mã cửa hàng là bắt buộc',
			'data.muid.present' => 'Mã cửa hàng phải là kiểu chuỗi',
		];
	}
} // End class
