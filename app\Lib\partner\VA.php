<?php

namespace App\Lib\partner;

use App\Lib\Helper;
use App\Lib\TelegramAlertWarning;
use Illuminate\Support\Facades\Log;

define('VAG_IPS', env('VAG_IPS'));
define('VAG_APP_ID', env('VAG_APP_ID'));
define('VAG_SECRETKEY', env('VAG_SECRETKEY'));
define('VAG_ENCRYPTTION_KEY', env('VAG_ENCRYPTTION_KEY'));
define('VAG_BASE_URL', env('VAG_BASE_URL'));

class VA
{
	public function __construct() {}

	public array $__logData = [];

	/**
	 * @param 
	 * $params array:4 [▼
				"app_id" => "vmmc_test"
				"data" => "{"merchantId":2,"mposMcId":"********","mposUserId":"","mposUserMobile":"","page":1,"row":5,"fromDate":1900000,"toDate":0,"vaNumber":""}"
				"checksum" => "0db55ffd1cfb0753412594b3f3227d9c"
				"ref_code" => "VIRTUALACCOUNT"
			]
	 */
	public function call($url, $params, $timeout=5)
	{
		$this->__logData['VA-CALL'] = 'YES';
		$this->__logData['VA-URL'] = $url;
		// $this->__logData['VA-PARAMS'] = $params;

		try {
			$headerAuth = array(
				'Content-Type: application/json',
			);
	
			$curl = curl_init();
	
			curl_setopt_array($curl, array(
				CURLOPT_URL => $url,
				CURLOPT_RETURNTRANSFER => true,
				CURLOPT_ENCODING => '',
				CURLOPT_MAXREDIRS => 10,
				CURLOPT_TIMEOUT => $timeout,
				CURLOPT_FOLLOWLOCATION => true,
				CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
				CURLOPT_CUSTOMREQUEST => 'POST',
				CURLOPT_SSL_VERIFYPEER => 0,
				CURLOPT_POSTFIELDS => json_encode($params),
				CURLOPT_HTTPHEADER => $headerAuth,
			));
	
			$result = curl_exec($curl);
	
			curl_close($curl);
	
			// $this->__logData['VA-RESULT'] = $result;
	
			if (is_array($result)) {
				return $result;
			}
	
			return json_decode($result, true);
		}catch(\Throwable $th) {
			TelegramAlertWarning::sendMessage(Helper::traceError($th));
			$this->__logData['VA-ERR'] = Helper::traceError($th);
			return [];
		}finally {
			mylog($this->__logData);
		}
	}


	function encrypt($text){
			$salt = openssl_random_pseudo_bytes(8);
			$salted = $dx = '';
			while (strlen($salted) < 48) {
					$dx = md5($dx . VAG_ENCRYPTTION_KEY . $salt, true);
					$salted .= $dx;
			}
			$key = substr($salted, 0, 32);
			$iv = substr($salted, 32, 16);
			return base64_encode('Salted__' . $salt . openssl_encrypt($text . '', 'aes-256-cbc', $key, OPENSSL_RAW_DATA, $iv));
	}

	public function createChecksum($data)
	{
		$checksum = MD5(VAG_APP_ID . $data . VAG_ENCRYPTTION_KEY);
		return $checksum;
	}

	public static function getAppIdVamc($bankCode='BIDV'): string {
		// if (Helper::isLocalOrDevEnv()) {
		// 	switch ($bankCode) {
		// 		case 'BIDV':
		// 			return env('VAG_APP_ID');
		// 			break;

		// 		case 'OCB':
		// 			return 'app_id';
		// 			break;

		// 		case 'VAB':
		// 			return 'app_va';
		// 			break;
				
		// 		default:
		// 			# code...
		// 			break;
		// 	}
		// }
		
		return env('VAG_APP_ID');
	}
}
