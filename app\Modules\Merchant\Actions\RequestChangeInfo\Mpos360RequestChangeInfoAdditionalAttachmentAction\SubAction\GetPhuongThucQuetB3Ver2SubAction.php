<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAdditionalAttachmentAction\SubAction;

use App\Modules\Merchant\Model\Mpos360ChungThuc;
use App\Modules\Merchant\Model\Mpos360PhuongThucBuoc3;

class GetPhuongThucQuetB3Ver2SubAction
{
	/**
	 * Nếu chưa chứng thực CCCD thì B3 chỉ có 1 phương thức (SDK) thôi
	 * Nếu đã chứng thực CCCD rồi thì B3 sẽ có toàn bộ phương thức và phương thức main là QTS (quét mặt)
	 */
	public function run($merchantId)
	{
		$mpos360ChungThucCccd = Mpos360ChungThuc::getChungThucCCCD($merchantId);
		
		if (!$mpos360ChungThucCccd) {
			return [
				[
					'method_code' => 'SDK',
					'method_name' => vmsg('GetPhuongThucQuetB3Ver2SubAction_SDK'),
					'is_main' => 'YES',
					'method_icon' => cumtomAsset('images/requestChangeInfo/step3_method/SDK.png')
				]
			];
		}

		$listPhuongThucScanBuoc3 = Mpos360PhuongThucBuoc3::getPhuongThucBuoc3();
		$scanMethod = $listPhuongThucScanBuoc3->map(function (Mpos360PhuongThucBuoc3 $pt) {
			return [
				'method_code' => $pt->method_code,
				'method_name' => vmsg('GetPhuongThucQuetB3Ver2SubAction_' . $pt->method_code),
				'is_main' => $pt->method_code == 'QTS' ? 'YES' : 'NO',
				'method_icon' => cumtomAsset("images/requestChangeInfo/step3_method/{$pt->method_code}.png")
			];
		})
		->values()
		->toArray();

		return $scanMethod;
	}
} // End class