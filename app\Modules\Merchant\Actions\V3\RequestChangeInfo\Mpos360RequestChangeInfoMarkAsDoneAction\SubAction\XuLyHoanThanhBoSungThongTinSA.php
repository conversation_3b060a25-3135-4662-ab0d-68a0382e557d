<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoMarkAsDoneAction\SubAction;

use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use Exception;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;

class XuLyHoanThanhBoSungThongTinSA
{
	public function run(Mpos360MerchantRequest $mpos360McRequest)
	{
		$mpos360McRqSupplement = $mpos360McRequest->mpos360McSupplementNew;
		$mpos360McRqSupplement->status = Mpos360Enum::MPOS360_BO_SUNG_HO_SO_DA_BO_SUNG_THONG_TIN;
		$mpos360McRqSupplement->time_created = now()->timestamp;
		$mpos360McRqSupplement->time_updated = now()->timestamp;
		$r = $mpos360McRqSupplement->save();

		if (!$r) {
			throw new BusinessException('Lỗi không cập nhật được yêu cầu bổ sung thông tin');
		}

		$dataRequest = json_decode($mpos360McRqSupplement->data_request, true);

		if (empty($dataRequest['raw_attachments'])) {
			throw new BusinessException('Bạn chưa cập nhật bổ sung thông tin');
		}
		
		return [
			'id' => $mpos360McRequest->id,
			'msg' => 'Chúng tôi đã tiếp nhận yêu cầu bổ sung thông tin của Qúy đơn vị. Hệ thống MPOS sẽ tiến hành kiểm tra và phản hồi trong vòng 02 ngày làm việc. Trân trọng cảm ơn Quý đơn vị đã tin tưởng và sử dụng dịch vụ của chúng tôi'
		];
	}
} // End class