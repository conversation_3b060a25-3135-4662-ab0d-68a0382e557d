<?php

namespace App\Modules\TingBoxVAMC\Actions\VAMC;

use App\Lib\Logs;
use App\Lib\partner\VA;

class ConfirmLinkVAMCAction
{
	public VA $va;

	public function __construct(VA $va)
	{
		$this->va = $va;
	}

	/**
	 * {
			"error_code": "00",
			"error_message": "Thành công",
			"checksum": "1198df0cc3b2da4ff6958281e0b8de66",
			"data": {
				"mcRequestId": "MPOS-**********",
				"vaNextpayNumber": "NPQQYIWRBHEE4PD",
				"vaBankNumber": "962VMS9476428177088",
				"qrCode": "38630010A000000727013300069704180119962VMS94764281770880208QRIBFTTA53037045802VN62280824MC8IKCMLJFO5GNP Mua hang63041b12",
				"qrImage": "",
				"status": "ACTIVED",
				"channelCode": "",
				"providerCode": "",
				"vaReference": "MC8IKCMLJFO5GNP",
				"transferDesc": "MC8IKCMLJFO5GNP Mua hang",
				"urlConffirm": "",
				"deepLinkConfirm": "",
				"methodConfirm": ""
			}
		}
	 */
	public function run($params = [], $merchantShopBank=null)
	{
		$p =  [
			'app_id' => env('VAG_APP_ID'),
			'mcRequestId' => $params['mcRequestId'],
			'vaNextpayNumber' => $params['vaNextpayNumber'],
			'otp' => $params['otp']
		];

		mylog(['Param-VA-Raw' => $p]);

		Logs::writeInfo("ConfirmLinkVAMCAction", $p);
		$dataAsJson = json_encode($p);
		$encrypt = $this->va->encrypt($dataAsJson);

		$checksum = $this->va->createChecksum($encrypt);

		$dataPost = [
			'app_id' => VAG_APP_ID,
			'data' => $encrypt,
			'checksum' => $checksum,
		];

		$url = VAG_BASE_URL . '/v1/merchant/va-mc/confirm-link';
		try {
			$r = $this->va->call($url, $dataPost, 5);
			Logs::writeInfo("ConfirmLinkVAMCAction-Response", $r);
		} catch (\Throwable $th) {
			$r = [];
		}

		mylog(['Response-VA-Raw' => $r]);
		return $r;
	}
} // End class