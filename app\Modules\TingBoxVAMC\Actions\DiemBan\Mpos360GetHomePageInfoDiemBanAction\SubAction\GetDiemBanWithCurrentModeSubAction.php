<?php

namespace App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360GetHomePageInfoDiemBanAction\SubAction;

use App\Lib\Helper;
use App\Lib\partner\SoundBox;

class GetDiemBanWithCurrentModeSubAction
{
	public function run(string $merchantId)
	{
		$sb = (new SoundBox())->getLocationByMcId([
			'mcId' => $merchantId,
			'partnerCode' => Helper::getPartnerCode($merchantId)
		]);

		if (empty($sb['data']['mcStores'])) {
			return [];
		}

		$listDiemBan = [];
		$randomIndex = array_rand($sb['data']['mcStores']);
		
		foreach ($sb['data']['mcStores'] as $index => $s) {

			$itemDiemBan = [
				'muid' => $s['mobileUser']['muName'],
				'name' => sprintf('%s (%s)', $s['name'], $s['mobileUser']['muName']),
				'address' => $s['address'],
				'bank' => [
					'holderName' => '',
					'accountNo' => '',
					'bankCode' => '',
					'integratedMethod' => 'VANP'
				],
				'selected' => $randomIndex == $index ? 'YES' : 'NO',
				'step_action' => [
					[
						'step' => 1,
						'status' => isset($s['bank']) && isset($s['bank']['accountNo']) && !empty($s['bank']['accountNo']) ? 2 : 1,
						'step_name' => 'Khai báo tài khoản ngân hàng',
					],
					[
						'step' => 2,
						'status' => isset($s['name']) && !empty($s['name']) ? 2 : 1,
						'step_name' => 'Khai báo cửa hàng kinh doanh',
					],
					[
						'step' => 3,
						'status' => isset($s['mobileUser']) && isset($s['mobileUser']['devices']) && !empty($s['mobileUser']['devices']) ? 2 : 1,
						'step_name' => 'Gán loa Tingbox',
					],
				]
			];

			if (!empty($s['bank'])) {
				$itemDiemBan['bank'] = [
					'holderName' => $s['bank']['holderName'],
					'accountNo' => $s['bank']['accountNo'],
					'bankCode' => $s['bank']['bankCode'],
					'integratedMethod' => $s['bank']['integratedMethod'],
				];
			}


			$listDiemBan[] = $itemDiemBan;
		}

		return $listDiemBan;
	}
}
