<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForRepresentationSA;

use Exception;
use Illuminate\Support\Facades\DB;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\DTOs\RequestChangeInfo\CreateMerchantRequestDto;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\SubAction\CreateMerchantRequestSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\SubAction\AddingAdditionalProfileSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoUpdateProfileAction\CreateForRepresentationSA\SubAction\ValidateHoSoRiengLeSubAction;

class CreateForRepresentationSA
{
	public function run(
		CreateMerchantRequestDto $createMerchantRequestDto,
		Mpos360RequestChangeInfoUpdateProfileRequest $request
	) {
		$verificationProfiles = $request->json('data.request_change_info.profiles');
/* --------------------------- Đổi thông tin liên hệ -------------------------- */
		if ($request->isDoiThongTinLienHe()) {
			$isThayDoiEmail = collect($verificationProfiles)->contains(function ($item) {
				return $item['profileKey'] == 'authoriserEmail';
			});

			$isThayDoiMobile = collect($verificationProfiles)->contains(function ($item) {
				return $item['profileKey'] == 'authoriserContactNumber';
			});

			if (empty($isThayDoiEmail) && empty($isThayDoiMobile)) {
				throw new BusinessException('Lỗi: ít nhất bạn phải thay đổi Email hoặc SĐT');
			}

			$dataThayDoiThongTin = [
				'representMobile' => 'CHANGE_CURRENT_REPRESENT_INFO' // vẫn là ng đại diện cũ
			];

			if ($isThayDoiEmail) {
				$emailProfile = collect($verificationProfiles)->where('profileKey', 'authoriserEmail')->first();
				app(ValidateHoSoRiengLeSubAction::class)->run('authoriserEmail', $emailProfile['value'] ?? '');
				$dataThayDoiThongTin['representEmail'] = $emailProfile['value'];
			}

			if ($isThayDoiMobile) {
				$mobileProfile = collect($verificationProfiles)->where('profileKey', 'authoriserContactNumber')->first();
				app(ValidateHoSoRiengLeSubAction::class)->run('authoriserContactNumber', $mobileProfile['value'] ?? '');
				$dataThayDoiThongTin['representMobile'] = $mobileProfile['value'];
			}

			$createMerchantRequestDto->setProfiles($dataThayDoiThongTin);

			$verificationProfiles = collect($verificationProfiles)->filter(function ($item) {
				return $item['profileKey'] == 'authoriserEmail' || $item['profileKey'] == 'authoriserContactNumber';
			})->values()->all();
		}
/* --------------------------- Đổi CCCD mới (người đại diện vẫn thế) -------------------------- */
		if ($request->isDoiCccdMoi()) {
			$verificationProfiles = collect($verificationProfiles)->filter(function ($item) {
				return $item['profileKey'] == 'passport';
			})->values()->toArray();

			if (empty($verificationProfiles)) {
				throw new BusinessException('CCCD mới là bắt buộc');
			}

			$createMerchantRequestDto->setProfiles([
				'typeChangeRepresent' => 'CHANGE_CURRENT_REPRESENT_INFO' // vẫn là người đại diện cũ
			]);
		}
/* --------------------------- Đổi hẳn người đại diện mới -------------------------- */
		if ($request->isDoiNguoiDaiDienMoi()) {
			$verificationProfiles = collect($verificationProfiles)->filter(function ($item) {
				return $item['profileKey'] == 'passport';
			})->values()->toArray();

			if (empty($verificationProfiles)) {
				throw new BusinessException('CCCD của người đại diện mới là bắt buộc');
			}

			$createMerchantRequestDto->setProfiles([
				'typeChangeRepresent' => 'CHANGE_NEW_REPRESENT' // type đổi người mới
			]);
		}
/* --------------------------- Vào luồng tạo yc -------------------------- */
		DB::beginTransaction();
		try {
			// Tạo bản ghi yêu cầu
			$merchantRequest = app(CreateMerchantRequestSubAction::class)->run($createMerchantRequestDto->toArray());
			$savedResult = $merchantRequest->save();

			if (!$savedResult) {
				throw new BusinessException('Lỗi không tạo được bản ghi yêu cầu thay đổi');
			}

			$can = '';

			if ($request->isDoiThongTinLienHe()) {
				$can = Mpos360Enum::MPOS360_CAN_NEED_VERIFY_OTP;
			}

			if ($request->isDoiCccdMoi()) {
				$can = Mpos360Enum::MPOS360_CAN_GOTO_SDK_FLOW;
			}

			if ($request->isDoiNguoiDaiDienMoi()) {
				$can = Mpos360Enum::MPOS360_CAN_GOTO_SDK_FLOW;
			}

			$returnData = [
				'id' => $merchantRequest->id,
				'request_change_info_id' => $merchantRequest->mynextpay_id,
				'status' => 'INFO',
				'msg' => 'Bạn cần xác minh otp',
				'can' => $can,
				'additional_profiles' => app(AddingAdditionalProfileSubAction::class)->run($request),
				'verification_profiles' => $verificationProfiles
			];

			DB::commit();
			return $returnData;
		} catch (\Throwable $th) {
			DB::rollBack();
			throw $th;
		}
	} // end method
} // End class
