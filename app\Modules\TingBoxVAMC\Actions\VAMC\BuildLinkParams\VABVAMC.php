<?php

namespace App\Modules\TingBoxVAMC\Actions\VAMC\BuildLinkParams;

use App\Lib\Helper;

class VABVAMC
{
	public function run($params)
	{
		$p =  [
			'app_id' => $params['app_id'],
			'mcRequestId' => 'MPOS-' . rand(1, 9999999).time(),
			'mcRequestAccNumber' => strval(rand(1000000, 9999999)),
			'description' => $params['mposMcId'] ?? '',
			'expiryDate' => 0,
			'equalAmount' => 0,
			'minAmount' => 0,
			'maxAmount' => 0,
			'detail1' => 'Mo ta 1',
			'detail2' => 'Mo ta 2',
			'detail3' => 'Mo ta 3',
			'detail4' => 'Mo ta 4',
			'detail5' => 'Mo ta 5', 

			'providerCode' => $params['providerCode'], // BIDVVAMC
			'bankCode' => $params['bankCode'], // BIDV

			'merchantType' => $params['merchantType'], // PERSONAL: Cá nhân; SOLE: Hộ kinh doanh; BUSINESS: Doanh nghiệp

			'merchantName' => Helper::titleString($params['merchantName']), // Tên cửa hàng
			'merchantAddress' => $params['merchantAddress'], // Địa chỉ cửa hàng
			'merchantMcc' => $params['merchantMcc'], // Mã ngành nghề
			'bankIssuerAccountNo' => $params['bankIssuerAccountNo'], // Số tài khoản: **********
			'bankIssuerAccountName' => $params['bankIssuerAccountName'], // Họ tên: Tran Thi Huyen
			'bankIssuerIdentity' => $params['bankIssuerIdentity'], // Số CCCD: ************
			'bankIssuerMobile' => $params['bankIssuerMobile'], // SĐT: **********
			'bankIssuerEmail' => $params['bankIssuerEmail'], // Email (option theo bank): <EMAIL>' 
			 

			// Thông tin merchant tingbox
			'mcUserId' => $params['mcUserId'], // Id user tingbox
			'mcMcId' => $params['mcMcId'], // Id merchant tingbox
			'mcUserMobile' => $params['mcUserMobile'], // Mobile user của tingbox
			'mcStoreId' => $params['mcStoreId'] ?? '', // Id cửa hàng tingbox, không có thì để empty string

			// Cụm mpos
			'mposMcId' => $params['mposMcId'], 
			'mposUserId' => $params['mposUserId'], 
			'mposUserMobile' => $params['mposUserMobile'], 
		];

		return $p;
	}
}
