<?php
namespace App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360Register;

use Illuminate\Foundation\Http\FormRequest;

class Mpos360SubmitFormAuthenAccountStep1ActionRequest extends FormRequest
{
 	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.merchant_id' => ['required', 'string'],
			'data.fullname' => ['required', 'string'],
			'data.cccd' => ['required', 'string'],
			'data.gender' => ['required', 'string'],
			'data.address' => ['required', 'string', 'regex:/^[a-zA-Z0-9\s\/,\u00C0-\u1EF9]*$/u'],
			'data.birthday' => ['required', 'string'],
		];
	}

	public function messages() {
		return [
			'data.merchant_id.required' => 'merchant_id là bắt buộc',
			'data.merchant_id.string' => 'merchant_id phải là kiểu chuỗi ký tự',
			'data.fullname.required' => 'Họ và tên là bắt buộc',
			'data.fullname.string' => 'Họ và tên phải là kiểu chuỗi ký tự',
			'data.cccd.required' => 'Căn cước công dân là bắt buộc',
			'data.cccd.string' => 'Căn cước công dân phải là kiểu chuỗi ký tự',
			'data.gender.required' => 'Giới tính là bắt buộc',
			'data.gender.string' => 'Giới tính phải là kiểu chuỗi ký tự',
			'data.address.required' => 'Địa chỉ thường trú là bắt buộc',
			'data.address.string' => 'Địa chỉ thường trú phải là kiểu chuỗi ký tự',
			'data.address.regex' => 'Địa chỉ thường trú chỉ được chứa chữ cái, số, khoảng trắng, các ký tự đặc biệt "/", "," và các ký tự tiếng Việt có dấu.',
			'data.birthday.required' => 'Ngày sinh là bắt buộc',
			'data.birthday.string' => 'Ngày sinh phải là kiểu chuỗi ký tự',
		];
	}
}