<?php

namespace App\Modules\Merchant\Requests\Config;

use Exception;
use Illuminate\Foundation\Http\FormRequest;
use App\Modules\Merchant\Model\PartnerConfig;
use App\Modules\Merchant\Requests\BaseRequest;

class GetConfigRequest extends BaseRequest
{
  public static $currentClient;

  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      //
    ];
  }
}
