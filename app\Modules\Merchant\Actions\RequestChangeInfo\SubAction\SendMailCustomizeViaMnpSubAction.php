<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\SubAction;

use Exception;
use App\Lib\partner\MNP;
use App\Exceptions\BusinessException;

class SendMailCustomizeViaMnpSubAction
{
	public MNP $mnp;


	public function __construct(MNP $mnp)
	{
		$this->mnp = $mnp;
	}

	/**
	 * @param $mnpToken: token authen của hệ thống mno
	 * @param $mailMeta[]: các thông số của bản ghi mail gồm:
	 * 		to[]: mảng email người nhận
	 * 		cc[]: mảng email cc
	 * 		html<string>: nội dung mail
	 * 		subject<string>: nội dung tiêu đề mail
	 */
	public function run(string $mnpToken, array $mailMeta = []): bool
	{
		$param = [
			'to' => $mailMeta['to'],
			'cc' => $mailMeta['cc'] ?? [],
			'html' => $mailMeta['content'],
			'subject' => $mailMeta['subject']
		];

		$sendMailResult = $this->mnp->sentEmail($param, $mnpToken);

		if (empty($sendMailResult['status'])) {
			$msg = $sendMailResult['message'] ?? 'không xác định';
			throw new BusinessException(vmsg('LoiGuiOtpQuaEmail') . $msg);
		}

		return true;
	} // End method
} // End clasds
