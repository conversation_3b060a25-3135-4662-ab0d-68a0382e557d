<?php

namespace App\Modules\Merchant\Actions\Transaction\V3P1\Mpos360TransactionHistoryAction;

use Carbon\Carbon;
use App\Lib\Helper;
use App\Lib\partner\MPOS;
use App\Lib\SettingHelper;
use App\Lib\DBConnectionHelper;
use App\Lib\partner\MposAWS;
use Illuminate\Support\Facades\DB;
use App\Modules\Merchant\Model\Setting;
use App\Modules\Merchant\Model\Mpos360TransactionSummary;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionHistoryRequest;

class Mpos360TransactionHistoryAction
{
	public MposAWS $mposAws;

	public function __construct(MposAWS $mposAWS)
	{
		$this->mposAws = $mposAWS;	
	}

	private function __convertDataReturn($data, $listTransaction)
	{
		foreach ($data as $key => $value) {
			if (isset($listTransaction[$key])) {
				$data[$key] = $listTransaction[$key];
			}
		}
		return $data;
	}

	public function getLichSuGiaoDichMacq(Mpos360TransactionHistoryRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		$merchantId = $deviceSession->getMerchantId();
		$merchantEmail = $deviceSession->getMerchantEmail();

		mylog(['merchantId' => $merchantId, 'merchantEmail' => $merchantEmail]);

		$data = [
			'totalAmountTransaction' => '',
			'totalAmountSettlement' => '',
			'totalAmountNotSettlement' => '',
			'countTransaction' => '',
			'countSettlement' => '',
			'countNotSettlement' => '',
			'countTransactionInstallment' => '',
			'countQuickWithdraw' => '',
			'countWithdraw' => '',
			'countPaymentNow' => '',
			'countPaymentQR' => '',

			'totalAmountTransactionPrice' => '--',
			'totalAmountSettlementPrice' => '--',
			'totalAmountNotSettlementPrice' => '--',
			'syncTime' => now()->format('H:i')
		];

		$params = [
			'serviceName' => 'DAILY_TRANSACTION_SUMMARY',
			'merchantFk' => $merchantId,
			'tokenLogin' => $deviceSession->getMposToken(),
		];

		// cùng endpoint thống kê theo ngày
		$r = $this->mposAws->getThongKeLichSuGiaoDich($params);
		
		if (!empty($r['data']['data']) && $merchantEmail == '<EMAIL>') {
			$dataLsgd = $r['data']['data'];

			if (!empty($dataLsgd['totalAmountTransaction'])) {
				$data['totalAmountTransaction'] = sprintf('(%s)', $dataLsgd['totalAmountTransaction']);
				$data['totalAmountTransactionPrice'] = Helper::priceFormat($dataLsgd['totalAmountTransaction']);
			}

			if (!empty($dataLsgd['totalAmountSettlement'])) {
				$data['totalAmountSettlement'] = sprintf('(%s)', $dataLsgd['totalAmountSettlement']);
				$data['totalAmountSettlementPrice'] = Helper::priceFormat($dataLsgd['totalAmountSettlement']);
			}

			if (!empty($dataLsgd['totalAmountNotSettlement'])) {
				$data['totalAmountNotSettlement'] = sprintf('(%s)', $dataLsgd['totalAmountNotSettlement']);
				$data['totalAmountNotSettlementPrice'] = Helper::priceFormat($dataLsgd['totalAmountNotSettlement']);
			}
			
			if (!empty($dataLsgd['lastTimeUpdate'])) {
				$data['syncTime'] = Carbon::createFromFormat('d/m/Y H:i:s', $dataLsgd['lastTimeUpdate'])->format('H:i');
			}
			
			$data['countTransaction'] = $dataLsgd['countTransaction'];
		}


		$returnData = [
			'report_today' => [
				[
					'label' => vmsg('Mpos360TransactionHistoryAction_TongTienGiaoDich'),
					'value' => $data['totalAmountTransaction'],
					'extra_value' => $data['totalAmountTransactionPrice'] ?? '--'
				],

				[
					'label' => vmsg('Mpos360TransactionHistoryAction_ChuaKetToan'),
					'value' => $data['totalAmountNotSettlement'],
					'extra_value' => $data['totalAmountNotSettlementPrice'] ?? '--'
				],

				[
					'label' => vmsg('Mpos360TransactionHistoryAction_DaKetToan'),
					'value' => $data['totalAmountSettlement'],
					'extra_value' => $data['totalAmountSettlementPrice'] ?? '--'
				]
			], // end report today

			'transaction_categories' => [
				// group item  cho bên mobile dễ foreach
				[
					[
						'icon' => cumtomAsset('images/transaction/transaction-history/regular_payment.png'),
						'label' => vmsg('Mpos360TransactionHistoryAction_ThanhToanThuong'),
						'value' => '',
						'code' => 'REGULAR_PAYMENT'
					],

					[
						'icon' => cumtomAsset('images/transaction/transaction-history/qr_transaction.png'),
						'label' => vmsg('Mpos360TransactionHistoryAction_GiaoDichQr'),
						'value' => '',
						'code' => 'QR_TRANSACTION'
					],

					[
						'icon' => cumtomAsset('images/transaction/transaction-history/installment_payment.png'),
						'label' => vmsg('Mpos360TransactionHistoryAction_ThanhToanTraGop'),
						'value' => '',
						'code' => 'INSTALLMENT_PAYMENT'
					],
				],

				[
					[
						'icon' => cumtomAsset('images/transaction/transaction-history/quick_withdrawal_request.png'),
						'label' => __('trans.Lịch sử nhận tiền'),
						'value' => '',
						'code' => 'QUICK_WITHDRAWAL_REQUEST'
					],
					[
						'icon' => cumtomAsset('images/transaction/transaction-history/bank_account_funds_receipt.png'),
						'label' =>  vmsg('Mpos360TransactionHistoryAction_NhanTienVeTKNH'),
						'value' => '',
						'code' => 'BANK_ACCOUNT_FUNDS_RECEIPT'
					],
				],
			],

			'syncTime' => '',
		];

		return $returnData;
	}

	public function run(Mpos360TransactionHistoryRequest $request)
	{
		$data = [
			'totalAmountTransaction' => 0,
			'totalAmountSettlement' => 0,
			'totalAmountNotSettlement' => 0,
			'countTransaction' => 0,
			'countSettlement' => 0,
			'countNotSettlement' => 0,
			'countTransactionInstallment' => 0,
			'countQuickWithdraw' => 0,
			'countWithdraw' => 0,
			'countPaymentNow' => 0,
			'countPaymentQR' => 0,

			'totalAmountTransactionPrice' => '--',
			'totalAmountSettlementPrice' => '--',
			'totalAmountNotSettlementPrice' => '--',
			'syncTime' => ''
		];

		$deviceSession = $request->getCurrentDeviceSession();
		$merchantId = $deviceSession->getMerchantId();
		$merchantEmail = $deviceSession->getMerchantEmail();

		// $settingBetaTest = Setting::query()->firstWhere(['key' => 'LIST_MC_USED_BETA_TEST']);
		// $isAllowLichSuGiaoDich = SettingHelper::hasBetaTest($settingBetaTest, 'LichSuGiaoDich', $deviceSession->getMerchantEmail());

		$transactionSummary = null;
		
		// if ($isAllowLichSuGiaoDich) {
		// 	$transactionSummary = Mpos360TransactionSummary::query()
		// 		->where('merchant_id', $merchantId)
		// 		->where('paydate', '>=', now()->startOfDay()->timestamp)
		// 		->where('paydate', '<=', now()->endOfDay()->timestamp)
		// 		->selectRaw("
		// 			merchant_id,
		// 			SUM(total_number) AS total_number,
		// 			SUM(total_amount) AS total_amount,
		// 			SUM(total_number_settled) AS total_number_settled,
		// 			SUM(total_number_pending) AS total_number_pending,
		// 			SUM(total_amount_settled) AS total_amount_settled, 
		// 			SUM(total_amount_pending) AS total_amount_pending
		// 		")
		// 		->groupBy("merchant_id")
		// 		->first();
		
		// 	DBConnectionHelper::closeIfExist();
		// }
		
		$data['totalAmountTransaction'] = '--';
		$data['totalAmountSettlement'] = '--';
		$data['totalAmountNotSettlement'] = '--';

		if ($transactionSummary) {
			if (!empty($transactionSummary->total_amount)) {
				$data['totalAmountTransaction'] = $transactionSummary->total_number > 0 ? $transactionSummary->total_number : ' ';
				$data['totalAmountTransactionPrice'] = Helper::priceFormat($transactionSummary->total_amount);
			}else {
				$data['totalAmountTransaction'] = $transactionSummary->total_number > 0 ? $transactionSummary->total_number : ' ';
				$data['totalAmountTransactionPrice'] = '--';
			}

			if (!empty($transactionSummary->total_amount_settled)) {
				$data['totalAmountSettlement'] = $transactionSummary->total_number_settled > 0 ? $transactionSummary->total_number_settled : ' ';
				$data['totalAmountSettlementPrice'] = Helper::priceFormat($transactionSummary->total_amount_settled);
			}else {
				$data['totalAmountSettlement'] = $transactionSummary->total_number_settled > 0 ? $transactionSummary->total_number_settled : ' ';
				$data['totalAmountSettlementPrice'] = '--';
			}

			if (!empty($transactionSummary->total_amount_pending)) {
				$data['totalAmountNotSettlement'] = $transactionSummary->total_number_pending > 0 ? $transactionSummary->total_number_pending : ' ';
				$data['totalAmountNotSettlementPrice'] = Helper::priceFormat($transactionSummary->total_amount_pending);
			}else {
				$data['totalAmountNotSettlement'] = $transactionSummary->total_number_pending > 0 ? $transactionSummary->total_number_pending : ' ';
				$data['totalAmountNotSettlementPrice'] = '--';
			}
			
			$data['countTransaction'] = $transactionSummary->total_number;
			
			try {
				$lastUpdateUntil = Mpos360TransactionSummary::query()->where('merchant_id', $merchantId)
																											->max('update_until');

				if (!empty($lastUpdateUntil) && $lastUpdateUntil != '0000000000') {
					$time = Carbon::createFromTimestamp($lastUpdateUntil)->format('H:i');
					$data['syncTime'] = sprintf('Đồng bộ đến: %s', $time);
				}
			}catch(\Throwable $th) {
				$data['syncTime'] = '';
			}
			
		}

		$returnData = [
			'report_today' => [
				[
					'label' => vmsg('Mpos360TransactionHistoryAction_TongTienGiaoDich'),
					'value' => $data['totalAmountTransaction'],
					'extra_value' => $data['totalAmountTransactionPrice'] ?? '--'
				],

				[
					'label' => vmsg('Mpos360TransactionHistoryAction_ChuaKetToan'),
					'value' => $data['totalAmountNotSettlement'],
					'extra_value' => $data['totalAmountNotSettlementPrice'] ?? '--'
				],

				[
					'label' => vmsg('Mpos360TransactionHistoryAction_DaKetToan'),
					'value' => $data['totalAmountSettlement'],
					'extra_value' => $data['totalAmountSettlementPrice'] ?? '--'
				]
			], // end report today

			'transaction_categories' => [
				// group item  cho bên mobile dễ foreach
				[
					[
						'icon' => cumtomAsset('images/transaction/transaction-history/regular_payment.png'),
						'label' => vmsg('Mpos360TransactionHistoryAction_ThanhToanThuong'),
						'value' => '',
						'code' => 'REGULAR_PAYMENT'
					],

					[
						'icon' => cumtomAsset('images/transaction/transaction-history/qr_transaction.png'),
						'label' => vmsg('Mpos360TransactionHistoryAction_GiaoDichQr'),
						'value' => '',
						'code' => 'QR_TRANSACTION'
					],

					[
						'icon' => cumtomAsset('images/transaction/transaction-history/installment_payment.png'),
						'label' => vmsg('Mpos360TransactionHistoryAction_ThanhToanTraGop'),
						'value' => '',
						'code' => 'INSTALLMENT_PAYMENT'
					],
				],

				[
					[
						'icon' => "https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/test/67cebec34465eb435f2b8c11LichSuRutTien2.png",
						'label' => 'Lịch sử rút tiền',
						'value' => '',
						'code' => 'QUICK_WITHDRAWAL_REQUEST'
					],
					[
						'icon' => cumtomAsset('images/transaction/transaction-history/bank_account_funds_receipt.png'),
						'label' =>  vmsg('Mpos360TransactionHistoryAction_NhanTienVeTKNH'),
						'value' => '',
						'code' => 'BANK_ACCOUNT_FUNDS_RECEIPT'
					],
				],
			],

			'syncTime' => '',
		];


		// if (true) {
			$returnData['report_today'] = [
				[
					'label' => 'Tổng tiền giao dịch',
					'value' => ' ',
					'extra_value' => '0 VND'
				],
				[
					'label' => 'Chưa kết toán',
					'value' => ' ',
					'extra_value' => '0 VND'
				],
				[
					'label' => 'Đã kết toán',
					'value' => ' ',
					'extra_value' => '0 VND'
				],
			];

			$loaiGiaoDichGiuLai = collect($returnData['transaction_categories'][0])->filter(function ($item) {
				return $item['code'] == 'QR_TRANSACTION';
			})
			->values()
			->all();

			$returnData['transaction_categories'][0] = $loaiGiaoDichGiuLai;

			// Gọi vào api lấy ra thống kê Qr hôm nay
			$thongKeCountSumQrHomNay = $this->mposAws->countSumTransByDate([
				'serviceName' => 'DAILY_COUNT_SUM_TRANSACTION',
				'merchantFk' => $deviceSession->getMerchantId(),
				'tokenLogin' => $deviceSession->getMposToken(),
				'rangeTime' => now()->format('d-m-Y'),
				'typeTransaction' => 'STATISTIC_QR'
			]);

			if (!empty($thongKeCountSumQrHomNay['data']['data'])) {
				$returnData['report_today'] = [
					[
						'label' => 'Tổng tiền giao dịch',
						'value' => ' ',
						'extra_value' => Helper::priceFormat($thongKeCountSumQrHomNay['data']['data'][0]['totalAmount'])
					],

					[
						'label' => 'Chưa kết toán',
						'value' => ' ',
						'extra_value' => '0 VND'
					],

					[
						'label' => 'Đã kết toán',
						'value' => sprintf('(%s)', $thongKeCountSumQrHomNay['data']['data'][0]['transactionCount']),
						'extra_value' => Helper::priceFormat($thongKeCountSumQrHomNay['data']['data'][0]['totalAmount'])
					]
				];
			}
		// } // end if

		return $returnData;
	}
}
