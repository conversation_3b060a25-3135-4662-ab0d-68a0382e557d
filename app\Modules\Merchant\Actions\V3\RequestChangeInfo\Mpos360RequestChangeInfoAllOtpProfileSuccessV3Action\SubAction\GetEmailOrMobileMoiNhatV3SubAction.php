<?php 
namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessV3Action\SubAction;

use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction\MnpGetMerchantProfileSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\GetMNPAccessTokenSubAction;
use App\Modules\Merchant\Model\DeviceSession;

class GetEmailOrMobileMoiNhatV3SubAction {
	/**
	 * Các thông tin đã được xác thực bằng công nghệ
	 * 
	 * array:2 [
			0 => array:4 [
				"field" => "representMobile"
				"value" => "0987456123"
				"status_verify" => "1"
				"date_verify" => 1725938498
			]
			1 => array:4 [
				"field" => "representPassport"
				"value" => "8aa84acc-dd3e-4958-b4a3-07dc2e73b8ca"
				"status_verify" => "1"
				"date_verify" => 1725938498
			]
		]
	 */
	public function run(DeviceSession $deviceSession, $requestVefifyArray = []) {
		$hasMobile = collect($requestVefifyArray)->contains('field', 'representMobile');

		$hasEmail = collect($requestVefifyArray)->contains('field', 'representEmail');

		if ($hasEmail && $hasMobile) {
			return $requestVefifyArray;
		}

		$deviceSessionWithToken = app(GetMNPAccessTokenSubAction::class)->run($deviceSession);

		// Gọi detail để lấy thông tin hiện tại
		$mnpMerchantDetail = app(MnpGetMerchantProfileSubAction::class)->run(
			$deviceSessionWithToken->getMerchantId(), 
			$deviceSessionWithToken->mnp_token
		);
		
		
		if (!$hasEmail) {
			$requestVefifyArray[] = [
				'field' => 'representEmail',
				'value' => $mnpMerchantDetail['data']['authoriserEmail']['value'],
				'status_verify' => '1',
				'date_verify' => now()->timestamp
			];
		}

		if (!$hasMobile) {
			$requestVefifyArray[] = [
				'field' => 'representMobile',
				'value' => $mnpMerchantDetail['data']['authoriserContactNumber']['value'],
				'status_verify' => '1',
				'date_verify' => now()->timestamp
			];
		}

		return $requestVefifyArray;
	}
} // End class