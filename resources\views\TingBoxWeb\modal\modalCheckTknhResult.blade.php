<div class="modal fade action-sheet" id="modalStkResult" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle"
	aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Tài khoản NH nhận tiền </h5>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<div class="d-flex align-items-start mb-3">
					@if (!empty($checkTknhResult['status']) && $checkTknhResult['status'] == 'STK_HOP_LE')
					<img src="assets/img/small-succs.svg" class="me-2">
					<div class="cleaxfjx">
						<span class="d-block f16 BE-bold cl1DA"><PERSON>ố tài khoản hợp lệ</span>
						<span class="cl890"><PERSON><PERSON> tránh sai sót khi nhận tiền, Vui lòng kiểm tra lại thông tin trước
							khi tiếp
							tục!</span>
					</div>
					@else
					<img src="assets/img/small-waring.svg" class="me-2">
					<div class="cleaxfjx">
						<span class="clE99">Rất tiếc, chúng tôi không thể xác thực số tài khoản này. Xin vui lòng liên hệ ********** để được hỗ trợ ngay!</span>
					</div>
					@endif
				</div>
				<div class="box-edit">
					<div class="info">
						<span class="f20 BE-bold">{{ $params['bank_account'] }}</span>
						<span class="text-uppercase BE-bold mb-1">{{ $params['bank_account_holder'] }}</span>
						<span class="cl890">{{ $params['bank_name'] }}</span>
					</div>
					<a href="javascript:void(0)" class="f12" onclick="return onEditFormTknh()">Sửa</a>
				</div>

				@if (!empty($checkTknhResult['status']) && $checkTknhResult['status'] == 'STK_HOP_LE')
					<div class="d-flex mt-5">
						<button type="button" class="text-center btn btn-block btn-blue btn-success w-100 d-flex align-items-center justify-content-center" onclick="return onUpdateThongTinNganHang(this)">
							<div class="spinner-border d-none" role="status" id="spinnerUpdateTknh">
									<span class="visually-hidden">Loading...</span>
								</div>

								<span id="updateTknhActionName">Lưu thông tin</span>
						</button>
					</div>
				@endif
			</div>
		</div>
	</div>

<script>
	


	function onUpdateThongTinNganHang(element) {
		// $(element).attr('disabled', true);
		// $('#spinnerUpdateTknh').addClass('d-block');
		// $('#updateTknhActionName').hide();
		var paramUpdateNganHangJson = {
			bankId: "{{ $paramUpdateNganHang['bankId'] }}",
			mposMcId: "{{ $paramUpdateNganHang['mposMcId'] }}",
			accountNo: "{{ $paramUpdateNganHang['accountNo'] }}",
			holderName: "{{ $paramUpdateNganHang['holderName'] }}",
			bankVerify: "{{ $paramUpdateNganHang['bankVerify'] }}",
		};

		$('#loadingPage').addClass('se-pre-con');
		$.post('Mpos360iTngBoxOnUpdateTknh', paramUpdateNganHangJson).then(function (res) {
			$('#loadingPage').removeClass('se-pre-con');
			if(res.success){
				alert("Cập nhật tài khoản ngân hàng thành công");
				return location.reload();
			}else{
				alert(res.message);
			}
			
		}).always(function () {
			// $('#spinnerUpdateTknh').removeClass('d-block');
			// $('#updateTknhActionName').show();
			// $(element).removeAttr('disabled');
		});
	}

	function onEditFormTknh() {
		if ($('#modalStkResult').length) {
			$('#modalStkResult').modal('hide');
			$('#modalStkResult').remove();
			$('#modalStkResult').off();
		}
	
		$('#formTknh').modal('show');
	}
</script>
</div>

