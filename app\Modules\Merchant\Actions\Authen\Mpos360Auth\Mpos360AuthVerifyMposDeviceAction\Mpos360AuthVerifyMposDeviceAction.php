<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthVerifyMposDeviceAction;

use Exception;
use App\Lib\partner\MPOS;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360AuthVerifyMposDeviceRequest;

class Mpos360AuthVerifyMposDeviceAction
{
	const MAX_TIME_TRY = 2;

	public function run(Mpos360AuthVerifyMposDeviceRequest $request): array
	{
		$deviceSession = $request->getCurrentDeviceSession();

		$params = [
			'merchantFk' => $deviceSession->getMerchantId(),
			'readerSerial' => $request->json('data.mpos_device_code', ''),
			'tokenLogin' => $deviceSession->getMposToken(),
		];

		for ($i=0; $i<=self::MAX_TIME_TRY; $i++) {
			$data = (new MPOS())->CHECK_READER_DEVICE_OF_MERCHANT($params);

			if (isset($data['status_code_partner']) && $data['status_code_partner'] == 1000) {
				return [
					'mpos_device_code' => $request->json('data.mpos_device_code'),
					'verify_result' => '1',
					'msg' => vmsg('XacMinhThietBiXacThucThanhCong'),
					'can' => Mpos360Enum::CAN_GO_TO_HOME_SCREEN_AND_CHANGE_PASSWORD
				];
			}

			if ($i != self::MAX_TIME_TRY) {
				$params['readerSerial'] = substr($request->json('data.mpos_device_code'), -8);
			}
		}

		$message = !empty($data['message']) ? $data['message'] : vmsg('XacMinhThietBiThongTinKhongChinhXac');
		throw new BusinessException($message);
	}
} // End class
