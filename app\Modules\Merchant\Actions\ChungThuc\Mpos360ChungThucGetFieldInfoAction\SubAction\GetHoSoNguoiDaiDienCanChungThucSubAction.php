<?php

namespace App\Modules\Merchant\Actions\ChungThuc\Mpos360ChungThucGetFieldInfoAction\SubAction;

use App\Exceptions\BusinessException;
use App\Lib\partner\MNP;
use App\Modules\Merchant\Model\Setting;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\DeviceSession;
use App\Modules\Merchant\Model\Mpos360ChungThuc;
use App\Modules\Merchant\Actions\RequestChangeInfo\SubAction\GetMNPAccessTokenSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListProfileAction\SubAction\MnpGetMerchantProfileSubAction;

class GetHoSoNguoiDaiDienCanChungThucSubAction
{
	public MNP $mnp;


	public function __construct(MNP $mnp)
	{
		$this->mnp = $mnp;
	}

	public function run(DeviceSession $deviceSession)
	{
		$merchantId = $deviceSession->getMerchantId();
		$deviceSessionWithToken = app(GetMNPAccessTokenSubAction::class)->run($deviceSession);

		// Thiếu thông tin ng đại diện => gọi để lấy
		$mnpMerchantDetail = app(MnpGetMerchantProfileSubAction::class)->run(
			$merchantId,
			$deviceSessionWithToken->mnp_token
		);

		if (empty($mnpMerchantDetail['status'])) {
			$msg = sprintf('MNP Err: Không lấy được thông tin hồ sơ người đại diện (%s)', $mnpMerchantDetail['code'] ?? '00');
			throw new BusinessException($msg);
		}

		$setting = Setting::query()->firstWhere(['key' => 'CONFIG_KHONG_GHI_DE_CHUNG_THUC']);
		
		foreach ($mnpMerchantDetail['data'] as $profileKey => $item) {
			if ($item['group'] == 'CHANGE_REPRESENT_INFO') {
				$item['profileKey'] = $profileKey;

				if ($profileKey == 'authoriserEmail') {
					$rc = $this->updateOrCreateChungThuc($setting, $merchantId, 'EMAIL', $item);
				}

				if ($profileKey == 'authoriserContactNumber') {
					$rc = $this->updateOrCreateChungThuc($setting, $merchantId, 'MOBILE', $item);
				}

				if ($profileKey == 'passport') {
					$rc = $this->updateOrCreateChungThuc($setting, $merchantId, 'CCCD', $item);
				}
			}
		}

		return Mpos360ChungThuc::query()->where('merchant_id', $merchantId)->get();
	}

	public function getMethodCodeByProfileKey(string $profileKey)
	{
		if ($profileKey == 'authoriserEmail') {
			return 'EMAIL';
		}

		if ($profileKey == 'authoriserContactNumber') {
			return 'SMS';
		}

		if ($profileKey == 'passport') {
			return 'QTS';
		}

		return '';
	}

	public function getKeyCodeByProfileKey(string $profileKey)
	{
		if ($profileKey == 'authoriserEmail') {
			return 'EMAIL';
		}

		if ($profileKey == 'authoriserContactNumber') {
			return 'MOBILE';
		}

		if ($profileKey == 'passport') {
			return 'CCCD';
		}

		return '';
	}

	public function updateOrCreateChungThuc(
		Setting $setting,
		$merchantId, 
		$keyCode='CCCD', 
		$item=[]
	) {
		$mpos360ChungThuc = Mpos360ChungThuc::query()->where([
			'key_code' => $keyCode,
			'merchant_id' => $merchantId
		])->orWhere([
			'method_code' => $this->getMethodCodeByProfileKey($item['profileKey']),
			'merchant_id' => $merchantId
		])->first();

		// Chưa có chứng thực thì tạo mới
		if (!$mpos360ChungThuc) {
			$r = Mpos360ChungThuc::query()->forceCreate([
				'merchant_id' => $merchantId,
				'method_code' => $this->getMethodCodeByProfileKey($item['profileKey']),
				'data_request' => '{}',
				'data_response' => '{}',
				'qts_request_id' => '',
				'key_code' => $this->getKeyCodeByProfileKey($item['profileKey']),
				'value_confirmed' => $item['value'],
				'time_expired' => now()->addYears(10)->timestamp,
				'other_data' => '[]',
				'status' => Mpos360Enum::MPOS360_CHUNG_THUC_STT_CHUA_XAC_NHAN,
				'time_created' => now()->timestamp,
				'time_updated' => now()->timestamp,
			]);

			return $r;
		}

		/**
		 * Có chứng thực rồi, kiểm tra xem value có đang khác cái hiện tại hay không?
		 * 		Nếu không: Để yên không làm gì
		 * 		Nếu có: kiểm tra rồi ghi đè
		 */
		if ($mpos360ChungThuc->value_confirmed == $item['value']) {
			return $mpos360ChungThuc;
		}

		// Có thay đổi
		$listMcKhongGhiDeChungThuc = json_decode($setting->value, true);
		if (in_array($merchantId, $listMcKhongGhiDeChungThuc)) {
			return $mpos360ChungThuc;
		}

		if ($keyCode == 'CCCD') {
			if ($mpos360ChungThuc->status == Mpos360Enum::MPOS360_CHUNG_THUC_STT_DA_XAC_NHAN) {
				$otherData = json_decode($mpos360ChungThuc->other_data, true);
				$qtsData = json_decode($otherData['data'], true);
				
				// Matching thông tin CCCD (cũ, mới) thì không ghi đè
				if (
					$qtsData['cardDetailInfo']['citizenIdentify'] == $item['value'] 
					|| $qtsData['cardDetailInfo']['oldCitizenIdentify'] == $item['value']
				) {
					return $mpos360ChungThuc;
				}
			}

			// Đợi cho nó xác nhận xong rồi quay lại xử lý cũng k muộn
			if ($mpos360ChungThuc->status == Mpos360Enum::MPOS360_CHUNG_THUC_STT_DANG_XAC_NHAN) {
				return $mpos360ChungThuc;
			}
		}

		$mpos360ChungThuc->qts_request_id = '';
		$mpos360ChungThuc->value_confirmed = $item['value'];
		$mpos360ChungThuc->status = Mpos360Enum::MPOS360_CHUNG_THUC_STT_CHUA_XAC_NHAN;
		$mpos360ChungThuc->other_data = '[]';
		$r = $mpos360ChungThuc->save();

		return $mpos360ChungThuc;
	}
} // End class