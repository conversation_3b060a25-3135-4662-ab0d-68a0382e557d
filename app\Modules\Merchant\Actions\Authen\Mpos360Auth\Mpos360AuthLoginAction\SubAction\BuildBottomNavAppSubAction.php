<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction;

use App\Modules\Merchant\Model\Setting;
use Illuminate\Http\Request;

class BuildBottomNavAppSubAction
{
	public function run() 
	{
		$request = request();
		if (!empty($request->get('isFullMenu'))) {
			$settingBottomNavApp = Setting::query()->firstWhere(['key' => 'LIST_MENU_BOTTOM_NAV_FULL_TINGBOX']);
		}else {
			$settingBottomNavApp = Setting::query()->firstWhere(['key' => 'LIST_MENU_BOTTOM_NAV_APP_WITH_QR']); 
		}
		
		$bottomNavAppMenu = [];

		if ($settingBottomNavApp) {
			$settingValue = json_decode($settingBottomNavApp->value, true);
			$bottomNavAppMenu = collect($settingValue['nav'])
				->filter(function ($item) {
					return  !empty($item['display']);
				})
				->sortBy(function ($item) {
					return $item['sort'];
				})
				->map(function ($item) use ($request) {
					return [
						'title' => $item['title']['vi'],
						'titleEn' => $item['title']['en'],
						'icon' => !empty($request->get('isFullMenu')) ? $item['icon'] : cumtomAsset($item['icon']),
						'screen' => $item['screen'],
						'sort' => $item['sort'],
						'display' => $item['display'],
						'is_main' => $item['is_main'],
					];
				})
				->values()
				->all();
		}

		return $bottomNavAppMenu;
	}
}
