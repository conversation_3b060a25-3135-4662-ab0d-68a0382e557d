<?php

namespace App\Modules\Merchant\Controllers\Screen;

use App\Lib\Helper;
use App\Modules\Merchant\Controllers\Controller;
use App\Modules\Merchant\Actions\Screen\Mpos360ScreenWordingConfigAction;
use App\Modules\Merchant\Requests\Screen\Mpos360ScreenWordingConfigRequest;

class ScreenController extends Controller
{
	public function Mpos360ScreenWordingConfig(Mpos360ScreenWordingConfigRequest $request)
	{
		try {
			$result = app(Mpos360ScreenWordingConfigAction::class)->run();
			return $this->successResponse($result, $request);
		} catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
