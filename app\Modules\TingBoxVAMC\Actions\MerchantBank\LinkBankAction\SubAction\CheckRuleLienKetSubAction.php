<?php

namespace App\Modules\TingBoxVAMC\Actions\MerchantBank\LinkBankAction\SubAction;

use App\Lib\partner\SoundBox;
use App\Exceptions\BusinessException;

class CheckRuleLienKetSubAction
{
	public SoundBox $soundBox;

	public function __construct(SoundBox $soundBox)
	{
		$this->soundBox = $soundBox;
	}

	public function run($detailMc = [], $request)
	{
		$listSerial = $this->getSerialByMobileUser($detailMc, $request->json('data.mobileUserId'));
		if (empty($listSerial)) {
			throw new BusinessException('Bạn chưa thực hiện gắn loa, không thể thực hiện tạo liên kết');
		}

		// Get serial configuration from SoundBox
		$configSerial = $this->soundBox->getConfigBySerial($listSerial[0]);

		if (empty($configSerial['result']) || $configSerial['code'] != 1000) {
			$msg = $configSerial['message'] ?? '<PERSON>hông tìm thấy cấu hình thiết bị để thực hiện liên kết';
			throw new BusinessException($msg, $configSerial['code'] ?? -1);
		}

		// Validate bank configuration
		$bankCode = strtoupper($request->json('data.bankCode'));
		$validConfig = false;

		foreach ($configSerial['data']['partnerConfigRes'] as $config) {
			if (
				$config['bankCode'] === $bankCode &&
				($config['vaMode'] === 'VAMC' || $config['vaMode'] === 'ALL')
			) {
				$validConfig = true;
				break;
			}
		}

		if (!$validConfig) {
			$msg = sprintf('Hệ thống chưa hỗ trợ liên kết tài khoản ngân hàng %s cho thiết bị này. Vui lòng liên hệ CSKH để được hỗ trợ.', $bankCode);
			throw new BusinessException($msg);
		}

		return true;
	}

	public function getSerialByMobileUser($detailMc, $mobileUserId)
	{
		$listSerial = [];

		if (empty($detailMc['data']['locations'])) {
			return $listSerial;
		}

		foreach ($detailMc['data']['locations'] as $location) {
			if (empty($location['deviceDTOs'])) {
				continue;
			}

			foreach ($location['deviceDTOs'] as $device) {
				if ($device['mobileUserId'] === $mobileUserId && !empty($device['serialNumber'])) {
					$listSerial[] = $device['serialNumber'];
				}
			}
		}

		return array_unique($listSerial);
	}
} // End class