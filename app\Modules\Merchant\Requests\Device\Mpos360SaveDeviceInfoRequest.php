<?php

namespace App\Modules\Merchant\Requests\Device;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class Mpos360SaveDeviceInfoRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data.device' => ['required', 'array'],
      'data.device.os' => ['required', 'max:25', Rule::in(['IOS', 'ANDROID'])],
      'data.device.token' => ['required', 'string', 'max:255'],
      'data.device.other_key' => ['nullable']
    ];
  }
}
