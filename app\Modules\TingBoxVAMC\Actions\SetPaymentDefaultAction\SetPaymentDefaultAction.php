<?php

namespace App\Modules\TingBoxVAMC\Actions\SetPaymentDefaultAction;

use App\Lib\Logs;
use Illuminate\Http\Request;
use App\Lib\partner\SoundBox;
use PhpParser\Node\Expr\Throw_;
use App\Lib\TelegramAlertWarning;
use App\Exceptions\BusinessException;
use App\Lib\Helper;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;
use App\Modules\Merchant\Model\Mpos360Logs\LogRequest;
use App\Modules\TingBoxVAMC\Requests\MerchantBank\SetPaymentDefaultRequest;
use App\Modules\TingBoxVAMC\Actions\SetPaymentDefaultAction\SubAction\IsDaDongBoVA2SoundBoxSubAction;
use App\Modules\TingBoxVAMC\Actions\SetPaymentDefaultAction\SubAction\IsVAHienTaiDaLaActiveSubAction;
use App\Modules\TingBoxVAMC\Actions\SetPaymentDefaultAction\SubAction\IsVAMCHienTaiDaLaActiveSubAction;
use App\Modules\TingBoxVAMC\Actions\MerchantBank\ConfirmOtpBankAction\SubAction\SyncAssignTingBoxNowSubAction;

class SetPaymentDefaultAction
{
	protected SoundBox $soundBox;

	public bool $mustRecallDetail = false;

	public function __construct(SoundBox $soundBox)
	{
		$this->soundBox = $soundBox;
	}

	public function run(SetPaymentDefaultRequest $request)
	{
		Logs::writeInfo("Set payment default param", $request->json('data'));

		$p = [
			'mcId' => $request->json('data.merchantId'),
			'muName' => $request->json('data.mobileUserId'),
			'partnerCode' => Helper::getPartnerCode($request->json('data.merchantId')),
		];

		$mcTingBox = $this->soundBox->getMcTingBox($p);
		if (empty($mcTingBox['data'])) {
			throw new BusinessException('Không tìm thấy MC Tingbox');
		}


		// shop bank duoc chon lam mac dinh
		$merchantShopBank = MerchantShopBank::query()->where([
			'merchant_id' => $request->json('data.merchantId'),
			'shop_id' => $request->json('data.mobileUserId'),
			'id' => $request->json('data.merchantShopBankId'),
		])->first();

		if (!$merchantShopBank) {
			throw new BusinessException('Không tìm thấy thông tin ngân hàng.');
		}

		if (!$merchantShopBank->isDaLienKet()) {
			throw new BusinessException('Lỗi tài khoản ngân hàng của bạn chưa được liên kết.');
		}

		// Kiểm tra mcShopBank đc gán làm mặc định đã được đẩy sang TingBox hay chưa?
		$isDongBoVA = app(IsDaDongBoVA2SoundBoxSubAction::class)->run($merchantShopBank, $mcTingBox);
		
		if (!$isDongBoVA) {
			$this->mustRecallDetail = true;
			app(SyncAssignTingBoxNowSubAction::class)->run($merchantShopBank);
			$merchantShopBank->refresh();
		}


		if ($this->mustRecallDetail) {
			$mcTingBox = $this->soundBox->getMcTingBox($p);
			if (empty($mcTingBox['data']['mcQr'])) {
				throw new BusinessException('Không tìm thấy thông tin VA của merchant');
			}
		}
	
		// Nếu VA hiện tại đã là mặc định rồi -> lại còn sét chính nó là mặc định nữa thì là không cần thiết
		$isVaHienTaiLaDefault = app(IsVAMCHienTaiDaLaActiveSubAction::class)->run(
			$merchantShopBank,
			$mcTingBox
		);

		if (!empty($isVaHienTaiLaDefault)) {
			return [
				'msg' => 'Cài đặt tài khoản mặc định thành công'
			];
		}

		$params = [
			"mcId" => $merchantShopBank->merchant_id,
			"mobileUserName" => $merchantShopBank->shop_id,
			"vaNextPayNumber" => $merchantShopBank->partner_request_id, // Tài khoản VA Nextpay number đang thao tác
			"integratedMethod" => $merchantShopBank->account_type == 1 ? 'VAMC' : 'VANP', // VAMC, VANP
			"partnerCode" => Helper::getPartnerCode($merchantShopBank->merchant_id),
		];

		$logRequestTingBox = LogRequest::query()->forceCreate([
			'merchant_id' => $merchantShopBank->merchant_id,
			'partner' => 'tingbox',
			'request' => json_encode($params, JSON_UNESCAPED_UNICODE),
			'created_at' => now()->format('Y-m-d H:i:s'),
			'updated_at' => now()->format('Y-m-d H:i:s'),
		]);


		$sendSoundBox = $this->soundBox->setDefaultVamc($params);

		$logRequestTingBox->update(['response' => json_encode($sendSoundBox, JSON_UNESCAPED_UNICODE), 'updated_at' => now()->format('Y-m-d H:i:s')]);
	
		if (empty($sendSoundBox['result'])) {
			$msg = sprintf('Lỗi set mặc định VA %s: %s', $merchantShopBank->partner_request_id, $sendSoundBox['message']);
			TelegramAlertWarning::sendMessage($msg);
			throw new BusinessException($msg);
		};

		return [
			'msg' => 'Cài đặt tài khoản mặc định thành công'
		];
	}
} // End class
