<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthRemoveAccountAction;

use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\DTOs\RequestChangeInfo\CreateMerchantRequestDto;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360AuthRemoveAccountRequest;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction\LoginMerchantViaMposSubAction;

class Mpos360AuthRemoveAccountAction
{
	public function run(Mpos360AuthRemoveAccountRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();

		$createMerchantRequestDto = new CreateMerchantRequestDto(
			$deviceSession->getMerchantId(),
			$deviceSession->getMerchantUserName(),
			'{}',
			'{}',
			''
		);

		// Không bị lỗi, tức là đăng nhập thành công
		$loginMpos = app(LoginMerchantViaMposSubAction::class)->run(
			$deviceSession->getMerchantUserName(),
			$request->json('data.password')
		);

		if (!$loginMpos) {
			throw new BusinessException('Mpos360AuthRemoveAccountAction_LoiMatKhauKhongChinhXac');
		}

		$params = $createMerchantRequestDto->toArray(Mpos360Enum::MPOS360_MC_REQUEST_YC_XOA_TAI_KHOAN);
		$mpos360MerchantRequest = Mpos360MerchantRequest::query()->forceCreate($params);

		if (!$mpos360MerchantRequest) {
			throw new BusinessException(vmsg('Mpos360AuthRemoveAccountAction_LoiKhongTaoDuocYeuCauXoaTaiKhoan'));
		}

		// Đoạn này xóa thành công thì đánh dấu time_expired
		$deviceSession->time_expired = now()->timestamp;
		

		$timeline = now()->addDays(15)->format('H:i d/m/Y');
		$msg = vmsg('Mpos360AuthRemoveAccountAction_XoaTaiKhoanThanhCong', ['timeline' => $timeline]);

		$returnData = [
			'is_removed_account' => 1,
			'msg' => $msg,
			'can' => Mpos360Enum::CAN_GO_TO_LOGIN_SCREEN,
			'other_data' => [
				'email' => [
					'text' => $deviceSession->getMerchantUserName(),
					'text_color' => '#73ae4a',
					'bg_color' => '#ffffff'
				]
			]
		];

		return $returnData;
	}
} // End class
