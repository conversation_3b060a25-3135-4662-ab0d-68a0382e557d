<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestCronPushSignAction;

use App\Lib\Helper;
use App\Lib\partner\MNP;
use App\Lib\TelegramAlert;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestCronPushRecordAction\SubAction\GetMnpTokenByMerchantIdSubAction;

class Mpos360MerchantRequestCronPushSignAction
{
	public MNP $mnp;

	public $signPushCountMax = 3; // So lan day ky toi da

	private array $__exceptIds = [];

	public function __construct(MNP $mnp)
	{
		$this->mnp = $mnp;
	}

	public function run(Request $request)
	{
		$returnData = [];

		for ($i = 1; $i < 10; $i++) {
			try {
				$result = $this->handle();

				if ($result == 'EMPTY') {
					$returnData[] = 'EMPTY';
					break;
				}

				if (optional($result)->id) {
					$returnData[] = $result->id;
				}
			} catch (\Throwable $th) {
				mylog(['Loi xu ly ban ghi' => Helper::traceError($th)]);
				TelegramAlert::sendMessage(Helper::traceError($th));
				throw $th;
			}
		}

		return $returnData;
	}

	public function handle()
	{
		$mpos360McRequest = Mpos360MerchantRequest::query()
			->where('status', Mpos360Enum::MPOS360_MC_REQUEST_STT_DA_GUI_SANG_MNP)
			->where('status_sign', Mpos360Enum::MPOS360_MC_SIGN_STT_CHUA_KY)
			->where('sign_push_count', '<=', $this->signPushCountMax)
			->whereRaw("LENGTH(mynextpay_id) > 0");

		if (!empty($this->__exceptIds)) {
			$mpos360McRequest = $mpos360McRequest->whereNotIn('id', $this->__exceptIds);
		}

		$mpos360McRequest = $mpos360McRequest->first();

		if (!$mpos360McRequest) {
			mylog(['EMPTY' => 'khong co thong tin']);
			return 'EMPTY';
		}

		$this->__exceptIds[] = $mpos360McRequest->id;

		mylog(['YeuCauCanKy' => $mpos360McRequest->only(['id', 'mynextpay_id', 'order_code', 'status', 'status_signed'])]);

		// Update lên thành đang ký
		$wasUpdatedDangKy = Mpos360MerchantRequest::query()
			->where('id', $mpos360McRequest->id)
			->where('status', Mpos360Enum::MPOS360_MC_REQUEST_STT_DA_GUI_SANG_MNP)
			->where('status_sign', Mpos360Enum::MPOS360_MC_SIGN_STT_CHUA_KY)
			->update(['status_sign' => Mpos360Enum::MPOS360_MC_SIGN_STT_DANG_KY]);

		if (!$wasUpdatedDangKy) {
			mylog(['Loi' => 'Loi update len dang ky']);
			throw new BusinessException('Lỗi: không thể update lên thành ĐANG KÝ');
		}

		$mpos360McRequest = Mpos360MerchantRequest::query()->find($mpos360McRequest->id);

		if ($mpos360McRequest->status_sign != Mpos360Enum::MPOS360_MC_SIGN_STT_DANG_KY) {
			throw new BusinessException('Lỗi update yêu cầu lên thành ĐANG KÝ');
		}

		DB::beginTransaction();
		$statusSignErr = $mpos360McRequest->sign_push_count < $this->signPushCountMax ? Mpos360Enum::MPOS360_MC_SIGN_STT_CHUA_KY 
																																									: Mpos360Enum::MPOS360_MC_SIGN_STT_KY_LOI;

		try {
			$mnpToken = app(GetMnpTokenByMerchantIdSubAction::class)->run($mpos360McRequest->merchant_id);

			if (empty($mnpToken)) {
				throw new BusinessException('Lỗi không có thông tin mnp token');
			}

			$param = $this->buildParam($mpos360McRequest);

			$pushSignResult = $this->mnp->sign($param, $mnpToken);

			// Thành công update về cuối
			if (!empty($pushSignResult['status'])) {
				$paramUpdate = [
					'status_sign' => Mpos360Enum::MPOS360_MC_SIGN_STT_DA_KY,
					'time_signed' => now()->timestamp,
					'sign_push_count' => DB::raw('sign_push_count + 1'),
					'comment' => '{}'
				];

				$r = $this->danhDauYeuCau($mpos360McRequest, $paramUpdate);

				if (!$r) {
					$message = 'Lỗi cập nhật yêu cầu: ' . $mpos360McRequest->mynextpay_id;
					mylog(['KetQuaCapNhat' => $message]);
					throw new BusinessException($message);
				}
			}

			// Lỗi thì đánh dấu thất bại
			if (empty($pushSignResult['status'])) {
				$message = 'Lỗi đẩy ký: ' . $pushSignResult['message'];

				$comment = json_decode($mpos360McRequest->comment, true);
				$comment['sign_error'] = $message;

				
				$paramUpdate = [
					'status_sign' => $statusSignErr,
					'comment' => json_encode($comment),
					'time_updated' => now()->timestamp,
					'sign_push_count' => DB::raw('sign_push_count + 1')
				];

				$r = $this->danhDauYeuCau($mpos360McRequest, $paramUpdate);

				if (!$r) {
					$message = 'Lỗi cập nhật yêu cầu: ' . $mpos360McRequest->mynextpay_id;
					mylog(['KetQuaCapNhat' => $message]);
					throw new BusinessException($message);
				}
			}

			DB::commit();
			return $mpos360McRequest;
		} catch (\Throwable $th) {
			mylog(['Error' => Helper::traceError($th)]);
			DB::rollBack();

			$this->danhDauYeuCau($mpos360McRequest, [
				'status_sign' => $statusSignErr,
				'time_signed' => now()->timestamp,
				'sign_push_count' => DB::raw('sign_push_count + 1')
			]);

			throw $th;
		}
	} // End method

	public function buildParam(Mpos360MerchantRequest $mpos360McRequest): array
	{
		$dataRequest = json_decode($mpos360McRequest->data_request, true);

		$param = [
			'reqId' => $mpos360McRequest->mynextpay_id,
			'fileUrl' => '', // File ký giất
			'signProcess' => $dataRequest[0]['signProcess']['code'],
			'signature' => '',
		];

		if ($mpos360McRequest->isKyGiay()) {
			$param['fileUrl'] = $dataRequest[0]['signProcess']['signature_url'];
		}

		if ($mpos360McRequest->isKyVeTay()) {
			$param['signature'] = $dataRequest[0]['signProcess']['signature_url'];
		}

		return $param;
	}

	/**
	 * [
			'status_sign' => Mpos360Enum::MPOS360_MC_SIGN_STT_DA_KY,
			'time_signed' => now()->timestamp
		]
	 */
	public function danhDauYeuCau(Mpos360MerchantRequest $mpos360McRequest, array $param = [])
	{
		$wasUpdated = $mpos360McRequest->where([
			'id' => $mpos360McRequest->id,
			'status_sign' => Mpos360Enum::MPOS360_MC_SIGN_STT_DANG_KY
		])
			->update($param);

		if (!$wasUpdated) {
			throw new BusinessException('Lỗi không cập nhật được thành đã ký');
		}

		return $wasUpdated;
	}
} // End class
