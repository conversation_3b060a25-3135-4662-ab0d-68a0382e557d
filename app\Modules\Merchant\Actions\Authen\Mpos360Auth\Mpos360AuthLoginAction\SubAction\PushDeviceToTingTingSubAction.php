<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction;

use App\Lib\Logs;
use App\Lib\partner\SoundBox;
use App\Lib\SettingHelper;
use App\Modules\Merchant\Model\Device;
use App\Modules\Merchant\Model\Setting;

class PushDeviceToTingTingSubAction
{
	public SoundBox $soundBox;

	public function __construct(SoundBox $soundBox)
	{
		$this->soundBox = $soundBox;
	}


	public function run(Device $device, $merchantBasicInfo)
	{
		if (!empty($device->status_sync_tingting)) {
			return $device;
		}

		if (empty($device->status_sync_tingting)) {
			$mangDongBo[] = [
				'mcId' => (string) $merchantBasicInfo['merchantId'],
				'mcEmail' => $merchantBasicInfo['merchantEmail'],
				'serial' => $device->token,
				'cateCode' => 'APP360',
				'status' => 'ACTIVE'
			];

			Logs::writeInfo("create-device-4-app", $mangDongBo);
			$r = $this->soundBox->sendRequestAsJson('/create-device-4-app', $mangDongBo);
			Logs::writeInfo("create-device-4-app-response", $r);
			
			if (!empty($r['result']) && !empty($r['success'])) {
				$device->status_sync_tingting = 1;
				$device->save();
			}
		}

		return $device;
	}
} // End class
