<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoAllOtpProfileSuccessAction\SubAction;

use Exception;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Model\Mpos360ChungThuc;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360MerchantRequestCronPushRecordAction\SubAction\GetCccdInfoByQtsRequestIdSubAction;

class XuLyCaseThayDoiCccdSubAction
{
	public $action;

	public function __construct(GetCccdInfoByQtsRequestIdSubAction $action)
	{
		$this->action = $action;
	}

	public function run(Mpos360MerchantRequest $mpos360McRequest, array $verifyResult)
	{
		$mpos360ChungThuc = Mpos360ChungThuc::getChungThucCCCD($mpos360McRequest->merchant_id);

		if (!$mpos360ChungThuc) {
			throw new BusinessException('Lỗi: không tìm được cccd chứng thực');
		}

		$passportItem = collect($verifyResult)->first(function ($item) {
			return !empty($item['value']) && $item['profileKey'] == 'passport';
		});

		if (empty($passportItem)) {
			throw new BusinessException('Lỗi: bạn phải truyền thông tin qts id lên');
		}

		// đoạn này kiểm tra bên ekyc
		$detailQtsResultDto = $this->action->run($passportItem['value']);

		$isTrungCccdChungThuc = $detailQtsResultDto->isMatchingChungThuc($mpos360ChungThuc);
		mylog(['isTrungCccdChungThuc' => $isTrungCccdChungThuc]);

		$profileLayTuCccd = $detailQtsResultDto->mapQtsDataAsNguoiDaiDienProfile(
			$mpos360McRequest->isYeuCauDoiNguoiDaiDienMoi()
		);

		return [
			'isTrungCccdChungThuc' => $isTrungCccdChungThuc,
			'profileLayTuCccd' => $profileLayTuCccd
		];
	}
} // End class
