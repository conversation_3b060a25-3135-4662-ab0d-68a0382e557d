<?php

namespace App\Modules\Merchant\Requests\Authen\Mpos360;

use Illuminate\Foundation\Http\FormRequest;
use App\Modules\Merchant\Requests\MerchantRequest;
class Mpos360AuthVerifyMposDeviceRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.mpos_device_code' => ['required', 'string', 'max:255', 'min:10'],
		];
	}

	public function messages() {
		return [
			'data.mpos_device_code.required' => vmsg('XacMinhThietBiMaThietBiLaBatBuoc'),
			'data.mpos_device_code.string' => vmsg('XacMinhThietBiMaThietBiPhaiLaKieuChuoi'),
			'data.mpos_device_code.max' => vmsg('XacMinhThietBiMaThietBiCoDoDaiToiDaLa255KyTu'),
			'data.mpos_device_code.min' => vmsg('XacMinhThietBiMaThietBiCoDoDaiToiDaLa10KyTu'),
		];
	}
} // End class
