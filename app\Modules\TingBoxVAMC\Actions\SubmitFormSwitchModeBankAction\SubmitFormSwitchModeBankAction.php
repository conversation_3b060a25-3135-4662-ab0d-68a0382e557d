<?php

namespace App\Modules\TingBoxVAMC\Actions\SubmitFormSwitchModeBankAction;

use Illuminate\Http\Request;
use App\Modules\Merchant\Enums\TingTingEnum;


class SubmitFormSwitchModeBankAction
{
    public function run(Request $request)
    {
       

        $returnData = [
            'status' => 'SUCCESS',
            'msg' => '',
            'username' => $request->json('data.username'),
            'channels' => ['ZALO', 'SMS'],
            'can' => TingTingEnum::CAN_DO_VERIFY_OTP
        ];

   

        return $returnData;
    }
}
