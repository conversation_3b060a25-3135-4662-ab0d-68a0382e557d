<?php
namespace App\Modules\Merchant\Actions\Authen\Mpos360Register\Mpos360GetFormAuthenAccountAction;

use Exception;

class Mpos360GetFormAuthenAccountAction
{
    public function __construct()
	{
	}

     	/**
	 * Submit form thì chỉ cần gọi otp có bản ghi
	 *
	 * @param Mpos360GetFormAuthenAccountRequest $request
	 * @return void
	 */
    public function run(): array 
    {
        $dataResponse = [
            'form' => [
				['field' => 'fullname', 'label' => 'Họ và tên'],
				['field' => 'cccd', 'label' => 'Số căn cước'],
				['field' => 'gender', 'label' => 'giới tính'],
				['field' => 'address', 'label' => 'Địa chỉ thường trú'],
				['field' => 'cccd_issue_date', 'label' => '<PERSON><PERSON>y cấp'],
				['field' => 'cccd_image_before', 'label' => 'Ảnh mặt trước'],
				['field' => 'cccd_image_after', 'label' => 'Ảnh mặt sau'],
			],
			'form_data' => [
                'gender' => [
                    'male' => 'Nam',
                    'female' => 'Nữ',
                ]
            ],
        ];

        return $dataResponse;
    }
}