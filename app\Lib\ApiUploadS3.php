<?php

namespace App\Lib;

use Illuminate\Http\UploadedFile;

class ApiUploadS3
{
  public $_urlApi = 'https://dev-data.nextlend.vn/profile/upload';
  public $_basicAuth = 'bmV4dGxlbmQ6Z0toVnU4dms=';

  public function __construct()
  {
    if (Helper::isProduction()) {
      $this->_urlApi = env('NEXTLEND_S3_API_URL');
      $this->_basicAuth = env('NEXTLEND_S3_BASIC_AUTH');
    }
  }

    
  /**
   * Hàm upload file
   *
   * @param $params $params [explicite description]
   * @param array $files<UploadedFile> [Danh sách file]
   *
   * @return void
   */
  public function uploadFiles($params, array $files=[])
  {
    $dataResponse = [];

    foreach ($files as $file) {

      $cFile = curl_file_create($file->getPathName(), $file->extension(), $file->getClientOriginalName());
      $dataUpload = array(
        'token' => $params['token'],
        'path' => $params['path'] . '/',
        'bucket_name' => 'nextlendpost',
        'file' => $cFile,
      );

      $urlFile = $this->call($dataUpload);

      if (!empty($urlFile)) {
        $dataResponse[] = $urlFile;
      }
    }
    
    return $dataResponse;
  }

  public function call($dataUpload)
  {
    $md5DataUpload = md5(json_encode($dataUpload));
    $key = 'S3_UPLOAD-' . $md5DataUpload;

    $headerArray = array(
      "Content-Type: multipart/form-data",
      'Content-Type: application/x-www-form-urlencoded',
      'Authorization: Basic ' . $this->_basicAuth,
    );

    
    
    $log = [$key => ['data_upload' => $dataUpload, 'header_upload' => $headerArray]];
    $log[$key]['url'] = $this->_urlApi;
    
    $ch = curl_init();

    curl_setopt($ch, CURLOPT_URL, $this->_urlApi);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, strtoupper("POST"));
    curl_setopt($ch, CURLOPT_POSTFIELDS, $dataUpload);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headerArray);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 60);
    curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);

    $result = curl_exec($ch);
    $resultCode = curl_getinfo($ch);

    $log[$key]['result'] = json_encode($result);
    app('mylog')->cc($log);


    if ($resultCode['http_code'] == 200) {
      $upload = json_decode($result);
      $url = str_replace('"', ' ', $upload->content[0]);
      return $url;
    }

    return false;
  }
} // End class
