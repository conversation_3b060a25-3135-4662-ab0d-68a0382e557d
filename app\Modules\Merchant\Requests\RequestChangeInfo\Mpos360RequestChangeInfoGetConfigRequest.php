<?php

namespace App\Modules\Merchant\Requests\RequestChangeInfo;

use Illuminate\Foundation\Http\FormRequest;

class Mpos360RequestChangeInfoGetConfigRequest extends FormRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.email' => ['required', 'string', 'email'],
		];
	}
}
