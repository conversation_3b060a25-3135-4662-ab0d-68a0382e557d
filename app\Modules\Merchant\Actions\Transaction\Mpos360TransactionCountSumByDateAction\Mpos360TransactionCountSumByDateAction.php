<?php

namespace App\Modules\Merchant\Actions\Transaction\Mpos360TransactionCountSumByDateAction;

use App\Lib\Helper;
use App\Lib\partner\MposAWS;
use App\Modules\Merchant\Requests\Transaction\Mpos360TransactionCountSumByDateRequest;

class Mpos360TransactionCountSumByDateAction
{
	public MposAWS $mposAws;

	public function __construct(MposAWS $mposAws)
	{
		$this->mposAws = $mposAws;
	}

	public function returnEmpty($request) {
		$returnData = [];
		$listDate = explode(',', $request->json('data.rangeTime'));
		
		foreach ($listDate as $d) {
			$returnData[] = [
				'transactionCount' => '',
				'formatDate' => $d,
				'totalAmount' => '',
				'wording' => '--',
			];
		}
		

		return ['statistic' => $returnData];
	}

	public function run(Mpos360TransactionCountSumByDateRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();
		$merchantEmail = $deviceSession->getMerchantEmail();
		$merchantId = $deviceSession->getMerchantId();



		mylog([
			'merchantEmail' => $merchantEmail,
			'merchantId' => $merchantId
		]);

		if ($merchantEmail && $merchantEmail == '<EMAIL>' && $request->isCountGiaoDichQr()) {
			return $this->getThongKeGiaoDichTheoNgay($deviceSession, $request);
		}else {
			return $this->returnEmpty($request);
		}
	}

	public function getThongKeGiaoDichTheoNgay($deviceSession, $request) {
		$params = [
			'serviceName' => 'DAILY_COUNT_SUM_TRANSACTION',
			'merchantFk' => $deviceSession->getMerchantId(),
			'tokenLogin' => $deviceSession->getMposToken(),
		];
		$params = array_merge($params, $request->json('data'));

		$result = $this->mposAws->countSumTransByDate($params);
		
		$countSum = [];

		$listDate = explode(',', $request->json('data.rangeTime'));
		foreach ($listDate as $d) {
			$wording = sprintf('%s GD   %s: %s', 0, __('trans.Tổng tiền'), Helper::priceFormat(0));

			if (!$request->isCountGiaoDichQr()) {
				$wording = 'O GD';
			}

			$countSum[$d] = [
				'transactionCount' => 0,
				'formatDate' => $d,
				'totalAmount' => 0,
				'wording' => $wording
			];
		}


		if (!empty($result['data']['data'])) {
			foreach ($result['data']['data'] as $item) {
				$countSum[$item['formatDate']]['transactionCount'] = $item['transactionCount'];
				
				if (now()->format('d-m-Y') == $item['formatDate']) {
					if ($request->json('lang') == 'vi') {
						$countSum[$item['formatDate']]['formatDate'] = 'Hôm nay';
					}

					if ($request->json('lang') == 'en') {
						$countSum[$item['formatDate']]['formatDate'] = 'Today';
					}
				}else {
					$countSum[$item['formatDate']]['formatDate'] = $item['formatDate'];
				}
				
				$countSum[$item['formatDate']]['totalAmount'] = $item['totalAmount'];
				
				$wording = sprintf('%s GD   %s: %s', $item['transactionCount'], __('trans.Tổng tiền'), Helper::priceFormat($item['totalAmount']));

				if (!$request->isCountGiaoDichQr()) {
					$wording = $item['transactionCount'] . ' GD';
				}

				$countSum[$item['formatDate']]['wording'] = $wording;
			}
		}

		$returnData['statistic'] = array_values($countSum);
		return $returnData;
	}
} // End class
