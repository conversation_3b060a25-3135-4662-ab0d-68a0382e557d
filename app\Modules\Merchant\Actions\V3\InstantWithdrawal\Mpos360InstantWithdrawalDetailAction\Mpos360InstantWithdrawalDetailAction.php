<?php

namespace App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalDetailAction;

use Exception;
use Carbon\Carbon;
use App\Lib\Helper;
use App\Lib\partner\MPOS;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Requests\InstantWithdrawal\V3\Mpos360InstantWithdrawalDetailRequest;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalListAction\SubAction\MapTrangThaiYcRtnSubAction;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalDetailAction\SubAction\GetThuThemTraLaiSubAction;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalListAction\SubAction\MapTrangThaiYcRtnDetailSubAction;
use App\Modules\Merchant\Actions\V3\InstantWithdrawal\Mpos360InstantWithdrawalDetailAction\SubAction\GetTrangThaiDetailNTNDungLuonCuaMposSA;

class Mpos360InstantWithdrawalDetailAction
{	
	public MPOS $mpos;

	public function __construct(MPOS $mpos)
	{
		$this->mpos = $mpos;
	}

	public function run(Mpos360InstantWithdrawalDetailRequest $request)
	{
		$deviceSession = $request->getCurrentDeviceSession();

		$params = [
			'serviceName' => 'PAYMENT_NOW_RQ_GET_DETAIL',
			'requestId' => $request->json('data.order_code'),
			'tokenLogin' => $deviceSession->getMposToken(),
			'merchantFk' => $deviceSession->getMerchantId()
		];

		$detail = $this->mpos->paymentNowGetDetail($params);
		
		if (empty($detail['data']['data'])) {
			throw new BusinessException('Record not found');
		}
		
		$rc = $detail['data']['data'];

		$mapTrangThaiDetail = app(GetTrangThaiDetailNTNDungLuonCuaMposSA::class)->run($rc);

		$thuThemTraLai = app(GetThuThemTraLaiSubAction::class)->run($rc);

		$returnData = [
			'warning' => [],
			'data' => [
				[
					'key' => 'ThongTinChung',
					'name' => __('rtn.Thông tin chung'),
					'list' => [
						[
							'key' => 'MaYc',
							'label' => __('rtn.Mã yêu cầu'),
							'value' => $rc['id'],
							'other_data' => (object) []
						],
						[
							'key' => 'MaPhieuChi',
							'label' => __('rtn.Mã phiếu chi'),
							'value' => !empty($rc['withdrawNumber']) ? $rc['withdrawNumber'] : __('rtn.Chưa có thông tin'),
							'other_data' => (object) []
						],
						[
							'key' => 'LoaiGiaoDich',
							'label' => __('rtn.Loại giao dịch'),
							'value' => $rc['requestType'],
							'other_data' => (object) []
						],
						[
							'key' => 'ThoiGianYeuCau',
							'label' => __('rtn.TG yêu cầu'),
							'value' => Carbon::parse($rc['createdDate'])->format('H:i, d/m/Y'),
							'other_data' => (object) []
						],

						[
							'key' => 'ThoiGianDuyet',
							'label' => __('rtn.TG duyệt'),
							'value' => empty($rc['approvedDate']) ? 'Đang cập nhật' : Carbon::parse($rc['approvedDate'])->format('H:i, d/m/Y'),
							'other_data' => (object) []
						],

						[
							'key' => 'ThoiGianThanhToan',
							'label' => __('rtn.TG thanh toán'),
							'value' => empty($rc['paymentDate']) ? 'Đang cập nhật' : Carbon::parse($rc['paymentDate'])->format('H:i, d/m/Y'),
							'other_data' => (object) []
						],
						[
							'key' => 'TrangThaiYc',
							'label' => __('rtn.Trạng thái yêu cầu'),
							'value' => $mapTrangThaiDetail['text'],
							'other_data' => (object) [
								'text' => $mapTrangThaiDetail['text'],
								'text_color' => $mapTrangThaiDetail['text_color'],
								'bg_color' => $mapTrangThaiDetail['bg_color'],
								'display_type' => 'pills',
							]
						],
					]
				],
			]
		];

		// Xử lý phần lý do chi tiền lỗi
		if (!empty($rc['payoutErrorDescription'])) {
			$returnData['data'][0]['list'][] = [
				'key' => 'LyDoLoiChiTien',
				'label' => 'Lý do:',
				'value' => $rc['payoutErrorDescription'],
				'other_data' => (object) [
					'text' => $rc['payoutErrorDescription'],
					'text_color' => '#da2128',
					'font_style' => 'italic'
				]
			];
		}
		
		$soGiaoDichDuocDuyet = [];
		
		if ($rc['status'] == 'APPROVED' || $rc['withdrawStatus'] == 'HAS_BALANCE') {
			$soGiaoDichDuocDuyet = [
				'key' => 'MaYc',
				'label' => __('rtn.Số GD được duyệt'),
				'value' => count($rc['transactionQRList']),
				'other_data' => (object) [
					'text_color' => '#018bf4',
					'linking' => [
						'app_screen' => 'LIST_VIETQR_TRANSACTION_EXCLUDING_DETAIL',
						'title' => __('rtn.Yêu cầu NTN được duyệt'),
						'params' => [
							'order_code' => $rc['id'],
							'type' => Mpos360Enum::MPOS360_RTN_TYPE_GD_VIETQR_DUOC_DUYET
						]
					]
				]
			];
		}

		$chiTietYc = [
			'key' => 'ChiTietYeuCau',
			'name' => __('rtn.Chi tiết yêu cầu'),
			'list' => collect([
				[
					'key' => 'SoGDVietQRYeuCau',
					'label' => __('rtn.Số GD VietQR yêu cầu'),
					'value' => count($rc['transactionQRList']),
					'other_data' => (object) [
						'text_color' => '#018bf4',
						'linking' => [
							'app_screen' => 'LIST_VIETQR_TRANSACTION_EXCLUDING_DETAIL',
							'title' => __('rtn.Yêu cầu NTN VietQR'),
							'params' => [
								'order_code' => $rc['id'],
								'type' => Mpos360Enum::MPOS360_RTN_TYPE_GD_VIETQR_TAO_YC
							]
						]
					]
				],

				$soGiaoDichDuocDuyet,

				[
					'key' => 'MaYc',
					'label' => __('rtn.Tổng tiền GD'),
					'value' => Helper::priceFormat($rc['totalAmount']),
					'other_data' => (object) []
				],
				[
					'key' => 'MaYc',
					'label' => __('rtn.Phí dịch vụ NTN'),
					'value' => Helper::priceFormat($rc['quickFeeAmount']),
					'other_data' => (object) []
				],

				[
					'key' => 'MaYc',
					'label' => __('rtn.Phí chuyển tiền'),
					'value' => Helper::priceFormat($rc['feeTransfer']),
					'other_data' => (object) []
				],


				
				$thuThemTraLai['thuThem'],

				$thuThemTraLai['traLai'],

				[
					'key' => 'ThucNhan',
					'label' => __('rtn.Thực nhận'),
					'value' =>  Helper::priceFormat($rc['recieveAmount']),
					'other_data' => (object) [
						'font_weight' => 'bold',
						'text_color' => '#73ae4a',
					]
				]
			])->filter()->values()->all()
		];

		// Số tiền thực nhận bằng chữ
		if (!empty($rc['recieveAmountString'])) {
			$chiTietYc['list'][] = [
				'key' => 'ThucNhanBangChu',
				'label' => '',
				'value' => trim($rc['recieveAmountString']),
				'other_data' => (object) [
					'display_type' => 'block',
					'font_style' => 'italic',
				]
			];
		}
		
				
		$returnData['data'][] = $chiTietYc;

		// Có thông tin tknh thì mới hiển thị group này
		if (!empty($rc['bankAccountNo'])) {
			$returnData['data'][] = [
				'key' => 'TaiKhoanNhanTien',
				'name' => __('rtn.Tài khoản nhận tiền'),
				'list' => [
					[
						'key' => 'Stk',
						'label' => $rc['bankAccountNo'],
						'value' => '',
						'other_data' => (object) [
							'label_text_color' => '#404041',
							'label_font_weight' => 'bold'
						]
					 ],
	
					[
						'key' => 'NguoiThuHuong',
						'label' => $rc['holderName'],
						'value' => '',
						'other_data' => (object) [
							'label_text_color' => '#404041',
						]
					],
	
					[
						'key' => 'NganHang',
						'label' => sprintf('%s - %s', $rc['bankName'], $rc['bankBranch']),
						'value' => '',
						'other_data' => (object) [
							'label_text_color' => '#404041',
						]
					],
				]
			];
		}
		

		return $returnData;
	} // End method
} // End clas
