<?php

namespace App\Modules\TingBoxVAMC\routes;

use Illuminate\Support\Facades\Route;
use App\Http\Middleware\MakeSureThatRequestIsJsonMiddleware;
use App\Modules\TingBoxVAMC\Controllers\MerchantBank\DiemBanController;
use App\Modules\TingBoxVAMC\Controllers\MerchantBank\MerchantBankController;
use App\Modules\TingBoxVAMC\Controllers\Transaction\Mpos360TransactionQRDiemBanController;


Route::group([
	'middleware' => [MakeSureThatRequestIsJsonMiddleware::class]
], function () {

	Route::any('/Mpos360GetInfoBank', [
		'uses' => MerchantBankController::class . '@getInfoBank',
		'as' => 'GetInfoBankAction'
	])->middleware('checkSumForAnyMobile:merchantId');

	Route::any('/Mpos360GetInfoBankV2', [
		'uses' => MerchantBankController::class . '@getInfoBankV2',
		'as' => 'GetInfoBankV2Action'
	])->middleware('checkSumForAnyMobile:merchantId');

	Route::any('/Mpos360GetInfoBankV3', [
		'uses' => MerchantBankController::class . '@getInfoBankV3',
		'as' => 'GetInfoBankV3Action'
	])->middleware('checkSumForAnyMobile:merchantId');

	Route::any('/Mpos360LinkBank', [
		'uses' => MerchantBankController::class . '@linkBank',
		'as' => 'LinkBankAction'
	])->middleware('checkSumForAnyMobile:bankAccountName|mobileUserId|bankIdentity|bankAccountNo|bankMobile|bankEmail|merchantId|channelCode|bankCode|default');

	Route::any('/Mpos360LinkBankV2', [
		'uses' => MerchantBankController::class . '@Mpos360LinkBankV2',
		'as' => 'LinkBankV2Action'
	])->middleware('checkSumForAnyMobile:merchantId');

	Route::any('/Mpos360ConfirmOtpBank', [
		'uses' => MerchantBankController::class . '@confirmOtpBank',
		'as' => 'ConfirmOtpBankAction'
	])->middleware('checkSumForAnyMobile:merchantId|mobileUserId');

	Route::any('/Mpos360CloseLinkBank', [
		'uses' => MerchantBankController::class . '@closeLinkBank',
		'as' => 'CloseLinkBankAction'
	])->middleware('checkSumForAnyMobile:merchantId|userMobileId');

	Route::any('/Mpos360CloseLinkBankFake', [
		'uses' => MerchantBankController::class . '@Mpos360CloseLinkBankFake',
		'as' => 'Mpos360CloseLinkBankFakeAction'
	])->middleware('checkSumForAnyMobile:merchantId|userMobileId');

	Route::any('/Mpos360ConfirmOtpBankFake', [
		'uses' => MerchantBankController::class . '@Mpos360ConfirmOtpBankFake',
		'as' => 'Mpos360ConfirmOtpBankFakeAction'
	])->middleware('checkSumForAnyMobile:merchantId|mobileUserId');

	Route::any('/Mpos360SetPaymentDefault', [
		'uses' => MerchantBankController::class . '@setPaymentDefault',
		'as' => 'SetPaymentDefaultAction'
	])->middleware('checkSumForAnyMobile:merchantId');

	Route::any('/Mpos360ListDiemBan', [
		'uses' => DiemBanController::class . '@Mpos360ListDiemBan',
		'as' => 'Mpos360ListDiemBanAction'
	])->middleware('checkSumForAnyMobile:merchantId');

	Route::any('/Mpos360DetailDiemBan', [
		'uses' => DiemBanController::class . '@Mpos360DetailDiemBan',
		'as' => 'Mpos360DetailDiemBanAction'
	])->middleware('checkSumForAnyMobile:merchantId');

	Route::any('/Mpos360TransactionQRListByDiemBan', [
		'uses' => Mpos360TransactionQRDiemBanController::class . '@Mpos360TransactionQRListByDiemBan',
		'as' => 'Mpos360TransactionQRListByDiemBanAction'
	])->middleware('checkSumForAnyMobile:merchantId');

	
	Route::any('/Mpos360CountSumDoanhThuByDiemBan', [
		'uses' => Mpos360TransactionQRDiemBanController::class . '@Mpos360CountSumDoanhThuByDiemBan',
		'as' => 'Mpos360CountSumDoanhThuByDiemBanAction'
	])->middleware('checkSumForAnyMobile:merchantId');

	Route::any('/Mpos360GetDsDiemBanMuid', [
		'uses' => Mpos360TransactionQRDiemBanController::class . '@Mpos360GetDsDiemBanMuid',
		'as' => 'Mpos360GetDsDiemBanMuidAction'
	])->middleware('checkSumForAnyMobile:merchantId');

	Route::post('/Mpos360GetStepKhaiBaoDiemBan', [
		'uses' => DiemBanController::class . '@Mpos360GetStepKhaiBaoDiemBan',
		'as' => 'Mpos360GetStepKhaiBaoDiemBanAction'
	])->middleware('checkSumForAnyMobile:merchantId|shopId');

	Route::post('/Mpos360GetStepKhaiBaoDiemBanV2', [
		'uses' => DiemBanController::class . '@Mpos360GetStepKhaiBaoDiemBanV2',
		'as' => 'Mpos360GetStepKhaiBaoDiemBanV2Action'
	])->middleware('checkSumForAnyMobile:merchantId|shopId');

	Route::post('/Mpos360GetHomePageInfoDiemBan', [
		'uses' => DiemBanController::class . '@Mpos360GetHomePageInfoDiemBan',
		'as' => 'Mpos360GetHomePageInfoDiemBanAction'
	])->middleware('checkSumForAnyMobile:merchantId');

	Route::post('/Mpos360ActiveQrNoDevice', [
		'uses' => MerchantBankController::class . '@activeQrNoDevice',
		'as' => 'ActiveQrNoDeviceAction'
	])->middleware('checkSumForAnyMobile:merchantId');

	Route::post('/Mpos360SubmitFormSwitchModeBank', [
		'uses' => MerchantBankController::class . '@submitFormSwitchModeBank',
		'as' => 'SubmitFormSwitchModeBankAction'
	])->middleware('checkSumForAnyMobile:merchantId');

	Route::post('/Mpos360SendOtpSwitchModeBank', [
		'uses' => MerchantBankController::class . '@sendOtpSwitchModeBank',
		'as' => 'SendOtpSwitchModeBankAction'
	])->middleware('checkSumForAnyMobile:merchantId');

	Route::post('/Mpos360CheckSwitchModeBank', [
		'uses' => MerchantBankController::class . '@Mpos360CheckSwitchModeBank',
		'as' => 'Mpos360CheckSwitchModeBankAction'
	])->middleware('checkSumForAnyMobile:merchantId');

	Route::post('/Mpos360VerifyOtpSwitchModeBank', [
		'uses' => MerchantBankController::class . '@verifyOtpSwitchModeBank',
		'as' => 'VerifyOtpSwitchModeBankAction'
	])->middleware('checkSumForAnyMobile:merchantId');

	Route::any('/Mpos360CheckVaBankNumber', [
		'uses' => MerchantBankController::class . '@checkVaBankNumber',
		'as' => 'Mpos360CheckVaBankNumberAction'
	]); 

	Route::any('/Mpos360GetInfoMc', [
		'uses' => MerchantBankController::class . '@Mpos360GetInfoMc',
		'as' => 'Mpos360GetInfoMcAction'
	])->middleware('checkSumForAnyMobile:merchantId');
});

// Cronjob public
Route::any('/Mpos360CronjobSyncQrSoundBox', [
	'uses' => MerchantBankController::class . '@cronjobSyncQrSoundBox',
	'as' => 'CronjobSyncQrSoundBoxAction'
]);
Route::any('/Mpos360CronJobUpdateOrCreateBank', [
	'uses' => MerchantBankController::class . '@cronJobUpdateOrCreateBank',
	'as' => 'CronJobUpdateOrCreateBankAction'
]);
Route::any('/Mpos360CronJobCancelSoundBox', [
	'uses' => MerchantBankController::class . '@cronJobCancelSoundBox',
	'as' => 'CronJobCancelSoundBoxAction'
]);
Route::any('/Mpos360CronJobSyncDeviceSoundBox', [
	'uses' => MerchantBankController::class . '@cronJobSyncDeviceSoundBox',
	'as' => 'CronJobSyncDeviceSoundBoxAction'
]);
Route::any('/Mpos360CronJobSwitchModeBank', [
	'uses' => MerchantBankController::class . '@cronJobSwitchModeBank',
	'as' => 'CronJobSwitchModeBankAction'
]);

Route::any('/Mpos360VAMCForVCB', [
	'uses' => MerchantBankController::class . '@Mpos360VAMCForVCB',
	'as' => 'Mpos360VAMCForVCBAction'
]);
