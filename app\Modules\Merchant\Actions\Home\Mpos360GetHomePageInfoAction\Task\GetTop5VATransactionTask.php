<?php 
namespace App\Modules\Merchant\Actions\Home\Mpos360GetHomePageInfoAction\Task;

use App\Lib\partner\VA;

class GetTop5VATransactionTask {
	public VA $va;

	public function __construct(VA $va)
	{
		$this->va = $va;
	}

	public function run($merchantId, $page=1, $limit=5) {
		// Param thật, param trên là fake
		$fromDate = now()->startOfDay()->timestamp;
		$toDate = now()->endOfDay()->timestamp;

		$param = [
			'mposMcId' => $merchantId,
			'mposUserId' => '', // không có -> để rỗng
			'mposUserMobile' => '', // không có -> để rỗng
			'page' => $page, // int
			'row' => $limit, // int
			'fromDate' => $fromDate,
			'toDate' => $toDate,
			'vaNumber' => '' // để rỗng
		];
		
		$dataAsJson = json_encode($param);
		$encrypt = $this->va->encrypt($dataAsJson);

		$checksum = $this->va->createChecksum($encrypt);

		$dataPost = [	
			'app_id' => VAG_APP_ID,
			'data' => $encrypt,
			'checksum' => $checksum,
		];

		$url = VAG_BASE_URL . '/v1/merchant/trans/list-by-mpos-info';
		
		try {
			$r = $this->va->call($url, $dataPost, 5);
		}catch(\Throwable $th) {
			$r = [];
		}
		
		return $r;
	}
}