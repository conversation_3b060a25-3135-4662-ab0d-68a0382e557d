<?php
namespace App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360Register;

use Illuminate\Foundation\Http\FormRequest;
use App\Modules\Merchant\Rules\TingTing\NgaySinhTu18TuoiRule;

class Mpos360SubmitFormAuthenAccountStep2ActionRequest extends FormRequest
{
 	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.username' => ['required', 'string'],
			'data.merchant_id' => ['required', 'string'],
			'data.fullname' => ['required', 'string'],
			'data.cccd' => [
				'required',
				'string',
				'digits:12',
			],
			'data.gender' => ['required', 'string', 'in:MALE,FEMALE'],
			'data.address' => ['required', 'string'],
			'data.birthday' => ['required', 'string', 'date_format:d/m/Y', new NgaySinhTu18TuoiRule()],
			'data.cccd_image_before' => ['required', 'string', 'url'],
			'data.cccd_image_after' => ['required', 'string', 'url'],
		];
	}

	public function messages() {
		return [
			'data.merchant_id.required' => 'merchant_id là bắt buộc',
			'data.merchant_id.string' => 'merchant_id phải là kiểu chuỗi ký tự',

			'data.fullname.required' => 'Họ và tên là bắt buộc',
			'data.fullname.string' => 'Họ và tên phải là kiểu chuỗi ký tự',

			'data.cccd.required' => 'Căn cước công dân là bắt buộc',
			'data.cccd.string' => 'Căn cước công dân phải là kiểu chuỗi ký tự',
			'data.cccd.digits' => 'Số CCCD phải đúng định dạng 12 ký tự số',

			'data.gender.required' => 'Giới tính là bắt buộc',
			'data.gender.string' => 'Giới tính phải là kiểu chuỗi ký tự',
			'data.gender.in' => 'Giới tính phải thuộc một trong các giá trị: Nam hoặc Nữ',

			'data.address.required' => 'Địa chỉ thường trú là bắt buộc',
			'data.address.string' => 'Địa chỉ thường trú phải là kiểu chuỗi ký tự',
			'data.address.regex' => 'Địa chỉ thường trú chỉ được chứa chữ cái, số, khoảng trắng, các ký tự đặc biệt "/", "," và các ký tự tiếng Việt có dấu.',

			'data.birthday.required' => 'Ngày sinh là bắt buộc',
			'data.birthday.string' => 'Ngày sinh phải là kiểu chuỗi ký tự',
			'data.birthday.date_format' => 'Ngày sinh phải đúng định dạng: ngày/tháng/năm',

			'data.cccd_image_before.required' => 'Ảnh căn cước công dân mặt trước là bắt buộc',
			'data.cccd_image_before.string' => 'Ảnh căn cước công dân mặt trước phải là kiểu chuỗi ký tự',
			'data.cccd_image_before.url' => 'Ảnh căn cước công dân mặt trước phải một url',

			'data.cccd_image_after.required' => 'Ảnh căn cước công dân mặt sau là bắt buộc',
			'data.cccd_image_after.string' => 'Ảnh căn cước công dân mặt sau phải là kiểu chuỗi ký tự',
			'data.cccd_image_after.url' => 'Ảnh căn cước công dân mặt sau phải một url',
		];
	}

	protected function passedValidation()
	{
		$params = $this->all();
		$params['data']['fullname'] = cleanXSS($params['data']['fullname']);

		$address = $params['data']['address'];
		$address = str_replace('.', ' ', $address);

		$params['data']['address'] = cleanXSS($address);
		$this->merge($params);
	}
} // End class