<?php

namespace App\Modules\TingBoxVAMC\Actions\DiemBan\Mpos360DetailDiemBanAction\SubAction;

use App\Exceptions\BusinessException;
use App\Lib\Helper;
use App\Lib\Mpos360UrlHelper;
use App\Modules\TingBoxVAMC\Models\MerchantShopBank;

class HandleDiemBanDetailSubAction
{
	public function run($detailMc, $merchantId, $muId, $linkBankCanLinking)
	{
		$listBank = $linkBankCanLinking->keyBy('bankCode')->toArray();
		
		if (empty($detailMc['data'])) {
			throw new BusinessException('Lỗi không có thông tin điểm bán');
		}

		$listThietBi = [];

		foreach ($detailMc['data']['locations'] as $location) {
			
			if (!empty($location['deviceDTOs'])) {
				foreach ($location['deviceDTOs'] as $index => $dv) {
					$dv['address'] = $location['areaAddress'];
					$dv['cityId'] = $location['areaCityCode'];
					$dv['districtId'] = $location['areaDistrictCode'];
					$dv['industryId'] = $location['mcc'];
					$dv['locationId'] = $location['id'];

					if (count($location['deviceDTOs']) == 1) {
						$dv['locationId'] = $location['id'];
						$dv['locationName'] = $location['areaName'];
					} else {
						$dv['locationId'] = $location['id'];
						$dv['locationName'] = sprintf('%s', $location['areaName'], $index + 1);
					}

					$listThietBi[] = $dv;
				}
			}
		}

		$listMuGroup = collect($listThietBi)->filter(function ($item) use ($muId) {
			return $muId == $item['mobileUserId'];
		})->groupBy('mobileUserId')->toArray();


		$diemBan = [];

		foreach ($listMuGroup as $mId => $muInfo) {
			if ($mId == $muId) {
				$diemBan = [
					'shopInfo' => [
						'shopId' => $muInfo[0]['locationId'],
						'muId' => $muId,
						'shopName' => $muInfo[0]['locationName'],
						'address' => $muInfo[0]['address'],
						'cityId' =>  $muInfo[0]['cityId'],
						'districtId' =>  $muInfo[0]['districtId'],
						'industryId' => $muInfo[0]['industryId'],
						'locationId' => $muInfo[0]['locationId'],
						'qrDisplayName' => $detailMc['data']['qrDisplayName']
					],

					'listTingBoxDevice' => collect($muInfo)->filter(function ($it) {
						return !empty($it['serialNumber']);
					})->map(function ($it) {
						$serial = !empty($it['serialNumber']) ? $it['serialNumber'] : 'Đang cập nhật';
						return [
							'serialNumber' => $serial,
							'status' => $it['status'],
							'mobileUserId' => $it['mobileUserId'],
							'otherData' => $this->getStatusDeviceTbAsText($it['status']),
							'icon' => Mpos360UrlHelper::getTingBoxThumbnail($serial)
						];
					}),
					'listBanking' => [
						'direct' => [],
						'inter' => []
					]
				];
			}
		}

		if (empty($diemBan['shopInfo'])) {
			throw new BusinessException('Lỗi không tìm thấy thông tin điểm bán');
		}
		
		$listMerchantShopBank = MerchantShopBank::query()->with(['merchantBank'])
			->where('shop_id', $diemBan['shopInfo']['muId'])
			->get();


		if ($listMerchantShopBank->isNotEmpty()) {

			$diemBan['listBanking']['direct'] = $listMerchantShopBank->filter(function (MerchantShopBank $mcShopBank) use ($detailMc, $listBank) {
				return $mcShopBank->account_type == MerchantShopBank::LOAI_TK_TRUC_TIEP && 
					(
						($mcShopBank->isDaLienKet() && $mcShopBank->isDongBoSoundBox())
						|| $mcShopBank->isHienThiVCB()
					);
			})
				->map(function ($mcShopBank) use ($detailMc, $listBank) {
					return [
						'merchantShopBankId' => $mcShopBank->id,
						"mobileUserId" => $mcShopBank->shop_id,
						"merchantBankId" => $mcShopBank->merchantBank->id,
						'vaBankNumber' => $mcShopBank->account_number_partner,
						'account_number' => $mcShopBank->getAccountNumber($detailMc['data']['mobile']),
						'account_holder' => $mcShopBank->getAccountHolder(),
						'account_qr'  => $mcShopBank->account_qr,
						'account_qr_display' => $mcShopBank->account_qr_display,
						'status' => $mcShopBank->getStatusLienKetForMobile(),
						'bank_branch' => $mcShopBank->merchantBank->bank_branch,
						'bank_code' =>  $mcShopBank->merchantBank->bank_code,
						'bank_id' => $mcShopBank->merchantBank->id,
						'request_id' => $mcShopBank->request_id,
						'partner_request_id' => $mcShopBank->partner_request_id,
						'is_default' => 'NO',
						'bank_icon' =>  $mcShopBank->merchantBank->getBankIconUrl(),
						'statusNotify' => $mcShopBank->statusNotify,
						'hasResendOtp' => $mcShopBank->hasResendOtp,
						'notificationRequired' => $mcShopBank->notificationRequired,
						'bankFullName' => $listBank[$mcShopBank->merchantBank->bank_code]['bankName'],
						'bankShortName' => $listBank[$mcShopBank->merchantBank->bank_code]['shortName'],
						'bankMobile' => $mcShopBank->merchantBank->bank_mobile
					];
				})->values()
				->toArray();
		}

		$diemBan['listBanking']['inter'] = [];

		if (!empty($detailMc['data']['bankId'])) {
			$bankName = isset($detailMc['data']) && isset($detailMc['data']['bankName']) ? $detailMc['data']['bankName'] : '';
			$bankCode = explode(' - ',$bankName)[0];

			$diemBan['listBanking']['inter'][] = [
				'merchantShopBankId' => '-1',
				'mobileUserId' => $muId,
				'merchantBankId' => '-1',
				'vaBankNumber' => '',
				'account_number' => isset($detailMc['data']) && isset($detailMc['data']['accountNo']) ? $detailMc['data']['accountNo'] : '', 
				'account_holder' => isset($detailMc['data']) && isset($detailMc['data']['holderName']) ? $detailMc['data']['holderName'] : '', 
				'account_qr'  => '',
				'account_qr_display' => isset($detailMc['data']) && isset($detailMc['data']['qrDisplayName']) ? $detailMc['data']['qrDisplayName'] : '',
				'status' => 'DA_LIEN_KET',
				'bank_branch' => '',
				'bank_code' =>  $bankCode,
				'bank_id' => isset($detailMc['data']) && isset($detailMc['data']['bankId']) ? $detailMc['data']['bankId'] : '',
				'request_id' => '',
				'partner_request_id' => '',
				'is_default' => 'NO',
				'bank_icon' => 'https://prod-nextpay-crm.s3-ap-southeast-1.amazonaws.com/test/67b5971c4465eb435f2991c5IconBank.png',
				'statusNotify' => '',
				'hasResendOtp' => '',
				'notificationRequired' => '',
				'bankFullName' => '',
				'bankShortName' => '',
				'bankMobile' => '',
			];								
		}

		$returnData = [
			'merchantId' => $merchantId,
			'muId' => $muId,
			'username' => $detailMc['data']['username'],
			'detail' => $diemBan
		];

		return $returnData;
	}

	public function getStatusDeviceTbAsText($sttCode = '') {
		if ($sttCode == '') {
			return [
				'text' => 'Chờ kích hoạt',
				'text_color' => '#fdb62f'
			];
		}

		if ($sttCode == 'ACTIVE') {
			return [
				'text' => 'Đã kích hoạt',
				'text_color' => '#73ae4a'
			];
		}

		if ($sttCode == 'INACTIVE') {
			return [
				'text' => 'Chờ kích hoạt',
				'text_color' => '#fdb62f'
			];
		}

		if ($sttCode == 'SUSPENDED') {
			return [
				'text' => 'Đã từ chối',
				'text_color' => '#da2128'
			];
		}

		return [
			'text' => 'Không xác định',
			'text_color' => '#bcd4da'
		];
	}
}
