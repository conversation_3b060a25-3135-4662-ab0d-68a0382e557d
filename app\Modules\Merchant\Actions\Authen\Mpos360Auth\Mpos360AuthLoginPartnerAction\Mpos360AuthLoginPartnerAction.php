<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginPartnerAction;

use App\Exceptions\BusinessException;
use App\Lib\Helper;
use App\Modules\Merchant\Requests\Authen\Mpos360\Mpos360AuthLoginUserNameRequest;
use App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginUserNameOnly\Mpos360AuthLoginUserNameOnlyAction;

class Mpos360AuthLoginPartnerAction
{

	public function run(Mpos360AuthLoginUserNameRequest $request)
	{
		$fixChecksum = '1a40604f4254d984ea6bea9d66022e0fc7bdc57eb63180e723c63aa75b6ddc449e345c347f13573ae34666ccfb3c73cf6d9e5cd3f1ab8214fba59419a4ef26cf';

		// for prod (2-3 weeks)
		if (Helper::isProduction()) {
			$fixChecksum = env('TINGBOXMC_APP_CHECKSUM');
		}

		if ($request->json('checksum') != $fixChecksum) {
			throw new BusinessException('Mã so khớp không đúng', 403);
		}

		$deviceSession = app(Mpos360AuthLoginUserNameOnlyAction::class)->run($request);
		$deviceSession->mpos_token = $deviceSession->getMposToken();
		return $deviceSession;
	}
} // End class
