<?php

namespace App\Modules\Merchant\Actions\TingBox\Mpos360GetCauHinhByMerchantAction;

use App\Lib\Helper;
use App\Lib\Logs;
use Illuminate\Support\Arr;
use App\Lib\partner\MposAWS;
use App\Lib\partner\SoundBox;
use App\Modules\Merchant\Actions\TingBox\Mpos360GetCauHinhByMerchantAction\SubAction\EmptyCauHinhMcSubAction;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Enums\TingTingEnum;
use App\Modules\Merchant\Model\CacheAction;
use App\Modules\Merchant\Requests\TingBox\Mpos360GetCauHinhByMerchantRequest;

class Mpos360GetCauHinhByMerchantV2Action
{
	public MposAWS $mposAws;

	public function __construct(MposAWS $mposAWS)
	{
		$this->mposAws = $mposAWS;
	}


	public function run(Mpos360GetCauHinhByMerchantRequest $request)
	{
		$returnData = [
			'tingbox' => [
				'currentMode' => 'VANP',
				'qrDefault' => (object)[],
				'tingting' => [
					'listMobileUser' => []
				]
			]
		];

		$merchantId = $request->json('data.merchantId');
		$clientId = sprintf('%s_%s', $merchantId, $request->json('data.deviceToken'));

		$tingting = [
			'merchantId' => $merchantId,
			'listMobileUser' => [],
			'clientId' => $clientId,
			'topic' => sprintf('/APP360/%s/data', $clientId),
			'mqAuthen' => [
				'userName' => env('SOUNDBOX_MQ_USERNAME'),
				'password' => env('SOUNDBOX_MQ_PASSWORD'),
				'mqtt' => env('SOUNDBOX_MQ_BROKERS')
			]
		];

		$listLocation = (new SoundBox())->getLocationByMcId([
			'mcId' => $merchantId,
			'partnerCode' => Helper::getPartnerCode($merchantId)
		]);


		if (!empty($listLocation['result']) && isset($listLocation['code']) && $listLocation['code'] == 1002) {
			$deviceSession = $request->getCurrentDeviceSession();
			return app(EmptyCauHinhMcSubAction::class)->run($tingting, $deviceSession);
		}

		$tingting['totalAmountCurrent'] = '--';
		$tingting['transactionCount'] = '--';

		if (!empty($listLocation['data'])) {
			$tingting['listMobileUser'] = [];

			if (!empty($listLocation['data']['currentMode'])) {
				$returnData['tingbox']['currentMode'] = $listLocation['data']['currentMode'];
			}

			if (!empty($listLocation['data']['qrDefault4App']['deviceMobileUsers'])) {

				$qrDefault = Arr::last($listLocation['data']['qrDefault4App']['deviceMobileUsers']);

				if ($qrDefault && isset($qrDefault['mobileUserName']) && !empty($qrDefault['mobileUserName'])) {
					$mobileUserDefault = Arr::first($listLocation['data']['mcStores'], function ($item) use ($qrDefault) {
						return isset($item['mobileUser']) && isset($item['mobileUser']['muName']) && $item['mobileUser']['muName'] == $qrDefault['mobileUserName'];
					});

					$qrDefault['address'] = $mobileUserDefault && isset($mobileUserDefault['address']) ? $mobileUserDefault['address'] : '';
					$qrDefault['muId'] = $mobileUserDefault && isset($mobileUserDefault['mobileUser']) && isset($mobileUserDefault['mobileUser']['muName'])  ? $mobileUserDefault['mobileUser']['muName'] : '';
					$qrDefault['bank'] =  $mobileUserDefault && isset($mobileUserDefault['bank']) && !empty($mobileUserDefault['bank']) ? $mobileUserDefault['bank'] : (object)[];
					$qrDefault['step_action'] =  [
						[
							'step' => 1,
							'status' => $mobileUserDefault && isset($mobileUserDefault['name']) && !empty($mobileUserDefault['name']) ? 2 : 1,
							'step_name' => 'Khai báo cửa hàng kinh doanh',
						],
						[
							'step' => 2,
							'status' => $mobileUserDefault && isset($mobileUserDefault['mobileUser']) && isset($mobileUserDefault['mobileUser']['devices']) && !empty($mobileUserDefault['mobileUser']['devices']) ? 2 : 1,
							'step_name' => 'Gán loa Tingbox',
						],
						[
							'step' => 3,
							'status' => $mobileUserDefault && isset($mobileUserDefault['bank']) && isset($mobileUserDefault['bank']['accountNo']) && !empty($mobileUserDefault['bank']['accountNo']) ? 2 : 1,
							'step_name' => 'Khai báo tài khoản ngân hàng',
						],
					];
				}
				$returnData['tingbox']['qrDefault'] = (object) $qrDefault;

				$tingting['listMobileUser'] = $listLocation['data']['qrDefault4App']['deviceMobileUsers'];

				$countStore = count($tingting['listMobileUser']);

				foreach ($tingting['listMobileUser'] as $key => $s) {
					$mobileUser = Arr::last($listLocation['data']['mcStores'], function ($item) use ($s) {
						return isset($item['mobileUser']) && isset($item['mobileUser']['muName']) && $item['mobileUser']['muName'] == $s['mobileUserName'];
					});

					$tingting['listMobileUser'][$key]['address'] = $mobileUser && isset($mobileUser['address']) ? $mobileUser['address'] : '';
					$tingting['listMobileUser'][$key]['can'] = Mpos360Enum::MPOS360_CAN_STAY_AT_FORM;
					$tingting['listMobileUser'][$key]['muId'] = $mobileUser && isset($mobileUser['mobileUser']) && isset($mobileUser['mobileUser']['muName'])  ? $mobileUser['mobileUser']['muName'] : '';
					$tingting['listMobileUser'][$key]['bank'] = $mobileUser && isset($mobileUser['bank']) && !empty($mobileUser['bank']) ? $mobileUser['bank'] : (object)[];
					$tingting['listMobileUser'][$key]['step_action'] =  [
						[
							'step' => 1,
							'status' => $mobileUser && isset($mobileUser['name']) && !empty($mobileUser['name']) ? 2 : 1,
							'step_name' => 'Khai báo cửa hàng kinh doanh',
						],
						[
							'step' => 2,
							'status' => $mobileUser && isset($mobileUser['mobileUser']) && isset($mobileUser['mobileUser']['devices']) && !empty($mobileUser['mobileUser']['devices']) ? 2 : 1,
							'step_name' => 'Gán loa Tingbox',
						],
						[
							'step' => 3,
							'status' => $mobileUser && isset($mobileUser['bank']) && isset($mobileUser['bank']['accountNo']) && !empty($mobileUser['bank']['accountNo']) ? 2 : 1,
							'step_name' => 'Khai báo tài khoản ngân hàng',
						],
					];

					foreach ($tingting['listMobileUser'][$key]['step_action'] as $action) {
						if ($action['status'] != 2) {
							if ($countStore == 1) {
								$tingting['listMobileUser'][$key]['can'] = TingTingEnum::CAN_GO_TO_KHAI_BAO_MAN_HINH_3STEP;
							} else {
								$tingting['listMobileUser'][$key]['can'] = TingTingEnum::CAN_GO_TO_CHI_TIET_CUA_HANG;
							}
							break;
						}
					}
				}
			} else {
				$returnData['tingbox']['qrDefault'] = (object) [
					"deviceId" => '',
					"storeName" => '',
					"mobileUserName" => $listLocation['data'] && isset($listLocation['data']['mcUserMobile']) ? $listLocation['data']['mcUserMobile'] : '',
					"qrDefault" => '',
					"qrTypeDefault" => '',
					"qrIdDefault" => '',
					"accountNumberDefault" => '',
					"accountNameDefault" => '',
					"address" => "",
					"muId" => $listLocation['data'] && isset($listLocation['data']['mcUserMobile']) ? $listLocation['data']['mcUserMobile'] : '',
					"bank" => (object) [],
					"step_action" => [
						[
							'step' => 1,
							'status' => 1,
							'step_name' => 'Khai báo cửa hàng kinh doanh',
						],
						[
							'step' => 2,
							'status' => 1,
							'step_name' => 'Gán loa Tingbox',
						],
						[
							'step' => 3,
							'status' => 1,
							'step_name' => 'Khai báo tài khoản ngân hàng',
						],
					],
					'integratedMethod' => 'VANP',
					'hasDevice' => 'NO',
				];

				$tingting['listMobileUser'][] = [
					"deviceId" => '',
					"storeName" => '',
					"mobileUserName" => $listLocation['data'] && isset($listLocation['data']['mcUserMobile']) ? $listLocation['data']['mcUserMobile'] : '',
					"qrDefault" => '',
					"qrTypeDefault" => '',
					"qrIdDefault" => '',
					"accountNumberDefault" => '',
					"accountNameDefault" => '',
					"address" => "",
					"muId" => $listLocation['data'] && isset($listLocation['data']['mcUserMobile']) ? $listLocation['data']['mcUserMobile'] : '',
					"can" => "",
					"bank" => (object) [],
					"step_action" => [
						[
							'step' => 1,
							'status' => 1,
							'step_name' => 'Khai báo cửa hàng kinh doanh',
						],
						[
							'step' => 2,
							'status' => 1,
							'step_name' => 'Gán loa Tingbox',
						],
						[
							'step' => 3,
							'status' => 1,
							'step_name' => 'Khai báo tài khoản ngân hàng',
						],
					],
					'integratedMethod' => 'VANP',
					'hasDevice' => 'NO',
				];
			}

			if (!empty($listLocation['data']['qrDefault4App']['mqttInfo'])) {
				$tingting['mqAuthen'] = [
					'userName' => $listLocation['data']['qrDefault4App']['mqttInfo']['username'],
					'password' => $listLocation['data']['qrDefault4App']['mqttInfo']['password'],
					'mqtt' => $listLocation['data']['qrDefault4App']['mqttInfo']['endpoint'],
				];
			}

			$returnData['tingbox']['tingting'] = $tingting;

			// For kiểm tra cửa hàng
			$listMobileUserQr = collect($listLocation['data']['qrDefault4App']['deviceMobileUsers'])->pluck('mobileUserName')->values()->toArray();

			if (!empty($listMobileUserQr)) {
				foreach ($listLocation['data']['mcStores'] as $store) {
					if (!in_array($store['mobileUser']['muName'], $listMobileUserQr)) {
						$item = [
							"deviceId" => '',
							"storeName" => sprintf('%s (%s)', $store['name'] ?? '', $store['mobileUser']['muName']),
							"mobileUserName" => $store['mobileUser']['muName'],
							"qrDefault" => '',
							"qrTypeDefault" => '',
							"qrIdDefault" => '',
							"accountNumberDefault" => '',
							"accountNameDefault" => '',
							"address" =>  $store['address'],
							"muId" => $store['mobileUser']['muName'],
							"can" => TingTingEnum::CAN_GO_TO_CHI_TIET_CUA_HANG,
							"bank" => (object) $store['bank'],
							"step_action" => [
								[
									'step' => 1,
									'status' => 1,
									'step_name' => 'Khai báo cửa hàng kinh doanh',
								],
								[
									'step' => 2,
									'status' => 1,
									'step_name' => 'Gán loa Tingbox',
								],
								[
									'step' => 3,
									'status' => 1,
									'step_name' => 'Khai báo tài khoản ngân hàng',
								],
							],

							'integratedMethod' => 'VANP',
							'hasDevice' => 'NO',
						];

						if (!empty($store['locationId'])) {
							$item['step_action'][0]['status'] = 2;
						}

						if (!empty($store['bank'])) {
							$item['step_action'][2]['status'] = 2;
						}

						if (!empty($store['mobileUser']['devices'])) {
							$item['step_action'][1]['status'] = 2;
						}

						$returnData['tingbox']['tingting']['listMobileUser'][] = $item;
					}
				}
			}
		} // End if !empty data

		// Xu ly luu dem
		$cacheAction = CacheAction::query()->firstWhere([
			'reference_id' => $request->json('data.merchantId'),
			'step_code' => 'KHAI_BAO_TKNH',
		]);
		
		foreach ($returnData['tingbox']['tingting']['listMobileUser'] as &$mu) {
			foreach ($listLocation['data']['mcStores'] as $s) {
				if ($mu['mobileUserName'] == $s['mobileUser']['muName']) {
					if (!empty($s['locationId'])) {
						$mu['step_action'][0]['status'] = 2;
					}
	
					if (!empty($s['mobileUser']['devices'])) {
						$mu['step_action'][1]['status'] = 2;
					}

					$mu['integratedMethod'] = $s['mobileUser']['integratedMethod'];
					$mu['hasDevice'] = !empty($s['mobileUser']['devices']) ? 'YES' : 'NO';

					if (!empty($s['bank'])) {
						$mu['bank'] = $s['bank'];
					}
				}
			}

			if ($cacheAction) {
				$mu['step_action'][2]['status'] = 2;
			}

			if (empty($mu['muId'])) {
				$mu['muId'] = $mu['mobileUserName'];
			}

			if (isset($mu['bank']) && is_array($mu['bank']) && !empty($mu['bank']['bankCode']) && $mu['bank']['bankCode'] == 'VCB') {
				$mu['bank']['holderName'] = '';
			}
		}

		return $returnData;
	}

	public function isAllVANPMode($data): bool
	{
		return collect($data)->every(function ($item) {
			return $item['currentMode'] == 'VANP';
		});
	}
} // End class
