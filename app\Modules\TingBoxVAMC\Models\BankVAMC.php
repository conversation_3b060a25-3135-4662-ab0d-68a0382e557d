<?php

namespace App\Modules\TingBoxVAMC\Models;

use Illuminate\Database\Eloquent\Model;

class BankVAMC extends Model
{
	const DISPLAY_PROD = 1;
	const DISPLAY_DEV_MODE = 2;
	const DISPLAY_NONE = 0;
	const DISPLAY_SAP_RA_MAT = 3;

	protected $connection = 'mpos360_data';

	protected $table      = 'bank_vamc';

	public function getTermLinkByVersionApp($version='') {
		// App cũ phía mobile đã tự gắn gview vào đầu rồi
		if (empty($version)) {
			return $this->termsLink;
		}
		
		// Nếu là PDF thì mới gắn thêm gview từ server
		if ($this->termsLinkType == 'PDF') {
			return sprintf('https://docs.google.com/gview?embedded=true&url=%s', $this->termsLink);
		}

		// Return link thuần túy
		return $this->termsLink;
	}
} // End class
