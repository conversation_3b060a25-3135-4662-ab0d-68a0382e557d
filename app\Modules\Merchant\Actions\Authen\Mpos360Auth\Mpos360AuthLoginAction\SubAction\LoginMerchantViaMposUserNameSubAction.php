<?php

namespace App\Modules\Merchant\Actions\Authen\Mpos360Auth\Mpos360AuthLoginAction\SubAction;

use App\Exceptions\BusinessException;
use Exception;
use App\Lib\partner\MPOS;
use App\Modules\Merchant\Enums\Mpos360Enum;

class LoginMerchantViaMposUserNameSubAction
{
	public function run(string $username, string $password, string $os='iOS', $fcmDeviceToken='')
	{
		$params = [
			'deviceIdentifier' => $fcmDeviceToken,
			'os'               => $os,
			'language'         => 'vi',
			'username'         => $username,
			'password'         => $password,
			'bundle'           => ''
		];

		$loginMpos = (new MPOS())->loginWithMobileUser($params);
		
		$params['password'] = 'HIDDEN_BY_SYSTEM';
		
		
		$apiCode = $loginMpos['data']['error']['code'];
		
		if (empty($apiCode)) {
			throw new BusinessException('MPOS Err: ' . vmsg('LoginMerchantViaMposSubAction_KhongCoThongTinTraVeTuDoiTac'));
		}

		if ($apiCode == Mpos360Enum::API_WRONG_PASSWORD_CODE) {
			throw new BusinessException(vmsg('LoginMerchantViaMposSubAction_MatKhauKhongChinhXac'));
		}

		if ($apiCode == Mpos360Enum::API_MERCHANT_NOT_FOUND) {
			throw new BusinessException(__('authen.KhongDungThongTinLogin'));
		}

		if ($apiCode != Mpos360Enum::API_SUCCESS_CODE) {
			$message = $loginMpos['data']['error']['message'] ?? vmsg('LoginMerchantViaMposSubAction_LoiApiDangNhapDoiTac');
			throw new BusinessException('MPOS Err: ' . $message);
		}

		$loginMpos['data'] = $loginMpos['data']['data'];
		return $loginMpos;
	}
}
