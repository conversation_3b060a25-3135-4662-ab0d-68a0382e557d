<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Test Qr</title>
	<script src="https://unpkg.com/html5-qrcode" type="text/javascript"></script>
</head>

<body>
	<div id="reader" width="600px">

	</div>
	
   
	<script>
		function onScanSuccess(decodedText, decodedResult) {
			// handle the scanned code as you like, for example:
			console.log(`Code matched = ${decodedText}`, decodedResult);
		}

		function onScanFailure(error) {
			// handle scan failure, usually better to ignore and keep scanning.
			// for example:
			console.warn(`Code scan error = ${error}`);
		}

		let html5QrcodeScanner = new Html5QrcodeScanner(
			"reader", {
				fps: 10,
				qrbox: {
					width: 250,
					height: 250
				}
			},
			/* verbose= */
			false);
		html5QrcodeScanner.render(onScanSuccess, onScanFailure);
	</script>
</body>

</html>