<?php

namespace App\Modules\Merchant\Actions\V3\RequestChangeInfo\SA;

/**
 * [20.09.2024] - Ver 2 của app sẽ có tùy chọn thay đổi "Loại tài khoản ngân hàng" để làm KYC
 * Tuy nhiên: phía MNP không thể cung cấp đủ dữ liệu bankAccType (Loại tknh) do dữ liệu đó bị cũ 
 * và số lượng MC trên live bị thiếu
 * 
 * Giải pháp: mpos360 sẽ thực hiện thêm vào dựa trên "Loại MC" để sát thực tế hơn
 * 
 * @param $customerType [
      "editable" => 1
      "display" => "Hộ kinh doanh"
      "format" => "TEXT"
      "position" => 0
      "label" => "Loại khách hàng"
      "type" => "DROPDOWN"
      "value" => "HOUSEHOLD"
      "group" => ""
    ]
		Loại MC
 */
class BuildBankAccTypeSubAction
{
	public function run($groupProfiles = [], $customerType = [], $mnpConfigMcBankAccType = [])
	{
		$hasBankAccountType = collect($groupProfiles['groups']['CHANGE_BANK_ACCOUN_INFO']['profiles'])->contains(function ($item) {
			return $item['profileKey'] == 'bankAccType';
		});


		$list = [];
		foreach ($mnpConfigMcBankAccType as $it) {
			$list[$it['code']] = $it['name'];
		}

		// Hộ kinh doanh
		if ($customerType['value'] == 'HOUSEHOLD') {
			$bankAccTypeItem = [
				"editable" => 1,
				"display" => "Hộ kinh doanh",
				"format" => "TEXT",
				"position" => 0,
				"label" => vmsg("Loại tài khoản ngân hàng"),
				"type" => "DROPDOWN",
				"value" => "MC_HOUSEHOLD",
				"group" => "CHANGE_BANK_ACCOUN_INFO",
				"isChanged" => "0",
				"other_data" => [
					"list" => $list
				],
				"profileKey" => "bankAccType"
			];
		}

		// Công ty
		if ($customerType['value'] == 'COMPANY') {
			$bankAccTypeItem = [
				"editable" => 1,
				"display" => "Công ty",
				"format" => "TEXT",
				"position" => 0,
				"label" => vmsg("Loại tài khoản ngân hàng"),
				"type" => "DROPDOWN",
				"value" => "MC_COMPANY",
				"group" => "CHANGE_BANK_ACCOUN_INFO",
				"isChanged" => "0",
				"other_data" => [
					"list" => $list
				],
				"profileKey" => "bankAccType"
			];
		}

		// Cá nhân
		if ($customerType['value'] == 'INDIVIDUAL') {
			$bankAccTypeItem = [
				"editable" => 1,
				"display" => "Cá nhân",
				"format" => "TEXT",
				"position" => 0,
				"label" => vmsg("Loại tài khoản ngân hàng"),
				"type" => "DROPDOWN",
				"value" => "MC_INDIVIDUAL",
				"group" => "CHANGE_BANK_ACCOUN_INFO",
				"isChanged" => "0",
				"other_data" => [
					"list" => $list
				],
				"profileKey" => "bankAccType"
			];
		}

		return [
			'is_need_push_profile' => !$hasBankAccountType,
			'bankAccTypeItem' => $bankAccTypeItem
		];
	}
} // End class