<?php

namespace App\Exceptions;

use Throwable;
use ErrorException;
use App\Traits\ApiResponser;
use App\Exceptions\BusinessException;
use Illuminate\Validation\ValidationException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;

class Handler extends ExceptionHandler
{
    use ApiResponser;
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    /**
     * Report or log an exception.
     *
     * @param  \Throwable  $exception
     * @return void
     *
     * @throws \Throwable
     */
    public function report(Throwable $exception)
    {
        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Throwable  $exception
     * @return \Symfony\Component\HttpFoundation\Response
     *
     * @throws \Throwable
     */
    public function render($request, Throwable $exception) 
    {
			mylog(['ParentLogError' => [
				'file' => $exception->getFile(),
				'line' => $exception->getLine(),
				'msg' => $exception->getMessage()
			]]);
				if ($exception instanceof BusinessException)
				{
					return $this->errorResponse($exception->getCode(), '', ['errors' => [$exception->getMessage()]]);
				}
				
        if ($exception instanceof MethodNotAllowedHttpException)
        {
            return $this->errorResponse(405,'Method is not allowed for the requested route');
        }

        if ($exception instanceof ValidationException)
        {
            return $this->errorResponse(422, 'Error Validate', ['errors' => $exception->validator->errors()->all()]);
        }

        if ($exception instanceof ErrorException)
        {
            return $this->errorResponse(500, '', ['errors' => [$exception->getMessage()]]);
        }

        return parent::render($request, $exception);
    }
}
