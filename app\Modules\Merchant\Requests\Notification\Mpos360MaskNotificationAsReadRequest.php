<?php

namespace App\Modules\Merchant\Requests\Notification;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360MaskNotificationAsReadRequest extends MerchantRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.id_list' => ['present', 'string', 'max:600'],
			'data.groupCode' => ['present', 'string', 'max:40'],
			'data.cateCode' => ['present', 'string', 'max:40'],
			'data.subCateCode' => ['present', 'string', 'max:40'], 
		];
	}

	protected function passedValidation()
	{
		$params = $this->all();
		
		if (!empty($params['data']['id_list'])) {
			$params['data']['id_list'] = explode(',', $params['data']['id_list']);
		}
		
		$this->merge($params);
	}
}
