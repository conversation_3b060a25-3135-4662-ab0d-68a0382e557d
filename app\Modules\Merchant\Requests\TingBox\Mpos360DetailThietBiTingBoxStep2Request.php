<?php

namespace App\Modules\Merchant\Requests\TingBox;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360DetailThietBiTingBoxStep2Request extends MerchantRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data.username' => ['required', 'string'],
      'data.tingboxSerial' => ['required', 'string'],
    ];
  }

	public function messages() {
		return [
			'data.username.required' => 'Username là bắt buộc',
			'data.username.string' => 'Username phải là kiểu chuỗi',
			'data.tingboxSerial.required' => 'Mã serial tingbox là bắt buộc',
			'data.tingboxSerial.string' => 'Mã serial tingbox phải là kiểu chuỗi',
		];
	}

	public function isTingBoxVer1(): bool {
		return substr($this->json('data.tingboxSerial'), 0, 4) == 'TB01';
	}

	public function isTingBoxVer2(): bool {
		return !$this->isTingBoxVer1();
	}
} // End class
