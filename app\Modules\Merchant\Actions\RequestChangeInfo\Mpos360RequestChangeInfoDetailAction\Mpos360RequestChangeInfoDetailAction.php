<?php

namespace App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoDetailAction;

use Exception;
use Carbon\Carbon;
use Illuminate\Support\Str;
use App\Exceptions\BusinessException;
use App\Modules\Merchant\Enums\Mpos360Enum;
use App\Modules\Merchant\Model\Mpos360MerchantRequest;
use App\Modules\Merchant\Requests\RequestChangeInfo\Mpos360RequestChangeInfoDetailRequest;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoListAction\SubAction\ConfigYeuCauThayDoiSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoDetailAction\SubAction\GetTrangThaiDuyetYcSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoDetailAction\SubAction\XuLyNoiTiepYeuDoiThongTinSubAction;
use App\Modules\Merchant\Actions\RequestChangeInfo\Mpos360RequestChangeInfoDetailAction\SubAction\GenGroupMnpDetailForMobileSubAction;
use App\Modules\Merchant\Actions\V3\RequestChangeInfo\Mpos360RequestChangeInfoDetailV3Action\SubAction\GetTrangThaiYcForMobileSubAction;

class Mpos360RequestChangeInfoDetailAction
{
	public bool $isDuyet = true;

	public function run(Mpos360RequestChangeInfoDetailRequest $request)
	{
		$mpos360McRequest = Mpos360MerchantRequest::query()->find($request->json('data.id'));
		if (!$mpos360McRequest) {
			throw new BusinessException('Lỗi: không tìm thấy yc đổi thông tin');
		}

		mylog(['BanGhiYeuCauChiTiet' => $mpos360McRequest]);
		$trangThaiXacThuc = $this->getTrangThaiXacThuc($mpos360McRequest);

		$trangThaiYeuCau = app(GetTrangThaiYcForMobileSubAction::class)->run($mpos360McRequest);

		$orderCode = !empty($mpos360McRequest->order_code) ? $mpos360McRequest->order_code : 'Đang cập nhật';
		$list = [
			[
				'label' => __('Id yêu cầu'),
				'value' =>  $mpos360McRequest->id,
				'other_data' => (object) [],
			],
			[
				'label' => __('Mã yêu cầu'),
				'value' => $orderCode,
				'other_data' => (object) [],
			],

			[
				'label' => 'Ngày khởi tạo',
				'value' => Carbon::createFromTimestamp($mpos360McRequest->time_created)->format('H:i d/m/Y'),
				'other_data' => (object) [],
			],

			[
				'label' => 'Trạng thái yêu cầu',
				'value' => $trangThaiYeuCau['name'],
				'other_data' => $trangThaiYeuCau['other_data']
			],
		];

		if (!$mpos360McRequest->isHetHanVaChuaTaoYc() && !$mpos360McRequest->isMcTuHuyYc()) {
			$list[] = [
				'label' => 'Trạng thái xác thực',
				'value' => $trangThaiXacThuc['name'],
				'other_data' => $trangThaiXacThuc['other_data']
			];
		}


		$trangThaiKyPhuLuc = $this->getTrangThaiKyPhuLuc($mpos360McRequest);
		if (!empty($trangThaiKyPhuLuc)) {
			$list[] = [
				'label' => 'Trạng thái ký phục lục',
				'value' => $trangThaiKyPhuLuc['name'],
				'other_data' => $trangThaiKyPhuLuc['other_data']
			];
		}
		

		$returnData = [
			'warning' => [],

			'data' => [
				[
					'key' => 'common_info',
					'name' => 'Thông tin chung',
					'list' => $list
				],
			],

			'other_data' => (object) [],
		];

		$groupThayDoi = app(GenGroupMnpDetailForMobileSubAction::class)->run($mpos360McRequest);

		foreach ($groupThayDoi as $group) {
			$returnData['data'][] = $group;
		}

		$can = [];

		if ($mpos360McRequest->status < Mpos360Enum::MPOS360_MC_REQUEST_STT_DA_GUI_SANG_MNP) {
			$can[] = [
				'can_code' => 'CAN_CANCEL_REQUEST',
				'target' => (object) [
					'screen' => '',
					'params' => (object) []
				]
			];
		}
		

		if (
			($mpos360McRequest->isChuaXacThuc() || $mpos360McRequest->isDaXacThucNhungChuaLamBuoc3())
			&& $mpos360McRequest->time_expired > now()->timestamp
		) {
			$can[] = [
				'can_code' => 'CAN_CONTINUE_MAKE_REQUEST',
				'target' => app(XuLyNoiTiepYeuDoiThongTinSubAction::class)->run($mpos360McRequest)
			];
		}

		$returnData['can'] = $can;
		return $returnData;
	}

	public function getTrangThaiXacThuc(Mpos360MerchantRequest $rq) {
		$returnData = [
			'name' => 'Không xác định',
			'other_data' => (object) [
				'text_color' => '#ffffff',
				'bg_color' => '#000000',
				'display_type' => 'pills'
			],
		];

		if ($rq->isKhongXacThuc()) {
			$returnData = [
				'name' => 'Không xác thực',
				'other_data' => (object) [
					'text_color' => '#ffffff',
					'bg_color' => '#000000',
					'display_type' => 'pills'
				],
			];
		}

		if ($rq->isChuaXacThuc()) {
			$returnData = [
				'name' => 'Chưa xác thực',
				'other_data' => (object) [
					'text_color' => '#ffffff',
					'bg_color' => '#ee9c00',
					'display_type' => 'pills'
				],
			];
		}

		if ($rq->isDaXacThucNhungChuaLamBuoc3()) {
			$returnData = [
				'name' => 'Cần xác minh bước 3',
				'other_data' => (object) [
					'text_color' => '#ffffff',
					'bg_color' => '#018bf4',
					'display_type' => 'pills'
				],
			];
		}

		if ($rq->isDaLamBuoc3()) {
			$returnData['name'] = 'Đã xác thực';
			$returnData['other_data'] = (object) [
				'text_color' => '#ffffff',
				'bg_color' => '#3bb54a',
				'display_type' => 'pills'
			];
		}

		return $returnData;
	}

	public function getTrangThaiYeuCau(Mpos360MerchantRequest $rq) {
		$returnData = [
			'name' => 'Không xác định',
			'other_data' => (object) [
				'text_color' => '#ffffff',
				'bg_color' => '#000000',
				'display_type' => 'pills'
			],
		];

		if ($rq->isHetHanVaChuaTaoYc()) {
			$returnData['name'] = 'Hết hạn';
			$returnData['other_data'] = (object) [
				'text_color' => '#ffffff',
				'bg_color' => '#808890',
				'display_type' => 'pills'
			];

			return $returnData;
		}

		if ($rq->isMcTuHuyYc()) {
			$returnData['name'] = 'MC hủy YC';
			$returnData['other_data'] = (object) [
				'text_color' => '#ffffff',
				'bg_color' => '#da2128',
				'display_type' => 'pills'
			];

			return $returnData;
		}

		$returnData['name'] = ConfigYeuCauThayDoiSubAction::getCog('status', $rq->status, 'label');
		$returnData['other_data'] = (object) [
			'text_color' => ConfigYeuCauThayDoiSubAction::getCog('status', $rq->status, 'text_color'),
			'bg_color' => ConfigYeuCauThayDoiSubAction::getCog('status', $rq->status, 'bg_color'),
			'display_type' => 'pills'
		];

		return $returnData;
	}

	public function getTrangThaiKyPhuLuc(Mpos360MerchantRequest $mpos360McRequest) {
		switch ($mpos360McRequest->status_sign) {
			case 0: 
				return [];
				break;

			case Mpos360Enum::MPOS360_MC_SIGN_STT_CHUA_KY: 
				return [
					'name' => 'Chưa ký',
					'other_data' => [ 'text_color' => '#ffffff', 'bg_color' => '#e99323', 'display_type' => 'pills' ]
				];
				break;

			case Mpos360Enum::MPOS360_MC_SIGN_STT_DANG_KY: 
				return [
					'name' => 'Đang gửi ký',
					'other_data' => [ 'text_color' => '#ffffff', 'bg_color' => '#018bf4', 'display_type' => 'pills' ]
				];
				break;

			case Mpos360Enum::MPOS360_MC_SIGN_STT_DA_KY: 
				return [
					'name' => 'Đã gửi ký',
					'other_data' => [ 'text_color' => '#ffffff', 'bg_color' => '#3bb54a', 'display_type' => 'pills' ]
				];
				break;

			case Mpos360Enum::MPOS360_MC_SIGN_STT_KY_LOI: 
				return [
					'name' => 'Gửi ký lỗi',
					'other_data' => [ 'text_color' => '#ffffff', 'bg_color' => '#da2128', 'display_type' => 'pills' ]
				];
				break;

			default:
				return [
					'name' => 'Không xác định',
					'other_data' => [ 'text_color' => '#ffffff', 'bg_color' => '#000000', 'display_type' => 'pills' ]
				];
				break;
		}
	}
} // End class
