<?php

namespace App\Modules\TingBoxVAMC\Actions\MerchantBank\LinkBankAction\SubAction;

use App\Modules\TingBoxVAMC\Models\MerchantShopBank;

class CreateMerchantShopBankSubAction
{

	public function run($params = []): MerchantShopBank
	{
		$p =  [
			'merchant_id'         => $params['merchant_id'],
			'merchant_bank_id'    => $params['merchant_bank_id'],
			'is_default'       		=> $params['is_default'] == 1 ? 1 : 0,
			'account_type'        => $params['account_type'],
			'account_qr'          => $params['account_qr'],
			'account_qr_display'  => $params['account_qr_display'],
			'data_linked'         => $params['data_linked'],
			'request_id'          => $params['request_id'],
			'partner_request_id'  => $params['partner_request_id'],
			'partner_code'        => $params['partner_code'],
			'shop_id'             => $params['shop_id'],
			'is_sync_tingbox'     => $params['is_sync_tingbox'],
			'number_sync_tingbox' => 0,
			'status_link'         => $params['status_link'],
			'time_created'        => time(),
			'time_updated'        => time(),

			'linkingMaxResend' => $params['linkingMaxResend'],
			'linkingTempLockedTime' => $params['linkingTempLockedTime'],
			'linkingOtpExpired' => $params['linkingOtpExpired'],
			'linkingMaxSubmit' => $params['linkingMaxSubmit'],
			'isNeedCreateTrungGian' => $params['isNeedCreateTrungGian'] ?? 1
		];

	
		$r = MerchantShopBank::query()->forceCreate($p);
		return $r;
	}
}
