<?php
namespace App\Modules\TingBoxVAMC\Requests\MerchantBank;

use App\Modules\Merchant\Requests\MerchantRequest;

class Mpos360CheckSwitchModeBankRequest extends MerchantRequest
{
    /**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'data.username' => ['required', 'string'],
			'data.merchantId' => ['required', 'numeric'],
			'data.merchantShopBankId' => ['required', 'numeric', 'integer', 'min:1'],
		];
	}

	public function messages() {
		return [
			'data.username.required' => 'username là bắt buộc',
			'data.username.string' => 'username phải là kiểu chuỗi ký tự',
			'data.merchantId.required' => 'Id merchant là bắt buộc',
			'data.merchantId.numeric' => 'Id merchant phải là kiểu số',
			'data.merchantShopBankId.required' => 'ID liên kết là bắt buộc',
			'data.merchantShopBankId.numeric' => 'ID liên kết phải là kiểu số',
			'data.merchantShopBankId.integer' => 'ID liên kết phải là kiểu số nguyên',
			'data.merchantShopBankId.min' => 'ID liên kết phải phải có giá trị nhỏ nhất là 1',
		];
	}
} // End class